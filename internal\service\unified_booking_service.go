package service

import (
	"errors"
	"fmt"
	"strings"
	"time"
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"gorm.io/gorm"
)

// UnifiedBookingService 统一预订服务
type UnifiedBookingService struct {
	db                       *gorm.DB
	flexibleDeductionService *FlexibleDeductionService
	dailyLimitService        *DailyLimitService
}

// NewUnifiedBookingService 创建统一预订服务
func NewUnifiedBookingService(db *gorm.DB) *UnifiedBookingService {
	return &UnifiedBookingService{
		db:                       db,
		flexibleDeductionService: NewFlexibleDeductionService(db),
		dailyLimitService:        NewDailyLimitService(db),
	}
}

// BookingRequest 统一预订请求
type BookingRequest struct {
	UserID           string `json:"user_id"`
	CourseID         uint   `json:"course_id"`                    // 使用CourseID而不是ScheduleID
	MembershipCardID uint   `json:"membership_card_id,omitempty"` // 可选，系统可以智能选择
	PeopleCount      int    `json:"people_count"`
}

// BookingResult 预订结果
type BookingResult struct {
	Success         bool    `json:"success"`
	BookingID       uint    `json:"booking_id,omitempty"`
	QueuePosition   int     `json:"queue_position,omitempty"`
	Message         string  `json:"message"`
	DeductedAmount  float64 `json:"deducted_amount,omitempty"`
	DeductedTimes   int     `json:"deducted_times,omitempty"`
	RemainingAmount float64 `json:"remaining_amount,omitempty"`
	RemainingTimes  int     `json:"remaining_times,omitempty"`
}

// ProcessBooking 处理预订（支持立即预订和排队）
func (s *UnifiedBookingService) ProcessBooking(req BookingRequest) (*BookingResult, error) {
	// 1. 验证课程
	course, err := s.validateSchedule(req.CourseID)
	if err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	// 2. 检查容量
	hasCapacity, err := s.checkCapacity(course, req.PeopleCount)
	if err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	// 3. 智能选择会员卡
	membershipCard, err := s.selectMembershipCard(req.UserID, course.ID, req.MembershipCardID)
	if err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	// 3.1 验证副卡预约权限
	if err := s.validateSubCardPermission(req, membershipCard); err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	// 3.2 检查单日预约限制
	if err := s.validateDailyLimit(req, membershipCard, course); err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	// 4. 使用灵活扣减服务查找规则
	deductionCtx := DeductionContext{
		CardTypeID:     membershipCard.CardTypeID,
		CourseID:       course.ID,
		CourseTypeCode: s.getCourseTypeCode(course),
		CourseName:     course.Name,
		PeopleCount:    req.PeopleCount,
		BookingTime:    time.Now(),
		UserID:         req.UserID,
	}

	deductionResult, err := s.flexibleDeductionService.FindMatchingRule(deductionCtx)
	if err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	if !deductionResult.CanBook {
		return &BookingResult{Success: false, Message: deductionResult.Message}, errors.New(deductionResult.Message)
	}

	// 5. 获取计算好的扣减量
	actualAmount := deductionResult.DeductionAmount
	actualTimes := deductionResult.DeductionTimes

	// 6. 验证余额
	if err := s.validateBalance(membershipCard, actualAmount, actualTimes); err != nil {
		return &BookingResult{Success: false, Message: err.Error()}, err
	}

	if hasCapacity {
		// 立即预订
		return s.createBooking(req, course, membershipCard, deductionResult.Rule, actualAmount, actualTimes)
	} else {
		// 加入排队
		return s.addToQueue(req, course)
	}
}

// validateSchedule 验证排期（使用Course模型）
func (s *UnifiedBookingService) validateSchedule(courseID uint) (*model.Course, error) {
	var course model.Course
	if err := s.db.First(&course, courseID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("课程不存在")
		}
		return nil, err
	}

	// 检查课程状态
	if course.Status != enum.CourseStatusNormal && course.Status != enum.CourseStatusPublished {
		return nil, errors.New("课程不可预约")
	}

	// 检查课程时间（如果有具体时间）
	if !course.StartTime.IsZero() && course.StartTime.Before(time.Now()) {
		return nil, errors.New("课程已开始，无法预约")
	}

	return &course, nil
}

// checkCapacity 检查容量
func (s *UnifiedBookingService) checkCapacity(course *model.Course, peopleCount int) (bool, error) {
	// 查询当前预订人数（使用课程ID）
	var currentBookings int64
	if err := s.db.Model(&model.Booking{}).
		Where("schedule_id = ? AND status IN (1, 2)", course.ID). // 已预约、已签到
		Count(&currentBookings).Error; err != nil {
		return false, err
	}

	availableCapacity := course.Capacity - int(currentBookings)
	return availableCapacity >= peopleCount, nil
}

// selectMembershipCard 智能选择会员卡
func (s *UnifiedBookingService) selectMembershipCard(userID string, courseID uint, preferredCardID uint) (*model.MembershipCard, error) {
	// 如果指定了会员卡ID，直接使用
	if preferredCardID > 0 {
		var card model.MembershipCard
		if err := s.db.Where("id = ? AND user_id = ? AND status = 1", preferredCardID, userID).First(&card).Error; err != nil {
			return nil, errors.New("指定的会员卡不存在或不可用")
		}
		return &card, nil
	}

	// 智能选择：查找支持该课程的会员卡
	var cards []model.MembershipCard
	if err := s.db.Where("user_id = ? AND status = 1", userID).Find(&cards).Error; err != nil {
		return nil, err
	}

	if len(cards) == 0 {
		return nil, errors.New("用户没有可用的会员卡")
	}

	// 使用灵活扣减系统查找支持的会员卡
	for _, card := range cards {
		// 构建扣减上下文
		deductionCtx := DeductionContext{
			CardTypeID:  card.CardTypeID,
			CourseID:    courseID,
			PeopleCount: 1, // 默认1人
			BookingTime: time.Now(),
			UserID:      userID,
		}

		// 查找匹配的规则
		result, err := s.flexibleDeductionService.FindMatchingRule(deductionCtx)
		if err == nil && result.CanBook {
			// 检查余额是否足够
			if (result.DeductionAmount == 0 || card.RemainingAmount >= result.DeductionAmount) &&
				(result.DeductionTimes == 0 || card.RemainingTimes >= result.DeductionTimes) {
				return &card, nil
			}
		}
	}

	return nil, errors.New("没有可用的会员卡支持此课程")
}

// 注意：findDeductionRule 和 calculateDeduction 方法已移除
// 现在使用灵活扣减系统的 FindMatchingRule 方法

// validateBalance 验证余额和有效性
func (s *UnifiedBookingService) validateBalance(card *model.MembershipCard, amount int, times int) error {
	// 检查会员卡是否过期
	if !card.EndDate.IsZero() && card.EndDate.Before(time.Now()) {
		return errors.New("会员卡已过期")
	}

	// 检查会员卡状态
	if card.Status != 1 {
		return errors.New("会员卡状态异常")
	}

	if amount > 0 && card.RemainingAmount < amount {
		return errors.New("会员卡余额不足")
	}
	if times > 0 && card.RemainingTimes < times {
		return errors.New("会员卡次数不足")
	}
	return nil
}

// createBooking 创建预订
func (s *UnifiedBookingService) createBooking(req BookingRequest, course *model.Course,
	card *model.MembershipCard, rule *model.FlexibleDeductionRule, amount int, times int) (*BookingResult, error) {

	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 扣减会员卡
	if amount > 0 || times > 0 {
		updates := make(map[string]any)
		if amount > 0 {
			updates["remaining_amount"] = gorm.Expr("remaining_amount - ?", amount)
		}
		if times > 0 {
			updates["remaining_times"] = gorm.Expr("remaining_times - ?", times)
		}

		if err := tx.Model(card).Updates(updates).Error; err != nil {
			tx.Rollback()
			return &BookingResult{Success: false, Message: "扣减会员卡失败"}, err
		}
	}

	// 创建预订记录
	booking := model.Booking{
		UserID:           req.UserID,
		ScheduleID:       req.CourseID, // 使用CourseID作为ScheduleID
		MembershipCardID: card.ID,
		BookingNumber:    s.generateBookingNumber(),
		PeopleCount:      req.PeopleCount,
		DeductTimes:      times,
		DeductAmount:     amount,
		Status:           1, // 已预约
	}

	if err := tx.Create(&booking).Error; err != nil {
		tx.Rollback()
		return &BookingResult{Success: false, Message: "创建预订失败"}, err
	}

	// 记录交易历史
	if amount > 0 || times > 0 {
		transaction := model.MembershipCardTransaction{
			CardID:           card.ID,
			TransactionType:  enum.CardTransactionTypeDeduct,
			AmountChange:     -amount,
			TimesChange:      -times,
			BeforeAmount:     card.RemainingAmount,
			BeforeTimes:      card.RemainingTimes,
			AfterAmount:      card.RemainingAmount - amount,
			AfterTimes:       card.RemainingTimes - times,
			OperatorID:       req.UserID,
			OperatorName:     "用户预订",
			Reason:           fmt.Sprintf("预订课程：%s", course.Name),
			RelatedBookingID: &booking.ID,
		}

		if err := tx.Create(&transaction).Error; err != nil {
			tx.Rollback()
			return &BookingResult{Success: false, Message: "记录交易历史失败"}, err
		}
	}

	if err := tx.Commit().Error; err != nil {
		return &BookingResult{Success: false, Message: "提交事务失败"}, err
	}

	log.Info("预订成功", "booking_id", booking.ID, "user_id", req.UserID, "course_id", req.CourseID)

	return &BookingResult{
		Success:         true,
		BookingID:       booking.ID,
		Message:         "预订成功",
		DeductedAmount:  float64(amount) / 100,
		DeductedTimes:   times,
		RemainingAmount: float64(card.RemainingAmount-amount) / 100,
		RemainingTimes:  card.RemainingTimes - times,
	}, nil
}

// addToQueue 加入排队
func (s *UnifiedBookingService) addToQueue(req BookingRequest, course *model.Course) (*BookingResult, error) {
	// 获取下一个排队号
	var maxQueueNumber int
	s.db.Model(&model.BookingQueue{}).
		Where("course_id = ? AND status = 1", course.ID).
		Select("COALESCE(MAX(queue_number), 0)").Scan(&maxQueueNumber)

	queue := model.BookingQueue{
		UserID:      req.UserID,
		CourseID:    course.ID,
		QueueNumber: maxQueueNumber + 1,
		Status:      1, // 排队中
	}

	if err := s.db.Create(&queue).Error; err != nil {
		return &BookingResult{Success: false, Message: "加入排队失败"}, err
	}

	log.Info("加入排队", "user_id", req.UserID, "course_id", course.ID, "queue_position", queue.QueueNumber)

	return &BookingResult{
		Success:       true,
		QueuePosition: queue.QueueNumber,
		Message:       fmt.Sprintf("课程已满，您已加入排队，排队位置：%d", queue.QueueNumber),
	}, nil
}

// generateBookingNumber 生成预订单号
func (s *UnifiedBookingService) generateBookingNumber() string {
	return fmt.Sprintf("BK%d", time.Now().Unix())
}

// getCourseTypeCode 获取课程类型代码
func (s *UnifiedBookingService) getCourseTypeCode(course *model.Course) string {
	// 根据课程类型枚举映射到类型代码
	switch course.Type {
	case 1: // 团课
		// 可以根据课程名称进一步细分
		if strings.Contains(course.Name, "普拉提") {
			return "pilates_small"
		}
		if strings.Contains(course.Name, "提升") {
			return "advanced_small"
		}
		if strings.Contains(course.Name, "特色") {
			return "special_small"
		}
		if strings.Contains(course.Name, "舞蹈") {
			if course.Duration >= 90 {
				return "dance_90"
			}
			return "dance_60"
		}
		return "yoga_group" // 默认瑜伽团课
	case 2: // 私教
		if strings.Contains(course.Name, "明星") {
			if strings.Contains(course.Name, "1V2") {
				return "private_star_1v2"
			}
			return "private_star_1v1"
		}
		return "private_std"
	case 3: // 小班课
		if strings.Contains(course.Name, "普拉提") {
			return "pilates_small"
		}
		if strings.Contains(course.Name, "提升") {
			return "advanced_small"
		}
		return "special_small"
	default:
		return "yoga_group"
	}
}

// validateSubCardPermission 验证副卡预约权限
func (s *UnifiedBookingService) validateSubCardPermission(req BookingRequest, card *model.MembershipCard) error {
	// 如果是主卡，直接允许
	if card.IsMainCard {
		return nil
	}

	// 如果是副卡，检查是否是卡片持有者本人
	if card.UserID == req.UserID {
		return nil
	}

	// 如果是主卡持有者为副卡预约，也允许
	if card.MainCardID != nil {
		var mainCard model.MembershipCard
		if err := s.db.First(&mainCard, *card.MainCardID).Error; err == nil {
			if mainCard.UserID == req.UserID {
				return nil
			}
		}
	}

	return errors.New("无权限使用该会员卡预约")
}

// validateDailyLimit 验证单日预约限制
func (s *UnifiedBookingService) validateDailyLimit(req BookingRequest, card *model.MembershipCard, course *model.Course) error {
	// 使用单日限制服务验证
	return s.dailyLimitService.ValidateBookingWithLimits(
		card.ID,
		req.UserID,
		course.ID,
		req.PeopleCount,
		time.Now(),
	)
}
