# 批量删除功能实现文档

## 📋 概述

本文档描述了项目中批量删除功能的通用化实现，包括工具函数、API设计和使用方式。

## 🛠️ 核心工具函数

### 核心工具函数

位置：`pkg/utils/batch.go`

#### `utils.ParseBatchIDs()` - 数字ID解析

**功能**：解析 `uint` 类型的批量ID列表。

**特性**：
- ✅ 使用 `spf13/cast` 库进行安全的类型转换
- ✅ 支持查询参数格式：`?ids=1,2,3,4`
- ✅ 支持请求体格式：`[1, 2, 3, 4]`
- ✅ 自动过滤无效ID（≤0的值）
- ✅ 统一的错误处理

```go
// 使用示例
ids, err := utils.ParseBatchIDs(c)
if err != nil {
    code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
    return
}
```

#### `utils.ParseBatchStrings()` - 字符串ID解析

**功能**：解析 `string` 类型的批量ID列表。

**使用场景**：适用于使用字符串作为主键的资源。

```go
// 查询参数: ?ids=abc,def,ghi
// 请求体: ["abc", "def", "ghi"]
ids, err := utils.ParseBatchStrings(c)
```

#### `utils.ParseBatchUUIDs()` - UUID解析

**功能**：解析 `uuid.UUID` 类型的批量ID列表。

**使用场景**：适用于使用UUID作为主键的资源。

**特性**：
- ✅ 自动验证UUID格式
- ✅ 解析错误时返回详细错误信息

```go
// 查询参数: ?ids=550e8400-e29b-41d4-a716-************,6ba7b810-9dad-11d1-80b4-00c04fd430c8
// 请求体: ["550e8400-e29b-41d4-a716-************", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"]
ids, err := utils.ParseBatchUUIDs(c)
```

#### `utils.ParseBatchIDsWithType[T]()` - 泛型类型解析

**功能**：支持指定类型的批量ID解析（泛型函数）。

**支持类型**：`uint`, `uint32`, `uint64`, `int`, `int32`, `int64`

```go
// 使用示例
ids, err := utils.ParseBatchIDsWithType[int64](c)
```

## 🎯 RESTful API 设计

### 设计原则

遵循RESTful最佳实践，使用资源导向的URL设计：

- ❌ **错误方式**：`DELETE /api/v1/admin/menus/batch`
- ✅ **正确方式**：`DELETE /api/v1/admin/menus`

### 支持的请求方式

#### 方式1：查询参数（推荐）
```bash
DELETE /api/v1/admin/menus?ids=1,2,3,4
DELETE /api/v1/admin/roles?ids=1,2,3,4
DELETE /api/v1/admin/permissions?ids=1,2,3,4
```

#### 方式2：请求体
```bash
DELETE /api/v1/admin/menus
Content-Type: application/json
[1, 2, 3, 4]
```

## 📡 已实现的批量删除API

| 资源 | 单个删除 | 批量删除 | 权限 | ID类型 |
|------|----------|----------|------|--------|
| 菜单 | `DELETE /menus/:id` | `DELETE /menus` | `menu:delete` | uint |
| 角色 | `DELETE /roles/:id` | `DELETE /roles` | `role:delete` | uint |
| 权限 | `DELETE /permissions/:id` | `DELETE /permissions` | `permission:delete` | uint |
| 文件 | `DELETE /files/:id` | `DELETE /files` | `file:delete` | string |

## 🎯 一致性要求

### 强制性规范

**所有批量删除接口必须遵循以下规范：**

1. **参数格式一致性**：
   - ✅ 只支持 `[1, 2, 3, 4]` 格式（数组直接传递）
   - ❌ 禁止使用 `{"ids": [1, 2, 3, 4]}` 格式
   - ❌ 禁止使用 `{"file_ids": [...]}` 等自定义字段名

2. **工具函数使用**：
   - ✅ 必须使用 `utils.ParseBatchIDs()` (uint类型)
   - ✅ 必须使用 `utils.ParseBatchStrings()` (string类型)
   - ✅ 必须使用 `utils.ParseBatchUUIDs()` (UUID类型)

3. **路由设计**：
   - ✅ 使用 `DELETE /resource` 而不是 `POST /resource/batch-delete`
   - ✅ 符合 RESTful 设计原则

4. **错误处理**：
   - ✅ 统一的错误响应格式
   - ✅ 详细的日志记录

## 🔧 实现特性

### 1. 菜单批量删除
- ✅ 检查菜单存在性
- ✅ 检查子菜单依赖关系
- ✅ 防止删除有子菜单的父菜单

### 2. 角色批量删除
- ✅ 检查角色存在性
- ✅ 事务安全操作
- ✅ 自动清理Casbin策略
- ✅ 清理用户-角色关联

### 3. 权限批量删除
- ✅ 检查权限存在性
- ✅ 检查角色使用情况
- ✅ 检查菜单使用情况
- ✅ 防止删除正在使用的权限

### 4. 文件批量删除
- ✅ 检查文件存在性
- ✅ 软删除机制
- ✅ 部分失败处理

## 📝 使用示例

### JavaScript/前端调用

```javascript
// 方式1：查询参数
const deleteMenus = async (ids) => {
  const response = await fetch(`/api/v1/admin/menus?ids=${ids.join(',')}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// 方式2：请求体
const deleteMenus = async (ids) => {
  const response = await fetch('/api/v1/admin/menus', {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(ids)
  });
  return response.json();
};
```

### cURL 示例

```bash
# 批量删除菜单
curl -X DELETE "http://localhost:9095/api/v1/admin/menus?ids=1,2,3,4" \
  -H "Authorization: Bearer your-token"

# 批量删除角色
curl -X DELETE "http://localhost:9095/api/v1/admin/roles" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '[1, 2, 3, 4]'
```

## 🛡️ 安全特性

### 1. 参数验证
- ID列表不能为空
- 自动过滤无效ID
- 类型安全转换

### 2. 业务逻辑检查
- 资源存在性验证
- 依赖关系检查
- 使用情况验证

### 3. 权限控制
- 使用相同的权限系统
- 统一的权限检查中间件

### 4. 事务安全
- 数据库事务保护
- 失败自动回滚
- Casbin策略同步

## 🎉 优势总结

1. **代码复用**：通用工具函数减少重复代码
2. **类型安全**：使用cast库进行安全转换
3. **RESTful规范**：符合资源导向的API设计
4. **灵活性**：支持多种请求方式
5. **安全性**：完整的验证和权限控制
6. **一致性**：统一的实现模式和错误处理

## 🔄 扩展指南

为新的资源添加批量删除功能：

1. **添加import**：
```go
import "yogaga/pkg/utils"
```

2. **实现BatchDelete方法**：
```go
func (h *YourHandler) BatchDelete(c *gin.Context) {
    ids, err := utils.ParseBatchIDs(c)
    if err != nil {
        // 错误处理
    }
    // 业务逻辑
}
```

3. **添加路由**：
```go
yourGroup.DELETE("", middleware.CheckPermissionByKey("your:delete", enforcer), yourHandler.BatchDelete)
```

## 🌟 不同ID类型的使用示例

### 数字ID（当前实现）
```go
// 菜单、角色、权限等使用 uint 类型ID
func (h *MenuHandler) BatchDelete(c *gin.Context) {
    ids, err := utils.ParseBatchIDs(c)
    // 处理逻辑...
}
```

### 字符串ID
```go
// 假设某个资源使用字符串ID
func (h *CategoryHandler) BatchDelete(c *gin.Context) {
    ids, err := utils.ParseBatchStrings(c)
    if err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
        return
    }
    // 删除逻辑: DELETE FROM categories WHERE code IN (ids)
}

// API调用示例:
// DELETE /api/v1/categories?ids=tech,sports,news
// DELETE /api/v1/categories
// Body: ["tech", "sports", "news"]
```

### UUID类型ID
```go
// 假设文件资源使用UUID作为ID
func (h *FileHandler) BatchDelete(c *gin.Context) {
    ids, err := utils.ParseBatchUUIDs(c)
    if err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的UUID格式"))
        return
    }
    // 删除逻辑: DELETE FROM files WHERE id IN (ids)
}

// API调用示例:
// DELETE /api/v1/files?ids=550e8400-e29b-41d4-a716-************,6ba7b810-9dad-11d1-80b4-00c04fd430c8
// DELETE /api/v1/files
// Body: ["550e8400-e29b-41d4-a716-************", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"]
```

### 其他数字类型
```go
// 使用 int64 类型ID
func (h *LogHandler) BatchDelete(c *gin.Context) {
    ids, err := utils.ParseBatchIDsWithType[int64](c)
    if err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
        return
    }
    // 删除逻辑...
}
```

## 🎯 类型选择指南

| ID类型 | 使用场景 | 工具函数 | 示例 |
|--------|----------|----------|------|
| `uint` | 自增主键 | `ParseBatchIDs()` | `[1, 2, 3]` |
| `string` | 业务编码 | `ParseBatchStrings()` | `["code1", "code2"]` |
| `uuid.UUID` | 分布式ID | `ParseBatchUUIDs()` | `["550e8400-..."]` |
| `int64` | 大数字ID | `ParseBatchIDsWithType[int64]()` | `[1001, 1002]` |

这样的设计确保了代码的一致性、可维护性和扩展性！
