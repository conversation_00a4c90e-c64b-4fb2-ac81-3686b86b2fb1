// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblQueneup = "tbl_queneup"

// TblQueneup mapped from table <tbl_queneup>
type TblQueneup struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	MemberID   int32     `gorm:"column:member_id;not null" json:"member_id"`
	ClassID    int32     `gorm:"column:class_id;not null" json:"class_id"`
	IsOrder    bool      `gorm:"column:is_order;not null" json:"is_order"`
	PeopleNum  int32     `gorm:"column:people_num;not null;default:1" json:"people_num"`
}

// TableName TblQueneup's table name
func (*TblQueneup) TableName() string {
	return TableNameTblQueneup
}
