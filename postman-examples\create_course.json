// ===== 创建课程 API 请求示例 =====
// 接口：POST /api/v1/admin/courses
// 认证：需要管理员Token
// 说明：创建课程基本信息，不包含排课时间（分离式创建）

{
  "name": "哈他瑜伽基础课",                      // 课程名称，必填，最大100字符
                                              // 说明：课程的显示名称，同一门店下不能重复
                                              // 验证：系统会检查同门店下是否已存在同名课程
                                              
  "description": "适合初学者的哈他瑜伽课程，注重基础体式练习和呼吸调节", // 课程描述，可选，最大500字符
                                              // 说明：课程的详细介绍，向用户展示课程内容和特色
                                              
  "duration": 60,                            // 课程时长，必填，整数，单位：分钟
                                              // 范围：15-480分钟（15分钟到8小时）
                                              // 常见值：60(1小时), 90(1.5小时), 120(2小时)
                                              // 验证：系统会检查时长是否在合理范围内
                                              
  "capacity": 20,                            // 课程容量，必填，正整数
                                              // 范围：1-100人
                                              // 说明：该课程最多可容纳的学员人数
                                              // 团课通常：15-30人，小班课：5-10人，私教：1-2人
                                              
  "price": 8000,                             // 课程价格，必填，非负整数，单位：分
                                              // 说明：课程的标准价格，以分为单位避免小数问题
                                              // 示例：80元 = 8000分，免费课程 = 0分
                                              // 注意：实际扣费通过灵活扣减系统配置
                                              
  "level": 1,                                // 课程级别，必填，整数
                                              // 可选值：1(初级), 2(中级), 3(高级)
                                              // 说明：课程难度等级，帮助用户选择合适的课程
                                              
  "category_id": 1,                          // 课程分类ID，必填，正整数
                                              // 说明：课程所属的分类，必须是启用状态的分类
                                              // 获取方式：GET /api/v1/admin/course-categories
                                              // 验证：系统会检查分类是否存在且启用
                                              
  "store_id": 1,                             // 门店ID，必填，正整数
                                              // 说明：课程所属的门店，必须是存在的门店
                                              // 获取方式：GET /api/v1/admin/stores
                                              // 验证：系统会检查门店是否存在
                                              
  "type": 1,                                 // 课程类型，必填，整数
                                              // 可选值：1(团课), 2(私教), 3(小班课)
                                              // 说明：课程的基本类型，影响预约和扣费逻辑
                                              // 团课：多人参与，统一时间
                                              // 私教：1对1或1对2，时间灵活
                                              // 小班课：小规模团体，精品教学
                                              
  "deduction_rules": [                       // 扣减规则，可选，数组
                                              // 说明：定义不同会员卡类型的扣费规则
                                              // 注意：现在推荐使用灵活扣减系统单独配置
    {
      "card_type_id": 1,                     // 会员卡类型ID，必填
      "deduction_type": "times",             // 扣减类型，必填
                                              // 可选值：times(按次数), amount(按金额), period(期限卡)
      "deduction_times": 1,                  // 扣减次数，当deduction_type=times时必填
      "deduction_amount": 0,                 // 扣减金额，当deduction_type=amount时必填，单位：分
      "per_person_deduction": false,         // 是否按人数计算扣减，可选，默认false
      "description": "团课卡扣减1次"          // 扣减说明，可选
    }
  ]
}

// ===== 不同类型课程创建示例 =====

// 1. 团课示例
{
  "name": "流瑜伽中级课",
  "description": "动态流畅的瑜伽练习，适合有一定基础的学员",
  "duration": 75,                            // 75分钟
  "capacity": 25,                            // 25人
  "price": 10000,                            // 100元
  "level": 2,                                // 中级
  "category_id": 1,                          // 瑜伽团课分类
  "store_id": 1,
  "type": 1                                  // 团课
}

// 2. 私教示例
{
  "name": "个人定制瑜伽私教",
  "description": "一对一个性化瑜伽指导，根据个人需求定制练习方案",
  "duration": 60,                            // 60分钟
  "capacity": 1,                             // 1人
  "price": 30000,                            // 300元
  "level": 1,                                // 初级（可根据学员调整）
  "category_id": 7,                          // 标准私教分类
  "store_id": 1,
  "type": 2                                  // 私教
}

// 3. 小班课示例
{
  "name": "普拉提核心训练小班",
  "description": "专注核心力量训练的普拉提小班课，精品小班教学",
  "duration": 50,                            // 50分钟
  "capacity": 8,                             // 8人小班
  "price": 15000,                            // 150元
  "level": 2,                                // 中级
  "category_id": 2,                          // 普拉提小班课分类
  "store_id": 1,
  "type": 3                                  // 小班课
}

// 4. 免费体验课示例
{
  "name": "瑜伽免费体验课",
  "description": "新会员免费体验课程，了解瑜伽基础知识和简单体式",
  "duration": 45,                            // 45分钟
  "capacity": 15,                            // 15人
  "price": 0,                                // 免费
  "level": 1,                                // 初级
  "category_id": 1,                          // 瑜伽团课分类
  "store_id": 1,
  "type": 1                                  // 团课
}

// ===== 业务逻辑说明 =====

/*
1. 课程创建逻辑：
   - 这是分离式创建，只创建课程基本信息
   - 不包含具体的上课时间和教练安排
   - 排课需要通过课程排期接口单独创建

2. 验证逻辑：
   - 门店存在性验证
   - 课程分类存在性验证
   - 同门店下课程名称唯一性验证
   - 时长合理性验证（15-480分钟）
   - 容量合理性验证（1-100人）

3. 自动设置：
   - 状态默认为正常(status=1)
   - 报名人数默认为0(enroll_count=0)
   - 创建时间和更新时间自动设置

4. 扣减规则（可选）：
   - 现在推荐使用灵活扣减系统单独配置
   - 如果传入deduction_rules，会创建对应的扣减规则
   - 不传入则需要后续通过灵活扣减系统配置

5. 课程类型说明：
   - type=1 团课：多人集体上课，固定时间
   - type=2 私教：1对1或1对2，时间灵活
   - type=3 小班课：小规模精品教学

6. 价格说明：
   - price字段是课程标准价格，用于显示
   - 实际扣费金额通过扣减规则或灵活扣减系统配置
   - 可以设置为0表示免费课程
*/

// ===== 常见分类ID参考 =====
/*
根据系统初始化数据，常见的分类ID：
1 - 瑜伽团课
2 - 普拉提小班课  
3 - 提升小班课
4 - 特色小班课
5 - 舞蹈课60min
6 - 舞蹈课90min
7 - 标准私教1V1
8 - 明星私教1V1
9 - 明星私教1V2

具体可通过 GET /api/v1/admin/course-categories 获取最新列表
*/

// ===== 常见错误和解决方案 =====

/*
1. "门店不存在" - 检查store_id是否正确
2. "课程分类不存在" - 检查category_id是否存在且启用
3. "该门店下已存在同名课程" - 修改课程名称或检查是否重复创建
4. "课程时长不能少于15分钟" - 调整duration到合理范围
5. "课程时长不能超过5小时" - 检查duration是否过大
6. "请求参数错误" - 检查必填字段和数据类型
*/

// ===== 成功响应示例 =====
/*
{
  "code": 0,
  "message": "success", 
  "data": {
    "id": 123,
    "name": "哈他瑜伽基础课",
    "description": "适合初学者的哈他瑜伽课程",
    "duration": 60,
    "capacity": 20,
    "enroll_count": 0,
    "price": 80.00,
    "level": 1,
    "category_id": 1,
    "status": 1,
    "store_id": 1,
    "type": 1,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "store": {
      "id": 1,
      "name": "总店",
      "address": "北京市朝阳区xxx"
    },
    "category": {
      "id": 1,
      "name": "瑜伽团课",
      "description": "瑜伽团体课程"
    }
  }
}
*/

// ===== 关于错误 "Coach: unsupported relations for schema Course" =====
/*
这个错误可能是因为：
1. Course模型中的Coach关联定义有问题
2. 查询时使用了不正确的Preload
3. 数据库表结构与模型定义不匹配

建议检查：
- Course模型中是否正确定义了Coach关联
- 查询课程时是否使用了错误的Preload("Coach")
- 数据库中是否存在对应的外键字段

当前Course模型使用CoachIDs数组存储多教练，不是直接的外键关联
*/
