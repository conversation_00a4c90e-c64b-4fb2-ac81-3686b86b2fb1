package service

import (
	"fmt"
	"time"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

// MembershipReminderService 会员卡提醒服务
type MembershipReminderService struct {
	db            *gorm.DB
	wechatService *WeChatService
	asynqClient   *asynq.Client
}

// NewMembershipReminderService 创建会员卡提醒服务实例
func NewMembershipReminderService(db *gorm.DB, wechatService *WeChatService, asynqClient *asynq.Client) *MembershipReminderService {
	return &MembershipReminderService{
		db:            db,
		wechatService: wechatService,
		asynqClient:   asynqClient,
	}
}

// CheckExpiringCards 检查即将到期的会员卡
func (s *MembershipReminderService) CheckExpiringCards() error {
	log.Info("开始检查即将到期的会员卡")

	// 计算60天后的日期
	expiryDate := time.Now().AddDate(0, 0, 60)

	// 查询60天内到期的期限卡和储值卡
	var cards []model.MembershipCard
	err := s.db.Preload("User").Preload("CardType").
		Where("status = 1 AND end_date IS NOT NULL AND end_date <= ? AND end_date > ?",
			expiryDate, time.Now()).
		Find(&cards).Error

	if err != nil {
		log.Error("查询即将到期的会员卡失败", "error", err)
		return fmt.Errorf("查询即将到期的会员卡失败: %w", err)
	}

	log.Info("找到即将到期的会员卡", "count", len(cards))

	// 处理每张即将到期的卡
	for _, card := range cards {
		if err := s.processExpiringCard(&card); err != nil {
			log.Error("处理即将到期会员卡失败", "card_id", card.ID, "error", err)
			continue
		}
	}

	return nil
}

// processExpiringCard 处理单张即将到期的会员卡
func (s *MembershipReminderService) processExpiringCard(card *model.MembershipCard) error {
	// 检查是否已经发送过提醒
	var existingNotification model.AdminNotification
	err := s.db.Where("type = ? AND card_id = ? AND created_at > ?",
		model.NotificationTypeCardExpiring, card.ID, time.Now().AddDate(0, 0, -7)).
		First(&existingNotification).Error

	if err == nil {
		// 7天内已发送过提醒，跳过
		return nil
	}

	// 计算剩余天数
	daysLeft := int(card.EndDate.Sub(time.Now()).Hours() / 24)

	// 创建后台通知
	notification := model.AdminNotification{
		Type:     model.NotificationTypeCardExpiring,
		Title:    "会员卡即将到期",
		Content:  fmt.Sprintf("用户 %s 的 %s 将在 %d 天后到期", card.User.NickName, card.CardType.Name, daysLeft),
		UserID:   &card.UserID,
		CardID:   &card.ID,
		Priority: model.PriorityMedium,
	}

	if err := s.db.Create(&notification).Error; err != nil {
		log.Error("创建后台通知失败", "card_id", card.ID, "error", err)
		return fmt.Errorf("创建后台通知失败: %w", err)
	}

	// 发送微信提醒
	if card.User.HasWeChatOpenID() {
		go func() {
			err := s.wechatService.SendCardExpiryReminder(
				card.User.GetWeChatOpenID(),
				card.CardType.Name,
				card.EndDate.Format("2006-01-02"),
				fmt.Sprintf("%d天", daysLeft),
			)
			if err != nil {
				log.Error("发送会员卡到期微信提醒失败", "card_id", card.ID, "error", err)
			} else {
				log.Info("会员卡到期微信提醒发送成功", "card_id", card.ID)
			}
		}()
	}

	log.Info("处理即将到期会员卡成功", "card_id", card.ID, "days_left", daysLeft)
	return nil
}

// CheckExpiredCards 检查已过期的会员卡
func (s *MembershipReminderService) CheckExpiredCards() error {
	log.Info("开始检查已过期的会员卡")

	// 查询昨天过期的会员卡
	yesterday := time.Now().AddDate(0, 0, -1)
	startOfYesterday := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
	endOfYesterday := startOfYesterday.Add(24 * time.Hour)

	var cards []model.MembershipCard
	err := s.db.Preload("User").Preload("CardType").
		Where("status = 1 AND end_date >= ? AND end_date < ?",
			startOfYesterday, endOfYesterday).
		Find(&cards).Error

	if err != nil {
		log.Error("查询已过期的会员卡失败", "error", err)
		return fmt.Errorf("查询已过期的会员卡失败: %w", err)
	}

	log.Info("找到已过期的会员卡", "count", len(cards))

	// 处理每张过期的卡
	for _, card := range cards {
		if err := s.processExpiredCard(&card); err != nil {
			log.Error("处理已过期会员卡失败", "card_id", card.ID, "error", err)
			continue
		}
	}

	return nil
}

// processExpiredCard 处理单张已过期的会员卡
func (s *MembershipReminderService) processExpiredCard(card *model.MembershipCard) error {
	// 创建后台通知
	notification := model.AdminNotification{
		Type:     model.NotificationTypeCardExpired,
		Title:    "会员卡已过期",
		Content:  fmt.Sprintf("用户 %s 的 %s 已于 %s 过期", card.User.NickName, card.CardType.Name, card.EndDate.Format("2006-01-02")),
		UserID:   &card.UserID,
		CardID:   &card.ID,
		Priority: model.PriorityHigh,
	}

	if err := s.db.Create(&notification).Error; err != nil {
		log.Error("创建过期通知失败", "card_id", card.ID, "error", err)
		return fmt.Errorf("创建过期通知失败: %w", err)
	}

	log.Info("处理已过期会员卡成功", "card_id", card.ID)
	return nil
}

// ScheduleDailyCheck 安排每日检查任务
func (s *MembershipReminderService) ScheduleDailyCheck() error {
	// 计算下次执行时间（明天上午9点）
	now := time.Now()
	tomorrow := now.AddDate(0, 0, 1)
	nextRun := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 9, 0, 0, 0, tomorrow.Location())

	// 创建检查即将到期会员卡的任务
	expiringTask := asynq.NewTask("membership:check_expiring", nil)

	// 安排在明天上午9点执行
	_, err := s.asynqClient.Enqueue(expiringTask, asynq.ProcessAt(nextRun))
	if err != nil {
		log.Error("安排会员卡到期检查任务失败", "error", err)
		return fmt.Errorf("安排会员卡到期检查任务失败: %w", err)
	}

	// 计算下次执行时间（明天上午10点）
	nextRunExpired := time.Date(tomorrow.Year(), tomorrow.Month(), tomorrow.Day(), 10, 0, 0, 0, tomorrow.Location())

	// 创建检查已过期会员卡的任务
	expiredTask := asynq.NewTask("membership:check_expired", nil)

	// 安排在明天上午10点执行
	_, err = s.asynqClient.Enqueue(expiredTask, asynq.ProcessAt(nextRunExpired))
	if err != nil {
		log.Error("安排已过期会员卡检查任务失败", "error", err)
		return fmt.Errorf("安排已过期会员卡检查任务失败: %w", err)
	}

	log.Info("会员卡检查定时任务安排成功", "next_expiring_check", nextRun, "next_expired_check", nextRunExpired)
	return nil
}
