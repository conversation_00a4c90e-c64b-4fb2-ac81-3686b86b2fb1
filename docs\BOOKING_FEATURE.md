# 📅 预约功能完整实现文档

## 🎯 **功能概述**

预约功能提供完整的预约管理体验，包括智能卡种选择、预约人数设置、微信提醒、历史预约查看、课程评价和详情查看等功能。

## 🔧 **技术实现**

### **核心接口**

| 接口 | 方法 | 功能描述 |
|------|------|----------|
| `/api/v1/app/bookings` | GET | 获取预约列表（含统计信息） |
| `/api/v1/app/bookings/:id` | GET | 获取预约详情 |
| `/api/v1/app/bookings/cancel` | DELETE | 取消预约 |
| `/api/v1/app/user/recent-bookings` | GET | 获取最近预约 |
| `/api/v1/app/user/stats` | GET | 获取用户统计 |

### **数据模型**

#### BookingStats 预约统计
```go
type BookingStats struct {
    TotalClasses    int // 总上课次数（已完成的预约）
    TotalDays       int // 总上课天数（按日期去重）
    UpcomingClasses int // 即将上课数量
}
```

## 📱 **功能特性**

### 1. **💳 预约课程 - 卡种选择和扣费**

#### 智能卡种选择优先级
1. **期限卡优先**：优先使用期限卡
2. **有效期排序**：按有效期临近排序
3. **余额充足**：确保卡内余额足够

#### 预约时显示信息
```json
{
  "available_cards": [
    {
      "id": 123,
      "card_number": "YG202401001",
      "card_type_name": "30次瑜伽卡",
      "category": "times_card",
      "remaining_times": 25,
      "remaining_amount": 0,
      "end_date": "2024-06-30",
      "deduct_info": {
        "deduct_times": 1,
        "deduct_amount": 0,
        "actual_amount": 0
      },
      "is_recommended": true
    }
  ],
  "course_info": {
    "name": "瑜伽基础课程",
    "price": 8800,
    "duration": 90
  }
}
```

#### 预约人数选择
- **单人预约**：默认1人
- **多人预约**：共享储值卡支持多人（如3人共享）
- **扣费计算**：按人数倍数计算扣费

### 2. **🔔 微信提醒功能**

#### 预约成功通知
```json
{
  "template_id": "booking_confirmation",
  "data": {
    "character_string1": "YG20240115001", // 预约单号
    "thing2": "瑜伽基础课程",              // 课程名称
    "time3": "2024-01-15 09:00",        // 课程时间
    "thing4": "Yogaga瑜伽馆(朝阳店)",    // 门店名称
    "phrase5": "预约成功"                // 状态
  }
}
```

#### 开课提醒通知
```json
{
  "template_id": "course_reminder",
  "data": {
    "thing1": "瑜伽基础课程",           // 课程名称
    "time2": "2024-01-15 09:00",     // 开课时间
    "thing3": "Yogaga瑜伽馆(朝阳店)", // 门店名称
    "phrase4": "即将开始"             // 状态
  }
}
```

### 3. **📋 历史预约功能**

#### 预约列表查看
```bash
GET /api/v1/app/bookings?type=history&page=1&page_size=10
Authorization: Bearer <token>

Response:
{
  "list": [
    {
      "id": 123,
      "booking_number": "YG20240115001",
      "schedule_info": {
        "course_name": "瑜伽基础课程",
        "coach_name": "张老师",
        "store_name": "Yogaga瑜伽馆(朝阳店)",
        "start_time": "2024-01-15 09:00:00"
      },
      "status": 3,
      "status_text": "已完成",
      "can_rate": true,
      "rating": 0,
      "comment": ""
    }
  ]
}
```

#### 课程评价功能
```bash
POST /api/v1/app/bookings/rate
Authorization: Bearer <token>

{
  "booking_id": 123,
  "rating": 5,
  "comment": "教练很专业，课程内容丰富，环境很好！"
}

Response:
{
  "message": "评价成功"
}
```

### 4. **🔍 查看详情功能**

#### 预约详情完整信息
```bash
GET /api/v1/app/bookings/123
Authorization: Bearer <token>

Response:
{
  "id": 123,
  "booking_number": "YG20240115001",
  "status": 3,
  "status_text": "已完成",
  "people_count": 1,
  "created_at": "2024-01-15 08:30:00",
  "course": {
    "id": 456,
    "name": "瑜伽基础课程",
    "start_time": "2024-01-15 09:00:00",
    "end_time": "2024-01-15 10:30:00",
    "duration": 90,
    "coach": {
      "id": 789,
      "name": "张老师",
      "avatar": "https://avatar.url"
    },
    "store": {
      "id": 101,
      "name": "Yogaga瑜伽馆(朝阳店)",
      "address": "朝阳区xxx路xxx号",
      "phone": "010-12345678"
    },
    "course_status": "课程已结束"
  },
  "order": {
    "booking_number": "YG20240115001",
    "total_price": 8800,
    "actual_amount": 0,
    "payment_time": "2024-01-15 08:30:00",
    "payment_method": "30次瑜伽卡会员卡支付",
    "deduct_times": 1,
    "deduct_amount": 0
  },
  "membership_card": {
    "id": 123,
    "name": "YG202401001",
    "card_type": "30次瑜伽卡"
  },
  "can_cancel": false,
  "can_rate": true,
  "rating": 5,
  "comment": "教练很专业！"
}
```

### 5. **📊 上课统计展示**

#### 获取预约列表（含统计）
```bash
GET /api/v1/app/bookings?type=all&page=1&page_size=10
Authorization: Bearer <token>

Response:
{
  "list": [
    {
      "id": 123,
      "course_name": "瑜伽基础课程",
      "coach_name": "张老师",
      "store_name": "Yogaga瑜伽馆(朝阳店)",
      "start_time": "2024-01-15T09:00:00Z",
      "status": 2,
      "status_text": "已确认",
      "can_cancel": true,
      "rating": 0,
      "comment": ""
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 10,
  "statistics": {
    "total_classes": 18,    // 总上课次数
    "total_days": 15,       // 总上课天数
    "upcoming_classes": 3   // 即将上课数量
  }
}
```

#### 用户统计信息
```bash
GET /api/v1/app/user/stats
Authorization: Bearer <token>

Response:
{
  "booking_stats": {
    "total": 25,      // 总预约次数
    "completed": 18,  // 已完成次数
    "cancelled": 2    // 已取消次数
  },
  "membership_stats": {
    "active_cards": 2,
    "total_balance": 1500.00
  },
  "favorite_stats": {
    "courses": 5,
    "coaches": 3,
    "stores": 2
  }
}
```

### 2. **📋 预约课程信息展示**

#### 预约类型筛选
- **即将上课**：`type=upcoming` - 显示未来的预约
- **历史记录**：`type=history` - 显示过去的预约
- **全部预约**：`type=all` - 显示所有预约

#### 预约状态说明
- **1 - 待确认**：预约已提交，等待确认
- **2 - 已确认**：预约已确认，可以上课
- **3 - 已完成**：课程已结束，可以评价
- **4 - 已取消**：预约已取消

#### 最近预约记录
```bash
GET /api/v1/app/user/recent-bookings
Authorization: Bearer <token>

Response:
[
  {
    "id": 123,
    "course_name": "瑜伽基础课程",
    "coach_name": "张老师",
    "store_name": "Yogaga瑜伽馆(朝阳店)",
    "start_time": "2024-01-15 09:00:00",
    "status": 2,
    "status_text": "已确认"
  }
]
```

### 3. **❌ 预约取消规则**

#### 取消时间限制
- ✅ **开课前2小时外**：可以取消预约
- ❌ **开课前2小时内**：不能取消预约

#### 取消预约
```bash
DELETE /api/v1/app/bookings/cancel
Authorization: Bearer <token>

{
  "booking_id": 123,
  "reason": "临时有事无法参加"
}

Response:
{
  "message": "预约已取消"
}
```

#### 取消逻辑
1. **时间检查**：验证是否在允许取消的时间范围内
2. **状态更新**：将预约状态更新为"已取消"
3. **退款处理**：根据会员卡类型进行退款
4. **排队通知**：通知排队用户有名额可预约

### 4. **🔔 开课提醒功能**

#### 微信推送提醒
- ⏰ **提醒时间**：开课前2小时
- 📱 **推送方式**：微信订阅消息
- 📝 **提醒内容**：课程名称、开课时间、门店地址

#### 提醒消息模板
```json
{
  "thing1": "瑜伽基础课程",           // 课程名称
  "time2": "2024-01-15 09:00",     // 开课时间
  "thing3": "Yogaga瑜伽馆(朝阳店)", // 门店名称
  "phrase4": "即将开始"             // 状态
}
```

#### 自动提醒流程
1. **任务调度**：系统自动在开课前2小时创建提醒任务
2. **用户查询**：查找该课程的所有已确认预约用户
3. **消息推送**：向每个用户发送微信订阅消息
4. **日志记录**：记录推送成功/失败状态

## 🌟 **功能亮点**

### 1. **📊 智能统计**
- **多维度统计**：上课次数、天数、即将上课
- **实时更新**：预约状态变化时自动更新统计
- **按日期去重**：上课天数统计避免重复计算

### 2. **🕐 灵活取消**
- **时间限制**：开课前2小时的合理取消窗口
- **自动退款**：根据会员卡类型自动处理退款
- **排队通知**：取消后自动通知排队用户

### 3. **🔔 智能提醒**
- **提前通知**：开课前2小时及时提醒
- **个性化消息**：包含课程和门店具体信息
- **可靠推送**：基于微信官方订阅消息

### 4. **📱 用户体验**
- **状态清晰**：预约状态一目了然
- **操作便捷**：一键取消、快速查看
- **信息完整**：课程、教练、门店信息齐全

## 🔄 **业务流程**

### **预约生命周期**
```mermaid
graph TD
    A[用户创建预约] --> B[待确认状态]
    B --> C[系统确认预约]
    C --> D[已确认状态]
    D --> E[开课前2小时]
    E --> F{用户操作}
    F -->|取消| G[已取消状态]
    F -->|参加| H[课程开始]
    H --> I[已完成状态]
    I --> J[用户评价]

    D --> K[开课前2小时提醒]
    K --> L[微信推送消息]
```

### **统计数据更新**
```mermaid
graph TD
    A[预约状态变化] --> B{状态类型}
    B -->|完成| C[总上课次数+1]
    C --> D[检查是否新日期]
    D -->|是| E[总上课天数+1]
    B -->|确认| F[即将上课数量+1]
    B -->|取消| G[相关统计-1]
```

## 🎉 **总结**

预约功能的完善实现了需求中的所有要点：

- ✅ **上课次数统计**：准确统计已完成的课程数量
- ✅ **上课天数统计**：按日期去重统计实际上课天数
- ✅ **预约信息展示**：完整的课程、教练、门店信息
- ✅ **取消规则**：开课前2小时外可取消，自动退款
- ✅ **开课提醒**：开课前2小时微信推送提醒

这为用户提供了完整、便捷、智能的预约管理体验！🚀
