package worker

import (
	"context"
	"encoding/json"
	"yogaga/internal/service"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

// MembershipWorker 会员卡相关任务处理器
type MembershipWorker struct {
	db                        *gorm.DB
	membershipReminderService *service.MembershipReminderService
	attendanceService         *service.AttendanceService
}

// NewMembershipWorker 创建会员卡任务处理器实例
func NewMembershipWorker(db *gorm.DB, membershipReminderService *service.MembershipReminderService, attendanceService *service.AttendanceService) *MembershipWorker {
	return &MembershipWorker{
		db:                        db,
		membershipReminderService: membershipReminderService,
		attendanceService:         attendanceService,
	}
}

// RegisterTasks 注册任务处理器
func (w *MembershipWorker) RegisterTasks(mux *asynq.ServeMux) {
	// 注册会员卡到期检查任务
	mux.HandleFunc("membership:check_expiring", w.HandleCheckExpiringCards)

	// 注册已过期会员卡检查任务
	mux.HandleFunc("membership:check_expired", w.HandleCheckExpiredCards)

	// 注册期限卡出勤检查任务
	mux.HandleFunc("membership:check_attendance", w.HandleCheckAttendance)
}

// HandleCheckExpiringCards 处理即将到期会员卡检查任务
func (w *MembershipWorker) HandleCheckExpiringCards(ctx context.Context, t *asynq.Task) error {
	log.Info("开始处理即将到期会员卡检查任务")

	err := w.membershipReminderService.CheckExpiringCards()
	if err != nil {
		log.Error("检查即将到期会员卡失败", "error", err)
		return err
	}

	log.Info("即将到期会员卡检查任务完成")
	return nil
}

// HandleCheckExpiredCards 处理已过期会员卡检查任务
func (w *MembershipWorker) HandleCheckExpiredCards(ctx context.Context, t *asynq.Task) error {
	log.Info("开始处理已过期会员卡检查任务")

	err := w.membershipReminderService.CheckExpiredCards()
	if err != nil {
		log.Error("检查已过期会员卡失败", "error", err)
		return err
	}

	log.Info("已过期会员卡检查任务完成")
	return nil
}

// HandleCheckAttendance 处理期限卡出勤检查任务
func (w *MembershipWorker) HandleCheckAttendance(ctx context.Context, t *asynq.Task) error {
	log.Info("开始处理期限卡出勤检查任务")

	err := w.attendanceService.CheckAllPeriodCards()
	if err != nil {
		log.Error("检查期限卡出勤失败", "error", err)
		return err
	}

	log.Info("期限卡出勤检查任务完成")
	return nil
}

// HandleCardValidityDeduction 处理会员卡有效期扣除任务
func (w *MembershipWorker) HandleCardValidityDeduction(ctx context.Context, t *asynq.Task) error {
	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析有效期扣除任务参数失败", "error", err)
		return err
	}

	cardID, ok := payload["card_id"].(float64)
	if !ok {
		log.Error("无效的会员卡ID")
		return asynq.SkipRetry
	}

	log.Info("处理会员卡有效期扣除任务", "card_id", uint(cardID))

	err := w.attendanceService.CheckValidityDeduction(uint(cardID))
	if err != nil {
		log.Error("处理有效期扣除失败", "card_id", uint(cardID), "error", err)
		return err
	}

	log.Info("会员卡有效期扣除任务完成", "card_id", uint(cardID))
	return nil
}
