# 标准登录接口返回格式

## 概述

本文档描述了标准的登录接口返回格式，遵循 OAuth 2.0 和 JWT 最佳实践。

## 登录接口返回数据

### 标准字段

| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `access_token` | string | 是 | 访问令牌，用于API调用认证 |
| `refresh_token` | string | 是 | 刷新令牌，用于获取新的访问令牌 |
| `token_type` | string | 是 | 令牌类型，通常为 "Bearer" |
| `expires_in` | number | 是 | 访问令牌过期时间（秒） |
| `refresh_expires_in` | number | 是 | 刷新令牌过期时间（秒） |
| `scope` | string | 可选 | 权限范围 |
| `user_info` | object | 可选 | 用户基本信息 |

### 用户信息字段 (user_info)

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `id` | number | 用户ID |
| `username` | string | 用户名 |
| `email` | string | 邮箱地址 |
| `platform` | string | 用户所属平台 |
| `roles` | array | 用户角色列表 |

## 示例响应

### 成功登录响应

```json
{
  "code": 0,
  "msg": "Ok",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 7200,
    "refresh_expires_in": 604800,
    "scope": "admin",
    "user_info": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "platform": "admin",
      "roles": ["admin", "user"]
    }
  }
}
```

### 登录失败响应

```json
{
  "code": 40001,
  "msg": "用户名或密码错误",
  "data": []
}
```

## 前端使用指南

### 1. 存储令牌

```javascript
// 登录成功后存储令牌
const loginResponse = await fetch('/api/v1/public/admin/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'admin',
    password: 'password'
  })
});

const result = await loginResponse.json();
if (result.code === 0) {
  const { access_token, refresh_token, expires_in, user_info } = result.data;

  // 存储到 localStorage 或 sessionStorage
  localStorage.setItem('access_token', access_token);
  localStorage.setItem('refresh_token', refresh_token);
  localStorage.setItem('user_info', JSON.stringify(user_info));

  // 计算过期时间
  const expiresAt = Date.now() + (expires_in * 1000);
  localStorage.setItem('token_expires_at', expiresAt.toString());
}
```

### 2. 使用访问令牌

```javascript
// API 调用时携带令牌
const apiCall = async (url, options = {}) => {
  const token = localStorage.getItem('access_token');

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
};
```

### 3. 令牌过期检查

```javascript
// 检查令牌是否过期
const isTokenExpired = () => {
  const expiresAt = localStorage.getItem('token_expires_at');
  if (!expiresAt) return true;

  return Date.now() > parseInt(expiresAt);
};

// 自动刷新令牌
const refreshTokenIfNeeded = async () => {
  if (isTokenExpired()) {
    const refreshToken = localStorage.getItem('refresh_token');
    if (!refreshToken) {
      // 重定向到登录页
      window.location.href = '/login';
      return;
    }

    try {
      const response = await fetch('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          refresh_token: refreshToken
        })
      });

      const result = await response.json();
      if (result.code === 0) {
        // 更新令牌和用户信息
        const { access_token, refresh_token: newRefreshToken, expires_in, user_info } = result.data;

        localStorage.setItem('access_token', access_token);
        localStorage.setItem('refresh_token', newRefreshToken);
        localStorage.setItem('user_info', JSON.stringify(user_info));

        const expiresAt = Date.now() + (expires_in * 1000);
        localStorage.setItem('token_expires_at', expiresAt.toString());

        console.log('Token refreshed successfully');
      } else {
        // 刷新失败，清除本地存储并重定向到登录页
        localStorage.removeItem('access_token');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('user_info');
        localStorage.removeItem('token_expires_at');
        window.location.href = '/login';
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      // 网络错误等，清除本地存储并重定向到登录页
      localStorage.removeItem('access_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user_info');
      localStorage.removeItem('token_expires_at');
      window.location.href = '/login';
    }
  }
};

// 带自动刷新的 API 调用封装
const apiCallWithRefresh = async (url, options = {}) => {
  // 先检查并刷新令牌
  await refreshTokenIfNeeded();

  const token = localStorage.getItem('access_token');
  if (!token) {
    window.location.href = '/login';
    return;
  }

  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });

  // 如果返回 401，尝试刷新令牌后重试
  if (response.status === 401) {
    await refreshTokenIfNeeded();
    const newToken = localStorage.getItem('access_token');
    if (newToken) {
      return fetch(url, {
        ...options,
        headers: {
          ...options.headers,
          'Authorization': `Bearer ${newToken}`,
          'Content-Type': 'application/json'
        }
      });
    }
  }

  return response;
};
```

## 安全建议

1. **令牌存储**:
   - 敏感应用建议使用 `httpOnly` cookies 存储令牌
   - 避免在 localStorage 中长期存储敏感信息

2. **令牌过期时间**:
   - 访问令牌：建议 15分钟 - 2小时
   - 刷新令牌：建议 7天 - 30天

3. **HTTPS**: 生产环境必须使用 HTTPS

4. **令牌撤销**: 提供登出接口撤销刷新令牌

## 刷新令牌接口

### 接口地址
`POST /api/v1/auth/refresh`

### 请求参数

| 字段名 | 类型 | 必需 | 描述 |
|--------|------|------|------|
| `refresh_token` | string | 是 | 有效的刷新令牌 |

### 请求示例

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 成功响应

返回格式与登录接口相同，包含新的访问令牌和刷新令牌：

```json
{
  "code": 0,
  "msg": "Ok",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "expires_in": 7200,
    "refresh_expires_in": 604800,
    "scope": "admin",
    "user_info": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "platform": "admin",
      "roles": ["admin", "user"]
    }
  }
}
```

### 错误响应

```json
{
  "code": 40001,
  "msg": "invalid refresh token",
  "data": []
}
```

## 相关接口

- `POST /api/v1/public/admin/login` - 管理员登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 登出（待实现）
