# Admin API 一致性检查运行脚本
# 用于定期检查路由器与API文档的一致性

param(
    [switch]$Verbose,
    [switch]$CI,
    [string]$OutputFormat = "console"  # console, json, html
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 脚本信息
$ScriptName = "Admin API 一致性检查工具"
$Version = "1.0.0"
$CheckDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"

Write-Host "🔍 $ScriptName v$Version" -ForegroundColor Cyan
Write-Host "检查时间: $CheckDate" -ForegroundColor Gray
Write-Host "=" * 50

# 检查必要文件是否存在
$RequiredFiles = @(
    "routers/router.go",
    "api-collections/admin/admin_api.json",
    "scripts/admin_api_consistency_checker.go"
)

Write-Host "📋 检查必要文件..." -ForegroundColor Yellow
foreach ($file in $RequiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Error "❌ 缺少必要文件: $file"
        exit 1
    }
    Write-Host "✅ $file" -ForegroundColor Green
}

# 编译检查工具
Write-Host "`n🔨 编译检查工具..." -ForegroundColor Yellow
try {
    $buildOutput = go build -o scripts/consistency_checker.exe scripts/admin_api_consistency_checker.go 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Error "编译失败: $buildOutput"
        exit 1
    }
    Write-Host "✅ 编译成功" -ForegroundColor Green
} catch {
    Write-Error "编译过程中发生错误: $_"
    exit 1
}

# 运行一致性检查
Write-Host "`n🚀 运行一致性检查..." -ForegroundColor Yellow
try {
    $checkOutput = & "./scripts/consistency_checker.exe" 2>&1
    $exitCode = $LASTEXITCODE
    
    if ($Verbose) {
        Write-Host $checkOutput
    }
    
    # 检查是否生成了报告文件
    if (-not (Test-Path "scripts/consistency_report.json")) {
        Write-Error "❌ 未生成检查报告文件"
        exit 1
    }
    
    # 读取报告
    $reportContent = Get-Content "scripts/consistency_report.json" -Raw | ConvertFrom-Json
    
    # 输出结果摘要
    Write-Host "`n📊 检查结果摘要:" -ForegroundColor Cyan
    Write-Host "总路由数: $($reportContent.total_routes)" -ForegroundColor White
    Write-Host "总API数: $($reportContent.total_apis)" -ForegroundColor White
    Write-Host "匹配数: $($reportContent.matched_count)" -ForegroundColor White
    Write-Host "一致性率: $([math]::Round($reportContent.consistency_rate, 1))%" -ForegroundColor White
    Write-Host "缺失在API文档中: $($reportContent.missing_in_api.Count)" -ForegroundColor Yellow
    Write-Host "缺失在路由器中: $($reportContent.missing_in_router.Count)" -ForegroundColor Yellow
    Write-Host "方法不匹配: $($reportContent.method_mismatches.Count)" -ForegroundColor Red
    
    # 根据一致性率显示状态
    $consistencyRate = $reportContent.consistency_rate
    if ($consistencyRate -ge 95) {
        Write-Host "`n🎉 一致性检查通过！" -ForegroundColor Green
        $status = "PASS"
    } elseif ($consistencyRate -ge 85) {
        Write-Host "`n⚠️  一致性需要改进" -ForegroundColor Yellow
        $status = "WARNING"
    } else {
        Write-Host "`n❌ 一致性检查失败，需要立即修复" -ForegroundColor Red
        $status = "FAIL"
    }
    
    # 显示详细问题（如果存在）
    if ($reportContent.missing_in_api.Count -gt 0) {
        Write-Host "`n🔍 缺失在API文档中的路由:" -ForegroundColor Yellow
        foreach ($missing in $reportContent.missing_in_api | Select-Object -First 5) {
            Write-Host "  - $($missing.method) $($missing.path)" -ForegroundColor Gray
        }
        if ($reportContent.missing_in_api.Count -gt 5) {
            Write-Host "  ... 还有 $($reportContent.missing_in_api.Count - 5) 个" -ForegroundColor Gray
        }
    }
    
    if ($reportContent.missing_in_router.Count -gt 0) {
        Write-Host "`n🔍 缺失在路由器中的接口:" -ForegroundColor Yellow
        foreach ($missing in $reportContent.missing_in_router | Select-Object -First 5) {
            Write-Host "  - $($missing.method) $($missing.endpoint)" -ForegroundColor Gray
        }
        if ($reportContent.missing_in_router.Count -gt 5) {
            Write-Host "  ... 还有 $($reportContent.missing_in_router.Count - 5) 个" -ForegroundColor Gray
        }
    }
    
    if ($reportContent.method_mismatches.Count -gt 0) {
        Write-Host "`n🔍 HTTP方法不匹配:" -ForegroundColor Red
        foreach ($mismatch in $reportContent.method_mismatches | Select-Object -First 5) {
            Write-Host "  - $($mismatch.path): 路由器($($mismatch.router_method)) vs API文档($($mismatch.api_method))" -ForegroundColor Gray
        }
        if ($reportContent.method_mismatches.Count -gt 5) {
            Write-Host "  ... 还有 $($reportContent.method_mismatches.Count - 5) 个" -ForegroundColor Gray
        }
    }
    
    # 显示建议
    if ($reportContent.recommendations.Count -gt 0) {
        Write-Host "`n💡 改进建议:" -ForegroundColor Cyan
        foreach ($recommendation in $reportContent.recommendations) {
            Write-Host "  • $recommendation" -ForegroundColor White
        }
    }
    
    # 生成不同格式的输出
    switch ($OutputFormat.ToLower()) {
        "json" {
            Write-Host "`n📄 JSON格式报告:" -ForegroundColor Cyan
            $reportContent | ConvertTo-Json -Depth 10
        }
        "html" {
            $htmlReport = Generate-HTMLReport $reportContent
            $htmlFile = "scripts/consistency_report.html"
            $htmlReport | Out-File -FilePath $htmlFile -Encoding UTF8
            Write-Host "`n📄 HTML报告已保存到: $htmlFile" -ForegroundColor Cyan
        }
        default {
            Write-Host "`n📄 详细报告已保存到: scripts/consistency_report.json" -ForegroundColor Cyan
        }
    }
    
    # CI模式下的退出码处理
    if ($CI) {
        if ($status -eq "FAIL") {
            Write-Host "`n❌ CI模式: 一致性检查失败" -ForegroundColor Red
            exit 1
        } elseif ($status -eq "WARNING") {
            Write-Host "`n⚠️  CI模式: 一致性需要改进，但允许通过" -ForegroundColor Yellow
            exit 0
        } else {
            Write-Host "`n✅ CI模式: 一致性检查通过" -ForegroundColor Green
            exit 0
        }
    }
    
} catch {
    Write-Error "运行检查工具时发生错误: $_"
    exit 1
} finally {
    # 清理临时文件
    if (Test-Path "scripts/consistency_checker.exe") {
        Remove-Item "scripts/consistency_checker.exe" -Force
    }
}

# 生成HTML报告的函数
function Generate-HTMLReport($reportData) {
    $html = @"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin API 一致性检查报告</title>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-pass { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-fail { color: #dc3545; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; }
        .summary-card h3 { margin: 0 0 10px 0; color: #495057; }
        .summary-card .number { font-size: 2em; font-weight: bold; color: #007bff; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #495057; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .issue-list { background: #f8f9fa; padding: 15px; border-radius: 5px; }
        .issue-item { margin-bottom: 10px; padding: 8px; background: white; border-left: 4px solid #007bff; }
        .recommendations { background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Admin API 一致性检查报告</h1>
            <p>检查时间: $($reportData.check_time)</p>
            <p class="status-$(if($reportData.consistency_rate -ge 95){'pass'}elseif($reportData.consistency_rate -ge 85){'warning'}else{'fail'})">
                一致性率: $([math]::Round($reportData.consistency_rate, 1))%
            </p>
        </div>
        
        <div class="summary">
            <div class="summary-card">
                <h3>总路由数</h3>
                <div class="number">$($reportData.total_routes)</div>
            </div>
            <div class="summary-card">
                <h3>总API数</h3>
                <div class="number">$($reportData.total_apis)</div>
            </div>
            <div class="summary-card">
                <h3>匹配数</h3>
                <div class="number">$($reportData.matched_count)</div>
            </div>
            <div class="summary-card">
                <h3>一致性率</h3>
                <div class="number">$([math]::Round($reportData.consistency_rate, 1))%</div>
            </div>
        </div>
        
        <div class="recommendations">
            <h3>💡 改进建议</h3>
            <ul>
"@
    
    foreach ($rec in $reportData.recommendations) {
        $html += "<li>$rec</li>"
    }
    
    $html += @"
            </ul>
        </div>
    </div>
</body>
</html>
"@
    
    return $html
}

Write-Host "`n✨ 检查完成！" -ForegroundColor Green
