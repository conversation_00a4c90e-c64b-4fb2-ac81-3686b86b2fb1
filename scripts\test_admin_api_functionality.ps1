# Admin API 功能测试脚本
# 测试修复后的关键接口是否正常工作

param(
    [string]$BaseURL = "http://127.0.0.1:9095",
    [string]$AdminUsername = "admin",
    [string]$AdminPassword = "123456",
    [switch]$Verbose
)

# 设置错误处理
$ErrorActionPreference = "Continue"

# 全局变量
$AccessToken = ""
$TestResults = @()

Write-Host "🧪 Admin API 功能测试" -ForegroundColor Cyan
Write-Host "基础URL: $BaseURL" -ForegroundColor Gray
Write-Host "=" * 50

# 测试结果记录函数
function Add-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Message = "",
        [object]$Response = $null
    )

    $result = @{
        TestName = $TestName
        Status = $Status
        Message = $Message
        Response = $Response
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    }

    $script:TestResults += $result

    $color = switch ($Status) {
        "PASS" { "Green" }
        "FAIL" { "Red" }
        "SKIP" { "Yellow" }
        default { "White" }
    }

    Write-Host "  $Status : $TestName" -ForegroundColor $color
    if ($Message) {
        Write-Host "    $Message" -ForegroundColor Gray
    }
}

# 管理员登录
function Test-AdminLogin {
    Write-Host "`n🔐 测试管理员登录..." -ForegroundColor Yellow

    try {
        $loginData = @{
            username = $AdminUsername
            password = $AdminPassword
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/public/admin/login" -Method POST -Body $loginData -ContentType "application/json"

        if ($response.code -eq 0 -and $response.data.access_token) {
            $script:AccessToken = $response.data.access_token
            Add-TestResult "管理员登录" "PASS" "获取到访问令牌"
            return $true
        } else {
            Add-TestResult "管理员登录" "FAIL" "登录失败: $($response.message)"
            return $false
        }
    } catch {
        Add-TestResult "管理员登录" "FAIL" "登录异常: $($_.Exception.Message)"
        return $false
    }
}

# 测试仪表盘接口
function Test-Dashboard {
    Write-Host "`n📊 测试仪表盘接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "仪表盘数据获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/dashboard" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "仪表盘数据获取" "PASS" "成功获取仪表盘数据"
        } else {
            Add-TestResult "仪表盘数据获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "仪表盘数据获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试用户管理接口
function Test-UserManagement {
    Write-Host "`n👥 测试用户管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "用户列表获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/users?page=1`&page_size=5" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "用户列表获取" "PASS" "成功获取用户列表，共 $($response.data.total) 个用户"
        } else {
            Add-TestResult "用户列表获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "用户列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试会员卡管理接口
function Test-MembershipCardManagement {
    Write-Host "`n💳 测试会员卡管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "会员卡列表获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/membership-cards?page=1`&page_size=5" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "会员卡列表获取" "PASS" "成功获取会员卡列表，共 $($response.data.total) 张卡"
        } else {
            Add-TestResult "会员卡列表获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "会员卡列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试会员卡类型管理接口
function Test-MembershipTypeManagement {
    Write-Host "`n🏷️  测试会员卡类型管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "会员卡类型列表获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/membership-types?page=1`&page_size=5" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "会员卡类型列表获取" "PASS" "成功获取会员卡类型列表，共 $($response.data.total) 种类型"
        } else {
            Add-TestResult "会员卡类型列表获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "会员卡类型列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试门店管理接口
function Test-StoreManagement {
    Write-Host "`n🏪 测试门店管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "门店列表获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/stores?page=1`&page_size=5" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "门店列表获取" "PASS" "成功获取门店列表，共 $($response.data.total) 个门店"

            # 测试门店教室列表接口（新增的接口）
            if ($response.data.list.Count -gt 0) {
                $storeId = $response.data.list[0].id
                try {
                    $classroomResponse = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/stores/$storeId/classrooms" -Method GET -Headers $headers
                    if ($classroomResponse.code -eq 0) {
                        Add-TestResult "门店教室列表获取" "PASS" "成功获取门店教室列表"
                    } else {
                        Add-TestResult "门店教室列表获取" "FAIL" "获取失败: $($classroomResponse.message)"
                    }
                } catch {
                    Add-TestResult "门店教室列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
                }
            }
        } else {
            Add-TestResult "门店列表获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "门店列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试课程管理接口
function Test-CourseManagement {
    Write-Host "`n📚 测试课程管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "课程列表获取" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/courses?page=1`&page_size=5" -Method GET -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "课程列表获取" "PASS" "成功获取课程列表，共 $($response.data.total) 个课程"

            # 测试课程支持的卡类型接口（新增的接口）
            if ($response.data.list.Count -gt 0) {
                $courseId = $response.data.list[0].id
                try {
                    $cardTypesResponse = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/courses/$courseId/supported-card-types" -Method GET -Headers $headers
                    if ($cardTypesResponse.code -eq 0) {
                        Add-TestResult "课程支持的卡类型获取" "PASS" "成功获取课程支持的卡类型"
                    } else {
                        Add-TestResult "课程支持的卡类型获取" "FAIL" "获取失败: $($cardTypesResponse.message)"
                    }
                } catch {
                    Add-TestResult "课程支持的卡类型获取" "FAIL" "请求异常: $($_.Exception.Message)"
                }
            }
        } else {
            Add-TestResult "课程列表获取" "FAIL" "获取失败: $($response.message)"
        }
    } catch {
        Add-TestResult "课程列表获取" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 测试批量课程管理接口（新增功能）
function Test-BatchCourseManagement {
    Write-Host "`n📅 测试批量课程管理接口..." -ForegroundColor Yellow

    if (-not $AccessToken) {
        Add-TestResult "批量课程预览" "SKIP" "未登录"
        return
    }

    try {
        $headers = @{ Authorization = "Bearer $AccessToken" }
        $batchData = @{
            start_date = "2025-08-01"
            end_date = "2025-08-07"
            start_time = "09:00"
            weekly_schedules = @(
                @{
                    weekday = 1
                    course_name = "测试瑜伽课程"
                    description = "测试用的瑜伽课程"
                    coach_ids = @("test-coach-id")
                    duration = 90
                }
            )
            global_description = "测试批量创建课程"
            capacity = 20
            price = 10800
            level = 1
            category_id = 1
            store_id = 1
            type = 1
        } | ConvertTo-Json -Depth 3

        $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/batch-courses/preview" -Method POST -Body $batchData -ContentType "application/json" -Headers $headers

        if ($response.code -eq 0) {
            Add-TestResult "批量课程预览" "PASS" "成功获取批量课程预览"
        } else {
            Add-TestResult "批量课程预览" "FAIL" "预览失败: $($response.message)"
        }
    } catch {
        Add-TestResult "批量课程预览" "FAIL" "请求异常: $($_.Exception.Message)"
    }
}

# 生成测试报告
function Generate-TestReport {
    Write-Host "`n📄 生成测试报告..." -ForegroundColor Yellow

    $passCount = ($TestResults | Where-Object { $_.Status -eq "PASS" }).Count
    $failCount = ($TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
    $skipCount = ($TestResults | Where-Object { $_.Status -eq "SKIP" }).Count
    $totalCount = $TestResults.Count

    $report = @{
        summary = @{
            total = $totalCount
            pass = $passCount
            fail = $failCount
            skip = $skipCount
            success_rate = if ($totalCount -gt 0) { [math]::Round(($passCount / $totalCount) * 100, 1) } else { 0 }
        }
        test_time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        base_url = $BaseURL
        results = $TestResults
    }

    # 保存JSON报告
    $reportJson = $report | ConvertTo-Json -Depth 5
    $reportFile = "scripts/admin_api_test_report.json"
    $reportJson | Out-File -FilePath $reportFile -Encoding UTF8

    # 输出摘要
    Write-Host "`n📊 测试结果摘要:" -ForegroundColor Cyan
    Write-Host "总测试数: $totalCount" -ForegroundColor White
    Write-Host "通过: $passCount" -ForegroundColor Green
    Write-Host "失败: $failCount" -ForegroundColor Red
    Write-Host "跳过: $skipCount" -ForegroundColor Yellow
    Write-Host "成功率: $($report.summary.success_rate)%" -ForegroundColor White

    Write-Host "`n📄 详细报告已保存到: $reportFile" -ForegroundColor Cyan

    return $report.summary.success_rate
}

# 主测试流程
function Main {
    # 1. 测试管理员登录
    $loginSuccess = Test-AdminLogin

    if (-not $loginSuccess) {
        Write-Host "`n❌ 登录失败，跳过后续测试" -ForegroundColor Red
        Generate-TestReport
        return
    }

    # 2. 测试各个模块
    Test-Dashboard
    Test-UserManagement
    Test-MembershipCardManagement
    Test-MembershipTypeManagement
    Test-StoreManagement
    Test-CourseManagement
    Test-BatchCourseManagement

    # 3. 生成报告
    $successRate = Generate-TestReport

    # 4. 根据成功率设置退出码
    if ($successRate -ge 80) {
        Write-Host "`n🎉 测试通过！" -ForegroundColor Green
        exit 0
    } elseif ($successRate -ge 60) {
        Write-Host "`n⚠️  测试部分通过，需要关注" -ForegroundColor Yellow
        exit 0
    } else {
        Write-Host "`n❌ 测试失败，需要修复" -ForegroundColor Red
        exit 1
    }
}

# 运行主程序
Main
