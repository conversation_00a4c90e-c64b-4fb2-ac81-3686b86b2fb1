# 会员卡业务流程指南

## 🎯 **业务概览**

本文档详细说明会员卡系统的业务流程、使用场景和操作指南，帮助理解数据库字段在实际业务中的应用。

---

## 📋 **1. 会员卡类型配置流程**

### Step 1: 创建卡种
```json
{
  "name": "Yogaga瑜伽团课卡",
  "category": "times",
  "description": "瑜伽团课专用次数卡，可预约所有瑜伽团课",
  "price": 150000,        // 1500元
  "original_price": 180000, // 1800元
  "times": 10,            // 10次
  "validity_days": 365,   // 1年有效
  "discount": 1.0,        // 无折扣
  "shareable": false,     // 不可共享
  "can_select_stores": true,  // 可选门店
  "daily_booking_limit": 1,   // 每日限约1次
  "status": 1             // 启用状态
}
```

### Step 2: 配置适用课程
```json
{
  "applicable_courses": [
    {"course_id": 1, "course_name": "哈他瑜伽"},
    {"course_id": 2, "course_name": "流瑜伽"},
    {"course_id": 3, "course_name": "阴瑜伽"}
  ]
}
```

### Step 3: 设置扣减规则
```json
{
  "course_deduct_rules": [
    {
      "course_type": "regular",
      "deduction_times": 1,
      "deduction_amount": 0
    },
    {
      "course_type": "premium", 
      "deduction_times": 2,
      "deduction_amount": 0
    }
  ]
}
```

---

## 💳 **2. 开卡业务流程**

### 次数卡开卡示例
```json
{
  "user_id": "user-uuid-123",
  "card_type_id": 5,
  "start_date": "2024-01-01",
  "purchase_price": 150000,  // 实付1500元
  "discount": 1.0,           // 无折扣
  "available_stores": [1, 2, 3], // 可用门店
  "daily_booking_limit": 1,   // 每日限约1次
  "remark": "新年促销活动"
}
```

**系统自动计算**：
- `remaining_times` = 10 (从卡种复制)
- `total_times` = 10
- `end_date` = "2024-12-31" (start_date + validity_days)
- `card_number` = "YG20240101001" (自动生成)
- `status` = 1 (正常状态)

### 期限卡开卡示例
```json
{
  "user_id": "user-uuid-456", 
  "card_type_id": 8,
  "start_date": "2024-01-01",
  "purchase_price": 300000,  // 实付3000元
  "available_stores": [],    // 全市通用
  "remark": "VIP客户专享"
}
```

**系统自动计算**：
- `remaining_times` = 0 (期限卡不限次数)
- `end_date` = "2024-03-31" (90天期限卡)
- `status` = 1

### 储值卡开卡示例
```json
{
  "user_id": "user-uuid-789",
  "card_type_id": 12,
  "start_date": "2024-01-01", 
  "purchase_price": 100000,  // 实付1000元
  "available_stores": [],    // 全市通用
  "remark": "充值优惠活动"
}
```

**系统自动计算**：
- `remaining_amount` = 100000 (1000元)
- `total_amount` = 100000
- `end_date` = "2024-12-31"

---

## 👥 **3. 主副卡业务流程**

### 主卡开卡
```json
{
  "user_id": "main-user-uuid",
  "card_type_id": 15,  // 支持副卡的卡种
  "is_main_card": true,
  "main_card_id": null,
  "purchase_price": 200000
}
```

### 副卡添加
```json
{
  "main_card_id": 1001,    // 主卡ID
  "user_id": "sub-user-uuid", // 副卡用户
  "is_main_card": false,
  "card_type_id": 15,      // 同主卡类型
  "remaining_times": 0,    // 副卡不独立计算
  "remaining_amount": 0    // 共享主卡余额
}
```

**业务规则**：
- 副卡共享主卡的 `remaining_times` 和 `remaining_amount`
- 副卡使用时扣减主卡余额
- 主卡可查看所有副卡的使用记录
- 副卡只能查看自己的使用记录

---

## 🏪 **4. 门店限制业务场景**

### 全市通用卡
```json
{
  "available_stores": [],  // 空数组表示全市通用
  "can_select_stores": false
}
```
**使用场景**：高端会员卡，任意门店都可使用

### 指定门店卡
```json
{
  "available_stores": [1, 3, 5],  // 只能在门店1,3,5使用
  "can_select_stores": true
}
```
**使用场景**：区域性会员卡，限制在特定门店使用

### 单门店卡
```json
{
  "available_stores": [2],  // 只能在门店2使用
  "can_select_stores": false
}
```
**使用场景**：门店专属卡，促进特定门店客流

---

## 📅 **5. 预约限制业务场景**

### 无限制预约
```json
{
  "daily_booking_limit": 0,  // 0表示无限制
  "can_set_daily_limit": false
}
```
**使用场景**：VIP卡，不限制预约次数

### 单日限制预约
```json
{
  "daily_booking_limit": 1,  // 每日最多1次
  "can_set_daily_limit": true
}
```
**使用场景**：普通会员卡，防止恶意占用资源

### 灵活限制预约
```json
{
  "daily_booking_limit": 2,  // 默认每日2次
  "can_set_daily_limit": true // 开卡时可调整
}
```
**使用场景**：可根据客户需求调整的灵活卡种

---

## 💰 **6. 扣费业务流程**

### 次数卡扣费
```sql
-- 预约课程时扣减次数
UPDATE membership_cards 
SET remaining_times = remaining_times - 1,
    updated_at = NOW()
WHERE id = :card_id 
  AND remaining_times > 0
  AND status = 1;
```

### 储值卡扣费
```sql
-- 预约课程时扣减金额
UPDATE membership_cards 
SET remaining_amount = remaining_amount - :course_price,
    updated_at = NOW()
WHERE id = :card_id 
  AND remaining_amount >= :course_price
  AND status = 1;
```

### 期限卡验证
```sql
-- 期限卡只验证有效期，不扣减
SELECT * FROM membership_cards 
WHERE id = :card_id 
  AND status = 1
  AND (end_date IS NULL OR end_date > NOW());
```

---

## 🔄 **7. 卡片状态管理**

### 状态自动更新
```sql
-- 定时任务：更新过期卡片状态
UPDATE membership_cards 
SET status = 3, updated_at = NOW()
WHERE status = 1 
  AND end_date IS NOT NULL 
  AND end_date < NOW();

-- 定时任务：更新用完卡片状态  
UPDATE membership_cards 
SET status = 4, updated_at = NOW()
WHERE status = 1 
  AND remaining_times = 0 
  AND remaining_amount = 0;
```

### 手动状态操作
```sql
-- 冻结卡片
UPDATE membership_cards 
SET status = 2, updated_at = NOW()
WHERE id = :card_id AND status = 1;

-- 解冻卡片
UPDATE membership_cards 
SET status = 1, updated_at = NOW() 
WHERE id = :card_id AND status = 2;
```

---

## 📊 **8. 业务报表查询**

### 卡种销售统计
```sql
SELECT 
  mct.name as card_type_name,
  COUNT(*) as sold_count,
  SUM(mc.purchase_price) as total_revenue
FROM membership_cards mc
JOIN membership_card_types mct ON mc.card_type_id = mct.id
WHERE mc.created_at >= '2024-01-01'
GROUP BY mct.id, mct.name
ORDER BY total_revenue DESC;
```

### 用户会员卡使用情况
```sql
SELECT 
  u.nickname,
  mct.name as card_type,
  mc.remaining_times,
  mc.remaining_amount,
  mc.status,
  mc.end_date
FROM membership_cards mc
JOIN users u ON mc.user_id = u.id
JOIN membership_card_types mct ON mc.card_type_id = mct.id
WHERE u.id = :user_id
ORDER BY mc.created_at DESC;
```

### 即将到期卡片提醒
```sql
SELECT 
  u.nickname,
  u.phone,
  mct.name as card_type,
  mc.end_date,
  DATEDIFF(mc.end_date, NOW()) as days_left
FROM membership_cards mc
JOIN users u ON mc.user_id = u.id  
JOIN membership_card_types mct ON mc.card_type_id = mct.id
WHERE mc.status = 1
  AND mc.end_date BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 7 DAY)
ORDER BY mc.end_date;
```

---

## 🎯 **9. 最佳实践建议**

### 卡种设计原则
1. **价格策略**：合理设置 `price` 和 `original_price`，突出优惠
2. **有效期设置**：根据课程频率合理设置 `validity_days`
3. **功能开关**：谨慎使用高级功能，避免业务复杂化

### 开卡操作建议
1. **信息确认**：开卡前确认用户信息和卡种选择
2. **门店选择**：根据用户需求合理配置 `available_stores`
3. **备注记录**：重要信息记录在 `remark` 字段

### 系统维护建议
1. **定时任务**：设置自动更新卡片状态的定时任务
2. **数据备份**：定期备份会员卡数据，防止数据丢失
3. **性能监控**：监控查询性能，及时优化慢查询

---

## 📞 **10. 技术支持**

如有疑问，请参考：
- 数据库字段详细说明：`MEMBERSHIP_CARD_DATABASE_SCHEMA.md`
- API接口文档：`/docs/openapi/`
- 系统架构文档：`CURRENT_SYSTEM_ARCHITECTURE.md`
