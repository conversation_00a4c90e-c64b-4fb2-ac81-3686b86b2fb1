package model

import (
	"time"
)

// AdminNotification 后台消息通知
type AdminNotification struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	Type      string    `json:"type" gorm:"size:50;not null;comment:通知类型"`
	Title     string    `json:"title" gorm:"size:255;not null;comment:通知标题"`
	Content   string    `json:"content" gorm:"type:text;comment:通知内容"`
	UserID    *string   `json:"user_id" gorm:"type:char(36);comment:关联用户ID"`
	CardID    *uint     `json:"card_id" gorm:"comment:关联会员卡ID"`
	IsRead    bool      `json:"is_read" gorm:"default:false;comment:是否已读"`
	Priority  int       `json:"priority" gorm:"default:1;comment:优先级 1-低 2-中 3-高"`
	ExtraData string    `json:"extra_data" gorm:"type:json;comment:额外数据"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// 关联
	User           *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	MembershipCard *MembershipCard `json:"membership_card,omitempty" gorm:"foreignKey:CardID"`
}

// NotificationType 通知类型常量
const (
	NotificationTypeCardExpiring    = "card_expiring"    // 会员卡即将到期
	NotificationTypeValidityDeduct  = "validity_deduct"  // 有效期扣除提醒
	NotificationTypeCardExpired     = "card_expired"     // 会员卡已过期
	NotificationTypeAttendanceAlert = "attendance_alert" // 出勤异常提醒
)

// NotificationPriority 通知优先级常量
const (
	PriorityLow    = 1 // 低优先级
	PriorityMedium = 2 // 中优先级
	PriorityHigh   = 3 // 高优先级
)
