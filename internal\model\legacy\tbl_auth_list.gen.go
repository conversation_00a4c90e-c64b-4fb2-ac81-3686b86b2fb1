// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

const TableNameTblAuthList = "tbl_auth_list"

// TblAuthList mapped from table <tbl_auth_list>
type TblAuthList struct {
	ID    int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Pid   int32  `gorm:"column:pid;not null" json:"pid"`
	Title string `gorm:"column:title" json:"title"`
}

// TableName TblAuthList's table name
func (*TblAuthList) TableName() string {
	return TableNameTblAuthList
}
