-- ===== 修复数据库自增序列问题 =====
-- 问题：初始化数据时直接插入了指定ID，但序列值没有更新
-- 解决：手动更新序列值到正确的位置

-- 1. 查看当前序列值
SELECT currval('membership_card_types_id_seq');

-- 2. 查看表中最大ID
SELECT MAX(id) FROM membership_card_types;

-- 3. 更新序列值到最大ID+1（确保下次插入不会冲突）
SELECT setval('membership_card_types_id_seq', (SELECT MAX(id) FROM membership_card_types) + 1);

-- 4. 验证序列值是否更新成功
SELECT currval('membership_card_types_id_seq');

-- ===== 其他可能需要修复的表 =====
-- 如果其他表也有类似问题，可以使用相同的方法

-- 用户表（如果有初始化数据）
-- SELECT setval('users_id_seq', (SELECT MAX(id) FROM users) + 1);

-- 角色表
-- SELECT setval('roles_id_seq', (SELECT MAX(id) FROM roles) + 1);

-- 权限表
-- SELECT setval('permissions_id_seq', (SELECT MAX(id) FROM permissions) + 1);

-- 菜单表
-- SELECT setval('menus_id_seq', (SELECT MAX(id) FROM menus) + 1);

-- 门店表
-- SELECT setval('stores_id_seq', (SELECT MAX(id) FROM stores) + 1);

-- 课程分类表
-- SELECT setval('course_categories_id_seq', (SELECT MAX(id) FROM course_categories) + 1);
