#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速会员卡支付测试
验证卡号重复问题是否已修复
"""

import requests
import time

def quick_test():
    """快速测试会员卡支付功能"""
    base_url = "http://localhost:9095"

    # 登录
    print("🔐 登录系统...")
    session = requests.Session()
    login_response = session.post(f"{base_url}/api/v1/public/admin/login",
                                json={"username": "admin", "password": "admin123"})

    if login_response.status_code != 200 or login_response.json().get('code') != 0:
        print("❌ 登录失败")
        return False

    token = login_response.json().get('data', {}).get('access_token')
    session.headers.update({
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    })
    print("✅ 登录成功")

    # 创建用户
    timestamp = int(time.time())
    user_data = {
        "username": f"quick_test_{timestamp}",
        "email": f"quick_test_{timestamp}@example.com",
        "password": "123456",
        "phone": f"138{timestamp % 100000000:08d}",
        "role_ids": []
    }

    user_response = session.post(f"{base_url}/api/v1/admin/users", json=user_data)
    if user_response.status_code != 200 or user_response.json().get('code') != 0:
        print("❌ 创建用户失败")
        return False

    user_id = user_response.json().get('data', {}).get('id')
    print(f"✅ 创建用户成功，ID: {user_id}")

    # 获取卡种
    types_response = session.get(f"{base_url}/api/v1/admin/membership-types",
                               params={"page": "1", "page_size": "20", "status": "1"})

    if types_response.status_code != 200 or types_response.json().get('code') != 0:
        print("❌ 获取卡种失败")
        return False

    card_types = types_response.json().get('data', {}).get('list', [])
    balance_type = next((ct for ct in card_types if ct.get('category') == 'balance'), None)
    times_type = next((ct for ct in card_types if ct.get('category') == 'times'), None)

    if not balance_type or not times_type:
        print("❌ 缺少必要的卡种")
        return False

    print("✅ 获取卡种成功")

    # 创建储值卡（用于支付）
    balance_card_data = {
        "user_id": user_id,
        "card_type_id": balance_type.get('id'),
        "start_date": f"2025-07-19T{(15 + timestamp % 8):02d}:00:00Z",
        "purchase_price": 100000,  # 1000元
        "discount": 1.0,
        "payment_method": "cash",
        "available_stores": [],
        "daily_booking_limit": 0,
        "remark": "快速测试储值卡"
    }

    balance_response = session.post(f"{base_url}/api/v1/admin/membership-cards", json=balance_card_data)
    if balance_response.status_code != 200 or balance_response.json().get('code') != 0:
        print(f"❌ 创建储值卡失败: {balance_response.text}")
        return False

    balance_card_info = balance_response.json().get('data', {})
    balance_card_id = balance_card_info.get('id')
    balance_card_number = balance_card_info.get('card_number')
    print(f"✅ 创建储值卡成功，卡号: {balance_card_number}")

    # 使用储值卡支付开次数卡
    times_card_data = {
        "user_id": user_id,
        "card_type_id": times_type.get('id'),
        "start_date": f"2025-07-19T{(16 + timestamp % 7):02d}:00:00Z",
        "purchase_price": 50000,  # 500元
        "discount": 1.0,
        "payment_method": "card",  # 使用会员卡支付
        "payment_card_id": balance_card_id,
        "available_stores": [],
        "daily_booking_limit": 0,
        "remark": "快速测试会员卡支付开卡"
    }

    times_response = session.post(f"{base_url}/api/v1/admin/membership-cards", json=times_card_data)

    print(f"会员卡支付开卡 - 状态: {times_response.status_code}")
    print(f"会员卡支付开卡 - 响应: {times_response.text}")

    if times_response.status_code == 200 and times_response.json().get('code') == 0:
        times_card_info = times_response.json().get('data', {})
        times_card_number = times_card_info.get('card_number')
        print(f"🎉 会员卡支付开卡成功！")
        print(f"   支付卡: {balance_card_number}")
        print(f"   新卡: {times_card_number}")

        # 验证卡号不重复
        if balance_card_number != times_card_number:
            print("✅ 卡号唯一性验证通过")
            return True
        else:
            print("❌ 卡号重复问题仍存在")
            return False
    else:
        print("❌ 会员卡支付开卡失败")
        return False

if __name__ == "__main__":
    print("🚀 开始快速会员卡支付测试...")
    print("=" * 50)

    success = quick_test()

    print("=" * 50)
    if success:
        print("🎉 会员卡支付功能修复成功！")
        print("✅ 卡号重复问题已解决")
        print("✅ 会员卡支付开卡正常工作")
    else:
        print("❌ 会员卡支付功能仍有问题")
        print("需要进一步检查系统状态")
