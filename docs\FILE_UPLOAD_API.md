# 文件上传 API 使用指南

## 📋 概述

我们采用了**元数据与对象分离**的最佳实践设计文件系统：
- **MinIO** 负责存储实际文件
- **数据库** 存储文件元数据
- **外键关联** 确保数据一致性

## 🚀 API 接口

### 1. 通用文件上传

**管理端：**
```
POST /api/v1/admin/files/upload
```

**小程序端：**
```
POST /api/v1/app/files/upload
```

### 2. 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `file` | File | ✅ | 上传的文件 |
| `purpose` | String | ✅ | 文件用途，决定存储桶和后续处理 |

### 3. 文件用途说明

| purpose | 存储桶 | 说明 | 特殊处理 |
|---------|--------|------|----------|
| `avatar` | avatars | 用户头像 | 自动更新用户的 avatar_id |
| `banner` | banners | 轮播图 | 无 |
| `coach` | coaches | 教练图片 | 无 |
| `store` | stores | 门店图片 | 无 |
| `general` | uploads | 通用文件 | 无（默认值） |

## 📝 使用示例

### 1. 上传用户头像

```bash
curl -X POST \
  http://localhost:9095/api/v1/app/files/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@avatar.jpg" \
  -F "purpose=avatar"
```

**响应示例：**
```json
{
  "error_code": 0,
  "message": "Ok",
  "data": {
    "file_id": "550e8400-e29b-41d4-a716-446655440000",
    "download_url": "/api/v1/file/download/2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg",
    "object_name": "2024/01/15/550e8400-e29b-41d4-a716-446655440000.jpg",
    "bucket_name": "avatars",
    "purpose": "avatar",
    "file_size": 102400,
    "content_type": "image/jpeg"
  }
}
```

### 2. 上传轮播图

```bash
curl -X POST \
  http://localhost:9095/api/v1/admin/files/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@banner.png" \
  -F "purpose=banner"
```

### 3. 前端 JavaScript 示例

```javascript
// 上传头像
async function uploadAvatar(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('purpose', 'avatar');

  const response = await fetch('/api/v1/app/files/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  const result = await response.json();
  if (result.error_code === 0) {
    console.log('头像上传成功:', result.data.download_url);
    // 头像会自动关联到当前用户
  }
}

// 上传轮播图
async function uploadBanner(file) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('purpose', 'banner');

  const response = await fetch('/api/v1/admin/files/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });

  const result = await response.json();
  if (result.error_code === 0) {
    // 需要手动创建轮播图记录，关联 file_id
    await createBanner({
      title: '轮播图标题',
      file_id: result.data.file_id,
      link_url: '/pages/course/list',
      description: '轮播图描述'
    });
  }
}
```

## 🗄️ 数据库设计

### 1. files 表（文件元数据）
```sql
CREATE TABLE `files` (
  `id` char(36) NOT NULL,
  `object_name` varchar(512) NOT NULL COMMENT '在MinIO中的唯一对象名',
  `bucket_name` varchar(100) NOT NULL COMMENT 'MinIO存储桶名称',
  `original_filename` varchar(255) DEFAULT NULL COMMENT '用户上传时的原始文件名',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小 (bytes)',
  `content_type` varchar(100) DEFAULT NULL COMMENT '文件的MIME类型',
  `uploader_id` int unsigned DEFAULT NULL COMMENT '上传者用户ID',
  `status` varchar(20) DEFAULT 'active' COMMENT '文件状态',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uni_files_object_name` (`object_name`)
);
```

### 2. users 表（新增字段）
```sql
ALTER TABLE users
ADD COLUMN avatar_id CHAR(36) NULL COMMENT '头像文件ID，关联files表';
```

### 3. banners 表（新增字段）
```sql
ALTER TABLE banners
ADD COLUMN file_id CHAR(36) NULL COMMENT '轮播图文件ID，关联files表';
```

## 🔧 文件管理 API

### 1. 获取文件列表
```
GET /api/v1/admin/files?page=1&page_size=10&entity_type=avatar
```

### 2. 删除文件
```
DELETE /api/v1/admin/files/{file_id}
```

### 3. 批量删除文件
```
POST /api/v1/admin/files/batch-delete
{
  "file_ids": ["file_id_1", "file_id_2"]
}
```

## 🎯 最佳实践

1. **文件类型明确**：始终传递正确的 `file_type` 参数
2. **头像自动关联**：上传头像时会自动更新用户记录，无需额外操作
3. **轮播图手动关联**：上传轮播图后需要手动创建 banner 记录
4. **错误处理**：检查响应中的 `error_code` 字段
5. **文件大小限制**：建议限制文件大小，避免过大文件影响性能

## 🚨 注意事项

1. 所有文件上传接口都需要 JWT 认证
2. 头像上传会自动更新当前用户的 avatar_id 字段
3. 文件删除是软删除，实际文件仍保留在 MinIO 中
4. 下载链接格式：`/api/v1/file/download/{object_name}`
5. 支持的图片格式：jpg, jpeg, png, gif, webp
