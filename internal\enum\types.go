package enum

import "strings"

// CourseType 课程类型枚举
type CourseType int

const (
	CourseTypeGroup   CourseType = 1 // 团课
	CourseTypePrivate CourseType = 2 // 私教
	CourseTypeSmall   CourseType = 3 // 小班课
)

// CourseTypeIdentifier 课程类型标识符（用于复杂扣减规则）
type CourseTypeIdentifier string

const (
	CourseTypeYogaGroup      CourseTypeIdentifier = "yoga_group"       // 瑜伽团课
	CourseTypePilatesSmall   CourseTypeIdentifier = "pilates_small"    // 普拉提小班课
	CourseTypeAdvancedSmall  CourseTypeIdentifier = "advanced_small"   // 提升小班课
	CourseTypeSpecialSmall   CourseTypeIdentifier = "special_small"    // 特色小班课
	CourseTypeDance60        CourseTypeIdentifier = "dance_60"         // 舞蹈课60min
	CourseTypeDance90        CourseTypeIdentifier = "dance_90"         // 舞蹈课90min
	CourseTypePrivateStd     CourseTypeIdentifier = "private_std"      // 标准私教1V1
	CourseTypePrivateStar1V1 CourseTypeIdentifier = "private_star_1v1" // 明星私教1V1
	CourseTypePrivateStar1V2 CourseTypeIdentifier = "private_star_1v2" // 明星私教1V2
)

// String 返回课程类型的字符串表示
func (t CourseType) String() string {
	switch t {
	case CourseTypeGroup:
		return "group"
	case CourseTypePrivate:
		return "private"
	case CourseTypeSmall:
		return "small"
	default:
		return "unknown"
	}
}

// GetChineseName 获取课程类型的中文名称
func (t CourseType) GetChineseName() string {
	switch t {
	case CourseTypeGroup:
		return "团课"
	case CourseTypePrivate:
		return "私教"
	case CourseTypeSmall:
		return "小班课"
	default:
		return "未知"
	}
}

// IsValid 验证课程类型值是否有效
func (t CourseType) IsValid() bool {
	switch t {
	case CourseTypeGroup, CourseTypePrivate, CourseTypeSmall:
		return true
	default:
		return false
	}
}

// String 返回课程类型标识符的字符串表示
func (c CourseTypeIdentifier) String() string {
	return string(c)
}

// GetChineseName 获取课程类型标识符的中文名称
func (c CourseTypeIdentifier) GetChineseName() string {
	switch c {
	case CourseTypeYogaGroup:
		return "瑜伽团课"
	case CourseTypePilatesSmall:
		return "普拉提小班课"
	case CourseTypeAdvancedSmall:
		return "提升小班课"
	case CourseTypeSpecialSmall:
		return "特色小班课"
	case CourseTypeDance60:
		return "舞蹈课60min"
	case CourseTypeDance90:
		return "舞蹈课90min"
	case CourseTypePrivateStd:
		return "标准私教1V1"
	case CourseTypePrivateStar1V1:
		return "明星私教1V1"
	case CourseTypePrivateStar1V2:
		return "明星私教1V2"
	default:
		return "未知课程类型"
	}
}

// IsValid 验证课程类型标识符是否有效
func (c CourseTypeIdentifier) IsValid() bool {
	switch c {
	case CourseTypeYogaGroup, CourseTypePilatesSmall, CourseTypeAdvancedSmall,
		CourseTypeSpecialSmall, CourseTypeDance60, CourseTypeDance90,
		CourseTypePrivateStd, CourseTypePrivateStar1V1, CourseTypePrivateStar1V2:
		return true
	default:
		return false
	}
}

// GetBasicType 获取课程类型标识符对应的基础类型
func (c CourseTypeIdentifier) GetBasicType() CourseType {
	switch c {
	case CourseTypeYogaGroup:
		return CourseTypeGroup
	case CourseTypePilatesSmall, CourseTypeAdvancedSmall, CourseTypeSpecialSmall:
		return CourseTypeSmall
	case CourseTypeDance60, CourseTypeDance90:
		return CourseTypeGroup // 舞蹈课按团课处理
	case CourseTypePrivateStd, CourseTypePrivateStar1V1, CourseTypePrivateStar1V2:
		return CourseTypePrivate
	default:
		return CourseTypeGroup // 默认为团课
	}
}

// MembershipCardCategory 会员卡类别枚举
type MembershipCardCategory string

const (
	MembershipCardCategoryTimes   MembershipCardCategory = "times"   // 次数卡
	MembershipCardCategoryPeriod  MembershipCardCategory = "period"  // 期限卡
	MembershipCardCategoryBalance MembershipCardCategory = "balance" // 储值卡
)

// String 返回会员卡类别的字符串表示
func (c MembershipCardCategory) String() string {
	return string(c)
}

// IsValid 验证会员卡类别值是否有效
func (c MembershipCardCategory) IsValid() bool {
	switch c {
	case MembershipCardCategoryTimes, MembershipCardCategoryPeriod, MembershipCardCategoryBalance:
		return true
	default:
		return false
	}
}

// MembershipCardScope 会员卡适用范围枚举
type MembershipCardScope string

const (
	MembershipCardScopeGroup     MembershipCardScope = "group"     // 团课专用
	MembershipCardScopePrivate   MembershipCardScope = "private"   // 私教专用
	MembershipCardScopeSmall     MembershipCardScope = "small"     // 小班课专用
	MembershipCardScopeUniversal MembershipCardScope = "universal" // 通用卡（所有课程类型）
)

// String 返回会员卡适用范围的字符串表示
func (s MembershipCardScope) String() string {
	return string(s)
}

// GetChineseName 获取会员卡适用范围的中文名称
func (s MembershipCardScope) GetChineseName() string {
	switch s {
	case MembershipCardScopeGroup:
		return "团课专用"
	case MembershipCardScopePrivate:
		return "私教专用"
	case MembershipCardScopeSmall:
		return "小班课专用"
	case MembershipCardScopeUniversal:
		return "通用卡"
	default:
		return "未知"
	}
}

// IsValid 验证会员卡适用范围值是否有效
func (s MembershipCardScope) IsValid() bool {
	switch s {
	case MembershipCardScopeGroup, MembershipCardScopePrivate, MembershipCardScopeSmall, MembershipCardScopeUniversal:
		return true
	default:
		return false
	}
}

// CanUseCourseType 检查会员卡是否可以用于指定课程类型
func (s MembershipCardScope) CanUseCourseType(courseType CourseType) bool {
	switch s {
	case MembershipCardScopeUniversal:
		return true // 通用卡可以用于所有课程类型
	case MembershipCardScopeGroup:
		return courseType == CourseTypeGroup
	case MembershipCardScopePrivate:
		return courseType == CourseTypePrivate
	case MembershipCardScopeSmall:
		return courseType == CourseTypeSmall
	default:
		return false
	}
}

// GetSupportedCourseTypes 获取会员卡支持的课程类型列表
func (s MembershipCardScope) GetSupportedCourseTypes() []CourseType {
	switch s {
	case MembershipCardScopeUniversal:
		return []CourseType{CourseTypeGroup, CourseTypePrivate, CourseTypeSmall}
	case MembershipCardScopeGroup:
		return []CourseType{CourseTypeGroup}
	case MembershipCardScopePrivate:
		return []CourseType{CourseTypePrivate}
	case MembershipCardScopeSmall:
		return []CourseType{CourseTypeSmall}
	default:
		return []CourseType{}
	}
}

// FavoriteType 收藏类型枚举
type FavoriteType string

const (
	FavoriteTypeCourse FavoriteType = "course" // 课程
	FavoriteTypeCoach  FavoriteType = "coach"  // 教练
	FavoriteTypeStore  FavoriteType = "store"  // 门店
)

// String 返回收藏类型的字符串表示
func (t FavoriteType) String() string {
	return string(t)
}

// IsValid 验证收藏类型值是否有效
func (t FavoriteType) IsValid() bool {
	switch t {
	case FavoriteTypeCourse, FavoriteTypeCoach, FavoriteTypeStore:
		return true
	default:
		return false
	}
}

// PaymentMethod 支付方式枚举
type PaymentMethod string

const (
	PaymentMethodMembershipCard PaymentMethod = "membership_card" // 会员卡
	PaymentMethodWechatPay      PaymentMethod = "wechat_pay"      // 微信支付
	PaymentMethodAlipay         PaymentMethod = "alipay"          // 支付宝
	PaymentMethodCash           PaymentMethod = "cash"            // 现金
)

// String 返回支付方式的字符串表示
func (p PaymentMethod) String() string {
	return string(p)
}

// IsValid 验证支付方式是否有效
func (p PaymentMethod) IsValid() bool {
	switch p {
	case PaymentMethodMembershipCard, PaymentMethodWechatPay, PaymentMethodAlipay, PaymentMethodCash:
		return true
	default:
		return false
	}
}

// Gender 性别枚举
type Gender int

const (
	GenderUnknown Gender = 0 // 未知
	GenderMale    Gender = 1 // 男
	GenderFemale  Gender = 2 // 女
)

// String 返回性别的字符串表示
func (g Gender) String() string {
	switch g {
	case GenderMale:
		return "male"
	case GenderFemale:
		return "female"
	case GenderUnknown:
		return "unknown"
	default:
		return "unknown"
	}
}

// IsValid 验证性别值是否有效
func (g Gender) IsValid() bool {
	switch g {
	case GenderUnknown, GenderMale, GenderFemale:
		return true
	default:
		return false
	}
}

// Platform 平台类型枚举
type Platform string

const (
	PlatformDefault Platform = "default" // 默认平台
	PlatformAdmin   Platform = "admin"   // 管理后台
	PlatformApp     Platform = "app"     // 移动应用
	PlatformWechat  Platform = "wechat"  // 微信小程序
)

// String 返回平台类型的字符串表示
func (p Platform) String() string {
	return string(p)
}

// IsValid 验证平台类型是否有效
func (p Platform) IsValid() bool {
	switch p {
	case PlatformDefault, PlatformAdmin, PlatformApp, PlatformWechat:
		return true
	default:
		return false
	}
}

// PlatformAccess 平台访问权限位掩码
type PlatformAccess int

const (
	PlatformAccessNone   PlatformAccess = 0 // 无访问权限
	PlatformAccessWechat PlatformAccess = 1 // 微信小程序访问权限 (1 << 0)
	PlatformAccessAdmin  PlatformAccess = 2 // 管理后台访问权限 (1 << 1)
	PlatformAccessApp    PlatformAccess = 4 // 移动应用访问权限 (1 << 2)
	PlatformAccessAll    PlatformAccess = 7 // 所有平台访问权限 (1|2|4)
)

// HasAccess 检查是否有指定平台的访问权限
func (p PlatformAccess) HasAccess(platform Platform) bool {
	switch platform {
	case PlatformWechat:
		return p&PlatformAccessWechat != 0
	case PlatformAdmin:
		return p&PlatformAccessAdmin != 0
	case PlatformApp:
		return p&PlatformAccessApp != 0
	default:
		return false
	}
}

// AddAccess 添加平台访问权限
func (p PlatformAccess) AddAccess(platform Platform) PlatformAccess {
	switch platform {
	case PlatformWechat:
		return p | PlatformAccessWechat
	case PlatformAdmin:
		return p | PlatformAccessAdmin
	case PlatformApp:
		return p | PlatformAccessApp
	default:
		return p
	}
}

// RemoveAccess 移除平台访问权限
func (p PlatformAccess) RemoveAccess(platform Platform) PlatformAccess {
	switch platform {
	case PlatformWechat:
		return p &^ PlatformAccessWechat
	case PlatformAdmin:
		return p &^ PlatformAccessAdmin
	case PlatformApp:
		return p &^ PlatformAccessApp
	default:
		return p
	}
}

// GetAccessiblePlatforms 获取可访问的平台列表
func (p PlatformAccess) GetAccessiblePlatforms() []Platform {
	var platforms []Platform
	if p&PlatformAccessWechat != 0 {
		platforms = append(platforms, PlatformWechat)
	}
	if p&PlatformAccessAdmin != 0 {
		platforms = append(platforms, PlatformAdmin)
	}
	if p&PlatformAccessApp != 0 {
		platforms = append(platforms, PlatformApp)
	}
	return platforms
}

// String 返回平台访问权限的字符串表示
func (p PlatformAccess) String() string {
	platforms := p.GetAccessiblePlatforms()
	if len(platforms) == 0 {
		return "none"
	}

	var result []string
	for _, platform := range platforms {
		result = append(result, string(platform))
	}
	return strings.Join(result, ",")
}

// IsValid 验证平台访问权限是否有效
func (p PlatformAccess) IsValid() bool {
	return p >= PlatformAccessNone && p <= PlatformAccessAll
}

// 移除复杂的角色枚举，改用纯数据库方案

// CardTransactionType 会员卡交易类型枚举
type CardTransactionType string

const (
	CardTransactionTypeIssue    CardTransactionType = "issue"    // 开卡
	CardTransactionTypeRecharge CardTransactionType = "recharge" // 充值
	CardTransactionTypeDeduct   CardTransactionType = "deduct"   // 扣费
	CardTransactionTypeTransfer CardTransactionType = "transfer" // 转卡
	CardTransactionTypeFreeze   CardTransactionType = "freeze"   // 冻结
	CardTransactionTypeUnfreeze CardTransactionType = "unfreeze" // 解冻
	CardTransactionTypeExtend   CardTransactionType = "extend"   // 延期
	CardTransactionTypeUpgrade  CardTransactionType = "upgrade"  // 升级
	CardTransactionTypeRefund   CardTransactionType = "refund"   // 退款
)

// String 返回会员卡交易类型的字符串表示
func (t CardTransactionType) String() string {
	return string(t)
}

// IsValid 验证会员卡交易类型值是否有效
func (t CardTransactionType) IsValid() bool {
	switch t {
	case CardTransactionTypeIssue, CardTransactionTypeRecharge, CardTransactionTypeDeduct,
		CardTransactionTypeTransfer, CardTransactionTypeFreeze, CardTransactionTypeUnfreeze,
		CardTransactionTypeExtend, CardTransactionTypeUpgrade, CardTransactionTypeRefund:
		return true
	default:
		return false
	}
}

// GetChineseName 获取交易类型的中文名称
func (t CardTransactionType) GetChineseName() string {
	switch t {
	case CardTransactionTypeIssue:
		return "开卡"
	case CardTransactionTypeRecharge:
		return "充值"
	case CardTransactionTypeDeduct:
		return "扣费"
	case CardTransactionTypeTransfer:
		return "转卡"
	case CardTransactionTypeFreeze:
		return "冻结"
	case CardTransactionTypeUnfreeze:
		return "解冻"
	case CardTransactionTypeExtend:
		return "延期"
	case CardTransactionTypeUpgrade:
		return "升级"
	case CardTransactionTypeRefund:
		return "退款"
	default:
		return "未知"
	}
}

// DeductionType 扣减类型枚举
type DeductionType string

const (
	DeductionTypeTimes  DeductionType = "times"  // 扣减次数
	DeductionTypeAmount DeductionType = "amount" // 扣减金额
	DeductionTypePeriod DeductionType = "period" // 期限卡（无需扣费）
	DeductionTypeBoth   DeductionType = "both"   // 同时扣减次数和金额
)

// String 返回扣减类型的字符串表示
func (t DeductionType) String() string {
	return string(t)
}

// IsValid 验证扣减类型值是否有效
func (t DeductionType) IsValid() bool {
	switch t {
	case DeductionTypeTimes, DeductionTypeAmount, DeductionTypePeriod, DeductionTypeBoth:
		return true
	default:
		return false
	}
}

// GetChineseName 获取扣减类型的中文名称
func (t DeductionType) GetChineseName() string {
	switch t {
	case DeductionTypeTimes:
		return "扣减次数"
	case DeductionTypeAmount:
		return "扣减金额"
	case DeductionTypePeriod:
		return "期限卡（无需扣费）"
	case DeductionTypeBoth:
		return "扣减次数和金额"
	default:
		return "未知"
	}
}

// DeductionRuleStatus 扣减规则状态枚举
type DeductionRuleStatus int

const (
	DeductionRuleStatusDisabled DeductionRuleStatus = 0 // 禁用
	DeductionRuleStatusEnabled  DeductionRuleStatus = 1 // 启用
)

// String 返回扣减规则状态的字符串表示
func (s DeductionRuleStatus) String() string {
	switch s {
	case DeductionRuleStatusDisabled:
		return "disabled"
	case DeductionRuleStatusEnabled:
		return "enabled"
	default:
		return "unknown"
	}
}

// IsValid 验证扣减规则状态值是否有效
func (s DeductionRuleStatus) IsValid() bool {
	switch s {
	case DeductionRuleStatusDisabled, DeductionRuleStatusEnabled:
		return true
	default:
		return false
	}
}

// BookingApplicationStatus 预约申请状态枚举
type BookingApplicationStatus string

const (
	BookingApplicationStatusPending    BookingApplicationStatus = "pending"    // 待处理
	BookingApplicationStatusProcessing BookingApplicationStatus = "processing" // 处理中
	BookingApplicationStatusCompleted  BookingApplicationStatus = "completed"  // 已完成
	BookingApplicationStatusFailed     BookingApplicationStatus = "failed"     // 失败
	BookingApplicationStatusQueued     BookingApplicationStatus = "queued"     // 已排队
)

// String 返回预约申请状态的字符串表示
func (s BookingApplicationStatus) String() string {
	return string(s)
}

// IsValid 验证预约申请状态值是否有效
func (s BookingApplicationStatus) IsValid() bool {
	switch s {
	case BookingApplicationStatusPending, BookingApplicationStatusProcessing,
		BookingApplicationStatusCompleted, BookingApplicationStatusFailed, BookingApplicationStatusQueued:
		return true
	default:
		return false
	}
}
