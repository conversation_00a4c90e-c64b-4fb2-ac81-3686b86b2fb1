package model

import (
	"encoding/json"
	"time"
)

// FlexibleDeductionRule 灵活扣减规则 - 完全基于数据库配置
type FlexibleDeductionRule struct {
	BaseModel

	// 基础关联 - 支持多种匹配方式
	CardTypeID     uint   `json:"card_type_id" gorm:"type:int;not null;comment:会员卡类型ID"`
	CourseTypeCode string `json:"course_type_code" gorm:"type:varchar(50);comment:课程类型代码，为空表示适用所有类型"`
	CourseID       uint   `json:"course_id" gorm:"type:int;comment:具体课程ID，为空表示适用该类型所有课程"`
	CourseName     string `json:"course_name" gorm:"type:varchar(100);comment:课程名称匹配，支持模糊匹配"`

	// 扣减规则
	DeductionType      string `json:"deduction_type" gorm:"type:varchar(20);not null;comment:扣减类型 times:次数 amount:金额 period:期限 none:不扣费"`
	DeductionTimes     int    `json:"deduction_times" gorm:"type:int;default:0;comment:扣减次数"`
	DeductionAmount    int    `json:"deduction_amount" gorm:"type:int;default:0;comment:扣减金额(分)"`
	PerPersonDeduction bool   `json:"per_person_deduction" gorm:"type:boolean;default:false;comment:是否按人数计算扣减"`

	// 期限卡限制规则
	DailyLimit   int `json:"daily_limit" gorm:"type:int;default:0;comment:单日预约限制，0表示无限制"`
	WeeklyLimit  int `json:"weekly_limit" gorm:"type:int;default:0;comment:单周预约限制，0表示无限制"`
	MonthlyLimit int `json:"monthly_limit" gorm:"type:int;default:0;comment:单月预约限制，0表示无限制"`

	// 人数限制
	MinPeopleCount int `json:"min_people_count" gorm:"type:int;default:1;comment:最小人数要求"`
	MaxPeopleCount int `json:"max_people_count" gorm:"type:int;default:0;comment:最大人数限制，0表示无限制"`

	// 时间条件
	ValidStartTime *string `json:"valid_start_time" gorm:"type:time;comment:有效开始时间，如09:00:00"`
	ValidEndTime   *string `json:"valid_end_time" gorm:"type:time;comment:有效结束时间，如21:00:00"`
	ValidWeekdays  string  `json:"valid_weekdays" gorm:"type:varchar(20);comment:有效星期，如1,2,3,4,5表示周一到周五"`
	ValidDateRange string  `json:"valid_date_range" gorm:"type:varchar(50);comment:有效日期范围，如2024-01-01,2024-12-31"`

	// 预约限制
	MinBookingHours   int  `json:"min_booking_hours" gorm:"type:int;default:0;comment:最少提前预约小时数"`
	MaxBookingDays    int  `json:"max_booking_days" gorm:"type:int;default:30;comment:最多提前预约天数"`
	AllowCancellation bool `json:"allow_cancellation" gorm:"type:boolean;default:true;comment:是否允许取消"`
	CancellationHours int  `json:"cancellation_hours" gorm:"type:int;default:2;comment:取消前最少小时数"`

	// 规则优先级和状态
	Priority    int    `json:"priority" gorm:"type:int;default:0;comment:优先级，数字越大优先级越高"`
	RuleName    string `json:"rule_name" gorm:"type:varchar(100);comment:规则名称，便于管理"`
	Description string `json:"description" gorm:"type:text;comment:规则描述"`
	Status      int    `json:"status" gorm:"type:int;default:1;comment:状态 1:启用 2:停用"`

	// 扩展配置 - JSON格式存储，支持未来扩展
	ExtendedConfig string `json:"extended_config" gorm:"type:text;comment:扩展配置JSON"`

	// 关联
	Course         *Course             `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	CardType       *MembershipCardType `json:"card_type,omitempty" gorm:"foreignKey:CardTypeID"`
	CourseTypeConf *CourseTypeConfig   `json:"course_type_config,omitempty" gorm:"foreignKey:CourseTypeCode;references:TypeCode"`
}

// TableName 指定表名
func (FlexibleDeductionRule) TableName() string {
	return "flexible_deduction_rules"
}

// IsEnabled 检查是否启用
func (r *FlexibleDeductionRule) IsEnabled() bool {
	return r.Status == 1
}

// GetExtendedConfig 获取扩展配置
func (r *FlexibleDeductionRule) GetExtendedConfig() map[string]interface{} {
	if r.ExtendedConfig == "" {
		return make(map[string]interface{})
	}

	var config map[string]interface{}
	if err := json.Unmarshal([]byte(r.ExtendedConfig), &config); err != nil {
		return make(map[string]interface{})
	}

	return config
}

// SetExtendedConfig 设置扩展配置
func (r *FlexibleDeductionRule) SetExtendedConfig(config map[string]interface{}) error {
	data, err := json.Marshal(config)
	if err != nil {
		return err
	}

	r.ExtendedConfig = string(data)
	return nil
}

// IsValidForTime 检查时间是否有效
func (r *FlexibleDeductionRule) IsValidForTime(t time.Time) bool {
	// 检查时间段
	if r.ValidStartTime != nil && r.ValidEndTime != nil {
		timeStr := t.Format("15:04:05")
		if timeStr < *r.ValidStartTime || timeStr > *r.ValidEndTime {
			return false
		}
	}

	// 检查星期几
	if r.ValidWeekdays != "" {
		weekday := int(t.Weekday())
		if weekday == 0 {
			weekday = 7 // 将周日从0改为7
		}
		// 这里可以添加更复杂的星期几检查逻辑
	}

	return true
}

// IsValidForPeopleCount 检查人数是否有效
func (r *FlexibleDeductionRule) IsValidForPeopleCount(count int) bool {
	if count < r.MinPeopleCount {
		return false
	}

	if r.MaxPeopleCount > 0 && count > r.MaxPeopleCount {
		return false
	}

	return true
}

// CalculateDeduction 计算扣减量
func (r *FlexibleDeductionRule) CalculateDeduction(peopleCount int) (times int, amount int) {
	baseTimes := r.DeductionTimes
	baseAmount := r.DeductionAmount

	if r.PerPersonDeduction {
		baseTimes *= peopleCount
		baseAmount *= peopleCount
	}

	return baseTimes, baseAmount
}

// 注意：MembershipCardExtension 模型已移除
// 相关功能已集成到主 MembershipCard 模型中，避免重复

// 注意：MembershipSubCard 模型已移除
// 副卡功能已通过主 MembershipCard 模型的 MainCardID 和 IsMainCard 字段实现
