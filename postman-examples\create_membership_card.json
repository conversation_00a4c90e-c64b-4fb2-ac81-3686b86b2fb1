// ===== 会员卡开卡 API 请求示例 =====
// 接口：POST /api/v1/admin/membership-cards
// 认证：需要管理员Token
// 说明：为用户开具会员卡，根据卡类型自动计算初始余额和有效期

{
  "user_id": "550e8400-e29b-41d4-a716-************",  // 用户ID，必填，36位UUID格式
                                                      // 说明：要为哪个用户开卡，必须是系统中已存在的用户
                                                      // 获取方式：通过用户管理接口查询用户列表获得
                                                      
  "card_type_id": 1,                                  // 会员卡类型ID，必填，正整数
                                                      // 说明：选择要开的卡类型，必须是启用状态的卡类型
                                                      // 获取方式：GET /api/v1/admin/membership-types
                                                      // 验证逻辑：系统会验证卡类型是否存在且状态为启用(status=1)
                                                      
  "start_date": "2024-01-15T00:00:00Z",             // 开始日期，必填，ISO 8601格式
                                                      // 说明：会员卡的生效日期，通常是购买当天
                                                      // 格式：YYYY-MM-DDTHH:mm:ssZ 或 YYYY-MM-DD
                                                      // 业务逻辑：期限卡会根据此日期+有效天数计算到期日期
                                                      
  "purchase_price": 19900,                           // 实际支付价格，必填，正整数，单位：分
                                                      // 说明：用户实际支付的金额，以分为单位避免小数问题
                                                      // 示例：199元 = 19900分，299.5元 = 29950分
                                                      // 业务逻辑：可以低于卡类型原价（打折销售）
                                                      
  "discount": 0.85,                                  // 折扣率，可选，0-1之间的小数，默认1.0
                                                      // 说明：本次开卡的折扣比例，0.85表示8.5折
                                                      // 计算：purchase_price = 原价 * discount
                                                      // 用途：用于记录和统计，不影响实际扣费逻辑
                                                      
  "payment_method": "wechat_pay",                    // 支付方式，可选，字符串，最大50字符
                                                      // 可选值：
                                                      // - "wechat_pay": 微信支付
                                                      // - "alipay": 支付宝
                                                      // - "cash": 现金
                                                      // - "membership_card": 会员卡支付（用其他卡余额购买）
                                                      // 用途：记录支付渠道，便于财务统计
                                                      
  "available_stores": [1, 2, 3],                    // 可用门店ID列表，可选，整数数组
                                                      // 说明：限制该会员卡只能在指定门店使用
                                                      // 空数组[]或不传：表示全部门店可用
                                                      // 业务逻辑：
                                                      // - 如果卡类型的can_select_stores=false，此字段无效，强制全店可用
                                                      // - 如果卡类型的can_select_stores=true，可以指定门店或留空表示全店
                                                      // 获取门店ID：GET /api/v1/admin/stores
                                                      
  "daily_booking_limit": 2,                         // 单日预约课程上限，可选，非负整数，默认0
                                                      // 说明：限制用户每天最多可以预约多少节课
                                                      // 0表示无限制
                                                      // 业务逻辑：
                                                      // - 如果传入0且卡类型有默认限制，使用卡类型的默认值
                                                      // - 如果传入具体数值，使用传入的值
                                                      // - 如果卡类型的can_set_daily_limit=false，此字段可能无效
                                                      
  "remark": "新用户优惠开卡，享受8.5折优惠"          // 备注，可选，字符串，最大500字符
                                                      // 说明：开卡时的备注信息，如优惠活动、特殊说明等
                                                      // 用途：记录开卡原因、活动信息等，便于后续查询和统计
}

// ===== 不同类型会员卡开卡示例 =====

// 1. 次数卡开卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "card_type_id": 1,                                  // 假设ID=1是10次团课卡
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 19900,                           // 199元
  "discount": 1.0,                                   // 无折扣
  "payment_method": "wechat_pay",
  "available_stores": [],                            // 全部门店可用
  "daily_booking_limit": 1,                         // 每天最多1节课
  "remark": "标准价格购买的10次团课卡"
}
// 开卡后自动设置：
// - remaining_times = 10 (根据卡类型的times字段)
// - remaining_amount = 0 (次数卡金额为0)
// - end_date = start_date + validity_days (如果卡类型设置了有效期)

// 2. 期限卡开卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-446655440001",
  "card_type_id": 2,                                  // 假设ID=2是3个月无限次团课卡
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 53910,                           // 599元的9折 = 539.1元
  "discount": 0.9,                                   // 9折
  "payment_method": "alipay",
  "available_stores": [1, 2],                        // 限制门店1和2
  "daily_booking_limit": 2,                         // 每天最多2节课
  "remark": "春节促销活动，9折优惠，限制门店使用"
}
// 开卡后自动设置：
// - remaining_times = 0 (期限卡不计次数)
// - remaining_amount = 0 (期限卡不计金额)
// - end_date = 2024-04-15 (start_date + 90天，根据卡类型的validity_days)

// 3. 储值卡开卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-446655440002",
  "card_type_id": 3,                                  // 假设ID=3是1000元储值卡
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 100000,                          // 1000元
  "discount": 1.0,                                   // 储值卡通常不打折
  "payment_method": "cash",
  "available_stores": [],                            // 全部门店可用
  "daily_booking_limit": 0,                         // 无限制
  "remark": "VIP客户储值，现金支付"
}
// 开卡后自动设置：
// - remaining_times = 0 (储值卡不计次数)
// - remaining_amount = 100000 (根据卡类型的amount字段，1000元)
// - end_date = 2025-01-15 (如果卡类型设置了有效期)

// ===== 业务逻辑说明 =====

/*
1. 用户验证：
   - 系统会验证user_id对应的用户是否存在
   - 用户ID必须是36位UUID格式

2. 卡类型验证：
   - 验证card_type_id对应的卡类型是否存在
   - 验证卡类型状态是否为启用(status=1)
   - 停用的卡类型无法开卡

3. 自动计算逻辑：
   - 次数卡：remaining_times = cardType.times, remaining_amount = 0
   - 期限卡：remaining_times = 0, remaining_amount = 0, end_date = start_date + validity_days
   - 储值卡：remaining_times = 0, remaining_amount = cardType.amount

4. 门店限制逻辑：
   - 如果卡类型can_select_stores=false：忽略available_stores，强制全店可用
   - 如果卡类型can_select_stores=true：
     * 传入空数组或不传：全店可用
     * 传入门店ID数组：限制指定门店

5. 每日限制逻辑：
   - 如果传入daily_booking_limit=0且卡类型有默认值：使用卡类型默认值
   - 如果传入具体数值：使用传入值
   - 如果卡类型can_set_daily_limit=false：可能忽略此设置

6. 卡号生成：
   - 系统自动生成唯一卡号，格式通常为：YG + 日期 + 序号
   - 例如：YG202401150001

7. 交易记录：
   - 开卡成功后自动记录开卡交易
   - 记录操作员、金额、原因等信息

8. 状态设置：
   - 新开的卡默认状态为正常(status=1)
   - 默认为主卡(is_main_card=true)
*/

// ===== 常见错误和解决方案 =====

/*
1. "用户不存在" - 检查user_id是否正确，是否为36位UUID格式
2. "卡种不存在" - 检查card_type_id是否存在于membership_card_types表中
3. "该卡种已停用" - 卡类型状态为0，需要先启用卡类型
4. "请求参数错误" - 检查必填字段是否完整，数据类型是否正确
5. "开卡失败" - 可能是数据库问题或事务冲突，检查日志详情
*/

// ===== 成功响应示例 =====
/*
{
  "code": 0,
  "message": "success",
  "data": {
    "id": 123,
    "user_id": "550e8400-e29b-41d4-a716-************",
    "card_type_id": 1,
    "card_number": "YG202401150001",
    "is_main_card": true,
    "remaining_times": 10,
    "remaining_amount": 0,
    "total_times": 10,
    "total_amount": 0,
    "start_date": "2024-01-15T00:00:00Z",
    "end_date": "2024-04-15T23:59:59Z",
    "purchase_price": 19900,
    "discount": 0.85,
    "available_stores": "[1,2,3]",
    "daily_booking_limit": 2,
    "status": 1,
    "remark": "新用户优惠开卡",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z",
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "张三",
      "phone": "13800138000"
    },
    "card_type": {
      "id": 1,
      "name": "10次团课卡",
      "category": "times",
      "price": 19900
    }
  }
}
*/
