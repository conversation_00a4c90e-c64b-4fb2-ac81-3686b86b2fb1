// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblCoupon = "tbl_coupons"

// TblCoupon mapped from table <tbl_coupons>
type TblCoupon struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID     int32     `gorm:"column:shop_id" json:"shop_id"`
	ShopIds    string    `gorm:"column:shop_ids" json:"shop_ids"`
	CoupontpID int32     `gorm:"column:coupontp_id" json:"coupontp_id"`
	MemberID   int32     `gorm:"column:member_id;not null" json:"member_id"`
	DateStart  time.Time `gorm:"column:date_start" json:"date_start"`
	DateEnd    time.Time `gorm:"column:date_end" json:"date_end"`
	Note       string    `gorm:"column:note" json:"note"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete   int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	IsOpen     bool      `gorm:"column:is_open;not null" json:"is_open"`
}

// TableName TblCoupon's table name
func (*TblCoupon) TableName() string {
	return TableNameTblCoupon
}
