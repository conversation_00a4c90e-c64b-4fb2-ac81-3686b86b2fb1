# 生成完整Admin API集合的PowerShell脚本

Write-Host "🚀 开始生成完整的Admin API集合..." -ForegroundColor Green

# 定义所有模块和接口
$modules = @{
    "员工管理" = @{
        "emoji" = "👥"
        "id" = "employee-folder"
        "endpoints" = @(
            @{ name = "获取员工列表"; method = "GET"; path = "/employees"; params = @("page", "page_size", "search") },
            @{ name = "获取在职员工列表"; method = "GET"; path = "/employees/active"; params = @() },
            @{ name = "更新员工状态"; method = "PUT"; path = "/employees/1/status"; body = '{"status": 1}' },
            @{ name = "获取销售员工列表"; method = "GET"; path = "/employees/sales"; params = @() }
        )
    }
    "教练状态管理" = @{
        "emoji" = "🧘‍♀️"
        "id" = "coach-status-folder"
        "endpoints" = @(
            @{ name = "获取教练列表"; method = "GET"; path = "/coaches"; params = @("page", "page_size") },
            @{ name = "更新教练状态"; method = "PUT"; path = "/coaches/1/status"; body = '{"status": 1}' }
        )
    }
    "资源分配管理" = @{
        "emoji" = "🔄"
        "id" = "resource-allocation-folder"
        "endpoints" = @(
            @{ name = "分配会员给销售"; method = "POST"; path = "/resource-allocations"; body = '{"member_id": 1, "sales_id": 1}' },
            @{ name = "获取销售负责的会员列表"; method = "GET"; path = "/resource-allocations/sales/1/members"; params = @("page", "page_size") },
            @{ name = "转移会员给其他销售"; method = "PUT"; path = "/resource-allocations/transfer"; body = '{"member_id": 1, "from_sales_id": 1, "to_sales_id": 2}' },
            @{ name = "获取未分配的会员列表"; method = "GET"; path = "/resource-allocations/unassigned-members"; params = @("page", "page_size") },
            @{ name = "移除会员分配"; method = "DELETE"; path = "/resource-allocations/1"; params = @() }
        )
    }
    "用户门店分配管理" = @{
        "emoji" = "🏪"
        "id" = "user-store-allocation-folder"
        "endpoints" = @(
            @{ name = "分配用户到门店"; method = "POST"; path = "/user-store-allocations"; body = '{"user_id": 1, "store_id": 1}' },
            @{ name = "获取用户门店分配列表"; method = "GET"; path = "/user-store-allocations"; params = @("page", "page_size") },
            @{ name = "获取门店的用户列表"; method = "GET"; path = "/user-store-allocations/store/1/users"; params = @("page", "page_size") },
            @{ name = "移除用户门店分配"; method = "DELETE"; path = "/user-store-allocations/1"; params = @() },
            @{ name = "更新用户平台访问权限"; method = "PUT"; path = "/user-store-allocations/1/platform"; body = '{"platform": "admin"}' },
            @{ name = "根据平台获取用户列表"; method = "GET"; path = "/user-store-allocations/platform/admin/users"; params = @("page", "page_size") }
        )
    }
    "文件管理" = @{
        "emoji" = "📁"
        "id" = "file-folder"
        "endpoints" = @(
            @{ name = "通用文件上传"; method = "POST"; path = "/files/upload"; body = 'form-data' },
            @{ name = "代理文件上传"; method = "POST"; path = "/files/proxy-upload"; body = '{"file_name": "test.jpg", "file_type": "avatar"}' },
            @{ name = "获取预签名上传URL"; method = "POST"; path = "/files/presigned-upload-url"; body = '{"file_name": "test.jpg", "file_type": "avatar"}' },
            @{ name = "获取预签名下载URL"; method = "GET"; path = "/files/presigned-download-url"; params = @("object_name") },
            @{ name = "获取文件列表"; method = "GET"; path = "/files"; params = @("page", "page_size", "file_type") },
            @{ name = "删除文件"; method = "DELETE"; path = "/files/1"; params = @() },
            @{ name = "批量删除文件"; method = "DELETE"; path = "/files"; body = '[1, 2, 3]' },
            @{ name = "获取实体关联的文件列表"; method = "GET"; path = "/files/entity/user/1"; params = @() }
        )
    }
}

# 生成JSON结构的函数
function Generate-Endpoint {
    param($endpoint, $basePath)
    
    $params = ""
    if ($endpoint.params -and $endpoint.params.Count -gt 0) {
        $paramArray = @()
        foreach ($param in $endpoint.params) {
            $paramArray += "            {`"key`": `"$param`", `"value`": `"1`", `"active`": true}"
        }
        $params = ",`n          `"params`": [`n" + ($paramArray -join ",`n") + "`n          ]"
    } else {
        $params = ",`n          `"params`": []"
    }
    
    $body = ""
    if ($endpoint.body) {
        if ($endpoint.body -eq 'form-data') {
            $body = ",`n          `"body`": {`n            `"contentType`": `"multipart/form-data`",`n            `"body`": `"form-data`"`n          }"
        } else {
            $body = ",`n          `"body`": {`n            `"contentType`": `"application/json`",`n            `"body`": `"$($endpoint.body)`"`n          }"
        }
    } else {
        $body = ",`n          `"body`": {`n            `"contentType`": null,`n            `"body`": null`n          }"
    }
    
    $headers = ""
    if ($endpoint.method -in @("POST", "PUT", "PATCH")) {
        $headers = ",`n            {`n              `"key`": `"Content-Type`",`n              `"value`": `"application/json`"`n            }"
    }
    
    return @"
        {
          "v": "14",
          "name": "$($endpoint.name)",
          "method": "$($endpoint.method)",
          "endpoint": "<<baseURL>>/api/v1/admin$($endpoint.path)"$params,
          "headers": [
            {
              "key": "Authorization",
              "value": "Bearer <<access_token>>"
            }$headers
          ],
          "preRequestScript": "",
          "testScript": "pw.test(`"Status code is 200`", () => {`n    pw.expect(pw.response.status).toBe(200);`n});",
          "auth": {
            "authType": "bearer",
            "authActive": true,
            "token": "<<access_token>>"
          }$body,
          "requestVariables": [],
          "responses": {}
        }
"@
}

# 生成模块的函数
function Generate-Module {
    param($moduleName, $moduleData)
    
    $endpoints = @()
    foreach ($endpoint in $moduleData.endpoints) {
        $endpoints += Generate-Endpoint -endpoint $endpoint -basePath "/api/v1/admin"
    }
    
    return @"
    {
      "v": 1,
      "id": "$($moduleData.id)",
      "name": "$($moduleData.emoji) $moduleName",
      "folders": [],
      "requests": [
$($endpoints -join ",`n")
      ]
    }
"@
}

Write-Host "✅ 脚本生成完成！请手动将生成的模块添加到JSON文件中。" -ForegroundColor Green
Write-Host "📝 建议使用str-replace-editor工具逐步添加各个模块。" -ForegroundColor Yellow
