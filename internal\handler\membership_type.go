package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// MembershipTypeHandler 会员卡类型处理器
type MembershipTypeHandler struct {
	db *gorm.DB
}

// NewMembershipTypeHandler 创建会员卡类型处理器实例
func NewMembershipTypeHandler(db *gorm.DB) *MembershipTypeHandler {
	return &MembershipTypeHandler{
		db: db,
	}
}

// GetMembershipTypes 获取会员卡类型列表
func (h *MembershipTypeHandler) GetMembershipTypes(c *gin.Context) {
	var req dto.MembershipTypeListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.MembershipCardType{})

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 分类筛选
	if req.Category != "" {
		query = query.Where("category = ?", req.Category)
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var cardTypes []model.MembershipCardType
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&cardTypes).Error

	if err != nil {
		log.Error("查询会员卡类型失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡类型失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     cardTypes,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetMembershipType 获取会员卡类型详情
func (h *MembershipTypeHandler) GetMembershipType(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡类型ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var cardType model.MembershipCardType
	if err := h.db.First(&cardType, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡类型不存在"))
		} else {
			log.Error("查询会员卡类型失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡类型失败"))
		}
		return
	}

	code.AutoResponse(c, cardType, nil)
}

// CreateMembershipType 创建会员卡类型
func (h *MembershipTypeHandler) CreateMembershipType(c *gin.Context) {
	var req dto.CreateMembershipTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建会员卡类型参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证分类类型
	if !isValidCategory(req.Category) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的卡种类别"))
		return
	}

	// 创建会员卡类型
	cardType := model.MembershipCardType{
		Name:              req.Name,
		Category:          enum.MembershipCardCategory(req.Category),
		Scope:             enum.MembershipCardScope(req.Scope),
		Description:       req.Description,
		ValidityDays:      req.ValidityDays,
		Times:             req.Times,
		Amount:            req.Amount,
		Price:             req.Price,
		Discount:          req.Discount,
		Shareable:         req.Shareable,
		CanSetExpiry:      req.CanSetExpiry,
		CanSelectStores:   req.CanSelectStores,
		CanAddSubCard:     req.CanAddSubCard,
		CanSetDailyLimit:  req.CanSetDailyLimit,
		DailyBookingLimit: req.DailyBookingLimit,
		ApplicableCourses: req.ApplicableCourses,
		SortOrder:         req.SortOrder,
		Status:            1, // 默认启用
	}

	if err := h.db.Create(&cardType).Error; err != nil {
		log.Error("创建会员卡类型失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建会员卡类型失败"))
		return
	}

	log.Info("创建会员卡类型成功", "id", cardType.ID, "name", cardType.Name)
	code.AutoResponse(c, cardType, nil)
}

// UpdateMembershipType 更新会员卡类型
func (h *MembershipTypeHandler) UpdateMembershipType(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡类型ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateMembershipTypeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新会员卡类型参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡类型是否存在
	var cardType model.MembershipCardType
	if err := h.db.First(&cardType, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡类型不存在"))
		} else {
			log.Error("查询会员卡类型失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡类型失败"))
		}
		return
	}

	// 验证分类类型
	if req.Category != "" && !isValidCategory(req.Category) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的卡种类别"))
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Category != "" {
		updates["category"] = req.Category
	}
	if req.Scope != "" {
		updates["scope"] = req.Scope
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.ValidityDays != nil {
		updates["validity_days"] = *req.ValidityDays
	}
	if req.Times != nil {
		updates["times"] = *req.Times
	}
	if req.Amount != nil {
		updates["amount"] = *req.Amount
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}
	if req.Discount != nil {
		updates["discount"] = *req.Discount
	}
	if req.Shareable != nil {
		updates["shareable"] = *req.Shareable
	}
	if req.CanSetExpiry != nil {
		updates["can_set_expiry"] = *req.CanSetExpiry
	}
	if req.CanSelectStores != nil {
		updates["can_select_stores"] = *req.CanSelectStores
	}
	if req.CanAddSubCard != nil {
		updates["can_add_sub_card"] = *req.CanAddSubCard
	}
	if req.CanSetDailyLimit != nil {
		updates["can_set_daily_limit"] = *req.CanSetDailyLimit
	}
	if req.DailyBookingLimit != nil {
		updates["daily_booking_limit"] = *req.DailyBookingLimit
	}
	if req.ApplicableCourses != "" {
		updates["applicable_courses"] = req.ApplicableCourses
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if err := h.db.Model(&cardType).Updates(updates).Error; err != nil {
		log.Error("更新会员卡类型失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新会员卡类型失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.First(&cardType, id)

	log.Info("更新会员卡类型成功", "id", id)
	code.AutoResponse(c, cardType, nil)
}

// DeleteMembershipType 删除会员卡类型
func (h *MembershipTypeHandler) DeleteMembershipType(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡类型ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 检查是否有关联的会员卡
	var count int64
	h.db.Model(&model.MembershipCard{}).Where("card_type_id = ?", id).Count(&count)
	if count > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该卡种下还有会员卡，无法删除"))
		return
	}

	// 删除会员卡类型
	if err := h.db.Delete(&model.MembershipCardType{}, id).Error; err != nil {
		log.Error("删除会员卡类型失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除会员卡类型失败"))
		return
	}

	log.Info("删除会员卡类型成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// isValidCategory 验证卡种类别是否有效
func isValidCategory(category string) bool {
	validCategories := []string{"times", "period", "balance"}
	for _, valid := range validCategories {
		if category == valid {
			return true
		}
	}
	return false
}
