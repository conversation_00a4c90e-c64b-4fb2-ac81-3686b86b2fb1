package service

import (
	"strings"
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// PermissionService 权限服务
type PermissionService struct {
	db *gorm.DB
}

// NewPermissionService 创建权限服务实例
func NewPermissionService(db *gorm.DB) *PermissionService {
	return &PermissionService{
		db: db,
	}
}

// UserPermissionContext 用户权限上下文
type UserPermissionContext struct {
	UserID    string
	Platform  enum.Platform
	Roles     []model.Role
	StoreIDs  []uint // 用户所在的门店
	IsAdmin   bool
	IsManager bool
	IsSales   bool
}

// GetUserPermissionContext 获取用户权限上下文
func (s *PermissionService) GetUserPermissionContext(userID string) (*UserPermissionContext, error) {
	// 查询用户及其角色
	var user model.User
	err := s.db.Preload("Roles").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}

	// 确定用户的主要平台（根据访问权限推断）
	var platform enum.Platform = enum.PlatformWechat // 默认微信平台
	if user.CanAccessPlatform(enum.PlatformAdmin) {
		platform = enum.PlatformAdmin
	}

	ctx := &UserPermissionContext{
		UserID:   userID,
		Platform: platform,
		Roles:    user.Roles,
	}

	// 分析角色类型（基于角色名称模糊匹配）
	for _, role := range user.Roles {
		roleName := strings.ToLower(role.Name)
		switch {
		case role.Name == "admin" || role.Name == "超级管理员" || strings.Contains(roleName, "admin"):
			ctx.IsAdmin = true
		case role.Name == "manager" || role.Name == "店长" || strings.Contains(roleName, "manager"):
			ctx.IsManager = true
		case role.Name == "sales" || role.Name == "销售" || strings.Contains(roleName, "sales"):
			ctx.IsSales = true
		}
	}

	// 获取用户所在的门店（所有用户都需要门店信息，包括管理员）
	storeIDs, err := model.GetUserStoreIDs(s.db, userID)
	if err != nil {
		return nil, err
	}
	ctx.StoreIDs = storeIDs

	return ctx, nil
}

// GetAccessibleMemberIDs 获取用户可访问的会员ID列表（基于门店关联）
func (s *PermissionService) GetAccessibleMemberIDs(ctx *UserPermissionContext) ([]string, error) {
	// 超级管理员可以访问所有会员
	if ctx.IsAdmin {
		var allMembers []model.User
		s.db.Model(&model.User{}).Where("platform_access & ? > 0", enum.PlatformAccessWechat).Select("id").Find(&allMembers)
		var memberIDs []string
		for _, member := range allMembers {
			memberIDs = append(memberIDs, member.ID.String())
		}
		return memberIDs, nil
	}

	var memberIDs []string

	// 店长和其他员工：基于门店关联获取会员
	if len(ctx.StoreIDs) > 0 {
		storeMembers, err := model.GetStoreMembers(s.db, ctx.StoreIDs)
		if err != nil {
			return nil, err
		}
		memberIDs = append(memberIDs, storeMembers...)
	}

	// 销售：额外获取特别分配给自己的会员
	if ctx.IsSales {
		assignedMembers, err := model.GetSalesAssignedMembers(s.db, ctx.UserID)
		if err != nil {
			return nil, err
		}
		memberIDs = append(memberIDs, assignedMembers...)
	}

	// 去重
	uniqueIDs := make([]string, 0)
	seen := make(map[string]bool)
	for _, id := range memberIDs {
		if !seen[id] {
			uniqueIDs = append(uniqueIDs, id)
			seen[id] = true
		}
	}

	return uniqueIDs, nil
}

// CanAccessMember 检查用户是否可以访问指定会员
func (s *PermissionService) CanAccessMember(ctx *UserPermissionContext, memberID string) bool {
	// 超级管理员可以访问所有会员
	if ctx.IsAdmin {
		return true
	}

	// 店长可以访问所在门店的会员
	if ctx.IsManager && len(ctx.StoreIDs) > 0 {
		var count int64
		s.db.Model(&model.MembershipCard{}).
			Where("user_id = ? AND store_id IN ?", memberID, ctx.StoreIDs).
			Count(&count)
		if count > 0 {
			return true
		}
	}

	// 销售可以访问分配给自己的会员
	if ctx.IsSales {
		var count int64
		s.db.Model(&model.MemberSalesAssignment{}).
			Where("sales_id = ? AND member_id = ? AND status = 1", ctx.UserID, memberID).
			Count(&count)
		if count > 0 {
			return true
		}
	}

	return false
}

// GetAccessibleStoreIDs 获取用户可访问的门店ID列表
func (s *PermissionService) GetAccessibleStoreIDs(ctx *UserPermissionContext) []uint {
	// 超级管理员可以访问所有门店
	if ctx.IsAdmin {
		var allStores []model.Store
		s.db.Model(&model.Store{}).Select("id").Find(&allStores)
		var storeIDs []uint
		for _, store := range allStores {
			storeIDs = append(storeIDs, store.ID)
		}
		return storeIDs
	}

	// 其他角色只能访问分配给自己的门店
	return ctx.StoreIDs
}

// AssignMemberToSales 分配会员给销售
func (s *PermissionService) AssignMemberToSales(assignerCtx *UserPermissionContext, memberID, salesID string, storeID uint, remark string) error {
	// 检查分配者权限（只有管理员和店长可以分配）
	if !assignerCtx.IsAdmin && !assignerCtx.IsManager {
		return gorm.ErrInvalidData
	}

	// 店长只能分配自己门店的会员
	if assignerCtx.IsManager && !contains(assignerCtx.StoreIDs, storeID) {
		return gorm.ErrInvalidData
	}

	// 检查是否已经分配
	var existingAssignment model.MemberSalesAssignment
	err := s.db.Where("member_id = ? AND status = 1", memberID).First(&existingAssignment).Error
	if err == nil {
		// 已经分配，更新分配
		return s.db.Model(&existingAssignment).Updates(map[string]interface{}{
			"sales_id":    salesID,
			"store_id":    storeID,
			"assigned_by": assignerCtx.UserID,
			"remark":      remark,
		}).Error
	}

	// 创建新分配
	assignment := model.MemberSalesAssignment{
		MemberID:   memberID,
		SalesID:    salesID,
		StoreID:    storeID,
		AssignedBy: assignerCtx.UserID,
		Remark:     remark,
		Status:     1,
	}

	return s.db.Create(&assignment).Error
}

// TransferMember 转移会员
func (s *PermissionService) TransferMember(assignerCtx *UserPermissionContext, memberID, fromSalesID, toSalesID string, remark string) error {
	// 检查分配者权限
	if !assignerCtx.IsAdmin && !assignerCtx.IsManager {
		return gorm.ErrInvalidData
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 将原分配设为无效
	err := tx.Model(&model.MemberSalesAssignment{}).
		Where("member_id = ? AND sales_id = ? AND status = 1", memberID, fromSalesID).
		Update("status", 0).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	// 创建新分配
	newAssignment := model.MemberSalesAssignment{
		MemberID:   memberID,
		SalesID:    toSalesID,
		AssignedBy: assignerCtx.UserID,
		Remark:     remark,
		Status:     1,
	}

	if err := tx.Create(&newAssignment).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// 辅助函数
func contains(slice []uint, item uint) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
