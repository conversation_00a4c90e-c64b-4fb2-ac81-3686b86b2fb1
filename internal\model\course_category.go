package model

import "yogaga/internal/enum"

// CourseCategory 课程分类（扁平化结构）
type CourseCategory struct {
	BaseModel
	Name        string                    `json:"name" gorm:"type:varchar(50);not null;comment:分类名称"`
	Description string                    `json:"description" gorm:"type:text;comment:分类描述"`
	SortOrder   int                       `json:"sort_order" gorm:"type:int;default:0;comment:排序"`
	Status      enum.CourseCategoryStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:启用 0:停用"`
}
