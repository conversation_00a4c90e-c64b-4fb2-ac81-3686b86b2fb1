# 测试小程序端免登录接口
param(
    [string]$BaseUrl = "http://127.0.0.1:9095"
)

Write-Host "=== 小程序端免登录接口测试 ===" -ForegroundColor Green
Write-Host "Base URL: $BaseUrl" -ForegroundColor Cyan
Write-Host ""

$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-API {
    param(
        [string]$Name,
        [string]$Url
    )

    $global:totalTests++
    Write-Host "测试: $Name" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray

    try {
        $response = Invoke-RestMethod -Uri $Url -Method Get -TimeoutSec 10

        if ($response.code -eq 0) {
            Write-Host "✅ 成功" -ForegroundColor Green
            if ($response.data.total) {
                Write-Host "   数据总数: $($response.data.total)" -ForegroundColor Gray
            } elseif ($response.data.Count) {
                Write-Host "   数据条数: $($response.data.Count)" -ForegroundColor Gray
            } elseif ($response.data.id) {
                Write-Host "   数据ID: $($response.data.id)" -ForegroundColor Gray
            }
            $global:passedTests++
        } else {
            Write-Host "❌ 失败 - 错误码: $($response.code), 消息: $($response.msg)" -ForegroundColor Red
            $global:failedTests++
        }
    } catch {
        Write-Host "❌ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
    }

    Write-Host ""
}

# 1. 健康检查接口
Test-API "健康检查" "$BaseUrl/healthz"

# 2. 课程相关接口（免登录）
Test-API "课程列表" "$BaseUrl/api/v1/app/courses?page=1`&page_size=10"
Test-API "课程分类列表" "$BaseUrl/api/v1/app/course-categories"
Test-API "课程日历" "$BaseUrl/api/v1/app/courses/calendar"
Test-API "按日期查询课程" "$BaseUrl/api/v1/app/courses/date?date=2025-07-29"

# 3. 教练相关接口（免登录）
Test-API "教练列表" "$BaseUrl/api/v1/app/coaches?page=1`&page_size=10"

# 4. 门店相关接口（免登录）
Test-API "门店列表" "$BaseUrl/api/v1/app/stores?page=1`&page_size=10"
Test-API "门店详情" "$BaseUrl/api/v1/app/stores/1"

# 5. 轮播图接口（免登录）
Test-API "轮播图列表" "$BaseUrl/api/v1/app/banners"

# 显示测试结果汇总
Write-Host "=== 测试结果汇总 ===" -ForegroundColor Green
Write-Host "总测试数: $totalTests" -ForegroundColor Cyan
Write-Host "通过: $passedTests" -ForegroundColor Green
Write-Host "失败: $failedTests" -ForegroundColor Red

if ($failedTests -eq 0) {
    Write-Host "🎉 所有测试通过！免登录接口配置正确。" -ForegroundColor Green
} else {
    Write-Host "⚠️  有 $failedTests 个测试失败，请检查配置。" -ForegroundColor Red
}
