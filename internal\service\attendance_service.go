package service

import (
	"fmt"
	"time"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"gorm.io/gorm"
)

// AttendanceService 出勤统计服务
type AttendanceService struct {
	db                        *gorm.DB
	membershipReminderService *MembershipReminderService
}

// NewAttendanceService 创建出勤统计服务实例
func NewAttendanceService(db *gorm.DB, membershipReminderService *MembershipReminderService) *AttendanceService {
	return &AttendanceService{
		db:                        db,
		membershipReminderService: membershipReminderService,
	}
}

// AttendanceStats 出勤统计结构
type AttendanceStats struct {
	UserID           uint    `json:"user_id"`
	CardID           uint    `json:"card_id"`
	TotalBookings    int     `json:"total_bookings"`    // 总预约次数
	AttendedBookings int     `json:"attended_bookings"` // 已签到次数
	NoShowBookings   int     `json:"no_show_bookings"`  // 未到课次数
	AttendanceRate   float64 `json:"attendance_rate"`   // 出勤率
}

// CountYearlyNoShows 统计年度未签到次数
func (s *AttendanceService) CountYearlyNoShows(userID string, cardID uint) (int, error) {
	// 获取当前年份的开始和结束时间
	now := time.Now()
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	yearEnd := time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, now.Location())

	var noShowCount int64
	err := s.db.Model(&model.Booking{}).
		Where("user_id = ? AND membership_card_id = ? AND status = 5 AND created_at >= ? AND created_at < ?",
			userID, cardID, yearStart, yearEnd).
		Count(&noShowCount).Error

	if err != nil {
		log.Error("统计年度未签到次数失败", "user_id", userID, "card_id", cardID, "error", err)
		return 0, fmt.Errorf("统计年度未签到次数失败: %w", err)
	}

	return int(noShowCount), nil
}

// GetAttendanceStats 获取出勤统计
func (s *AttendanceService) GetAttendanceStats(userID uint, cardID uint) (*AttendanceStats, error) {
	// 获取当前年份的开始和结束时间
	now := time.Now()
	yearStart := time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	yearEnd := time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, now.Location())

	// 统计总预约次数
	var totalBookings int64
	err := s.db.Model(&model.Booking{}).
		Where("user_id = ? AND membership_card_id = ? AND created_at >= ? AND created_at < ?",
			userID, cardID, yearStart, yearEnd).
		Count(&totalBookings).Error

	if err != nil {
		return nil, fmt.Errorf("统计总预约次数失败: %w", err)
	}

	// 统计已签到次数
	var attendedBookings int64
	err = s.db.Model(&model.Booking{}).
		Where("user_id = ? AND membership_card_id = ? AND checkin_time IS NOT NULL AND created_at >= ? AND created_at < ?",
			userID, cardID, yearStart, yearEnd).
		Count(&attendedBookings).Error

	if err != nil {
		return nil, fmt.Errorf("统计已签到次数失败: %w", err)
	}

	// 统计未到课次数
	var noShowBookings int64
	err = s.db.Model(&model.Booking{}).
		Where("user_id = ? AND membership_card_id = ? AND status = 5 AND created_at >= ? AND created_at < ?",
			userID, cardID, yearStart, yearEnd).
		Count(&noShowBookings).Error

	if err != nil {
		return nil, fmt.Errorf("统计未到课次数失败: %w", err)
	}

	// 计算出勤率
	var attendanceRate float64
	if totalBookings > 0 {
		attendanceRate = float64(attendedBookings) / float64(totalBookings) * 100
	}

	stats := &AttendanceStats{
		UserID:           userID,
		CardID:           cardID,
		TotalBookings:    int(totalBookings),
		AttendedBookings: int(attendedBookings),
		NoShowBookings:   int(noShowBookings),
		AttendanceRate:   attendanceRate,
	}

	return stats, nil
}

// CheckValidityDeduction 检查是否需要扣除有效期
func (s *AttendanceService) CheckValidityDeduction(cardID uint) error {
	// 查询会员卡信息
	var card model.MembershipCard
	err := s.db.Preload("User").Preload("CardType").First(&card, cardID).Error
	if err != nil {
		return fmt.Errorf("查询会员卡失败: %w", err)
	}

	// 只处理期限卡
	if card.CardType.Category != "period" {
		return nil
	}

	// 统计年度未签到次数
	noShowCount, err := s.CountYearlyNoShows(card.UserID, cardID)
	if err != nil {
		return fmt.Errorf("统计未签到次数失败: %w", err)
	}

	// 检查是否超过2次
	if noShowCount > 2 {
		return s.processValidityDeduction(&card, noShowCount)
	}

	return nil
}

// processValidityDeduction 处理有效期扣除
func (s *AttendanceService) processValidityDeduction(card *model.MembershipCard, noShowCount int) error {
	// 检查是否已经发送过提醒
	var existingNotification model.AdminNotification
	err := s.db.Where("type = ? AND card_id = ? AND created_at > ?",
		model.NotificationTypeValidityDeduct, card.ID, time.Now().AddDate(0, 0, -30)).
		First(&existingNotification).Error

	if err == nil {
		// 30天内已发送过提醒，跳过
		return nil
	}

	// 计算扣除天数（每超过2次未签到扣除7天）
	deductDays := (noShowCount - 2) * 7

	// 创建后台通知
	notification := model.AdminNotification{
		Type:      model.NotificationTypeValidityDeduct,
		Title:     "期限卡有效期扣除提醒",
		Content:   fmt.Sprintf("用户 %s 的 %s 年度未签到 %d 次，建议扣除有效期 %d 天", card.User.NickName, card.CardType.Name, noShowCount, deductDays),
		UserID:    &card.UserID,
		CardID:    &card.ID,
		Priority:  model.PriorityHigh,
		ExtraData: fmt.Sprintf(`{"no_show_count": %d, "deduct_days": %d}`, noShowCount, deductDays),
	}

	if err := s.db.Create(&notification).Error; err != nil {
		log.Error("创建有效期扣除通知失败", "card_id", card.ID, "error", err)
		return fmt.Errorf("创建有效期扣除通知失败: %w", err)
	}

	// 发送微信提醒
	if card.User.HasWeChatOpenID() {
		go func() {
			err := s.membershipReminderService.wechatService.SendValidityDeductReminder(
				card.User.GetWeChatOpenID(),
				card.CardType.Name,
				fmt.Sprintf("%d次", noShowCount),
				fmt.Sprintf("%d天", deductDays),
			)
			if err != nil {
				log.Error("发送有效期扣除微信提醒失败", "card_id", card.ID, "error", err)
			} else {
				log.Info("有效期扣除微信提醒发送成功", "card_id", card.ID)
			}
		}()
	}

	log.Info("处理有效期扣除提醒成功", "card_id", card.ID, "no_show_count", noShowCount, "deduct_days", deductDays)
	return nil
}

// CheckAllPeriodCards 检查所有期限卡的出勤情况
func (s *AttendanceService) CheckAllPeriodCards() error {
	log.Info("开始检查所有期限卡的出勤情况")

	// 查询所有有效的期限卡
	var cards []model.MembershipCard
	err := s.db.Preload("CardType").
		Joins("JOIN card_types ON card_types.id = membership_cards.card_type_id").
		Where("membership_cards.status = 1 AND card_types.category = 'period'").
		Find(&cards).Error

	if err != nil {
		log.Error("查询期限卡失败", "error", err)
		return fmt.Errorf("查询期限卡失败: %w", err)
	}

	log.Info("找到期限卡", "count", len(cards))

	// 检查每张期限卡
	for _, card := range cards {
		if err := s.CheckValidityDeduction(card.ID); err != nil {
			log.Error("检查期限卡有效期扣除失败", "card_id", card.ID, "error", err)
			continue
		}
	}

	log.Info("期限卡出勤检查完成")
	return nil
}

// GetUserAttendanceReport 获取用户出勤报告
func (s *AttendanceService) GetUserAttendanceReport(userID uint) ([]AttendanceStats, error) {
	// 查询用户的所有会员卡
	var cards []model.MembershipCard
	err := s.db.Where("user_id = ? AND status = 1", userID).Find(&cards).Error
	if err != nil {
		return nil, fmt.Errorf("查询用户会员卡失败: %w", err)
	}

	var reports []AttendanceStats
	for _, card := range cards {
		stats, err := s.GetAttendanceStats(userID, card.ID)
		if err != nil {
			log.Error("获取出勤统计失败", "user_id", userID, "card_id", card.ID, "error", err)
			continue
		}
		reports = append(reports, *stats)
	}

	return reports, nil
}
