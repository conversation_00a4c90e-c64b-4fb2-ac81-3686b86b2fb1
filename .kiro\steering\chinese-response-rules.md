# Kiro 中文回复规则

## 语言要求

- **所有回复必须使用中文**
- 代码注释使用中文
- 错误信息使用中文
- 文档说明使用中文

## 项目规则引用

请严格遵循项目中 `.cursor/rules` 文件中定义的所有开发规则，包括但不限于：

#[[file:.cursor/rules]]

## Spec 工作流程规则

### 任务执行规范
- 在执行任何任务之前，**必须**先阅读 spec 的 requirements.md、design.md 和 tasks.md 文件
- 没有需求和设计文档就执行任务会导致实现不准确
- **一次只专注于一个任务**，不要实现其他任务的功能
- 完成请求的任务后停止，让用户审查，**不要**自动继续下一个任务
- 如果用户没有指定要执行哪个任务，查看该 spec 的任务列表并推荐下一个要执行的任务

### 文档审查流程
- 每个 spec 文档（需求、设计、任务）更新后，**必须**使用 'userInput' 工具请求用户审查
- **必须**在进入下一阶段之前获得用户明确批准
- 如果用户提供反馈，**必须**进行修改并再次请求明确批准
- **必须**继续反馈-修订循环直到用户明确批准文档
- **不得**在没有完成早期步骤并获得用户明确批准的情况下跳到后续步骤

### 工作流程约束
- **必须**按顺序遵循工作流程步骤
- **必须**将工作流程中的每个约束视为严格要求
- **不得**假设用户偏好或需求 - 始终明确询问
- **必须**保持当前所在步骤的清晰记录
- **不得**将多个步骤合并到单个交互中
- **必须**一次只执行一个任务，完成后不要自动移动到下一个任务

## 关键开发规范

### 1. 数据模型规范
- 必须使用 `BaseModel` 而不是 `gorm.Model`
- 所有字段必须有 snake_case 格式的 `json` 标签
- 敏感字段必须使用 `json:"-"`

### 2. API 响应格式
- 必须使用 `code.AutoResponse()` 方法
- 禁止使用 `gin.H`（健康检查除外）
- 响应格式必须保持一致的 snake_case

### 3. 错误处理
- 用户友好的中文错误信息
- 详细错误仅记录在服务器日志中
- 使用统一的错误代码格式

### 4. 代码质量
- 使用结构化日志记录
- GORM 注释使用中文
- 遵循 Go 命名约定
- 对所有数据库操作添加适当的错误处理

## 回复风格

- 简洁明了，直接回答问题
- 提供具体的代码示例
- 解释技术决策的原因
- 保持专业但友好的语调

## 代码示例格式

当提供代码示例时，请包含中文注释：

```go
// 创建用户处理器
func (h *UserHandler) CreateUser(c *gin.Context) {
    var req dto.CreateUserRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
        return
    }
    
    // 创建用户实例
    user := model.User{
        Username: req.Username,
        Email:    req.Email,
    }
    
    // 保存到数据库
    if err := h.db.Create(&user).Error; err != nil {
        log.Error("创建用户失败", "error", err)
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建用户失败"))
        return
    }
    
    code.AutoResponse(c, user, nil)
}
```