#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员卡管理系统业务需求全面验证测试
验证系统是否完全满足业务设计要求
"""

import requests
import time
from datetime import datetime, timedelta

class BusinessRequirementTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        self.test_results = {}
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def api_request(self, method: str, endpoint: str, data=None) -> dict:
        """统一API请求方法"""
        url = f"{self.base_url}{endpoint}"
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=data)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_card_type_management(self) -> dict:
        """测试会员卡种类管理功能"""
        print("\n📋 测试会员卡种类管理功能...")
        results = {}
        
        # 1. 获取所有卡种
        result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                {"page": "1", "page_size": "50"})
        if result['success']:
            card_types = result['data'].get('data', {}).get('list', [])
            results['获取卡种列表'] = True
            print(f"✅ 成功获取 {len(card_types)} 种会员卡类型")
            
            # 验证必需的10种卡种是否存在
            required_cards = [
                "团课通卡", "普拉提次卡", "提升小班卡", "标准私教1V1", "明星私教1V1",
                "瑜伽期限卡", "舞蹈期限卡", "瑜/普期限卡", "共享储值卡", "储值卡"
            ]
            
            existing_cards = [card.get('name', '') for card in card_types]
            missing_cards = [card for card in required_cards if card not in existing_cards]
            
            if not missing_cards:
                results['必需卡种完整性'] = True
                print("✅ 所有必需的10种卡种都存在")
            else:
                results['必需卡种完整性'] = False
                print(f"❌ 缺少卡种: {missing_cards}")
            
            # 验证卡种属性
            for card in card_types:
                name = card.get('name', '')
                if name in required_cards:
                    print(f"   📋 {name}:")
                    print(f"      类别: {card.get('category', 'N/A')}")
                    print(f"      可设置有效期: {card.get('can_set_expiry', False)}")
                    print(f"      可选择门店: {card.get('can_select_stores', False)}")
                    print(f"      可添加副卡: {card.get('can_add_sub_card', False)}")
                    print(f"      可设置单日限制: {card.get('can_set_daily_limit', False)}")
                    print(f"      状态: {'启用' if card.get('status') == 1 else '停用'}")
        else:
            results['获取卡种列表'] = False
            print(f"❌ 获取卡种列表失败: {result['error']}")
        
        return results
    
    def test_card_categories(self) -> dict:
        """测试会员卡大类管理"""
        print("\n📋 测试会员卡大类管理...")
        results = {}
        
        result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                {"page": "1", "page_size": "50"})
        if result['success']:
            card_types = result['data'].get('data', {}).get('list', [])
            categories = set(card.get('category', '') for card in card_types)
            
            required_categories = {'times', 'period', 'balance'}
            
            if required_categories.issubset(categories):
                results['卡种大类完整性'] = True
                print("✅ 支持所有必需的卡种大类:")
                print("   - times (次数卡)")
                print("   - period (期限卡)")
                print("   - balance (储值卡)")
            else:
                results['卡种大类完整性'] = False
                missing = required_categories - categories
                print(f"❌ 缺少卡种大类: {missing}")
        else:
            results['卡种大类完整性'] = False
            print(f"❌ 无法验证卡种大类: {result['error']}")
        
        return results
    
    def test_card_opening_modes(self) -> dict:
        """测试开卡的两种模式"""
        print("\n💳 测试开卡功能...")
        results = {}
        
        # 创建测试用户
        timestamp = int(time.time())
        test_user = {
            "username": f"test_user_{timestamp}",
            "email": f"test_user_{timestamp}@example.com",
            "password": "123456",
            "role_ids": []
        }
        
        user_result = self.api_request('POST', '/api/v1/admin/users', test_user)
        if not user_result['success']:
            results['创建测试用户'] = False
            print(f"❌ 创建测试用户失败: {user_result['error']}")
            return results
        
        user_id = user_result['data'].get('data', {}).get('id')
        results['创建测试用户'] = True
        print(f"✅ 创建测试用户成功，ID: {user_id}")
        
        # 获取可用卡种
        card_types_result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                           {"page": "1", "page_size": "20", "status": "1"})
        if not card_types_result['success']:
            results['获取可用卡种'] = False
            return results
        
        card_types = card_types_result['data'].get('data', {}).get('list', [])
        if not card_types:
            results['获取可用卡种'] = False
            return results
        
        results['获取可用卡种'] = True
        selected_card_type = card_types[0]
        
        # 测试模式1：线下支付开卡
        print("   📋 测试模式1：线下支付开卡")
        card_data = {
            "user_id": user_id,
            "card_type_id": selected_card_type.get('id'),
            "start_date": datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
            "purchase_price": int(selected_card_type.get('price', 0) * 0.9),
            "discount": 0.9,
            "payment_method": "cash",
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "线下支付开卡测试"
        }
        
        card_result = self.api_request('POST', '/api/v1/admin/membership-cards', card_data)
        if card_result['success']:
            results['线下支付开卡'] = True
            card_info = card_result['data'].get('data', {})
            print(f"   ✅ 线下支付开卡成功，卡号: {card_info.get('card_number')}")
            print(f"      折扣: {card_info.get('discount', 1.0)}")
            print(f"      有效期: {card_info.get('start_date')} 到 {card_info.get('end_date')}")
        else:
            results['线下支付开卡'] = False
            print(f"   ❌ 线下支付开卡失败: {card_result['error']}")
        
        # 测试模式2：其他会员卡支付（需要先有一张可用的卡）
        print("   📋 测试模式2：其他会员卡支付")
        # 这个功能需要更复杂的实现，暂时标记为需要验证
        results['其他会员卡支付开卡'] = None  # 需要进一步实现
        print("   ⚠️  其他会员卡支付功能需要进一步验证")
        
        return results
    
    def test_advanced_features(self) -> dict:
        """测试高级功能"""
        print("\n🚀 测试高级功能...")
        results = {}
        
        # 这些功能需要根据API接口进一步实现
        advanced_features = [
            "转卡功能", "充值功能", "扣费功能", "升级功能", 
            "请假功能", "停用功能", "延期功能", "预约详情"
        ]
        
        for feature in advanced_features:
            # 暂时标记为需要验证
            results[feature] = None
            print(f"   ⚠️  {feature} 需要进一步验证API接口")
        
        return results
    
    def test_sub_card_functionality(self) -> dict:
        """测试主副卡功能"""
        print("\n👥 测试主副卡功能...")
        results = {}
        
        # 检查共享储值卡的副卡功能
        card_types_result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                           {"page": "1", "page_size": "50"})
        if card_types_result['success']:
            card_types = card_types_result['data'].get('data', {}).get('list', [])
            shared_cards = [card for card in card_types 
                          if card.get('name') == '共享储值卡' and card.get('can_add_sub_card')]
            
            if shared_cards:
                results['共享储值卡支持副卡'] = True
                print("✅ 共享储值卡支持副卡功能")
            else:
                results['共享储值卡支持副卡'] = False
                print("❌ 共享储值卡不支持副卡功能")
        
        return results
    
    def test_daily_limit_functionality(self) -> dict:
        """测试单日预约限制功能"""
        print("\n📅 测试单日预约限制功能...")
        results = {}
        
        card_types_result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                           {"page": "1", "page_size": "50"})
        if card_types_result['success']:
            card_types = card_types_result['data'].get('data', {}).get('list', [])
            
            # 检查期限卡的单日限制功能
            period_cards = [card for card in card_types 
                          if 'period' in card.get('category', '') or '期限卡' in card.get('name', '')]
            
            period_cards_with_limit = [card for card in period_cards 
                                     if card.get('can_set_daily_limit')]
            
            if period_cards_with_limit:
                results['期限卡单日限制'] = True
                print("✅ 期限卡支持单日预约限制功能")
                for card in period_cards_with_limit:
                    print(f"   - {card.get('name')}: 可设置单日限制")
            else:
                results['期限卡单日限制'] = False
                print("❌ 期限卡不支持单日预约限制功能")
        
        return results
    
    def generate_business_compliance_report(self, all_results: dict):
        """生成业务需求符合性报告"""
        print("\n📊 业务需求符合性分析报告")
        print("=" * 80)
        
        # 统计结果
        total_tests = 0
        passed_tests = 0
        failed_tests = 0
        pending_tests = 0
        
        for category, tests in all_results.items():
            print(f"\n📋 {category}:")
            for test_name, result in tests.items():
                total_tests += 1
                if result is True:
                    status = "✅ 通过"
                    passed_tests += 1
                elif result is False:
                    status = "❌ 失败"
                    failed_tests += 1
                else:
                    status = "⚠️  待验证"
                    pending_tests += 1
                print(f"   {test_name:<25}: {status}")
        
        print("\n" + "=" * 80)
        print(f"📈 测试统计:")
        print(f"   总测试项: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   待验证: {pending_tests}")
        
        if total_tests > 0:
            pass_rate = (passed_tests / (total_tests - pending_tests)) * 100 if (total_tests - pending_tests) > 0 else 0
            print(f"   通过率: {pass_rate:.1f}%")
        
        # 业务需求匹配度分析
        print(f"\n🎯 业务需求匹配度分析:")
        print("=" * 80)
        
        if passed_tests >= (total_tests - pending_tests) * 0.8:
            print("🎉 系统基本满足业务需求！")
            print("✅ 核心功能完整")
            print("✅ 会员卡类型齐全")
            print("✅ 基础管理功能正常")
        else:
            print("⚠️  系统部分满足业务需求")
            print("❌ 部分核心功能需要完善")
        
        if pending_tests > 0:
            print(f"\n📝 需要进一步验证的功能 ({pending_tests}项):")
            print("   建议进行详细的API接口测试和功能验证")
    
    def run_comprehensive_test(self):
        """运行全面的业务需求测试"""
        print("🚀 开始会员卡管理系统业务需求全面验证...")
        print("=" * 80)
        
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        all_results = {}
        
        # 执行各项测试
        all_results['会员卡种类管理'] = self.test_card_type_management()
        all_results['会员卡大类管理'] = self.test_card_categories()
        all_results['开卡功能'] = self.test_card_opening_modes()
        all_results['主副卡功能'] = self.test_sub_card_functionality()
        all_results['单日限制功能'] = self.test_daily_limit_functionality()
        all_results['高级功能'] = self.test_advanced_features()
        
        # 生成报告
        self.generate_business_compliance_report(all_results)

if __name__ == "__main__":
    tester = BusinessRequirementTester()
    tester.run_comprehensive_test()
