#!/bin/bash

# Yogaga Docker 镜像构建脚本
# 使用方法: ./deploy/build.sh [tag]

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否在项目根目录
if [ ! -f "go.mod" ] || [ ! -f "main.go" ]; then
    print_error "请在项目根目录运行此脚本"
    exit 1
fi

# 获取项目名称
if [ -d ".git" ]; then
    APP_NAME=$(basename -s .git `git config --get remote.origin.url` 2>/dev/null || echo "yogaga")
else
    APP_NAME="yogaga"
fi

# 获取标签，默认为 latest
TAG=${1:-latest}
IMAGE_NAME="${APP_NAME}:${TAG}"

print_info "开始构建 Docker 镜像: ${IMAGE_NAME}"

# 获取构建信息
if [ -d ".git" ]; then
    COMMIT_HASH=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        COMMIT_HASH="${COMMIT_HASH}-dirty"
        print_warn "检测到未提交的更改，构建版本将标记为 dirty"
    fi
else
    COMMIT_HASH="unknown"
fi

BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
PLATFORM=$(go version | awk '{print $4}' 2>/dev/null || echo "unknown")

print_info "构建信息:"
print_info "  应用名称: ${APP_NAME}"
print_info "  镜像标签: ${TAG}"
print_info "  提交哈希: ${COMMIT_HASH}"
print_info "  构建时间: ${BUILD_TIME}"
print_info "  平台信息: ${PLATFORM}"

# 检查 Dockerfile 是否存在
if [ ! -f "deploy/Dockerfile" ]; then
    print_error "Dockerfile 不存在: deploy/Dockerfile"
    exit 1
fi

# 检查必要的配置文件
if [ ! -f "configs/config.example.yaml" ]; then
    print_error "配置文件不存在: configs/config.example.yaml"
    exit 1
fi

if [ ! -f "configs/rbac_model.conf" ]; then
    print_error "Casbin 配置文件不存在: configs/rbac_model.conf"
    exit 1
fi

# 构建 Docker 镜像
print_info "开始构建镜像..."

docker build \
    --build-arg APP_NAME="${APP_NAME}" \
    --build-arg COMMIT_HASH="${COMMIT_HASH}" \
    --build-arg BUILD_TIME="${BUILD_TIME}" \
    --build-arg PLATFORM="${PLATFORM}" \
    --build-arg CONFIG_FILE="config.example.yaml" \
    --build-arg APP_PORT=9095 \
    -t "${IMAGE_NAME}" \
    -f deploy/Dockerfile .

if [ $? -eq 0 ]; then
    print_info "镜像构建成功: ${IMAGE_NAME}"
    
    # 显示镜像信息
    print_info "镜像信息:"
    docker images "${APP_NAME}" --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}\t{{.Size}}"
    
    print_info "运行建议:"
    echo "  1. 简单运行:"
    echo "     docker run -d -p 9095:9095 -v \$(pwd)/config:/etc/${APP_NAME} --name ${APP_NAME}_app ${IMAGE_NAME}"
    echo ""
    echo "  2. 使用 docker-compose:"
    echo "     cp deploy/docker-compose.simple.yml docker-compose.yml"
    echo "     docker-compose up -d"
    echo ""
    echo "  3. 查看日志:"
    echo "     docker logs ${APP_NAME}_app"
    
else
    print_error "镜像构建失败"
    exit 1
fi
