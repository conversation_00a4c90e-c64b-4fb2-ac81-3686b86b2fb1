# Admin API Key Interfaces Test Script

param(
    [string]$BaseURL = "http://127.0.0.1:9095"
)

$ErrorActionPreference = "Continue"

Write-Host "Admin API Key Interfaces Test" -ForegroundColor Cyan
Write-Host "Base URL: $BaseURL" -ForegroundColor Gray
Write-Host "=" * 50

# Test admin login
Write-Host "`nTesting admin login..." -ForegroundColor Yellow

$loginData = @{
    username = "admin"
    password = "123456"
} | ConvertTo-Json

try {
    $loginResponse = Invoke-RestMethod -Uri "$BaseURL/api/v1/public/admin/login" -Method POST -Body $loginData -ContentType "application/json"
    
    if ($loginResponse.code -eq 0) {
        Write-Host "  PASS: Admin login successful" -ForegroundColor Green
        $AccessToken = $loginResponse.data.access_token
    } else {
        Write-Host "  FAIL: Admin login failed - $($loginResponse.message)" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "  FAIL: Admin login error - $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test authenticated endpoints
$authHeaders = @{ Authorization = "Bearer $AccessToken" }

Write-Host "`nTesting authenticated endpoints..." -ForegroundColor Yellow

# Test dashboard
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/dashboard" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Dashboard data retrieved" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Dashboard failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Dashboard error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test users list
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/users?page=1&page_size=5" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Users list retrieved (Total: $($response.data.total))" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Users list failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Users list error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test membership cards
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/membership-cards?page=1&page_size=5" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Membership cards retrieved (Total: $($response.data.total))" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Membership cards failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Membership cards error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test membership types
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/membership-types?page=1&page_size=5" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Membership types retrieved (Total: $($response.data.total))" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Membership types failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Membership types error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test stores
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/stores?page=1&page_size=5" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Stores retrieved (Total: $($response.data.total))" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Stores failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Stores error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test courses
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/courses?page=1&page_size=5" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Courses retrieved (Total: $($response.data.total))" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Courses failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Courses error - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTesting newly added endpoints..." -ForegroundColor Yellow

# Test store classrooms (new endpoint)
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/stores/1/classrooms" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Store classrooms retrieved" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Store classrooms failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Store classrooms error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test course supported card types (new endpoint)
try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/courses/1/supported-card-types" -Method GET -Headers $authHeaders
    if ($response.code -eq 0) {
        Write-Host "  PASS: Course supported card types retrieved" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Course supported card types failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Course supported card types error - $($_.Exception.Message)" -ForegroundColor Red
}

# Test batch course preview (new endpoint)
$batchData = @{
    start_date = "2025-08-01"
    end_date = "2025-08-07"
    start_time = "09:00"
    weekly_schedules = @(
        @{
            weekday = 1
            course_name = "Test Yoga Course"
            description = "Test yoga course description"
            coach_ids = @("test-coach-id")
            duration = 90
        }
    )
    global_description = "Test batch course creation"
    capacity = 20
    price = 10800
    level = 1
    category_id = 1
    store_id = 1
    type = 1
} | ConvertTo-Json -Depth 3

try {
    $response = Invoke-RestMethod -Uri "$BaseURL/api/v1/admin/batch-courses/preview" -Method POST -Headers $authHeaders -Body $batchData -ContentType "application/json"
    if ($response.code -eq 0) {
        Write-Host "  PASS: Batch course preview successful" -ForegroundColor Green
    } else {
        Write-Host "  FAIL: Batch course preview failed - $($response.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "  FAIL: Batch course preview error - $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Cyan
