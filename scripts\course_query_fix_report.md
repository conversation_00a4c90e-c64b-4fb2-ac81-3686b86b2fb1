# Course查询错误修复报告

## 🐛 问题描述

错误信息：`Coach: unsupported relations for schema Course`

**根本原因**：Course模型使用了 `CoachIDs pq.StringArray` 存储多教练ID，但在查询时仍然使用了 `Preload("Coach")`，而Course模型中实际上没有定义Coach关联关系。

## 🔍 问题分析

### 模型设计
```go
// Course 模型中的教练字段设计
type Course struct {
    // ...其他字段
    CoachIDs pq.StringArray `json:"coach_ids" gorm:"type:text[];comment:教练ID数组，支持多教练"`
    // 注意：没有 Coach *User 这样的关联定义
}
```

### 错误的查询方式
```go
// ❌ 错误：尝试预加载不存在的Coach关联
query := h.db.Model(&model.Course{}).Preload("Coach").Preload("Store")

// ❌ 错误：使用单教练字段查询
query = query.Where("coach_id = ?", req.CoachID)
```

## ✅ 修复方案

### 1. 移除错误的Preload("Coach")

**修复文件**：
- `internal/handler/course_handler.go`
- `internal/handler/share_handler.go` 
- `internal/handler/app_course_handler.go`
- `internal/handler/favorite_handler.go`

**修复前**：
```go
query := h.db.Model(&model.Course{}).Preload("Coach").Preload("Store")
```

**修复后**：
```go
query := h.db.Model(&model.Course{}).Preload("Store").Preload("ClassRoom").Preload("CategoryRef")
```

### 2. 修复教练筛选查询

**修复前**：
```go
// 错误：使用不存在的coach_id字段
query = query.Where("coach_id = ?", req.CoachID)
```

**修复后**：
```go
// 正确：使用PostgreSQL数组查询
query = query.Where("? = ANY(coach_ids)", req.CoachID)
```

### 3. 为Course模型添加辅助方法

在 `internal/model/course.go` 中添加了以下方法：

```go
// GetAllCoachIDs 获取所有教练ID
func (c *Course) GetAllCoachIDs() []string {
    if c == nil {
        return []string{}
    }
    return []string(c.CoachIDs)
}

// GetPrimaryCoachID 获取主教练ID（第一个教练）
func (c *Course) GetPrimaryCoachID() string {
    if c == nil || len(c.CoachIDs) == 0 {
        return ""
    }
    return c.CoachIDs[0]
}

// HasCoach 检查是否包含指定教练
func (c *Course) HasCoach(coachID string) bool {
    if c == nil {
        return false
    }
    for _, id := range c.CoachIDs {
        if id == coachID {
            return true
        }
    }
    return false
}
```

## 📋 修复详情

### 修复的文件和位置

#### 1. internal/handler/course_handler.go
- **第40-41行**：移除 `Preload("Coach")`
- **第43-46行**：修复教练筛选查询，使用 `? = ANY(coach_ids)`
- **第142-143行**：移除课程详情查询中的 `Preload("Coach")`

#### 2. internal/handler/share_handler.go
- **第47-49行**：移除分享功能中的 `Preload("Coach")`

#### 3. internal/handler/app_course_handler.go
- **第53-56行**：修复小程序端教练筛选查询

#### 4. internal/handler/favorite_handler.go
- **第261-266行**：移除收藏功能中的 `Preload("Coach")`

#### 5. internal/model/course.go
- **第35-65行**：添加教练相关的辅助方法

### 保持不变的文件

#### internal/handler/course_review_handler.go
- **保持不变**：因为这里使用的是 `Preload("Schedule.Coach")`，而Schedule模型中有正确的Coach关联定义

## 🎯 修复效果

### 修复前的错误
```
2025-07-29T16:43:57+08:00 ERRO 🌏 starter: 查询课程失败 error="Coach: unsupported relations for schema Course"
```

### 修复后的正确行为
- ✅ 课程列表查询正常
- ✅ 课程详情查询正常
- ✅ 教练筛选功能正常
- ✅ 分享功能正常
- ✅ 收藏功能正常

## 🔧 PostgreSQL数组查询语法

### 检查数组中是否包含某个值
```sql
-- 检查coach_ids数组中是否包含指定教练ID
SELECT * FROM courses WHERE 'coach-uuid-123' = ANY(coach_ids);
```

### GORM中的使用方式
```go
// 查询包含指定教练的课程
query = query.Where("? = ANY(coach_ids)", coachID)

// 查询包含多个教练中任意一个的课程
query = query.Where("coach_ids && ?", pq.Array([]string{coachID1, coachID2}))
```

## 💡 设计说明

### 为什么使用数组而不是关联表？

**优势**：
- ✅ 简化数据结构，避免额外的关联表
- ✅ 查询性能更好，减少JOIN操作
- ✅ PostgreSQL原生支持数组操作
- ✅ 适合教练数量不多的场景

**适用场景**：
- 每个课程的教练数量有限（通常1-3个）
- 教练信息相对稳定
- 查询以课程为主，教练为辅

### 如何获取教练详细信息？

由于Course模型不直接关联User，需要手动查询：

```go
// 获取课程的主教练信息
func getCoachInfo(db *gorm.DB, course *model.Course) *model.User {
    if course == nil || len(course.CoachIDs) == 0 {
        return nil
    }
    
    var coach model.User
    err := db.Where("id = ? AND is_coach = ? AND is_active = ?", 
        course.CoachIDs[0], true, true).First(&coach).Error
    if err != nil {
        return nil
    }
    
    return &coach
}
```

## 🚀 验证修复

### 测试步骤
1. 重启应用
2. 访问课程列表接口：`GET /api/v1/admin/courses`
3. 访问课程详情接口：`GET /api/v1/admin/courses/{id}`
4. 测试教练筛选：`GET /api/v1/admin/courses?coach_id=xxx`

### 预期结果
- 不再出现 "Coach: unsupported relations" 错误
- 课程查询功能正常
- 教练筛选功能正常

## 📝 后续优化建议

1. **统一教练信息获取**：创建统一的教练信息获取服务
2. **缓存优化**：对频繁查询的教练信息进行缓存
3. **数据一致性**：定期检查CoachIDs中的教练是否仍然有效
4. **查询优化**：考虑为coach_ids字段添加GIN索引以提升查询性能

修复完成！🎉
