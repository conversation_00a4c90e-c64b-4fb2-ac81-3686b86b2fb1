package service

import (
	"strings"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// RoleService 角色服务（纯数据库方案）
type RoleService struct {
	db *gorm.DB
}

// NewRoleService 创建角色服务实例
func NewRoleService(db *gorm.DB) *RoleService {
	return &RoleService{
		db: db,
	}
}

// IsUserInRole 检查用户是否具有指定角色（支持模糊匹配）
func (s *RoleService) IsUserInRole(userRoles []model.Role, targetRole string) bool {
	for _, role := range userRoles {
		// 精确匹配
		if role.Name == targetRole {
			return true
		}

		// 模糊匹配（兼容中文角色名）
		if s.isRoleMatch(role.Name, targetRole) {
			return true
		}
	}

	return false
}

// isRoleMatch 角色匹配检查（支持中文和英文）
func (s *RoleService) isRoleMatch(roleName, targetRole string) bool {
	roleLower := strings.ToLower(roleName)
	targetLower := strings.ToLower(targetRole)

	// 检查常见角色的中英文匹配
	switch targetRole {
	case "admin":
		return strings.Contains(roleLower, "admin") || strings.Contains(roleName, "管理员")
	case "manager":
		return strings.Contains(roleLower, "manager") || strings.Contains(roleName, "店长")
	case "sales":
		return strings.Contains(roleLower, "sales") || strings.Contains(roleName, "销售")
	case "coach":
		return strings.Contains(roleLower, "coach") || strings.Contains(roleName, "教练")
	case "receptionist":
		return strings.Contains(roleLower, "receptionist") || strings.Contains(roleName, "前台")
	case "supervisor":
		return strings.Contains(roleLower, "supervisor") || strings.Contains(roleName, "总监")
	case "user":
		return strings.Contains(roleLower, "user") || strings.Contains(roleName, "用户") || strings.Contains(roleName, "会员")
	}

	// 部分匹配
	return strings.Contains(roleLower, targetLower)
}

// IsAdmin 检查用户是否为管理员
func (s *RoleService) IsAdmin(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "admin")
}

// IsManager 检查用户是否为店长
func (s *RoleService) IsManager(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "manager")
}

// IsSales 检查用户是否为销售
func (s *RoleService) IsSales(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "sales")
}

// IsCoach 检查用户是否为教练
func (s *RoleService) IsCoach(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "coach")
}

// IsReceptionist 检查用户是否为前台
func (s *RoleService) IsReceptionist(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "receptionist")
}

// IsSupervisor 检查用户是否为总监
func (s *RoleService) IsSupervisor(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "supervisor")
}

// IsUser 检查用户是否为普通用户
func (s *RoleService) IsUser(userRoles []model.Role) bool {
	return s.IsUserInRole(userRoles, "user")
}

// CreateRole 创建角色
func (s *RoleService) CreateRole(name, description string) (*model.Role, error) {
	// 检查是否已存在
	var existingRole model.Role
	err := s.db.Where("name = ?", name).First(&existingRole).Error
	if err == nil {
		return nil, gorm.ErrDuplicatedKey
	}

	// 创建新角色
	role := model.Role{
		Name:        name,
		Description: description,
	}

	err = s.db.Create(&role).Error
	if err != nil {
		return nil, err
	}

	return &role, nil
}

// DeleteRole 删除角色
func (s *RoleService) DeleteRole(roleID uint) error {
	// 查询角色
	var role model.Role
	err := s.db.First(&role, roleID).Error
	if err != nil {
		return err
	}

	// 检查是否有用户使用该角色
	var userCount int64
	s.db.Model(&model.User{}).
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Where("user_roles.role_id = ?", roleID).
		Count(&userCount)

	if userCount > 0 {
		return gorm.ErrInvalidData // 有用户使用该角色，不能删除
	}

	// 删除角色
	return s.db.Delete(&role).Error
}

// GetUserHighestRole 获取用户的最高权限角色
func (s *RoleService) GetUserHighestRole(userRoles []model.Role) string {
	// 按权限级别排序检查（从高到低）
	roleHierarchy := []string{
		"admin",        // 超级管理员
		"supervisor",   // 总监
		"manager",      // 店长
		"coach",        // 教练
		"sales",        // 销售
		"receptionist", // 前台
		"user",         // 普通用户
	}

	for _, targetRole := range roleHierarchy {
		if s.IsUserInRole(userRoles, targetRole) {
			return targetRole
		}
	}

	// 如果没有匹配的角色，返回第一个角色
	if len(userRoles) > 0 {
		return userRoles[0].Name
	}

	return "user"
}
