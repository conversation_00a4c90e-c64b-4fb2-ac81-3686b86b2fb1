// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblMembersTemp = "tbl_members_temp"

// TblMembersTemp mapped from table <tbl_members_temp>
type TblMembersTemp struct {
	ID     int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID int32     `gorm:"column:shop_id;not null" json:"shop_id"`
	SaleID int32     `gorm:"column:sale_id;not null" json:"sale_id"`
	Name   string    `gorm:"column:name" json:"name"`
	Sex    string    `gorm:"column:sex" json:"sex"`
	Mobile string    `gorm:"column:mobile" json:"mobile"`
	Birth  time.Time `gorm:"column:birth" json:"birth"`
	Note   string    `gorm:"column:note" json:"note"`
	Istrue bool      `gorm:"column:istrue;not null;default:1" json:"istrue"`
	Reason string    `gorm:"column:reason" json:"reason"`
}

// TableName TblMembersTemp's table name
func (*TblMembersTemp) TableName() string {
	return TableNameTblMembersTemp
}
