package model

import "yogaga/internal/enum"

// Banner 轮播图模型
type Banner struct {
	BaseModel
	Title       string              `json:"title" gorm:"type:varchar(100);not null;comment:轮播图标题"`
	FileID      string              `json:"file_id" gorm:"type:char(36);not null;comment:轮播图文件ID，关联files表"`
	LinkURL     string              `json:"link_url" gorm:"type:varchar(500);comment:点击跳转链接，可以是小程序页面路径或外部URL"`
	Description string              `json:"description" gorm:"type:text;comment:轮播图描述"`
	SortOrder   int                 `json:"sort_order" gorm:"default:0;comment:排序"`
	Status      enum.BannerStatus   `json:"status" gorm:"default:1;comment:状态 0:禁用 1:启用"`
	Position    enum.BannerPosition `json:"position" gorm:"type:varchar(20);default:'home';comment:展示位置"`

	// 关联
	File *File `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
}

// CoachBanner 教练轮播图模型
type CoachBanner struct {
	BaseModel
	CoachID   string            `json:"coach_id" gorm:"type:char(36);not null;comment:教练ID"`
	FileID    string            `json:"file_id" gorm:"type:char(36);not null;comment:轮播图文件ID，关联files表"`
	Title     string            `json:"title" gorm:"type:varchar(100);comment:图片标题"`
	SortOrder int               `json:"sort_order" gorm:"default:0;comment:排序"`
	Status    enum.BannerStatus `json:"status" gorm:"default:1;comment:状态 0:禁用 1:启用"`

	// 关联
	Coach *User `json:"coach,omitempty" gorm:"foreignKey:CoachID"`
	File  *File `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
}

// StoreBanner 门店轮播图模型
type StoreBanner struct {
	BaseModel
	StoreID   uint              `json:"store_id" gorm:"not null;comment:门店ID"`
	FileID    string            `json:"file_id" gorm:"type:char(36);not null;comment:轮播图文件ID，关联files表"`
	Title     string            `json:"title" gorm:"type:varchar(100);comment:图片标题"`
	SortOrder int               `json:"sort_order" gorm:"default:0;comment:排序"`
	Status    enum.BannerStatus `json:"status" gorm:"default:1;comment:状态 0:禁用 1:启用"`

	// 关联
	Store *Store `json:"store,omitempty" gorm:"foreignKey:StoreID"`
	File  *File  `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
}
