version: '3.8'

services:
  # 主应用服务
  app:
    image: yogaga:latest
    container_name: yogaga_app
    restart: always
    ports:
      - "9095:9095"
    volumes:
      # 挂载配置文件目录
      - ./config:/etc/yogaga:ro
      # 挂载日志目录（可选）
      - ./logs:/app/logs
    environment:
      # 可以通过环境变量覆盖配置
      - APP_ENV=prod
    networks:
      - yogaga-net
    depends_on:
      - mysql
      - redis
      - minio
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9095/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: yogaga_mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: root_password_change_me
      MYSQL_DATABASE: yogaga
      MYSQL_USER: yogaga
      MYSQL_PASSWORD: password_change_me
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    networks:
      - yogaga-net
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: yogaga_redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - yogaga-net
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 3s
      retries: 3

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    container_name: yogaga_minio
    restart: always
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin_change_me
    volumes:
      - minio_data:/data
    networks:
      - yogaga-net
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  yogaga-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
