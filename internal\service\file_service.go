package service

import (
	"context"
	"fmt"
	"mime/multipart"
	"net/url"
	"path/filepath"
	"time"

	"yogaga/internal/model"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type FileService struct {
	db      *gorm.DB
	storage storage.Storage
}

func NewFileService(db *gorm.DB, storage storage.Storage) *FileService {
	return &FileService{
		db:      db,
		storage: storage,
	}
}

// CreateFileRecordRequest 创建文件记录请求
type CreateFileRecordRequest struct {
	ObjectName       string
	BucketName       string
	OriginalFilename string
	FileName         string
	FileSize         int64
	ContentType      string
	Purpose          string
	UploaderID       string
}

// CreateFileRecord 在数据库中创建文件记录
func (s *FileService) CreateFileRecord(req CreateFileRecordRequest) (*model.File, error) {
	file := &model.File{
		UUIDModel:        &model.UUIDModel{}, // UUID 会在 BeforeCreate 钩子中自动生成
		ObjectName:       req.ObjectName,
		BucketName:       req.BucketName,
		OriginalFilename: req.OriginalFilename,
		FileName:         req.FileName,
		FileSize:         req.FileSize,
		ContentType:      req.ContentType,
		Purpose:          req.Purpose,
		UploaderID:       req.UploaderID,
		Status:           model.StatusActive,
	}

	if err := s.db.Create(file).Error; err != nil {
		log.Error("创建文件记录失败", "error", err)
		return nil, err
	}

	return file, nil
}

// UploadFile 上传文件并保存记录（简化版本）
func (s *FileService) UploadFile(
	uploaderID string,
	fileHeader *multipart.FileHeader,
	bucketName string,
) (*model.File, error) {
	// 打开文件
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 生成唯一对象名称
	objectName := s.generateObjectNameWithPurpose(fileHeader.Filename, "general")

	// 上传文件到MinIO
	_, err = s.storage.UploadToSpecificBucket(bucketName, objectName, file)
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %w", err)
	}

	// 创建文件记录
	fileRecord, err := s.CreateFileRecord(CreateFileRecordRequest{
		ObjectName:       objectName,
		BucketName:       bucketName,
		OriginalFilename: fileHeader.Filename,
		FileName:         fileHeader.Filename,
		FileSize:         fileHeader.Size,
		ContentType:      fileHeader.Header.Get("Content-Type"),
		Purpose:          "general", // 默认用途
		UploaderID:       uploaderID,
	})
	if err != nil {
		// 如果数据库保存失败，尝试删除已上传的文件
		if deleteErr := s.storage.Delete(objectName); deleteErr != nil {
			log.Error("删除上传失败的文件时出错", "object_name", objectName, "error", deleteErr)
		}
		return nil, fmt.Errorf("保存文件记录失败: %w", err)
	}

	return fileRecord, nil
}

// ProxyUploadWithPurpose 代理上传文件并保存记录（带用途）
func (s *FileService) ProxyUploadWithPurpose(
	uploaderID string,
	fileHeader *multipart.FileHeader,
	purpose string,
) (*model.File, error) {
	// 打开文件
	file, err := fileHeader.Open()
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	// 生成唯一对象名称（包含用途前缀）
	objectName := s.generateObjectNameWithPurpose(fileHeader.Filename, purpose)

	// 上传文件到MinIO（使用默认存储桶）
	_, err = s.storage.Upload(objectName, file)
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %w", err)
	}

	// 创建文件记录
	fileRecord, err := s.CreateFileRecord(CreateFileRecordRequest{
		ObjectName:       objectName,
		BucketName:       "default", // 使用默认存储桶
		OriginalFilename: fileHeader.Filename,
		FileName:         fileHeader.Filename,
		FileSize:         fileHeader.Size,
		ContentType:      fileHeader.Header.Get("Content-Type"),
		Purpose:          purpose,
		UploaderID:       uploaderID,
	})
	if err != nil {
		// 如果数据库保存失败，尝试删除已上传的文件
		if deleteErr := s.storage.Delete(objectName); deleteErr != nil {
			log.Error("删除上传失败的文件时出错", "object_name", objectName, "error", deleteErr)
		}
		return nil, fmt.Errorf("保存文件记录失败: %w", err)
	}

	return fileRecord, nil
}

// generateObjectNameWithPurpose 生成带用途的唯一对象名称
func (s *FileService) generateObjectNameWithPurpose(filename, purpose string) string {
	// 获取文件扩展名
	ext := filepath.Ext(filename)
	if ext == "" {
		ext = ".bin"
	}

	// 生成唯一文件名
	uniqueID := uuid.New().String()

	// 构造路径: 用途/年/月/日/唯一ID.扩展名
	now := time.Now()
	datePath := now.Format("2006/01/02")

	return fmt.Sprintf("%s/%s/%s%s", purpose, datePath, uniqueID, ext)
}

// GetFileURL 获取文件的预签名URL
func (s *FileService) GetFileURL(fileID string) (string, error) {
	// 查询文件记录
	file, err := s.GetFileByID(fileID)
	if err != nil {
		return "", fmt.Errorf("获取文件记录失败: %w", err)
	}

	// 生成预签名URL（1小时有效期）
	presignedURL, err := s.storage.GetPresignedDownloadURL(context.Background(), file.ObjectName, time.Hour, url.Values{})
	if err != nil {
		return "", fmt.Errorf("生成预签名URL失败: %w", err)
	}

	return presignedURL.String(), nil
}

// GetFileByID 根据ID获取文件
func (s *FileService) GetFileByID(id string) (*model.File, error) {
	var file model.File
	if err := s.db.Where("id = ?", id).First(&file).Error; err != nil {
		return nil, err
	}
	return &file, nil
}

// GetFilesByEntity 根据实体获取文件列表
func (s *FileService) GetFilesByEntity(entityType string, entityID uint) ([]model.File, error) {
	var files []model.File
	if err := s.db.Where("entity_type = ? AND entity_id = ? AND status = ?",
		entityType, entityID, model.StatusActive).Find(&files).Error; err != nil {
		return nil, err
	}
	return files, nil
}

// DeleteFile 删除文件（软删除）
func (s *FileService) DeleteFile(id string) error {
	file, err := s.GetFileByID(id)
	if err != nil {
		return err
	}

	// 更新状态为已删除
	if err := s.db.Model(file).Update("status", model.StatusArchived).Error; err != nil {
		return err
	}

	return nil
}

// DeleteFilePhysically 物理删除文件
func (s *FileService) DeleteFilePhysically(id string) error {
	file, err := s.GetFileByID(id)
	if err != nil {
		return err
	}

	// 从存储中删除文件
	if err := s.storage.Delete(file.ObjectName); err != nil {
		log.Error("从存储中删除文件失败", "object_name", file.ObjectName, "error", err)
		// 继续执行数据库删除，即使存储删除失败
	}

	// 从数据库中删除记录
	if err := s.db.Delete(file).Error; err != nil {
		return err
	}

	return nil
}

// AssociateFileWithEntity 关联文件到实体
func (s *FileService) AssociateFileWithEntity(fileID string, entityType string, entityID uint) error {
	return s.db.Model(&model.File{}).Where("id = ?", fileID).Updates(map[string]interface{}{
		"entity_type": entityType,
		"entity_id":   entityID,
	}).Error
}

// GetFileList 获取文件列表（分页）
func (s *FileService) GetFileList(page, pageSize int, entityType string) ([]model.File, int64, error) {
	var files []model.File
	var total int64

	query := s.db.Model(&model.File{}).Where("status = ?", model.StatusActive)

	if entityType != "" {
		query = query.Where("entity_type = ?", entityType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, err
	}

	return files, total, nil
}
