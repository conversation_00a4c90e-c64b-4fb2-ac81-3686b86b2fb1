# 🏗️ 当前系统架构详解：关联关系存储在哪里？

## 🤔 **您的困惑是核心问题**

您问得很对！这确实是权限系统设计中最容易混淆的地方：
- **关联关系到底存在哪里？**
- **数据库和 Casbin 各自的职责是什么？**
- **为什么需要两套存储？**

## 📊 **我们当前的实际架构**

### 🔍 **关联关系存储位置**

```mermaid
graph TB
    subgraph "数据库层 (MySQL)"
        U[users 表] 
        R[roles 表]
        P[permissions 表]
        M[menus 表]
        UR[user_roles 关联表]
        RP[role_permissions 关联表]
        
        U -.-> UR
        R -.-> UR
        R -.-> RP
        P -.-> RP
        M -.-> P
    end
    
    subgraph "Casbin 层 (casbin_rule 表)"
        CR[casbin_rule 策略表]
    end
    
    subgraph "同步机制"
        SYNC[实时同步]
    end
    
    UR --> SYNC
    RP --> SYNC
    SYNC --> CR
    
    style UR fill:#ff9999
    style RP fill:#ff9999
    style CR fill:#99ccff
```

### 📋 **关键答案：关联关系存储在两个地方**

| 关联类型 | 数据库存储 | Casbin 存储 | 用途 |
|----------|------------|-------------|------|
| **用户-角色** | `user_roles` 表 | `casbin_rule` 表 (g 类型) | 管理 + 权限检查 |
| **角色-权限** | `role_permissions` 表 | `casbin_rule` 表 (p 类型) | 管理 + 权限检查 |
| **菜单-权限** | `menus.permission_key` 字段 | 无直接存储 | 菜单过滤 |

## 🔄 **数据流转：从数据库到 Casbin**

### 1. **用户-角色关联流程**

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant API as API接口
    participant DB as 数据库
    participant Casbin as Casbin

    Admin->>API: 分配角色给用户
    API->>DB: INSERT INTO user_roles
    API->>Casbin: AddRoleForUser(user, role)
    Casbin->>DB: INSERT INTO casbin_rule (g类型)
    API-->>Admin: 操作成功
```

**实际数据示例**：
```sql
-- 数据库中的关联
INSERT INTO user_roles (user_id, role_id) VALUES (1, 4);

-- Casbin 中的策略
INSERT INTO casbin_rule (ptype, v0, v1) VALUES ('g', 'admin', 'admin');
```

### 2. **角色-权限关联流程**

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant API as API接口
    participant DB as 数据库
    participant Casbin as Casbin

    Admin->>API: 分配权限给角色
    API->>DB: INSERT INTO role_permissions
    API->>Casbin: AddPolicy(role, permission, method)
    Casbin->>DB: INSERT INTO casbin_rule (p类型)
    API-->>Admin: 操作成功
```

**实际数据示例**：
```sql
-- 数据库中的关联
INSERT INTO role_permissions (role_id, permission_id) VALUES (4, 1);

-- Casbin 中的策略 (每个权限生成4条策略)
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES 
('p', 'admin', 'role:create', 'GET'),
('p', 'admin', 'role:create', 'POST'),
('p', 'admin', 'role:create', 'PUT'),
('p', 'admin', 'role:create', 'DELETE');
```

## 🎯 **为什么需要两套存储？**

### 📊 **数据库层的职责**

```go
// 数据库存储：丰富的元数据和管理功能
type User struct {
    ID       uint   `json:"id"`
    Username string `json:"username"`
    Platform string `json:"platform"`
    IsActive bool   `json:"is_active"`
    Roles    []Role `gorm:"many2many:user_roles;"` // 关联关系
}

type Role struct {
    ID          uint         `json:"id"`
    Name        string       `json:"name"`
    Description string       `json:"description"`
    Permissions []Permission `gorm:"many2many:role_permissions;"` // 关联关系
}
```

**用途**：
- ✅ **权限管理界面**：显示用户有哪些角色、角色有哪些权限
- ✅ **数据完整性**：外键约束、级联删除
- ✅ **审计日志**：记录权限变更历史
- ✅ **复杂查询**：统计分析、报表生成

### ⚡ **Casbin 层的职责**

```sql
-- Casbin 存储：高性能的策略执行
CREATE TABLE casbin_rule (
    ptype VARCHAR(100),  -- 'p' = 权限策略, 'g' = 角色继承
    v0    VARCHAR(100),  -- 角色名
    v1    VARCHAR(100),  -- 权限标识 或 用户名
    v2    VARCHAR(100),  -- HTTP方法 或 角色名
);

-- 实际数据
| ptype | v0    | v1          | v2     |
|-------|-------|-------------|--------|
| p     | admin | role:create | POST   |  -- 权限策略
| g     | admin | admin       |        |  -- 用户角色
```

**用途**：
- ⚡ **高性能权限检查**：微秒级响应
- 🔧 **复杂策略支持**：RBAC、ABAC、ACL 等
- 🚀 **内存执行**：策略加载到内存中执行

## 🔍 **实际权限检查流程**

### 当用户访问 API 时发生了什么？

```go
// 1. JWT 中间件提取用户角色
func JWTAuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        // 从 JWT 中提取用户名
        username := claims.Username
        
        // 从数据库查询用户角色 (这里用数据库)
        var user model.User
        db.Preload("Roles").Where("username = ?", username).First(&user)
        
        // 将角色名存储到上下文
        roleNames := make([]string, len(user.Roles))
        for i, role := range user.Roles {
            roleNames[i] = role.Name
        }
        c.Set("roles", roleNames)
    }
}

// 2. 权限检查中间件使用 Casbin
func CheckPermissionByKey(key string, enforcer *casbin.Enforcer) gin.HandlerFunc {
    return func(c *gin.Context) {
        roles := c.Get("roles").([]string)
        method := c.Request.Method
        
        // 使用 Casbin 检查权限 (这里用 Casbin)
        for _, role := range roles {
            allowed, _ := enforcer.Enforce(role, key, method)
            if allowed {
                c.Next() // 权限通过
                return
            }
        }
        
        // 权限不足
        c.JSON(403, gin.H{"error": "权限不足"})
        c.Abort()
    }
}
```

## 🔗 **菜单权限关联的特殊设计**

### 菜单与权限的关系

```go
type Menu struct {
    BaseModel
    Title         string `json:"title"`
    Path          string `json:"path"`
    Type          MenuType `json:"type"`
    
    // 关键：菜单通过 permission_key 关联权限
    PermissionKey string      `json:"permission"`
    Permission    *Permission `gorm:"foreignKey:PermissionKey;references:Key"`
}
```

**菜单访问逻辑**：
```go
// 获取用户可访问的菜单
func GetUserMenus(username string, enforcer *casbin.Enforcer) []Menu {
    var menus []Menu
    db.Find(&menus)
    
    var accessibleMenus []Menu
    for _, menu := range menus {
        if menu.PermissionKey == "" {
            // 无需权限的菜单，直接可访问
            accessibleMenus = append(accessibleMenus, menu)
        } else {
            // 需要权限的菜单，通过 Casbin 检查
            roles := getUserRoles(username)
            for _, role := range roles {
                allowed, _ := enforcer.Enforce(role, menu.PermissionKey, "GET")
                if allowed {
                    accessibleMenus = append(accessibleMenus, menu)
                    break
                }
            }
        }
    }
    return accessibleMenus
}
```

## 🎯 **最佳实践总结**

### ✅ **我们当前的做法是最佳实践**

1. **数据库存储关联关系**：
   - `user_roles` 表：用户-角色关联
   - `role_permissions` 表：角色-权限关联
   - `menus.permission_key` 字段：菜单-权限关联

2. **Casbin 存储执行策略**：
   - 从数据库关联自动生成策略
   - 高性能的权限检查执行

3. **实时同步机制**：
   - 数据库变更时自动同步到 Casbin
   - 确保两层数据一致性

### 🚫 **不推荐的做法**

#### ❌ **只用数据库**
```go
// 性能差的权限检查
func CheckPermission(userID uint, permissionKey string) bool {
    var count int64
    db.Table("users").
        Joins("JOIN user_roles ON users.id = user_roles.user_id").
        Joins("JOIN roles ON user_roles.role_id = roles.id").
        Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
        Joins("JOIN permissions ON role_permissions.permission_id = permissions.id").
        Where("users.id = ? AND permissions.key = ?", userID, permissionKey).
        Count(&count)
    return count > 0
}
```

#### ❌ **只用 Casbin**
```go
// 缺少管理功能
// 无法知道权限的中文名称、描述
// 无法进行复杂的权限管理操作
```

## 📊 **当前系统数据示例**

### 数据库中的实际数据

```sql
-- users 表
| id | username | platform |
|----|----------|----------|
| 1  | admin    | admin    |

-- roles 表  
| id | name  | description |
|----|-------|-------------|
| 4  | admin | 管理员       |

-- permissions 表
| id | name     | key         |
|----|----------|-------------|
| 1  | 创建角色  | role:create |

-- user_roles 表 (关联关系)
| user_id | role_id |
|---------|---------|
| 1       | 4       |

-- role_permissions 表 (关联关系)  
| role_id | permission_id |
|---------|---------------|
| 4       | 1             |
```

### Casbin 中的对应策略

```sql
-- casbin_rule 表
| ptype | v0    | v1          | v2   |
|-------|-------|-------------|------|
| g     | admin | admin       |      |  -- 用户角色关联
| p     | admin | role:create | GET  |  -- 权限策略
| p     | admin | role:create | POST |  -- 权限策略
| p     | admin | role:create | PUT  |  -- 权限策略
| p     | admin | role:create | DELETE| -- 权限策略
```

## 🎉 **结论**

### 🔑 **关键理解**

1. **关联关系存储在数据库**：便于管理和维护
2. **执行策略存储在 Casbin**：高性能权限检查
3. **两层数据实时同步**：确保一致性
4. **各司其职，优势互补**：既有管理能力又有执行性能

### 💡 **为什么这样设计？**

- **数据库**：擅长复杂查询、事务处理、数据完整性
- **Casbin**：擅长高性能权限检查、复杂策略执行
- **结合使用**：发挥各自优势，构建完整的权限系统

这种双层架构是权限系统的最佳实践，既保证了功能的完整性，又确保了性能的优越性！🚀
