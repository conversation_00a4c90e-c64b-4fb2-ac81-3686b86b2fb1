package bootstrap

import (
	"fmt"
	"log"

	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// 定义课程分类初始化数据（扁平化结构）
var autoCourseCategories = []struct {
	ID          uint
	Name        string
	Description string
	SortOrder   int
	Status      enum.CourseCategoryStatus
}{
	// 直接使用业务需要的课程分类
	{ID: 1, Name: "瑜伽团课", Description: "瑜伽团体课程", SortOrder: 1, Status: enum.CourseCategoryStatusEnabled},
	{ID: 2, Name: "普拉提小班课", Description: "普拉提小班课程", SortOrder: 2, Status: enum.CourseCategoryStatusEnabled},
	{ID: 3, Name: "提升小班课", Description: "提升类小班课程", SortOrder: 3, Status: enum.CourseCategoryStatusEnabled},
	{ID: 4, Name: "特色小班课", Description: "特色小班课程", SortOrder: 4, Status: enum.CourseCategoryStatusEnabled},
	{ID: 5, Name: "舞蹈课60min", Description: "60分钟舞蹈课程", SortOrder: 5, Status: enum.CourseCategoryStatusEnabled},
	{ID: 6, Name: "舞蹈课90min", Description: "90分钟舞蹈课程", SortOrder: 6, Status: enum.CourseCategoryStatusEnabled},
	{ID: 7, Name: "标准私教1V1", Description: "标准一对一私教课程", SortOrder: 7, Status: enum.CourseCategoryStatusEnabled},
	{ID: 8, Name: "明星私教1V1", Description: "明星教练一对一私教课程", SortOrder: 8, Status: enum.CourseCategoryStatusEnabled},
	{ID: 9, Name: "明星私教1V2", Description: "明星教练一对二私教课程", SortOrder: 9, Status: enum.CourseCategoryStatusEnabled},
}

// initializeCourseCategories 初始化课程分类数据
func initializeCourseCategories(db *gorm.DB) error {
	log.Println("📚 开始检查课程分类数据...")

	// 检查是否已有课程分类数据
	var categoryCount int64
	if err := db.Model(&model.CourseCategory{}).Count(&categoryCount).Error; err != nil {
		return fmt.Errorf("检查课程分类数据失败: %v", err)
	}

	if categoryCount > 0 {
		log.Printf("✅ 发现 %d 个课程分类，跳过初始化", categoryCount)
		return nil
	}

	log.Printf("📝 开始创建 %d 个默认课程分类...", len(autoCourseCategories))

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建课程分类
	for _, categoryInfo := range autoCourseCategories {
		category := model.CourseCategory{
			BaseModel: model.BaseModel{
				ID: categoryInfo.ID,
			},
			Name:        categoryInfo.Name,
			Description: categoryInfo.Description,
			SortOrder:   categoryInfo.SortOrder,
			Status:      categoryInfo.Status,
		}

		if err := tx.Create(&category).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建课程分类 %s 失败: %v", categoryInfo.Name, err)
		}

		log.Printf("✅ 创建课程分类: %s (ID: %d)", categoryInfo.Name, categoryInfo.ID)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交课程分类初始化事务失败: %v", err)
	}

	// 更新PostgreSQL序列
	if err := updatePostgreSQLSequences(db); err != nil {
		log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
		// 不返回错误，因为这不是致命问题
	}

	log.Printf("✅ 课程分类初始化完成: %d 个分类", len(autoCourseCategories))
	return nil
}
