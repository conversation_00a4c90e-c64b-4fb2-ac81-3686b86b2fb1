// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

const TableNameTblRotateTp = "tbl_rotate_tp"

// TblRotateTp mapped from table <tbl_rotate_tp>
type TblRotateTp struct {
	ID      int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Title   string `gorm:"column:title" json:"title"`
	Orderby int32  `gorm:"column:orderby;not null" json:"orderby"`
	IsShow  bool   `gorm:"column:is_show;not null;default:1" json:"is_show"`
}

// TableName TblRotateTp's table name
func (*TblRotateTp) TableName() string {
	return TableNameTblRotateTp
}
