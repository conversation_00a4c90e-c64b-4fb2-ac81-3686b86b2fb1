{"v": 8, "id": "yogaga-public-api", "name": "Yogaga Public API", "description": "Yogaga瑜伽管理系统 - 公开API集合，包含3个接口，提供认证登录和系统健康检查功能", "version": "1.0.0", "author": "Yogaga Development Team", "created": "2024-01-16", "updated": "2024-01-16", "folders": [{"v": 1, "id": "system-folder", "name": "🔧 系统接口", "folders": [], "requests": [{"v": "14", "name": "健康检查", "method": "GET", "endpoint": "<<baseURL>>/healthz", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has status ok\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.status).toBe(\"ok\");\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "auth-folder", "name": "🔐 认证接口", "folders": [], "requests": [{"v": "14", "name": "管理员登录", "method": "POST", "endpoint": "<<baseURL>>/api/v1/public/admin/login", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has access_token\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.env.set(\"access_token\", jsonData.data.access_token);\n    pw.env.set(\"refresh_token\", jsonData.data.refresh_token);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": "application/json", "body": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "刷新令牌", "method": "POST", "endpoint": "<<baseURL>>/api/v1/public/auth/refresh", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has new access_token\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.env.set(\"access_token\", jsonData.data.access_token);\n    pw.env.set(\"refresh_token\", jsonData.data.refresh_token);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": "application/json", "body": "{\n  \"refresh_token\": \"<<refresh_token>>\"\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "file-folder", "name": "📁 文件接口", "folders": [], "requests": [{"v": "14", "name": "文件下载", "method": "GET", "endpoint": "<<baseURL>>/files/<<file_id>>", "params": [{"key": "filename", "value": "custom_filename.jpg", "active": false}], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200 or 302\", () => {\n    pw.expect(pw.response.status === 200 || pw.response.status === 302).toBe(true);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}], "requests": [], "auth": {"authType": "none", "authActive": false}, "headers": [], "variables": [{"key": "baseURL", "value": "http://localhost:9095", "secret": false}, {"key": "access_token", "value": "", "secret": true}, {"key": "refresh_token", "value": "", "secret": true}, {"key": "file_id", "value": "", "secret": false}]}