// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

const TableNameTblStaff = "tbl_staff"

// TblStaff mapped from table <tbl_staff>
type TblStaff struct {
	ID       int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID   int32  `gorm:"column:shop_id" json:"shop_id"`
	Tp       bool   `gorm:"column:tp;not null;default:1" json:"tp"`
	Name     string `gorm:"column:name;not null" json:"name"`
	Sex      string `gorm:"column:sex;not null" json:"sex"`
	Mobile   string `gorm:"column:mobile" json:"mobile"`
	Status   int32  `gorm:"column:status;not null" json:"status"`
	IsDelete bool   `gorm:"column:is_delete;not null" json:"is_delete"`
	Note     string `gorm:"column:note" json:"note"`
}

// TableName TblStaff's table name
func (*TblStaff) TableName() string {
	return TableNameTblStaff
}
