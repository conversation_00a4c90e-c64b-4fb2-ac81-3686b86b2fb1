package model

import (
	"time"
	"yogaga/internal/enum"
)

// ClassSchedule 课程排期
type ClassSchedule struct {
	BaseModel
	StoreID      uint                `json:"store_id" gorm:"not null;comment:门店ID"`
	CourseID     uint                `json:"course_id" gorm:"not null;comment:课程ID"`
	CoachID      string              `json:"coach_id" gorm:"type:char(36);not null;comment:教练用户ID"`
	ClassRoomID  uint                `json:"classroom_id" gorm:"not null;comment:教室ID"`
	StartTime    time.Time           `json:"start_time" gorm:"not null;comment:开始时间"`
	EndTime      time.Time           `json:"end_time" gorm:"not null;comment:结束时间"`
	MaxCapacity  int                 `json:"max_capacity" gorm:"type:int;not null;comment:最大容量"`
	MinCapacity  int                 `json:"min_capacity" gorm:"type:int;default:1;comment:最小开课人数"`
	CurrentCount int                 `json:"current_count" gorm:"type:int;default:0;comment:当前预约人数"`
	WaitingCount int                 `json:"waiting_count" gorm:"type:int;default:0;comment:排队人数"`
	Price        int                 `json:"price" gorm:"type:int;default:0;comment:单次价格(分)"`
	Status       enum.ScheduleStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:可预约 2:已满员 3:已开课 4:已结束 5:已取消"`
	CancelReason string              `json:"cancel_reason" gorm:"type:varchar(255);comment:取消原因"`

	// 关联
	Store     *Store     `json:"store,omitempty" gorm:"foreignKey:StoreID"`
	Course    *Course    `json:"course,omitempty" gorm:"foreignKey:CourseID"`
	Coach     *User      `json:"coach,omitempty" gorm:"foreignKey:CoachID"`
	ClassRoom *ClassRoom `json:"classroom,omitempty" gorm:"foreignKey:ClassRoomID"`
}
