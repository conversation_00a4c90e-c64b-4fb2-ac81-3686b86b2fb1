#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复副卡数据一致性的Python脚本
"""

import requests
import json

class SubCardDataFixer:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_all_cards(self) -> list:
        """获取所有会员卡"""
        print("\n📋 获取所有会员卡...")
        
        all_cards = []
        page = 1
        page_size = 50
        
        while True:
            try:
                params = {
                    "page": page,
                    "page_size": page_size
                }
                response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards", params=params)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        data = result.get('data', {})
                        cards = data.get('list', [])
                        total = data.get('total', 0)
                        
                        all_cards.extend(cards)
                        print(f"   获取第 {page} 页，{len(cards)} 张卡片")
                        
                        # 如果已获取所有卡片，退出循环
                        if len(all_cards) >= total or len(cards) < page_size:
                            break
                        
                        page += 1
                    else:
                        print(f"❌ 获取会员卡失败: {result.get('msg')}")
                        break
                else:
                    print(f"❌ 获取会员卡失败: {response.status_code}")
                    break
            except Exception as e:
                print(f"❌ 获取会员卡异常: {e}")
                break
        
        print(f"✅ 总共获取到 {len(all_cards)} 张会员卡")
        return all_cards
    
    def identify_problematic_cards(self, cards: list) -> list:
        """识别有问题的副卡"""
        print("\n🔍 识别有问题的副卡...")
        
        problematic_cards = []
        sub_card_count = 0
        
        for card in cards:
            card_id = card.get('id')
            card_number = card.get('card_number')
            is_main_card = card.get('is_main_card')
            main_card_id = card.get('main_card_id')
            remaining_amount = card.get('remaining_amount', 0)
            remaining_times = card.get('remaining_times', 0)
            total_amount = card.get('total_amount', 0)
            total_times = card.get('total_times', 0)
            purchase_price = card.get('purchase_price', 0)
            
            # 如果有 main_card_id，说明这是副卡
            if main_card_id is not None:
                sub_card_count += 1
                issues = []
                
                print(f"\n📄 副卡 {card_id} ({card_number}):")
                print(f"   主卡ID: {main_card_id}")
                print(f"   是否主卡: {is_main_card}")
                print(f"   剩余金额: {remaining_amount / 100}元")
                print(f"   剩余次数: {remaining_times}")
                print(f"   总金额: {total_amount / 100}元")
                print(f"   总次数: {total_times}")
                print(f"   购买价格: {purchase_price / 100}元")
                
                # 检查各种问题
                if is_main_card:
                    issues.append("is_main_card_should_be_false")
                    print("   ⚠️ 问题：is_main_card 应该为 false")
                
                if remaining_amount > 0:
                    issues.append("remaining_amount_should_be_zero")
                    print("   ⚠️ 问题：remaining_amount 应该为 0")
                
                if remaining_times > 0:
                    issues.append("remaining_times_should_be_zero")
                    print("   ⚠️ 问题：remaining_times 应该为 0")
                
                if total_amount > 0:
                    issues.append("total_amount_should_be_zero")
                    print("   ⚠️ 问题：total_amount 应该为 0")
                
                if total_times > 0:
                    issues.append("total_times_should_be_zero")
                    print("   ⚠️ 问题：total_times 应该为 0")
                
                if purchase_price > 0:
                    issues.append("purchase_price_should_be_zero")
                    print("   ⚠️ 问题：purchase_price 应该为 0")
                
                if issues:
                    problematic_cards.append({
                        'card': card,
                        'issues': issues
                    })
                    print("   ❌ 需要修复")
                else:
                    print("   ✅ 数据正常")
        
        print(f"\n📊 统计结果:")
        print(f"   副卡总数: {sub_card_count}")
        print(f"   有问题的副卡: {len(problematic_cards)}")
        print(f"   正常的副卡: {sub_card_count - len(problematic_cards)}")
        
        return problematic_cards
    
    def fix_card_via_sql(self, card_id: int, fixes: dict) -> bool:
        """通过SQL直接修复卡片数据（这里模拟，实际需要数据库访问）"""
        print(f"   🔧 修复卡片 {card_id}...")
        
        # 注意：这里只是演示，实际需要直接访问数据库
        # 或者通过管理API来修复数据
        
        print(f"   需要修复的字段: {list(fixes.keys())}")
        
        # 这里可以生成SQL语句供手动执行
        sql_parts = []
        for field, value in fixes.items():
            sql_parts.append(f"{field} = {value}")
        
        sql = f"UPDATE membership_cards SET {', '.join(sql_parts)} WHERE id = {card_id};"
        print(f"   SQL: {sql}")
        
        return True
    
    def generate_fix_sql(self, problematic_cards: list) -> str:
        """生成修复SQL语句"""
        print("\n📝 生成修复SQL语句...")
        
        sql_statements = []
        sql_statements.append("-- 副卡数据修复SQL")
        sql_statements.append("-- 执行前请备份数据库")
        sql_statements.append("")
        
        for item in problematic_cards:
            card = item['card']
            issues = item['issues']
            card_id = card['id']
            card_number = card['card_number']
            
            sql_statements.append(f"-- 修复副卡 {card_id} ({card_number})")
            
            fixes = {}
            if "is_main_card_should_be_false" in issues:
                fixes["is_main_card"] = "false"
            if "remaining_amount_should_be_zero" in issues:
                fixes["remaining_amount"] = "0"
            if "remaining_times_should_be_zero" in issues:
                fixes["remaining_times"] = "0"
            if "total_amount_should_be_zero" in issues:
                fixes["total_amount"] = "0"
            if "total_times_should_be_zero" in issues:
                fixes["total_times"] = "0"
            if "purchase_price_should_be_zero" in issues:
                fixes["purchase_price"] = "0"
            
            if fixes:
                sql_parts = []
                for field, value in fixes.items():
                    sql_parts.append(f"{field} = {value}")
                
                sql = f"UPDATE membership_cards SET {', '.join(sql_parts)} WHERE id = {card_id};"
                sql_statements.append(sql)
            
            sql_statements.append("")
        
        return "\n".join(sql_statements)
    
    def run_fix(self):
        """运行修复流程"""
        print("🔧 开始副卡数据修复...")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取所有会员卡
        cards = self.get_all_cards()
        if not cards:
            return False
        
        # 3. 识别有问题的副卡
        problematic_cards = self.identify_problematic_cards(cards)
        
        if not problematic_cards:
            print("\n✅ 没有发现需要修复的副卡数据！")
            return True
        
        # 4. 生成修复SQL
        fix_sql = self.generate_fix_sql(problematic_cards)
        
        # 5. 保存SQL到文件
        sql_file = "scripts/fix_sub_card_data.sql"
        try:
            with open(sql_file, 'w', encoding='utf-8') as f:
                f.write(fix_sql)
            print(f"\n📄 修复SQL已保存到: {sql_file}")
        except Exception as e:
            print(f"❌ 保存SQL文件失败: {e}")
        
        print("\n" + "=" * 80)
        print("🎯 修复建议:")
        print("1. 请先备份数据库")
        print(f"2. 执行生成的SQL文件: {sql_file}")
        print("3. 重新运行测试验证修复结果")
        print("\n⚠️ 注意：请在执行SQL前仔细检查语句的正确性")
        
        return True

def main():
    """主函数"""
    fixer = SubCardDataFixer()
    success = fixer.run_fix()
    
    if success:
        print("\n🏆 副卡数据修复流程完成！")
        exit(0)
    else:
        print("\n💥 副卡数据修复失败！")
        exit(1)

if __name__ == "__main__":
    main()
