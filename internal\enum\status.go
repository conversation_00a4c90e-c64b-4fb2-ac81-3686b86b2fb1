package enum

// Status 通用状态枚举
type Status int

const (
	StatusDeleted Status = iota // 删除
	StatusNormal                // 正常
)

// String 返回状态的字符串表示
func (s Status) String() string {
	switch s {
	case StatusNormal:
		return "normal"
	case StatusDeleted:
		return "deleted"
	default:
		return "unknown"
	}
}

// IsValid 验证状态值是否有效
func (s Status) IsValid() bool {
	switch s {
	case StatusNormal, StatusDeleted:
		return true
	default:
		return false
	}
}

// StoreStatus 门店状态枚举
type StoreStatus int

const (
	StoreStatusClosed StoreStatus = 0 // 停业
	StoreStatusOpen   StoreStatus = 1 // 营业
)

// String 返回门店状态的字符串表示
func (s StoreStatus) String() string {
	switch s {
	case StoreStatusOpen:
		return "open"
	case StoreStatusClosed:
		return "closed"
	default:
		return "unknown"
	}
}

// IsValid 验证门店状态值是否有效
func (s StoreStatus) IsValid() bool {
	switch s {
	case StoreStatusOpen, StoreStatusClosed:
		return true
	default:
		return false
	}
}

// ClassRoomStatus 教室状态枚举
type ClassRoomStatus int

const (
	ClassRoomStatusMaintenance ClassRoomStatus = 0 // 维护
	ClassRoomStatusAvailable   ClassRoomStatus = 1 // 可用
)

// String 返回教室状态的字符串表示
func (s ClassRoomStatus) String() string {
	switch s {
	case ClassRoomStatusAvailable:
		return "available"
	case ClassRoomStatusMaintenance:
		return "maintenance"
	default:
		return "unknown"
	}
}

// IsValid 验证教室状态值是否有效
func (s ClassRoomStatus) IsValid() bool {
	switch s {
	case ClassRoomStatusAvailable, ClassRoomStatusMaintenance:
		return true
	default:
		return false
	}
}

// CourseStatus 课程状态枚举
type CourseStatus int

const (
	CourseStatusCancelled CourseStatus = 0 // 取消
	CourseStatusNormal    CourseStatus = 1 // 正常
	CourseStatusPublished CourseStatus = 2 // 已发布
)

// String 返回课程状态的字符串表示
func (s CourseStatus) String() string {
	switch s {
	case CourseStatusNormal:
		return "normal"
	case CourseStatusCancelled:
		return "cancelled"
	case CourseStatusPublished:
		return "published"
	default:
		return "unknown"
	}
}

// IsValid 验证课程状态值是否有效
func (s CourseStatus) IsValid() bool {
	switch s {
	case CourseStatusNormal, CourseStatusCancelled, CourseStatusPublished:
		return true
	default:
		return false
	}
}

// CourseCategoryStatus 课程分类状态枚举
type CourseCategoryStatus int

const (
	CourseCategoryStatusDisabled CourseCategoryStatus = 0 // 停用
	CourseCategoryStatusEnabled  CourseCategoryStatus = 1 // 启用
)

// BannerStatus 轮播图状态枚举
type BannerStatus int

const (
	BannerStatusDisabled BannerStatus = 0 // 禁用
	BannerStatusEnabled  BannerStatus = 1 // 启用
)

// String 返回轮播图状态的字符串表示
func (s BannerStatus) String() string {
	switch s {
	case BannerStatusEnabled:
		return "enabled"
	case BannerStatusDisabled:
		return "disabled"
	default:
		return "unknown"
	}
}

// IsValid 验证轮播图状态值是否有效
func (s BannerStatus) IsValid() bool {
	switch s {
	case BannerStatusEnabled, BannerStatusDisabled:
		return true
	default:
		return false
	}
}

// BannerPosition 轮播图位置枚举
type BannerPosition string

const (
	BannerPositionHome  BannerPosition = "home"  // 首页
	BannerPositionCoach BannerPosition = "coach" // 教练详情页
	BannerPositionStore BannerPosition = "store" // 门店详情页
)

// String 返回课程分类状态的字符串表示
func (s CourseCategoryStatus) String() string {
	switch s {
	case CourseCategoryStatusEnabled:
		return "enabled"
	case CourseCategoryStatusDisabled:
		return "disabled"
	default:
		return "unknown"
	}
}

// IsValid 验证课程分类状态值是否有效
func (s CourseCategoryStatus) IsValid() bool {
	switch s {
	case CourseCategoryStatusEnabled, CourseCategoryStatusDisabled:
		return true
	default:
		return false
	}
}

// MembershipCardStatus 会员卡状态枚举
type MembershipCardStatus int

const (
	MembershipCardStatusNormal  MembershipCardStatus = 1 // 正常
	MembershipCardStatusFrozen  MembershipCardStatus = 2 // 冻结
	MembershipCardStatusExpired MembershipCardStatus = 3 // 过期
	MembershipCardStatusUsedUp  MembershipCardStatus = 4 // 用完
)

// String 返回会员卡状态的字符串表示
func (s MembershipCardStatus) String() string {
	switch s {
	case MembershipCardStatusNormal:
		return "normal"
	case MembershipCardStatusFrozen:
		return "frozen"
	case MembershipCardStatusExpired:
		return "expired"
	case MembershipCardStatusUsedUp:
		return "used_up"
	default:
		return "unknown"
	}
}

// IsValid 验证会员卡状态值是否有效
func (s MembershipCardStatus) IsValid() bool {
	switch s {
	case MembershipCardStatusNormal, MembershipCardStatusFrozen,
		MembershipCardStatusExpired, MembershipCardStatusUsedUp:
		return true
	default:
		return false
	}
}

// BookingStatus 预约状态枚举
type BookingStatus int

const (
	BookingStatusBooked    BookingStatus = 1 // 已预约
	BookingStatusCheckedIn BookingStatus = 2 // 已签到
	BookingStatusCompleted BookingStatus = 3 // 已完成
	BookingStatusCancelled BookingStatus = 4 // 已取消
	BookingStatusNoShow    BookingStatus = 5 // 未到课
)

// String 返回预约状态的字符串表示
func (s BookingStatus) String() string {
	switch s {
	case BookingStatusBooked:
		return "booked"
	case BookingStatusCheckedIn:
		return "checked_in"
	case BookingStatusCompleted:
		return "completed"
	case BookingStatusCancelled:
		return "cancelled"
	case BookingStatusNoShow:
		return "no_show"
	default:
		return "unknown"
	}
}

// IsValid 验证预约状态值是否有效
func (s BookingStatus) IsValid() bool {
	switch s {
	case BookingStatusBooked, BookingStatusCheckedIn, BookingStatusCompleted,
		BookingStatusCancelled, BookingStatusNoShow:
		return true
	default:
		return false
	}
}

// QueueStatus 排队状态枚举
type QueueStatus int

const (
	QueueStatusWaiting   QueueStatus = 1 // 排队中
	QueueStatusNotified  QueueStatus = 2 // 已通知
	QueueStatusConverted QueueStatus = 3 // 已转预约
	QueueStatusCancelled QueueStatus = 4 // 已取消
)

// String 返回排队状态的字符串表示
func (s QueueStatus) String() string {
	switch s {
	case QueueStatusWaiting:
		return "waiting"
	case QueueStatusNotified:
		return "notified"
	case QueueStatusConverted:
		return "converted"
	case QueueStatusCancelled:
		return "cancelled"
	default:
		return "unknown"
	}
}

// IsValid 验证排队状态值是否有效
func (s QueueStatus) IsValid() bool {
	switch s {
	case QueueStatusWaiting, QueueStatusNotified, QueueStatusConverted, QueueStatusCancelled:
		return true
	default:
		return false
	}
}

// ScheduleStatus 课程排期状态枚举
type ScheduleStatus int

const (
	ScheduleStatusBookable  ScheduleStatus = 1 // 可预约
	ScheduleStatusFull      ScheduleStatus = 2 // 已满员
	ScheduleStatusStarted   ScheduleStatus = 3 // 已开课
	ScheduleStatusEnded     ScheduleStatus = 4 // 已结束
	ScheduleStatusCancelled ScheduleStatus = 5 // 已取消
)

// String 返回课程排期状态的字符串表示
func (s ScheduleStatus) String() string {
	switch s {
	case ScheduleStatusBookable:
		return "bookable"
	case ScheduleStatusFull:
		return "full"
	case ScheduleStatusStarted:
		return "started"
	case ScheduleStatusEnded:
		return "ended"
	case ScheduleStatusCancelled:
		return "cancelled"
	default:
		return "unknown"
	}
}

// IsValid 验证课程排期状态值是否有效
func (s ScheduleStatus) IsValid() bool {
	switch s {
	case ScheduleStatusBookable, ScheduleStatusFull, ScheduleStatusStarted,
		ScheduleStatusEnded, ScheduleStatusCancelled:
		return true
	default:
		return false
	}
}
