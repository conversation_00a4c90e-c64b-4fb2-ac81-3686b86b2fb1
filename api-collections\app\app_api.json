{"v": 8, "id": "yogaga-app-api", "name": "Yogaga App API (小程序端)", "description": "Yogaga瑜伽管理系统 - 微信小程序API集合，包含42个接口，覆盖用户认证、课程浏览、预约管理、个人中心等功能模块", "version": "1.0.0", "author": "Yogaga Development Team", "created": "2024-01-16", "updated": "2024-01-16", "folders": [{"v": 1, "id": "auth-folder", "name": "🔐 认证模块", "folders": [], "requests": [{"v": "14", "name": "微信小程序登录", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/wechat/login", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has access_token\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.env.set(\"access_token\", jsonData.data.access_token);\n    pw.env.set(\"refresh_token\", jsonData.data.refresh_token);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": "application/json", "body": "{\n  \"code\": \"wx_auth_code_example\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新用户微信信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/app/wechat/userinfo", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"nick_name\": \"测试用户\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\",\n  \"gender\": 1,\n  \"country\": \"中国\",\n  \"province\": \"广东省\",\n  \"city\": \"深圳市\",\n  \"language\": \"zh_CN\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取当前用户信息", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/wechat/userinfo", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "course-folder", "name": "🏃‍♀️ 课程模块", "folders": [], "requests": [{"v": "14", "name": "获取课程列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/courses", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "store_id", "value": "1", "active": false}, {"key": "coach_id", "value": "1", "active": false}, {"key": "category_id", "value": "1", "active": false}, {"key": "level", "value": "beginner", "active": false}], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/courses/1", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "按日期查询课程", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/courses/date", "params": [{"key": "date", "value": "2025-07-15", "active": true}, {"key": "store_id", "value": "1", "active": false}], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程日历", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/courses/calendar", "params": [{"key": "month", "value": "2025-07", "active": true}], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程分类列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/course-categories", "params": [], "headers": [], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "none", "authActive": false}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "booking-folder", "name": "📅 预约模块", "folders": [], "requests": [{"v": "14", "name": "创建预约", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/bookings", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has booking data\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"booking_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"schedule_id\": 1,\n  \"membership_card_id\": 1,\n  \"people_count\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取用户预约列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/bookings", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "status", "value": "confirmed", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取预约详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/bookings/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "取消预约", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/app/bookings/cancel", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"booking_id\": 1,\n  \"reason\": \"临时有事无法参加\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "评价课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/bookings/rate", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"booking_id\": 1,\n  \"rating\": 5,\n  \"comment\": \"课程很棒，教练很专业！\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "查询异步预约任务状态", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/bookings/status/task123", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取取消预约信息", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/bookings/1/cancel-info", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "favorite-folder", "name": "❤️ 收藏模块", "folders": [], "requests": [{"v": "14", "name": "获取收藏列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/favorites", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "target_type", "value": "course", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "添加收藏", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/favorites", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"favorite_type\": \"course\",\n  \"target_id\": \"1\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "取消收藏", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/app/favorites/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "检查是否已收藏", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/favorites/check", "params": [{"key": "type", "value": "course", "active": true}, {"key": "target_id", "value": "1", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "membership-folder", "name": "💳 会员卡模块", "folders": [], "requests": [{"v": "14", "name": "获取用户会员卡列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/membership-cards", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取会员卡详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/membership-cards/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "store-folder", "name": "🏪 门店信息模块", "folders": [], "requests": [{"v": "14", "name": "获取门店列表（带距离）", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/stores", "params": [{"key": "latitude", "value": "39.9042", "active": true}, {"key": "longitude", "value": "116.4074", "active": true}, {"key": "radius", "value": "10", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取门店详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/stores/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "coach-folder", "name": "🧘‍♀️ 教练信息模块", "folders": [], "requests": [{"v": "14", "name": "获取教练列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/coaches", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "store_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取教练详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/coaches/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "home-folder", "name": "🏠 首页相关模块", "folders": [], "requests": [{"v": "14", "name": "获取首页数据", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/home", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取轮播图", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/banners", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取广告", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/ads", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取精选内容", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/featured", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "coach-banner-folder", "name": "🎨 教练轮播图模块", "folders": [], "requests": [{"v": "14", "name": "获取教练轮播图", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/coaches/1/banners", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "user-center-folder", "name": "👤 用户中心模块", "folders": [], "requests": [{"v": "14", "name": "获取用户资料", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/user/profile", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新用户资料", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/app/user/profile", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"nickname\": \"新昵称\",\n  \"avatar_url\": \"https://example.com/avatar.jpg\",\n  \"phone\": \"13800138000\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取用户统计", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/user/stats", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取会员卡汇总", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/user/membership-summary", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取最近预约", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/user/recent-bookings", "params": [{"key": "limit", "value": "5", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "checkin-folder", "name": "📱 扫码签到模块", "folders": [], "requests": [{"v": "14", "name": "生成签到二维码", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/checkin/courses/1/qrcode", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "扫码签到", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/checkin", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"qr_data\": {\n    \"type\": \"checkin\",\n    \"course_id\": 1,\n    \"checkin_code\": \"checkin_token_123\",\n    \"store_id\": 1,\n    \"latitude\": 39.9042,\n    \"longitude\": 116.4074,\n    \"expire_time\": 1672531200\n  },\n  \"latitude\": 39.9042,\n  \"longitude\": 116.4074\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取签到状态", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/checkin/courses/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "queue-folder", "name": "🎯 智能排队模块", "folders": [], "requests": [{"v": "14", "name": "加入排队", "method": "POST", "endpoint": "<<baseURL>>/api/v1/app/queue", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"course_id\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取排队状态", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/queue/courses/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "取消排队", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/app/queue/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取用户排队列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/app/queue", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}], "requests": [], "auth": {"authType": "none", "authActive": false}, "headers": [], "variables": [{"key": "baseURL", "value": "http://localhost:9095", "secret": false}, {"key": "access_token", "value": "", "secret": true}, {"key": "refresh_token", "value": "", "secret": true}]}