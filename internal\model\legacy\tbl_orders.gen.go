// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblOrder = "tbl_orders"

// TblOrder mapped from table <tbl_orders>
type TblOrder struct {
	ID            int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID        int32     `gorm:"column:shop_id" json:"shop_id"`
	ClassID       int32     `gorm:"column:class_id;not null" json:"class_id"`
	CoachID       int32     `gorm:"column:coach_id;not null" json:"coach_id"`
	MemberID      int32     `gorm:"column:member_id;not null" json:"member_id"`
	PayNum        float64   `gorm:"column:pay_num;not null;default:0.00" json:"pay_num"`
	PeopleNum     int32     `gorm:"column:people_num;not null;default:1" json:"people_num"`
	CardID        int32     `gorm:"column:card_id;not null" json:"card_id"`
	CardTp        bool      `gorm:"column:card_tp;not null" json:"card_tp"`
	CardRemain    string    `gorm:"column:card_remain" json:"card_remain"`
	Note          string    `gorm:"column:note" json:"note"`
	CreateTime    time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete      int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	Status        bool      `gorm:"column:status;not null" json:"status"`
	CancelTime    time.Time `gorm:"column:cancel_time" json:"cancel_time"`
	CancelAdminid int32     `gorm:"column:cancel_adminid;not null" json:"cancel_adminid"`
	Errcode       string    `gorm:"column:errcode" json:"errcode"`
	Errmsg        string    `gorm:"column:errmsg" json:"errmsg"`
}

// TableName TblOrder's table name
func (*TblOrder) TableName() string {
	return TableNameTblOrder
}
