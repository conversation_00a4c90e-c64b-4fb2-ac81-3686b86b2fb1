package enum

import (
	"testing"
)

func TestErrorCode(t *testing.T) {
	tests := []struct {
		name     string
		code     ErrorCode
		expected string
		valid    bool
	}{
		{"Success", ErrorCodeSuccess, "success", true},
		{"InvalidParams", ErrorCodeInvalidParams, "invalid_params", true},
		{"Unknown", ErrorCodeUnknown, "unknown_error", true},
		{"UserNotFound", ErrorCodeUserNotFound, "user_not_found", true},
		{"Invalid", ErrorCode(99999), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.code.String(); got != tt.expected {
				t.Errorf("ErrorCode.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.code.IsValid(); got != tt.valid {
				t.<PERSON><PERSON>("ErrorCode.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestFileAccessType(t *testing.T) {
	tests := []struct {
		name     string
		access   FileAccessType
		expected string
		valid    bool
	}{
		{"Public", FileAccessTypePublic, "public", true},
		{"Private", FileAccessTypePrivate, "private", true},
		{"Invalid", FileAccessType("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.access.String(); got != tt.expected {
				t.Errorf("FileAccessType.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.access.IsValid(); got != tt.valid {
				t.Errorf("FileAccessType.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestFileType(t *testing.T) {
	tests := []struct {
		name     string
		ftype    FileType
		expected string
		valid    bool
	}{
		{"Avatar", FileTypeAvatar, "avatar", true},
		{"Banner", FileTypeBanner, "banner", true},
		{"Image", FileTypeImage, "image", true},
		{"Video", FileTypeVideo, "video", true},
		{"CardsTP", FileTypeCardsTP, "cards_tp", true},
		{"Invalid", FileType("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ftype.String(); got != tt.expected {
				t.Errorf("FileType.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.ftype.IsValid(); got != tt.valid {
				t.Errorf("FileType.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

// 测试错误码范围
func TestErrorCodeRange(t *testing.T) {
	// 测试成功码
	if !ErrorCodeSuccess.IsValid() {
		t.Error("ErrorCodeSuccess should be valid")
	}

	// 测试通用错误码范围
	generalErrors := []ErrorCode{
		ErrorCodeInvalidParams,
		ErrorCodeUnknown,
		ErrorCodeNotExistCert,
		ErrorCodeInvalidRoleID,
		ErrorCodeInvalidFileType,
		ErrorCodeInvalidAccessType,
		ErrorCodeResourceNotFound,
		ErrorCodeFileOperation,
	}

	for _, code := range generalErrors {
		if !code.IsValid() {
			t.Errorf("General error code %d should be valid", code)
		}
		if code < 10000 || code > 19999 {
			t.Errorf("General error code %d should be in range 10000-19999", code)
		}
	}

	// 测试数据库错误码范围
	dbErrors := []ErrorCode{
		ErrorCodeDatabaseInsert,
		ErrorCodeDatabaseDelete,
		ErrorCodeDatabaseQuery,
		ErrorCodeDatabaseUpdate,
	}

	for _, code := range dbErrors {
		if !code.IsValid() {
			t.Errorf("Database error code %d should be valid", code)
		}
		if code < 20000 || code > 29999 {
			t.Errorf("Database error code %d should be in range 20000-29999", code)
		}
	}

	// 测试用户错误码范围
	userErrors := []ErrorCode{
		ErrorCodeUserNoLogin,
		ErrorCodeUserNotFound,
		ErrorCodeUserPasswordError,
		ErrorCodeUserNotVerify,
		ErrorCodeUserLocked,
		ErrorCodeUserDisabled,
		ErrorCodeUserExpired,
		ErrorCodeUserAlreadyExists,
		ErrorCodeUserNameOrPasswordError,
		ErrorCodeUserAuthFailed,
		ErrorCodeUserNoPermission,
	}

	for _, code := range userErrors {
		if !code.IsValid() {
			t.Errorf("User error code %d should be valid", code)
		}
		if code < 30000 || code > 39999 {
			t.Errorf("User error code %d should be in range 30000-39999", code)
		}
	}
}
