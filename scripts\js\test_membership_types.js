#!/usr/bin/env node

/**
 * 会员卡类型管理测试脚本
 * 测试会员卡类型的创建、查询、更新、启用/停用功能
 */

const https = require('https');
const http = require('http');

// 配置
const config = {
    baseURL: 'http://localhost:9095',
    adminCredentials: {
        username: 'admin',
        password: 'admin123'
    }
};

// HTTP请求工具函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;

        const req = protocol.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });

            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body ? JSON.parse(body) : null
                    };
                    resolve(result);
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 登录获取token
async function login() {
    console.log('🔐 正在登录...');

    const url = new URL('/api/v1/public/admin/login', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options, config.adminCredentials);

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log('✅ 登录成功');
            return response.body.data.access_token;
        } else {
            console.error('❌ 登录失败:', response.body);
            return null;
        }
    } catch (error) {
        console.error('❌ 登录请求失败:', error.message);
        return null;
    }
}

// 获取会员卡类型列表
async function getMembershipTypes(token) {
    console.log('📋 获取会员卡类型列表...');

    const url = new URL('/api/v1/admin/membership-types?page=1&page_size=50', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname + url.search,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200 && response.body.code === 0) {
            const types = Array.isArray(response.body.data) ? response.body.data : response.body.data.list || [];
            console.log(`✅ 获取会员卡类型列表成功，共 ${types.length} 个类型`);

            // 按类别分组显示
            const categories = {
                'times': '次数卡',
                'period': '期限卡',
                'balance': '储值卡'
            };

            Object.keys(categories).forEach(category => {
                const categoryTypes = types.filter(type => type.category === category);
                if (categoryTypes.length > 0) {
                    console.log(`\n📦 ${categories[category]} (${categoryTypes.length}个):`);
                    categoryTypes.forEach(type => {
                        const status = type.status === 1 ? '✅启用' : '❌停用';
                        const price = type.price ? `¥${(type.price / 100).toFixed(2)}` : '免费';
                        console.log(`   - ${type.name} (ID: ${type.id}) - ${price} - ${status}`);
                    });
                }
            });

            return types;
        } else {
            console.error('❌ 获取会员卡类型列表失败:', response.body);
            return [];
        }
    } catch (error) {
        console.error('❌ 获取会员卡类型列表请求失败:', error.message);
        return [];
    }
}

// 创建会员卡类型
async function createMembershipType(token, typeData) {
    console.log(`🆕 创建会员卡类型: ${typeData.name}`);

    const url = new URL('/api/v1/admin/membership-types', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options, typeData);

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log(`✅ 会员卡类型创建成功: ${typeData.name}`);
            console.log(`   - 新类型ID: ${response.body.data.id}`);
            return response.body.data;
        } else {
            console.error(`❌ 会员卡类型创建失败: ${typeData.name}`, response.body);
            return null;
        }
    } catch (error) {
        console.error(`❌ 会员卡类型创建请求失败: ${typeData.name}`, error.message);
        return null;
    }
}

// 更新会员卡类型状态
async function updateMembershipTypeStatus(token, typeId, status) {
    console.log(`🔄 ${status === 1 ? '启用' : '停用'}会员卡类型: ID ${typeId}`);

    const url = new URL(`/api/v1/admin/membership-types/${typeId}`, config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'PUT',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options, { status });

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log(`✅ 会员卡类型状态更新成功: ID ${typeId}`);
            return true;
        } else {
            console.error(`❌ 会员卡类型状态更新失败: ID ${typeId}`, response.body);
            return false;
        }
    } catch (error) {
        console.error(`❌ 会员卡类型状态更新请求失败: ID ${typeId}`, error.message);
        return false;
    }
}

// 测试会员卡类型功能
async function testMembershipTypeFeatures(token) {
    console.log('\n🧪 测试会员卡类型功能...');

    // 测试创建不同类型的会员卡
    const testTypes = [
        {
            name: '测试次数卡',
            category: 'times',
            description: '测试用次数卡',
            price: 10000, // 100元
            times: 10,
            validity_days: 365,
            discount: 1.0,
            shareable: false
        },
        {
            name: '测试期限卡',
            category: 'period',
            description: '测试用期限卡',
            price: 20000, // 200元
            validity_days: 90,
            discount: 1.0,
            shareable: false
        },
        {
            name: '测试储值卡',
            category: 'balance',
            description: '测试用储值卡',
            amount: 50000, // 500元
            validity_days: 365,
            discount: 1.0,
            shareable: true
        }
    ];

    const createdTypes = [];

    for (const typeData of testTypes) {
        const createdType = await createMembershipType(token, typeData);
        if (createdType) {
            createdTypes.push(createdType);
        }
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 测试状态切换
    if (createdTypes.length > 0) {
        console.log('\n🔄 测试状态切换功能...');
        const testType = createdTypes[0];

        // 停用
        await updateMembershipTypeStatus(token, testType.id, 0);
        await new Promise(resolve => setTimeout(resolve, 500));

        // 重新启用
        await updateMembershipTypeStatus(token, testType.id, 1);
    }

    return createdTypes;
}

// 主测试函数
async function main() {
    console.log('🚀 开始测试会员卡类型管理功能...');
    console.log('='.repeat(60));

    // 1. 登录
    const token = await login();
    if (!token) {
        console.error('❌ 无法获取访问令牌，测试终止');
        return;
    }

    // 2. 获取现有会员卡类型列表
    const existingTypes = await getMembershipTypes(token);

    // 3. 测试会员卡类型功能
    const createdTypes = await testMembershipTypeFeatures(token);

    // 4. 再次获取列表验证
    console.log('\n📋 验证创建结果...');
    await getMembershipTypes(token);

    console.log('\n🎉 会员卡类型管理测试完成！');
    console.log('\n💡 提示：');
    console.log('- 当前系统支持三种会员卡大类：次数卡、期限卡、储值卡');
    console.log('- 支持启用/停用功能，停用的卡种不会出现在课程管理选项中');
    console.log('- 支持共享储值卡功能（多人使用）');
    console.log('- 支持门店选择、副卡添加等高级功能');
}

// 运行测试
main().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
});
