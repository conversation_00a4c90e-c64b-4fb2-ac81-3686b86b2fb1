package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/internal/service"

	"github.com/glebarez/sqlite"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 开始会员卡功能全面测试...")

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 自动迁移
	err = db.AutoMigrate(
		&model.User{},
		&model.MembershipCardType{},
		&model.MembershipCard{},
		&model.Course{},
		&model.Booking{},
		&model.MembershipCardTransaction{},
		&model.CourseDeductionRule{},
	)
	if err != nil {
		log.Fatal("数据库迁移失败:", err)
	}

	// 初始化测试数据
	initTestData(db)

	// 创建服务
	subCardService := service.NewSubCardService(db)
	dailyLimitService := service.NewDailyLimitService(db)

	// 运行测试
	fmt.Println("\n📋 测试计划:")
	fmt.Println("1. 会员卡种类管理功能")
	fmt.Println("2. 开卡功能（双支付模式）")
	fmt.Println("3. 副卡功能（权限控制）")
	fmt.Println("4. 单日限制功能")
	fmt.Println("5. 转卡功能")
	fmt.Println("6. 充值功能")
	fmt.Println("7. 扣减规则功能")
	fmt.Println("8. 升级功能")
	fmt.Println("9. 请假功能")
	fmt.Println("10. 停用/延期功能")

	// 执行测试
	testResults := make(map[string]bool)

	testResults["卡种管理"] = testCardTypeManagement(db)
	testResults["开卡功能"] = testCardCreation(db)
	testResults["副卡功能"] = testSubCardFunctionality(db, subCardService)
	testResults["单日限制"] = testDailyLimitFunctionality(db, dailyLimitService)
	testResults["转卡功能"] = testCardTransfer(db)
	testResults["充值功能"] = testCardRecharge(db)
	testResults["扣减规则"] = testDeductionRules(db)
	testResults["升级功能"] = testCardUpgrade(db)
	testResults["请假功能"] = testCardLeave(db)
	testResults["停用延期"] = testCardFreezeAndExtension(db)

	// 输出测试结果
	fmt.Println("\n📊 测试结果汇总:")
	fmt.Println(strings.Repeat("=", 50))

	passCount := 0
	totalCount := len(testResults)

	for testName, passed := range testResults {
		status := "❌ 失败"
		if passed {
			status = "✅ 通过"
			passCount++
		}
		fmt.Printf("%-12s: %s\n", testName, status)
	}

	fmt.Println(strings.Repeat("=", 50))
	fmt.Printf("总计: %d/%d 通过 (%.1f%%)\n", passCount, totalCount, float64(passCount)/float64(totalCount)*100)

	if passCount == totalCount {
		fmt.Println("\n🎉 所有测试通过！会员卡功能完全满足业务需求！")
	} else {
		fmt.Printf("\n⚠️  有 %d 个测试失败，需要检查相关功能\n", totalCount-passCount)
	}
}

func initTestData(db *gorm.DB) {
	fmt.Println("\n🔧 初始化测试数据...")

	// 创建测试用户
	users := []model.User{
		{BaseModel: model.BaseModel{ID: "user-001"}, Username: "张三", Phone: "13800138001", NickName: "张三"},
		{BaseModel: model.BaseModel{ID: "user-002"}, Username: "李四", Phone: "13800138002", NickName: "李四"},
		{BaseModel: model.BaseModel{ID: "user-003"}, Username: "王五", Phone: "13800138003", NickName: "王五"},
		{BaseModel: model.BaseModel{ID: "user-004"}, Username: "赵六", Phone: "13800138004", NickName: "赵六"},
	}

	for _, user := range users {
		db.Create(&user)
	}

	// 创建测试卡种
	cardTypes := []model.MembershipCardType{
		{
			Name:             "团课通卡",
			Category:         enum.MembershipCardCategoryTimes,
			Price:            200000,
			Times:            15,
			ValidityDays:     365,
			CanSetExpiry:     true,
			CanSelectStores:  true,
			CanAddSubCard:    false,
			CanSetDailyLimit: false,
			Status:           enum.MembershipCardTypeStatusEnabled,
		},
		{
			Name:              "瑜伽期限卡",
			Category:          enum.MembershipCardCategoryPeriod,
			Price:             150000,
			ValidityDays:      90,
			CanSetExpiry:      true,
			CanSelectStores:   true,
			CanAddSubCard:     false,
			CanSetDailyLimit:  true,
			DailyBookingLimit: 2,
			Status:            enum.MembershipCardTypeStatusEnabled,
		},
		{
			Name:             "共享储值卡",
			Category:         enum.MembershipCardCategoryBalance,
			Price:            100000,
			Amount:           100000,
			ValidityDays:     365,
			CanSetExpiry:     true,
			CanSelectStores:  true,
			CanAddSubCard:    true,
			CanSetDailyLimit: false,
			Status:           enum.MembershipCardTypeStatusEnabled,
		},
		{
			Name:     "停用测试卡",
			Category: enum.MembershipCardCategoryTimes,
			Price:    100000,
			Times:    10,
			Status:   enum.MembershipCardTypeStatusDisabled,
		},
	}

	for _, cardType := range cardTypes {
		db.Create(&cardType)
	}

	// 创建测试课程
	courses := []model.Course{
		{
			Name:     "瑜伽团课",
			Type:     enum.CourseTypeGroup,
			Capacity: 20,
			Price:    5000, // 50元
		},
		{
			Name:     "舞蹈课",
			Type:     enum.CourseTypeGroup,
			Capacity: 15,
			Price:    6000, // 60元
		},
		{
			Name:     "私教课",
			Type:     enum.CourseTypePrivate,
			Capacity: 1,
			Price:    20000, // 200元
		},
	}

	for _, course := range courses {
		db.Create(&course)
	}

	fmt.Println("✅ 测试数据初始化完成")
}

func testCardTypeManagement(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试会员卡种类管理功能...")

	// 测试创建新卡种
	newCardType := model.MembershipCardType{
		Name:             "测试新卡种",
		Category:         enum.MembershipCardCategoryTimes,
		Price:            50000,
		Times:            5,
		ValidityDays:     180,
		CanSetExpiry:     true,
		CanSelectStores:  true,
		CanAddSubCard:    false,
		CanSetDailyLimit: false,
		Status:           enum.MembershipCardTypeStatusEnabled,
	}

	if err := db.Create(&newCardType).Error; err != nil {
		fmt.Printf("❌ 创建新卡种失败: %v\n", err)
		return false
	}

	// 测试停用功能
	if err := db.Model(&newCardType).Update("status", enum.MembershipCardTypeStatusDisabled).Error; err != nil {
		fmt.Printf("❌ 停用卡种失败: %v\n", err)
		return false
	}

	// 验证停用卡种不出现在查询中
	var enabledCardTypes []model.MembershipCardType
	db.Where("status = ?", enum.MembershipCardTypeStatusEnabled).Find(&enabledCardTypes)

	for _, ct := range enabledCardTypes {
		if ct.ID == newCardType.ID {
			fmt.Println("❌ 停用的卡种仍出现在启用列表中")
			return false
		}
	}

	fmt.Println("✅ 卡种管理功能测试通过")
	return true
}

func testCardCreation(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试开卡功能...")

	// 测试线下支付开卡
	card1 := model.MembershipCard{
		UserID:         "user-001",
		CardTypeID:     1, // 团课通卡
		CardNumber:     "TC001",
		IsMainCard:     true,
		RemainingTimes: 15,
		TotalTimes:     15,
		StartDate:      time.Now(),
		EndDate:        time.Now().AddDate(0, 0, 365),
		PurchasePrice:  180000, // 1800元，打9折
		Discount:       0.9,
		Status:         enum.MembershipCardStatusNormal,
	}

	if err := db.Create(&card1).Error; err != nil {
		fmt.Printf("❌ 线下支付开卡失败: %v\n", err)
		return false
	}

	// 测试储值卡开卡
	card2 := model.MembershipCard{
		UserID:          "user-002",
		CardTypeID:      3, // 共享储值卡
		CardNumber:      "SC001",
		IsMainCard:      true,
		RemainingAmount: 100000,
		TotalAmount:     100000,
		StartDate:       time.Now(),
		EndDate:         time.Now().AddDate(0, 0, 365),
		PurchasePrice:   100000,
		Discount:        1.0,
		Status:          enum.MembershipCardStatusNormal,
	}

	if err := db.Create(&card2).Error; err != nil {
		fmt.Printf("❌ 储值卡开卡失败: %v\n", err)
		return false
	}

	fmt.Println("✅ 开卡功能测试通过")
	return true
}

func testSubCardFunctionality(db *gorm.DB, subCardService *service.SubCardService) bool {
	fmt.Println("\n🧪 测试副卡功能...")

	// 创建副卡
	subCard := model.MembershipCard{
		UserID:          "user-003",
		CardTypeID:      3, // 共享储值卡
		CardNumber:      "SC001-SUB1",
		MainCardID:      func() *uint { id := uint(2); return &id }(), // 主卡ID
		IsMainCard:      false,
		RemainingAmount: 0, // 副卡不独立计算余额
		StartDate:       time.Now(),
		EndDate:         time.Now().AddDate(0, 0, 365),
		Status:          enum.MembershipCardStatusNormal,
	}

	if err := db.Create(&subCard).Error; err != nil {
		fmt.Printf("❌ 创建副卡失败: %v\n", err)
		return false
	}

	// 测试主卡权限
	permission, err := subCardService.GetCardPermission("user-002", 2) // 主卡用户查看主卡
	if err != nil {
		fmt.Printf("❌ 获取主卡权限失败: %v\n", err)
		return false
	}

	if !permission.CanBook || !permission.CanViewAll || !permission.CanManageCards {
		fmt.Println("❌ 主卡权限不正确")
		return false
	}

	// 测试副卡权限
	permission, err = subCardService.GetCardPermission("user-003", subCard.ID) // 副卡用户查看副卡
	if err != nil {
		fmt.Printf("❌ 获取副卡权限失败: %v\n", err)
		return false
	}

	if !permission.CanBook || permission.CanViewAll || permission.CanManageCards {
		fmt.Println("❌ 副卡权限不正确")
		return false
	}

	// 测试主卡查看副卡权限
	permission, err = subCardService.GetCardPermission("user-002", subCard.ID) // 主卡用户查看副卡
	if err != nil {
		fmt.Printf("❌ 获取主卡查看副卡权限失败: %v\n", err)
		return false
	}

	if !permission.CanBook || !permission.CanViewAll || !permission.CanManageCards {
		fmt.Println("❌ 主卡查看副卡权限不正确")
		return false
	}

	fmt.Println("✅ 副卡功能测试通过")
	return true
}

func testDailyLimitFunctionality(db *gorm.DB, dailyLimitService *service.DailyLimitService) bool {
	fmt.Println("\n🧪 测试单日限制功能...")

	// 创建期限卡
	periodCard := model.MembershipCard{
		UserID:            "user-004",
		CardTypeID:        2, // 瑜伽期限卡
		CardNumber:        "PC001",
		IsMainCard:        true,
		StartDate:         time.Now(),
		EndDate:           time.Now().AddDate(0, 0, 90),
		DailyBookingLimit: 2,
		Status:            enum.MembershipCardStatusNormal,
	}

	if err := db.Create(&periodCard).Error; err != nil {
		fmt.Printf("❌ 创建期限卡失败: %v\n", err)
		return false
	}

	// 测试无预约时的限制检查
	check, err := dailyLimitService.CheckDailyLimit(periodCard.ID, "user-004", 1, time.Now())
	if err != nil {
		fmt.Printf("❌ 检查单日限制失败: %v\n", err)
		return false
	}

	if !check.CanBook || check.CurrentCount != 0 || check.LimitCount != 2 || check.RemainingCount != 2 {
		fmt.Printf("❌ 单日限制检查结果不正确: %+v\n", check)
		return false
	}

	// 创建第一个预约
	booking1 := model.Booking{
		UserID:           "user-004",
		CourseID:         1,
		MembershipCardID: periodCard.ID,
		Status:           1,
		CreatedAt:        time.Now(),
	}
	if err := db.Create(&booking1).Error; err != nil {
		fmt.Printf("❌ 创建预约失败: %v\n", err)
		return false
	}

	// 再次检查限制
	check, err = dailyLimitService.CheckDailyLimit(periodCard.ID, "user-004", 1, time.Now())
	if err != nil {
		fmt.Printf("❌ 检查单日限制失败: %v\n", err)
		return false
	}

	if !check.CanBook || check.CurrentCount != 1 || check.RemainingCount != 1 {
		fmt.Printf("❌ 第一次预约后限制检查不正确: %+v\n", check)
		return false
	}

	// 创建第二个预约
	booking2 := model.Booking{
		UserID:           "user-004",
		CourseID:         2,
		MembershipCardID: periodCard.ID,
		Status:           1,
		CreatedAt:        time.Now(),
	}
	if err := db.Create(&booking2).Error; err != nil {
		fmt.Printf("❌ 创建第二个预约失败: %v\n", err)
		return false
	}

	// 检查是否达到限制
	check, err = dailyLimitService.CheckDailyLimit(periodCard.ID, "user-004", 1, time.Now())
	if err != nil {
		fmt.Printf("❌ 检查单日限制失败: %v\n", err)
		return false
	}

	if check.CanBook || check.CurrentCount != 2 || check.RemainingCount != 0 {
		fmt.Printf("❌ 达到限制后检查不正确: %+v\n", check)
		return false
	}

	fmt.Println("✅ 单日限制功能测试通过")
	return true
}

func testCardTransfer(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试转卡功能...")

	// 执行转卡
	err := db.Model(&model.MembershipCard{}).Where("id = ?", 1).Update("user_id", "user-004").Error
	if err != nil {
		fmt.Printf("❌ 转卡失败: %v\n", err)
		return false
	}

	// 创建转卡交易记录
	transaction := model.MembershipCardTransaction{
		CardID:          1,
		TransactionType: enum.CardTransactionTypeTransfer,
		OperatorID:      "admin-001",
		OperatorName:    "管理员",
		Reason:          "会员卡转让",
		Remarks:         "从用户 user-001 转给用户 user-004，手续费50元",
		RealCost:        5000, // 手续费50元
	}

	if err := db.Create(&transaction).Error; err != nil {
		fmt.Printf("❌ 创建转卡交易记录失败: %v\n", err)
		return false
	}

	// 验证转卡结果
	var updatedCard model.MembershipCard
	if err := db.First(&updatedCard, 1).Error; err != nil {
		fmt.Printf("❌ 查询转卡后的卡片失败: %v\n", err)
		return false
	}

	if updatedCard.UserID != "user-004" {
		fmt.Println("❌ 转卡后用户ID不正确")
		return false
	}

	fmt.Println("✅ 转卡功能测试通过")
	return true
}

func testCardRecharge(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试充值功能...")

	// 充值次数卡
	originalTimes := 15
	rechargeAmount := 10

	err := db.Model(&model.MembershipCard{}).Where("id = ?", 1).Updates(map[string]interface{}{
		"remaining_times": originalTimes + rechargeAmount,
		"total_times":     originalTimes + rechargeAmount,
	}).Error

	if err != nil {
		fmt.Printf("❌ 充值失败: %v\n", err)
		return false
	}

	// 创建充值交易记录
	transaction := model.MembershipCardTransaction{
		CardID:          1,
		TransactionType: enum.CardTransactionTypeRecharge,
		TimesChange:     rechargeAmount,
		OperatorID:      "admin-001",
		OperatorName:    "管理员",
		Reason:          "线下充值",
		Remarks:         "充值10次，实收1000元",
		RealCost:        100000, // 1000元
	}

	if err := db.Create(&transaction).Error; err != nil {
		fmt.Printf("❌ 创建充值交易记录失败: %v\n", err)
		return false
	}

	// 验证充值结果
	var updatedCard model.MembershipCard
	if err := db.First(&updatedCard, 1).Error; err != nil {
		fmt.Printf("❌ 查询充值后的卡片失败: %v\n", err)
		return false
	}

	if updatedCard.RemainingTimes != originalTimes+rechargeAmount {
		fmt.Printf("❌ 充值后次数不正确，期望: %d，实际: %d\n", originalTimes+rechargeAmount, updatedCard.RemainingTimes)
		return false
	}

	fmt.Println("✅ 充值功能测试通过")
	return true
}

func testDeductionRules(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试扣减规则功能...")
	fmt.Println("⚠️ 注意：旧扣减规则系统已废弃，请使用灵活扣减系统")
	fmt.Println("✅ 跳过旧扣减规则测试")
	return true
}

func testCardUpgrade(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试升级功能...")

	// 升级会员卡类型
	newCardTypeID := uint(3) // 升级到共享储值卡

	err := db.Model(&model.MembershipCard{}).Where("id = ?", 1).Update("card_type_id", newCardTypeID).Error
	if err != nil {
		fmt.Printf("❌ 升级会员卡失败: %v\n", err)
		return false
	}

	// 创建升级交易记录
	transaction := model.MembershipCardTransaction{
		CardID:          1,
		TransactionType: enum.CardTransactionTypeUpgrade,
		OperatorID:      "admin-001",
		OperatorName:    "管理员",
		Reason:          "卡种升级",
		Remarks:         "从团课通卡升级到共享储值卡",
	}

	if err := db.Create(&transaction).Error; err != nil {
		fmt.Printf("❌ 创建升级交易记录失败: %v\n", err)
		return false
	}

	// 验证升级结果
	var upgradedCard model.MembershipCard
	if err := db.Preload("CardType").First(&upgradedCard, 1).Error; err != nil {
		fmt.Printf("❌ 查询升级后的卡片失败: %v\n", err)
		return false
	}

	if upgradedCard.CardTypeID != newCardTypeID {
		fmt.Printf("❌ 升级后卡种不正确，期望: %d，实际: %d\n", newCardTypeID, upgradedCard.CardTypeID)
		return false
	}

	fmt.Println("✅ 升级功能测试通过")
	return true
}

func testCardLeave(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试请假功能...")

	// 创建期限卡用于请假测试
	leaveCard := model.MembershipCard{
		UserID:     "user-001",
		CardTypeID: 2, // 瑜伽期限卡
		CardNumber: "LEAVE001",
		IsMainCard: true,
		StartDate:  time.Now(),
		EndDate:    time.Now().AddDate(0, 0, 90),
		Status:     enum.MembershipCardStatusNormal,
	}

	if err := db.Create(&leaveCard).Error; err != nil {
		fmt.Printf("❌ 创建期限卡失败: %v\n", err)
		return false
	}

	originalEndDate := leaveCard.EndDate
	leaveDays := 7 // 请假7天

	// 执行请假
	newEndDate := originalEndDate.AddDate(0, 0, leaveDays)
	err := db.Model(&leaveCard).Updates(map[string]interface{}{
		"end_date": newEndDate,
		"status":   enum.MembershipCardStatusFrozen, // 请假期间冻结
		"remark":   "请假7天",
	}).Error

	if err != nil {
		fmt.Printf("❌ 请假失败: %v\n", err)
		return false
	}

	// 验证请假结果
	var updatedCard model.MembershipCard
	if err := db.First(&updatedCard, leaveCard.ID).Error; err != nil {
		fmt.Printf("❌ 查询请假后的卡片失败: %v\n", err)
		return false
	}

	if updatedCard.EndDate.Format("2006-01-02") != newEndDate.Format("2006-01-02") {
		fmt.Println("❌ 请假后到期日期不正确")
		return false
	}

	if updatedCard.Status != enum.MembershipCardStatusFrozen {
		fmt.Println("❌ 请假后状态不正确")
		return false
	}

	fmt.Println("✅ 请假功能测试通过")
	return true
}

func testCardFreezeAndExtension(db *gorm.DB) bool {
	fmt.Println("\n🧪 测试停用和延期功能...")

	// 测试冻结功能
	err := db.Model(&model.MembershipCard{}).Where("id = ?", 2).Updates(map[string]interface{}{
		"status": enum.MembershipCardStatusFrozen,
		"remark": "用户申请冻结",
	}).Error

	if err != nil {
		fmt.Printf("❌ 冻结会员卡失败: %v\n", err)
		return false
	}

	// 验证冻结状态
	var frozenCard model.MembershipCard
	if err := db.First(&frozenCard, 2).Error; err != nil {
		fmt.Printf("❌ 查询冻结后的卡片失败: %v\n", err)
		return false
	}

	if frozenCard.Status != enum.MembershipCardStatusFrozen {
		fmt.Println("❌ 冻结后状态不正确")
		return false
	}

	// 测试解冻功能
	err = db.Model(&model.MembershipCard{}).Where("id = ?", 2).Updates(map[string]interface{}{
		"status": enum.MembershipCardStatusNormal,
		"remark": "解冻恢复使用",
	}).Error

	if err != nil {
		fmt.Printf("❌ 解冻会员卡失败: %v\n", err)
		return false
	}

	// 测试延期功能
	extensionDays := 30
	newEndDate := time.Now().AddDate(0, 0, extensionDays)

	err = db.Model(&model.MembershipCard{}).Where("id = ?", 2).Updates(map[string]interface{}{
		"end_date": newEndDate,
		"status":   enum.MembershipCardStatusNormal,
		"remark":   "延期30天",
	}).Error

	if err != nil {
		fmt.Printf("❌ 延期失败: %v\n", err)
		return false
	}

	// 验证延期结果
	var extendedCard model.MembershipCard
	if err := db.First(&extendedCard, 2).Error; err != nil {
		fmt.Printf("❌ 查询延期后的卡片失败: %v\n", err)
		return false
	}

	if extendedCard.EndDate.Format("2006-01-02") != newEndDate.Format("2006-01-02") {
		fmt.Println("❌ 延期后到期日期不正确")
		return false
	}

	fmt.Println("✅ 停用和延期功能测试通过")
	return true
}
