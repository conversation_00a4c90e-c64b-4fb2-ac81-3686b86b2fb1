package main

import (
	"fmt"
	"log"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func main() {
	// 数据库连接配置（请根据实际情况修改）
	dsn := "host=************ user=postgres password=your_password dbname=yogaga port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}

	// 修复会员卡类型表的序列
	if err := fixSequence(db, "membership_card_types", "membership_card_types_id_seq"); err != nil {
		log.Printf("修复会员卡类型序列失败: %v", err)
	} else {
		log.Println("✅ 会员卡类型序列修复成功")
	}

	// 可以添加其他表的修复
	tables := []struct {
		tableName    string
		sequenceName string
	}{
		{"roles", "roles_id_seq"},
		{"permissions", "permissions_id_seq"},
		{"menus", "menus_id_seq"},
		{"stores", "stores_id_seq"},
		{"course_categories", "course_categories_id_seq"},
	}

	for _, table := range tables {
		if err := fixSequence(db, table.tableName, table.sequenceName); err != nil {
			log.Printf("修复 %s 序列失败: %v", table.tableName, err)
		} else {
			log.Printf("✅ %s 序列修复成功", table.tableName)
		}
	}

	log.Println("🎉 所有序列修复完成！")
}

func fixSequence(db *gorm.DB, tableName, sequenceName string) error {
	// 1. 获取表中最大ID
	var maxID int64
	if err := db.Raw(fmt.Sprintf("SELECT COALESCE(MAX(id), 0) FROM %s", tableName)).Scan(&maxID).Error; err != nil {
		return fmt.Errorf("获取最大ID失败: %w", err)
	}

	// 2. 设置序列值为最大ID+1
	nextVal := maxID + 1
	if err := db.Exec(fmt.Sprintf("SELECT setval('%s', %d)", sequenceName, nextVal)).Error; err != nil {
		return fmt.Errorf("设置序列值失败: %w", err)
	}

	log.Printf("表 %s: 最大ID=%d, 序列值已设置为=%d", tableName, maxID, nextVal)
	return nil
}
