package middleware

import (
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DataAccessControl 数据访问控制中间件
func DataAccessControl(db *gorm.DB) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取当前用户ID
		userID, exists := c.Get("user_id")
		if !exists {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
			c.Abort()
			return
		}

		// 查询用户及其角色
		var user model.User
		err := db.Preload("Roles").Where("id = ?", userID).First(&user).Error
		if err != nil {
			log.Error("查询用户失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "用户查询失败"))
			c.Abort()
			return
		}

		// 将用户权限信息存储到上下文
		c.Set("user_roles", user.Roles)
		// 确定用户的主要平台（根据访问权限推断）
		var platform enum.Platform = enum.PlatformWechat // 默认微信平台
		if user.CanAccessPlatform(enum.PlatformAdmin) {
			platform = enum.PlatformAdmin
		}
		c.Set("user_platform", platform)

		c.Next()
	}
}

// CheckMemberAccess 检查会员访问权限
func CheckMemberAccess(db *gorm.DB, userID uint, memberID uint) bool {
	// 查询用户及其角色
	var user model.User
	err := db.Preload("Roles").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return false
	}

	// 超级管理员可以访问所有会员
	for _, role := range user.Roles {
		if role.Name == "admin" || role.Name == "超级管理员" {
			return true
		}
	}

	// 店长可以访问本门店所有会员（通过会员卡关联）
	for _, role := range user.Roles {
		if role.Name == "manager" || role.Name == "店长" {
			// 查询店长所在门店
			var storeCount int64
			db.Model(&model.MembershipCard{}).
				Joins("JOIN stores ON membership_cards.store_id = stores.id").
				Joins("JOIN resource_assignments ON stores.id = resource_assignments.resource_id").
				Where("membership_cards.user_id = ? AND resource_assignments.staff_id = ? AND resource_assignments.resource_type = 'store' AND resource_assignments.status = 1",
					memberID, userID).
				Count(&storeCount)

			if storeCount > 0 {
				return true
			}
		}
	}

	// 销售只能访问分配给自己的会员
	for _, role := range user.Roles {
		if role.Name == "sales" || role.Name == "销售" {
			var assignmentCount int64
			db.Model(&model.ResourceAssignment{}).
				Where("staff_id = ? AND resource_type = 'member' AND resource_id = ? AND status = 1",
					userID, memberID).
				Count(&assignmentCount)

			if assignmentCount > 0 {
				return true
			}
		}
	}

	return false
}

// GetAccessibleMembers 获取用户可访问的会员ID列表
func GetAccessibleMembers(db *gorm.DB, userID string) ([]string, error) {
	// 查询用户及其角色
	var user model.User
	err := db.Preload("Roles").Where("id = ?", userID).First(&user).Error
	if err != nil {
		return nil, err
	}

	var memberIDs []string

	// 超级管理员可以访问所有会员
	for _, role := range user.Roles {
		if role.Name == "admin" || role.Name == "超级管理员" {
			// 返回所有会员ID
			var allMembers []model.User
			db.Where("platform = ?", enum.PlatformWechat).Select("id").Find(&allMembers)
			for _, member := range allMembers {
				memberIDs = append(memberIDs, member.ID.String())
			}
			return memberIDs, nil
		}
	}

	// 店长可以访问本门店所有会员
	for _, role := range user.Roles {
		if role.Name == "manager" || role.Name == "店长" {
			// 店长权限逻辑：根据用户门店分配获取可访问的会员
			// 当前实现：暂时允许店长访问所有会员，后续可根据门店分配进行细化控制
			var allMembers []model.User
			db.Where("platform = ?", enum.PlatformWechat).Select("id").Find(&allMembers)
			for _, member := range allMembers {
				memberIDs = append(memberIDs, member.ID.String())
			}
			return memberIDs, nil
		}
	}

	// 销售只能访问分配给自己的会员
	for _, role := range user.Roles {
		if role.Name == "sales" || role.Name == "销售" {
			var assignedMembers []model.ResourceAssignment
			db.Model(&model.ResourceAssignment{}).
				Where("staff_id = ? AND resource_type = 'member' AND status = 1", userID).
				Select("resource_id").
				Find(&assignedMembers)

			for _, assignment := range assignedMembers {
				memberIDs = append(memberIDs, assignment.ResourceID)
			}
		}
	}

	// 去重
	uniqueIDs := make([]string, 0)
	seen := make(map[string]bool)
	for _, id := range memberIDs {
		if !seen[id] {
			uniqueIDs = append(uniqueIDs, id)
			seen[id] = true
		}
	}

	return uniqueIDs, nil
}

// IsAdmin 检查用户是否为超级管理员
func IsAdmin(roles []model.Role) bool {
	for _, role := range roles {
		if role.Name == "admin" || role.Name == "超级管理员" {
			return true
		}
	}
	return false
}

// IsManager 检查用户是否为店长
func IsManager(roles []model.Role) bool {
	for _, role := range roles {
		if role.Name == "manager" || role.Name == "店长" {
			return true
		}
	}
	return false
}

// IsSales 检查用户是否为销售
func IsSales(roles []model.Role) bool {
	for _, role := range roles {
		if role.Name == "sales" || role.Name == "销售" {
			return true
		}
	}
	return false
}
