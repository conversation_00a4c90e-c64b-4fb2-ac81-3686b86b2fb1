package service

import (
	"fmt"
	"time"

	"yogaga/internal/model"

	"gorm.io/gorm"
)

// FlexibleDeductionService 灵活扣减规则服务
type FlexibleDeductionService struct {
	db *gorm.DB
}

// NewFlexibleDeductionService 创建灵活扣减规则服务
func NewFlexibleDeductionService(db *gorm.DB) *FlexibleDeductionService {
	return &FlexibleDeductionService{db: db}
}

// DeductionContext 扣减上下文
type DeductionContext struct {
	CardTypeID     uint      `json:"card_type_id"`
	CourseID       uint      `json:"course_id"`
	CourseTypeCode string    `json:"course_type_code"`
	CourseName     string    `json:"course_name"`
	PeopleCount    int       `json:"people_count"`
	BookingTime    time.Time `json:"booking_time"`
	UserID         string    `json:"user_id"`
}

// DeductionResult 扣减结果
type DeductionResult struct {
	Rule            *model.FlexibleDeductionRule `json:"rule"`
	DeductionTimes  int                          `json:"deduction_times"`
	DeductionAmount int                          `json:"deduction_amount"`
	CanBook         bool                         `json:"can_book"`
	Message         string                       `json:"message"`
}

// FindMatchingRule 查找匹配的扣减规则
func (s *FlexibleDeductionService) FindMatchingRule(ctx DeductionContext) (*DeductionResult, error) {
	var rules []model.FlexibleDeductionRule

	// 构建查询条件
	query := s.db.Where("card_type_id = ? AND status = 1", ctx.CardTypeID)

	// 按优先级查找规则
	// 1. 精确匹配具体课程ID
	if ctx.CourseID > 0 {
		query = query.Where("(course_id = ? OR course_id = 0 OR course_id IS NULL)", ctx.CourseID)
	}

	// 2. 匹配课程类型代码
	if ctx.CourseTypeCode != "" {
		query = query.Where("(course_type_code = ? OR course_type_code = '' OR course_type_code IS NULL)", ctx.CourseTypeCode)
	}

	// 3. 匹配课程名称（支持模糊匹配）
	if ctx.CourseName != "" {
		query = query.Where("(course_name = '' OR course_name IS NULL OR course_name = ? OR ? LIKE CONCAT('%', course_name, '%'))",
			ctx.CourseName, ctx.CourseName)
	}

	// 按优先级排序：具体课程 > 课程类型 > 通用规则
	if err := query.Order("priority DESC, course_id DESC, course_type_code DESC, id ASC").Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("查询扣减规则失败: %w", err)
	}

	// 逐个验证规则
	for _, rule := range rules {
		if result := s.validateRule(&rule, ctx); result != nil {
			return result, nil
		}
	}

	return &DeductionResult{
		CanBook: false,
		Message: "未找到匹配的扣减规则",
	}, nil
}

// validateRule 验证单个规则
func (s *FlexibleDeductionService) validateRule(rule *model.FlexibleDeductionRule, ctx DeductionContext) *DeductionResult {
	// 1. 检查人数限制
	if !rule.IsValidForPeopleCount(ctx.PeopleCount) {
		return nil
	}

	// 2. 检查时间限制
	if !rule.IsValidForTime(ctx.BookingTime) {
		return nil
	}

	// 3. 检查预约时间限制
	if !s.checkBookingTimeLimit(rule, ctx.BookingTime) {
		return nil
	}

	// 4. 检查期限卡限制
	if err := s.checkPeriodLimits(rule, ctx); err != nil {
		return &DeductionResult{
			Rule:    rule,
			CanBook: false,
			Message: err.Error(),
		}
	}

	// 5. 计算扣减量
	times, amount := rule.CalculateDeduction(ctx.PeopleCount)

	return &DeductionResult{
		Rule:            rule,
		DeductionTimes:  times,
		DeductionAmount: amount,
		CanBook:         true,
		Message:         "规则匹配成功",
	}
}

// checkBookingTimeLimit 检查预约时间限制
func (s *FlexibleDeductionService) checkBookingTimeLimit(rule *model.FlexibleDeductionRule, bookingTime time.Time) bool {
	now := time.Now()

	// 检查最少提前预约时间
	if rule.MinBookingHours > 0 {
		minTime := now.Add(time.Duration(rule.MinBookingHours) * time.Hour)
		if bookingTime.Before(minTime) {
			return false
		}
	}

	// 检查最多提前预约天数
	if rule.MaxBookingDays > 0 {
		maxTime := now.AddDate(0, 0, rule.MaxBookingDays)
		if bookingTime.After(maxTime) {
			return false
		}
	}

	return true
}

// checkPeriodLimits 检查期限卡限制
func (s *FlexibleDeductionService) checkPeriodLimits(rule *model.FlexibleDeductionRule, ctx DeductionContext) error {
	if rule.DeductionType != "period" {
		return nil // 非期限卡无需检查
	}

	// 检查单日限制
	if rule.DailyLimit > 0 {
		if err := s.checkDailyLimit(rule, ctx); err != nil {
			return err
		}
	}

	// 检查单周限制
	if rule.WeeklyLimit > 0 {
		if err := s.checkWeeklyLimit(rule, ctx); err != nil {
			return err
		}
	}

	// 检查单月限制
	if rule.MonthlyLimit > 0 {
		if err := s.checkMonthlyLimit(rule, ctx); err != nil {
			return err
		}
	}

	return nil
}

// checkDailyLimit 检查单日限制
func (s *FlexibleDeductionService) checkDailyLimit(rule *model.FlexibleDeductionRule, ctx DeductionContext) error {
	today := ctx.BookingTime.Format("2006-01-02")

	var count int64
	query := s.db.Model(&model.Booking{}).
		Joins("JOIN membership_cards mc ON bookings.membership_card_id = mc.id").
		Where("mc.card_type_id = ? AND DATE(bookings.created_at) = ? AND bookings.status IN (1, 2)",
			ctx.CardTypeID, today)

	// 如果规则指定了课程类型，只统计该类型
	if rule.CourseTypeCode != "" {
		query = query.Joins("JOIN courses c ON bookings.course_id = c.id").
			Joins("JOIN course_type_configs ctc ON c.type_code = ctc.type_code").
			Where("ctc.type_code = ?", rule.CourseTypeCode)
	}

	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("查询单日预约数量失败: %w", err)
	}

	if int(count) >= rule.DailyLimit {
		return fmt.Errorf("已达到单日预约限制(%d节)", rule.DailyLimit)
	}

	return nil
}

// checkWeeklyLimit 检查单周限制
func (s *FlexibleDeductionService) checkWeeklyLimit(rule *model.FlexibleDeductionRule, ctx DeductionContext) error {
	// 获取本周的开始和结束时间
	weekStart := getWeekStart(ctx.BookingTime)
	weekEnd := weekStart.AddDate(0, 0, 7)

	var count int64
	query := s.db.Model(&model.Booking{}).
		Joins("JOIN membership_cards mc ON bookings.membership_card_id = mc.id").
		Where("mc.card_type_id = ? AND bookings.created_at >= ? AND bookings.created_at < ? AND bookings.status IN (1, 2)",
			ctx.CardTypeID, weekStart, weekEnd)

	if rule.CourseTypeCode != "" {
		query = query.Joins("JOIN courses c ON bookings.course_id = c.id").
			Joins("JOIN course_type_configs ctc ON c.type_code = ctc.type_code").
			Where("ctc.type_code = ?", rule.CourseTypeCode)
	}

	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("查询单周预约数量失败: %w", err)
	}

	if int(count) >= rule.WeeklyLimit {
		return fmt.Errorf("已达到单周预约限制(%d节)", rule.WeeklyLimit)
	}

	return nil
}

// checkMonthlyLimit 检查单月限制
func (s *FlexibleDeductionService) checkMonthlyLimit(rule *model.FlexibleDeductionRule, ctx DeductionContext) error {
	// 获取本月的开始和结束时间
	year, month, _ := ctx.BookingTime.Date()
	monthStart := time.Date(year, month, 1, 0, 0, 0, 0, ctx.BookingTime.Location())
	monthEnd := monthStart.AddDate(0, 1, 0)

	var count int64
	query := s.db.Model(&model.Booking{}).
		Joins("JOIN membership_cards mc ON bookings.membership_card_id = mc.id").
		Where("mc.card_type_id = ? AND bookings.created_at >= ? AND bookings.created_at < ? AND bookings.status IN (1, 2)",
			ctx.CardTypeID, monthStart, monthEnd)

	if rule.CourseTypeCode != "" {
		query = query.Joins("JOIN courses c ON bookings.course_id = c.id").
			Joins("JOIN course_type_configs ctc ON c.type_code = ctc.type_code").
			Where("ctc.type_code = ?", rule.CourseTypeCode)
	}

	if err := query.Count(&count).Error; err != nil {
		return fmt.Errorf("查询单月预约数量失败: %w", err)
	}

	if int(count) >= rule.MonthlyLimit {
		return fmt.Errorf("已达到单月预约限制(%d节)", rule.MonthlyLimit)
	}

	return nil
}

// getWeekStart 获取一周的开始时间（周一）
func getWeekStart(t time.Time) time.Time {
	weekday := int(t.Weekday())
	if weekday == 0 {
		weekday = 7 // 将周日从0改为7
	}

	days := weekday - 1 // 距离周一的天数
	weekStart := t.AddDate(0, 0, -days)

	// 设置为当天的00:00:00
	return time.Date(weekStart.Year(), weekStart.Month(), weekStart.Day(), 0, 0, 0, 0, weekStart.Location())
}

// GetSupportedCourseTypes 获取会员卡支持的课程类型
func (s *FlexibleDeductionService) GetSupportedCourseTypes(cardTypeID uint) ([]string, error) {
	var rules []model.FlexibleDeductionRule

	if err := s.db.Where("card_type_id = ? AND status = 1", cardTypeID).
		Distinct("course_type_code").
		Find(&rules).Error; err != nil {
		return nil, fmt.Errorf("查询支持的课程类型失败: %w", err)
	}

	var courseTypes []string
	for _, rule := range rules {
		if rule.CourseTypeCode != "" {
			courseTypes = append(courseTypes, rule.CourseTypeCode)
		}
	}

	return courseTypes, nil
}
