// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblCardsHistory = "tbl_cards_history"

// TblCardsHistory mapped from table <tbl_cards_history>
type TblCardsHistory struct {
	ID             int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ActID          int32     `gorm:"column:act_id;not null" json:"act_id"`
	AdminID        int32     `gorm:"column:admin_id;not null" json:"admin_id"`
	AdminTp        bool      `gorm:"column:admin_tp;not null;default:2" json:"admin_tp"`
	CardID         int32     `gorm:"column:card_id;not null" json:"card_id"`
	ShopID         int32     `gorm:"column:shop_id" json:"shop_id"`
	ShopIds        string    `gorm:"column:shop_ids" json:"shop_ids"`
	CardtpID       int32     `gorm:"column:cardtp_id" json:"cardtp_id"`
	CardTp         int32     `gorm:"column:card_tp;not null" json:"card_tp"`
	MemberID       int32     `gorm:"column:member_id;not null" json:"member_id"`
	SaleID         int32     `gorm:"column:sale_id;not null" json:"sale_id"`
	ClassID        int32     `gorm:"column:class_id;not null" json:"class_id"`
	OrderID        int32     `gorm:"column:order_id;not null" json:"order_id"`
	AmountActually float64   `gorm:"column:amount_actually;not null;default:0.00" json:"amount_actually"`
	Times          int32     `gorm:"column:times" json:"times"`
	Amount         float64   `gorm:"column:amount;not null;default:0.00" json:"amount"`
	DateStart      time.Time `gorm:"column:date_start" json:"date_start"`
	DateEnd        time.Time `gorm:"column:date_end" json:"date_end"`
	Note           string    `gorm:"column:note" json:"note"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete       int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	IsOpen         bool      `gorm:"column:is_open;not null" json:"is_open"`
	Notes          string    `gorm:"column:notes" json:"notes"`
	FromCardID     int32     `gorm:"column:from_card_id;not null" json:"from_card_id"`
	PayMethod      int32     `gorm:"column:pay_method;not null;default:1" json:"pay_method"`
	AmountDeduct   float64   `gorm:"column:amount_deduct;not null;default:0.00" json:"amount_deduct"`
}

// TableName TblCardsHistory's table name
func (*TblCardsHistory) TableName() string {
	return TableNameTblCardsHistory
}
