package dto

// FileType 文件类型枚举
type FileType string

const (
	FileTypeAvatar   FileType = "avatar"   // 头像
	FileTypeBanner   FileType = "banner"   // 横幅图
	FileTypeHeader   FileType = "header"   // 头图
	FileTypeDocument FileType = "document" // 文档
	FileTypeImage    FileType = "image"    // 普通图片
	FileTypeVideo    FileType = "video"    // 视频
	FileTypeAudio    FileType = "audio"    // 音频
	FileTypeOther    FileType = "other"    // 其他

	// 兼容旧系统的文件类型
	FileTypeCardsTP          FileType = "cards_tp"          // 卡片模板
	FileTypeClassesTimetable FileType = "classes_timetable" // 课程时间表
	FileTypeClassesTP        FileType = "classes_tp"        // 课程模板
	FileTypeClassesTPRotate  FileType = "classestp_rotate"  // 课程模板轮播
	FileTypeCoachRotate      FileType = "coach_rotate"      // 教练轮播
	FileTypeCoachVideo       FileType = "coach_video"       // 教练视频
	FileTypeCoaches          FileType = "coaches"           // 教练
	FileTypeHeadimg          FileType = "headimg"           // 头图（旧）
	FileTypeMembers          FileType = "members"           // 会员
	FileTypeRotate           FileType = "rotate"            // 轮播图
	FileTypeShopRotate       FileType = "shop_rotate"       // 商店轮播
	FileTypeShops            FileType = "shops"             // 商店
)

// AccessType 访问权限类型
type AccessType string

const (
	AccessTypePublic  AccessType = "public"  // 公共访问
	AccessTypePrivate AccessType = "private" // 私有访问
)

// FileUploadRequest 文件上传请求结构
type FileUploadRequest struct {
	FileType   FileType   `form:"file_type" json:"file_type" binding:"required"`     // 文件类型
	AccessType AccessType `form:"access_type" json:"access_type" binding:"required"` // 访问权限
	ObjectName string     `form:"object_name" json:"object_name"`                    // 对象名称（可选，如果不提供会自动生成）
}

// FileUploadResponse 文件上传响应结构
type FileUploadResponse struct {
	FileID      string `json:"file_id"`      // 文件ID
	UploadURL   string `json:"upload_url"`   // 上传后的访问URL
	DownloadURL string `json:"download_url"` // 固定下载链接（GitHub式）
	ObjectName  string `json:"object_name"`  // 对象存储中的键名
	FileType    string `json:"file_type"`    // 文件类型
	AccessType  string `json:"access_type"`  // 访问权限
}

// FileUploadDetailResponse 文件上传详细响应结构（包含更多信息）
type FileUploadDetailResponse struct {
	FileID      string `json:"file_id"`      // 文件ID
	DownloadURL string `json:"download_url"` // 固定下载链接（GitHub式）
	ObjectName  string `json:"object_name"`  // 对象存储中的键名
	BucketName  string `json:"bucket_name"`  // 存储桶名称
	Purpose     string `json:"purpose"`      // 文件用途
	FileSize    int64  `json:"file_size"`    // 文件大小
	ContentType string `json:"content_type"` // 文件类型
}

// PresignedUploadURLRequest 预签名上传URL请求
type PresignedUploadURLRequest struct {
	FileType   FileType   `form:"file_type" json:"file_type" binding:"required"`     // 文件类型
	AccessType AccessType `form:"access_type" json:"access_type" binding:"required"` // 访问权限
	ObjectName string     `form:"object_name" json:"object_name"`                    // 对象名称（可选）
	Expires    int        `form:"expires" json:"expires" example:"3600"`             // 过期时间（秒）
}

// PresignedDownloadURLRequest 预签名下载URL请求
type PresignedDownloadURLRequest struct {
	ObjectName string `form:"object_name" json:"object_name" binding:"required"` // 对象名称
	Filename   string `form:"filename" json:"filename"`                          // 下载时的文件名
	Expires    int    `form:"expires" json:"expires" example:"3600"`             // 过期时间（秒）
}

// GetFileTypePrefix 根据文件类型获取路径前缀
func (ft FileType) GetPrefix() string {
	switch ft {
	case FileTypeAvatar:
		return "avatars"
	case FileTypeBanner:
		return "banners"
	case FileTypeHeader:
		return "headers"
	case FileTypeDocument:
		return "documents"
	case FileTypeImage:
		return "images"
	case FileTypeVideo:
		return "videos"
	case FileTypeAudio:
		return "audios"
	// 兼容旧系统的文件类型 - 使用legacy前缀
	case FileTypeCardsTP:
		return "legacy/cards_tp"
	case FileTypeClassesTimetable:
		return "legacy/classes_timetable"
	case FileTypeClassesTP:
		return "legacy/classes_tp"
	case FileTypeClassesTPRotate:
		return "legacy/classestp_rotate"
	case FileTypeCoachRotate:
		return "legacy/coach_rotate"
	case FileTypeCoachVideo:
		return "legacy/coach_video"
	case FileTypeCoaches:
		return "legacy/coaches"
	case FileTypeHeadimg:
		return "legacy/headimg"
	case FileTypeMembers:
		return "legacy/members"
	case FileTypeRotate:
		return "legacy/rotate"
	case FileTypeShopRotate:
		return "legacy/shop_rotate"
	case FileTypeShops:
		return "legacy/shops"
	default:
		return "others"
	}
}

// GetFullPrefix 根据访问类型和文件类型获取完整路径前缀
func (ft FileType) GetFullPrefix(accessType AccessType) string {
	var accessPrefix string
	if accessType == AccessTypePublic {
		accessPrefix = "public"
	} else {
		accessPrefix = "private"
	}
	return accessPrefix + "/" + ft.GetPrefix()
}

// IsValid 验证文件类型是否有效
func (ft FileType) IsValid() bool {
	switch ft {
	case FileTypeAvatar, FileTypeBanner, FileTypeHeader, FileTypeDocument,
		FileTypeImage, FileTypeVideo, FileTypeAudio, FileTypeOther,
		// 兼容旧系统的文件类型
		FileTypeCardsTP, FileTypeClassesTimetable, FileTypeClassesTP, FileTypeClassesTPRotate,
		FileTypeCoachRotate, FileTypeCoachVideo, FileTypeCoaches, FileTypeHeadimg,
		FileTypeMembers, FileTypeRotate, FileTypeShopRotate, FileTypeShops:
		return true
	default:
		return false
	}
}

// IsValid 验证访问类型是否有效
func (at AccessType) IsValid() bool {
	switch at {
	case AccessTypePublic, AccessTypePrivate:
		return true
	default:
		return false
	}
}

// LegacyFileUploadRequest 兼容旧系统的文件上传请求结构
type LegacyFileUploadRequest struct {
	FileType         FileType `form:"file_type" json:"file_type" binding:"required"` // 文件类型（使用旧的类型）
	PreserveFilename bool     `form:"preserve_filename" json:"preserve_filename"`    // 是否保留原始文件名
	CustomPath       string   `form:"custom_path" json:"custom_path"`                // 自定义路径（可选）
}

// LegacyFileUploadResponse 兼容旧系统的文件上传响应结构
type LegacyFileUploadResponse struct {
	FileID      string `json:"file_id"`      // 文件ID
	UploadURL   string `json:"upload_url"`   // 上传后的访问URL
	DownloadURL string `json:"download_url"` // 固定下载链接（GitHub式）
	ObjectName  string `json:"object_name"`  // 对象存储中的键名
	FileType    string `json:"file_type"`    // 文件类型
	LegacyPath  string `json:"legacy_path"`  // 兼容旧系统的路径
}

// FileDownloadRequest 文件下载请求（用于重定向）
type FileDownloadRequest struct {
	ObjectName string `uri:"object_name" binding:"required"` // 对象名称
	Filename   string `query:"filename"`                     // 可选的下载文件名
}
