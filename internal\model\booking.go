package model

import (
	"time"
	"yogaga/internal/enum"
)

// Booking 预约记录
type Booking struct {
	BaseModel
	UserID           string             `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	CourseID         uint               `json:"course_id" gorm:"not null;comment:课程ID"`
	ScheduleID       uint               `json:"schedule_id" gorm:"comment:课程排期ID"`
	MembershipCardID uint               `json:"membership_card_id" gorm:"comment:使用的会员卡ID"`
	BookingNumber    string             `json:"booking_number" gorm:"type:varchar(50);uniqueIndex;not null;comment:预约单号"`
	PeopleCount      int                `json:"people_count" gorm:"type:int;default:1;comment:预约人数"`
	DeductTimes      int                `json:"deduct_times" gorm:"type:int;default:0;comment:扣除次数"`
	DeductAmount     int                `json:"deduct_amount" gorm:"type:int;default:0;comment:扣除金额(分)"`
	ActualAmount     int                `json:"actual_amount" gorm:"type:int;default:0;comment:实际消费金额(分)"`
	Amount           float64            `json:"amount" gorm:"type:decimal(10,2);default:0;comment:预约金额"`
	PaymentMethod    enum.PaymentMethod `json:"payment_method" gorm:"type:varchar(20);default:'membership_card';comment:支付方式"`
	Status           enum.BookingStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:已预约 2:已签到 3:已完成 4:已取消 5:未到课"`
	CancelTime       *time.Time         `json:"cancel_time" gorm:"comment:取消时间"`
	CancelReason     string             `json:"cancel_reason" gorm:"type:varchar(255);comment:取消原因"`
	CheckinTime      *time.Time         `json:"checkin_time" gorm:"comment:签到时间"`
	Rating           int                `json:"rating" gorm:"type:smallint;default:0;comment:评分 1-5星"`
	Comment          string             `json:"comment" gorm:"type:text;comment:评价内容"`

	// 关联
	User           User            `json:"user" gorm:"foreignKey:UserID"`
	Course         Course          `json:"course" gorm:"foreignKey:CourseID"`
	Schedule       *ClassSchedule  `json:"schedule,omitempty" gorm:"foreignKey:ScheduleID"`
	MembershipCard *MembershipCard `json:"membership_card,omitempty" gorm:"foreignKey:MembershipCardID"`
}

// BookingQueue 预约排队
type BookingQueue struct {
	BaseModel
	UserID      string           `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	CourseID    uint             `json:"course_id" gorm:"not null;comment:课程ID"`
	QueueNumber int              `json:"queue_number" gorm:"type:int;not null;comment:排队号"`
	Status      enum.QueueStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:排队中 2:已通知 3:已转预约 4:已取消"`
	NotifiedAt  *time.Time       `json:"notified_at" gorm:"comment:通知时间"`

	// 关联
	User   User   `gorm:"foreignKey:UserID" json:"user"`
	Course Course `gorm:"foreignKey:CourseID" json:"course"`
}

// BookingApplication 预约申请记录
type BookingApplication struct {
	BaseModel
	UserID           string                        `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	ScheduleID       uint                          `json:"schedule_id" gorm:"not null;comment:课程排期ID"`
	MembershipCardID uint                          `json:"membership_card_id" gorm:"comment:指定会员卡ID"`
	PeopleCount      int                           `json:"people_count" gorm:"type:int;default:1;comment:预约人数"`
	TaskID           string                        `json:"task_id" gorm:"type:varchar(100);uniqueIndex;not null;comment:异步任务ID"`
	Status           enum.BookingApplicationStatus `json:"status" gorm:"type:varchar(20);default:'pending';comment:处理状态 pending processing completed failed queued"`
	Result           string                        `json:"result" gorm:"type:text;comment:处理结果消息"`
	BookingID        uint                          `json:"booking_id" gorm:"comment:成功后的预约ID"`
	QueuePosition    int                           `json:"queue_position" gorm:"type:int;default:0;comment:排队位置 0表示未排队"`
	ErrorCode        string                        `json:"error_code" gorm:"type:varchar(50);comment:错误码"`

	// 关联
	User     *User          `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Schedule *ClassSchedule `json:"schedule,omitempty" gorm:"foreignKey:ScheduleID"`
	Booking  *Booking       `json:"booking,omitempty" gorm:"foreignKey:BookingID"`
}
