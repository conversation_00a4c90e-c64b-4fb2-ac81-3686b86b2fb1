# 提取API文档中的所有接口信息
param(
    [string]$ApiDocPath = "api-collections/admin/admin_api.json"
)

Write-Host "🔍 提取API文档中的所有接口..." -ForegroundColor Green
Write-Host ""

# 读取API文档
if (-not (Test-Path $ApiDocPath)) {
    Write-Host "❌ API文档不存在: $ApiDocPath" -ForegroundColor Red
    exit 1
}

try {
    $apiDoc = Get-Content $ApiDocPath -Raw | ConvertFrom-Json
    Write-Host "✅ JSON格式验证通过" -ForegroundColor Green
} catch {
    Write-Host "❌ JSON格式错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 提取所有接口
$allRoutes = @()

function Extract-Routes($folder, $folderPath = "") {
    $currentPath = if ($folderPath) { "$folderPath/$($folder.name)" } else { $folder.name }
    
    Write-Host "📁 处理文件夹: $currentPath" -ForegroundColor Yellow
    
    # 处理当前文件夹中的请求
    foreach ($request in $folder.requests) {
        $endpoint = $request.endpoint -replace "<<baseURL>>", ""
        $method = $request.method
        $name = $request.name
        
        # 提取请求体参数
        $bodyParams = ""
        if ($request.body -and $request.body.body) {
            try {
                $bodyJson = $request.body.body | ConvertFrom-Json
                $bodyParams = ($bodyJson.PSObject.Properties | ForEach-Object { $_.Name }) -join ", "
            } catch {
                $bodyParams = "解析失败"
            }
        }
        
        $route = @{
            Folder = $currentPath
            Name = $name
            Method = $method
            Endpoint = $endpoint
            BodyParams = $bodyParams
        }
        
        $script:allRoutes += $route
        Write-Host "  ✓ $method $endpoint - $name" -ForegroundColor Gray
    }
    
    # 递归处理子文件夹
    foreach ($subfolder in $folder.folders) {
        Extract-Routes $subfolder $currentPath
    }
}

# 处理所有文件夹
foreach ($folder in $apiDoc.folders) {
    Extract-Routes $folder
}

Write-Host ""
Write-Host "📊 提取结果统计:" -ForegroundColor Green
Write-Host "  总接口数: $($allRoutes.Count)" -ForegroundColor Cyan

# 按HTTP方法分组统计
$methodStats = $allRoutes | Group-Object Method | Sort-Object Name
foreach ($group in $methodStats) {
    Write-Host "  $($group.Name): $($group.Count)个" -ForegroundColor Gray
}

Write-Host ""

# 导出到文件
$outputFile = "scripts/api_doc_routes.json"
$allRoutes | ConvertTo-Json -Depth 3 | Out-File $outputFile -Encoding UTF8
Write-Host "📄 接口信息已导出到: $outputFile" -ForegroundColor Green

# 生成Markdown报告
$markdownFile = "scripts/api_doc_routes.md"
$markdown = @"
# API文档接口提取结果

## 📊 统计信息
- **总接口数**: $($allRoutes.Count)
- **文件夹数**: $($apiDoc.folders.Count)

### HTTP方法分布
$(foreach ($group in $methodStats) { "- **$($group.Name)**: $($group.Count)个`n" })

## 📋 详细接口列表

"@

# 按文件夹分组
$folderGroups = $allRoutes | Group-Object Folder | Sort-Object Name
foreach ($folderGroup in $folderGroups) {
    $markdown += "### $($folderGroup.Name)`n`n"
    
    foreach ($route in ($folderGroup.Group | Sort-Object Method, Endpoint)) {
        $markdown += "- ``$($route.Method) $($route.Endpoint)`` - $($route.Name)"
        if ($route.BodyParams) {
            $markdown += " (参数: $($route.BodyParams))"
        }
        $markdown += "`n"
    }
    $markdown += "`n"
}

$markdown | Out-File $markdownFile -Encoding UTF8
Write-Host "📄 Markdown报告已生成: $markdownFile" -ForegroundColor Green

Write-Host ""
Write-Host "🎯 下一步: 比较路由器与API文档的一致性" -ForegroundColor Cyan
