package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// AdminNotificationHandler 后台消息通知处理器
type AdminNotificationHandler struct {
	db *gorm.DB
}

// NewAdminNotificationHandler 创建后台消息通知处理器实例
func NewAdminNotificationHandler(db *gorm.DB) *AdminNotificationHandler {
	return &AdminNotificationHandler{
		db: db,
	}
}

// GetNotifications 获取后台消息列表
func (h *AdminNotificationHandler) GetNotifications(c *gin.Context) {
	// 获取查询参数
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	notificationType := c.Query("type")
	isReadStr := c.Query("is_read")

	// 构建查询条件
	query := h.db.Model(&model.AdminNotification{}).
		Preload("User").
		Preload("MembershipCard").
		Preload("MembershipCard.CardType")

	if notificationType != "" {
		query = query.Where("type = ?", notificationType)
	}

	if isReadStr != "" {
		isRead := cast.ToBool(isReadStr)
		query = query.Where("is_read = ?", isRead)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("统计消息总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 分页查询
	var notifications []model.AdminNotification
	if err := query.Order("priority DESC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&notifications).Error; err != nil {
		log.Error("查询消息列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     notifications,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetUnreadCount 获取未读消息数量
func (h *AdminNotificationHandler) GetUnreadCount(c *gin.Context) {
	var count int64
	err := h.db.Model(&model.AdminNotification{}).
		Where("is_read = false").
		Count(&count).Error

	if err != nil {
		log.Error("统计未读消息数量失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	response := map[string]interface{}{
		"unread_count": count,
	}

	code.AutoResponse(c, response, nil)
}

// MarkAsRead 标记消息为已读
func (h *AdminNotificationHandler) MarkAsRead(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("消息ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "消息ID格式错误"))
		return
	}

	// 更新消息状态
	if err := h.db.Model(&model.AdminNotification{}).
		Where("id = ?", id).
		Update("is_read", true).Error; err != nil {
		log.Error("标记消息已读失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "操作失败"))
		return
	}

	log.Info("标记消息已读成功", "id", id)
	response := dto.MessageResponse{Message: "操作成功"}
	code.AutoResponse(c, response, nil)
}

// MarkAllAsRead 标记所有消息为已读
func (h *AdminNotificationHandler) MarkAllAsRead(c *gin.Context) {
	err := h.db.Model(&model.AdminNotification{}).
		Where("is_read = false").
		Update("is_read", true).Error

	if err != nil {
		log.Error("标记所有消息已读失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "操作失败"))
		return
	}

	log.Info("标记所有消息已读成功")
	response := dto.MessageResponse{Message: "操作成功"}
	code.AutoResponse(c, response, nil)
}

// DeleteNotification 删除消息
func (h *AdminNotificationHandler) DeleteNotification(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("消息ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "消息ID格式错误"))
		return
	}

	// 删除消息
	if err := h.db.Delete(&model.AdminNotification{}, id).Error; err != nil {
		log.Error("删除消息失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "删除失败"))
		return
	}

	log.Info("删除消息成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// GetNotificationStats 获取消息统计
func (h *AdminNotificationHandler) GetNotificationStats(c *gin.Context) {
	// 统计各类型消息数量
	var stats []struct {
		Type  string `json:"type"`
		Count int64  `json:"count"`
	}

	err := h.db.Model(&model.AdminNotification{}).
		Select("type, COUNT(*) as count").
		Group("type").
		Find(&stats).Error

	if err != nil {
		log.Error("统计消息类型失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 统计未读消息数量
	var unreadCount int64
	err = h.db.Model(&model.AdminNotification{}).
		Where("is_read = false").
		Count(&unreadCount).Error

	if err != nil {
		log.Error("统计未读消息数量失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 统计今日新增消息数量
	var todayCount int64
	err = h.db.Model(&model.AdminNotification{}).
		Where("DATE(created_at) = CURDATE()").
		Count(&todayCount).Error

	if err != nil {
		log.Error("统计今日消息数量失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	response := map[string]interface{}{
		"type_stats":   stats,
		"unread_count": unreadCount,
		"today_count":  todayCount,
	}

	code.AutoResponse(c, response, nil)
}
