package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// CourseCategoryHandler 课程分类处理器
type CourseCategoryHandler struct {
	db *gorm.DB
}

// NewCourseCategoryHandler 创建课程分类处理器实例
func NewCourseCategoryHandler(db *gorm.DB) *CourseCategoryHandler {
	return &CourseCategoryHandler{
		db: db,
	}
}

// GetCourseCategories 获取课程分类列表
func (h *CourseCategoryHandler) GetCourseCategories(c *gin.Context) {
	var req dto.CourseCategoryListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.CourseCategory{})

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 名称搜索
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var categories []model.CourseCategory
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&categories).Error

	if err != nil {
		log.Error("查询课程分类失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程分类失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     categories,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetAllCourseCategories 获取所有启用的课程分类（简化版）
func (h *CourseCategoryHandler) GetAllCourseCategories(c *gin.Context) {
	// 查询所有启用的分类
	var categories []model.CourseCategory
	err := h.db.Where("status = 1").
		Order("sort_order ASC, created_at ASC").
		Find(&categories).Error

	if err != nil {
		log.Error("查询课程分类失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程分类失败"))
		return
	}

	code.AutoResponse(c, categories, nil)
}

// GetCourseCategory 获取课程分类详情
func (h *CourseCategoryHandler) GetCourseCategory(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程分类ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var category model.CourseCategory
	if err := h.db.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程分类不存在"))
		} else {
			log.Error("查询课程分类失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程分类失败"))
		}
		return
	}

	code.AutoResponse(c, category, nil)
}

// CreateCourseCategory 创建课程分类
func (h *CourseCategoryHandler) CreateCourseCategory(c *gin.Context) {
	var req dto.CreateCourseCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建课程分类参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 创建课程分类（扁平化结构）
	category := model.CourseCategory{
		Name:        req.Name,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		Status:      1, // 默认启用
	}

	if err := h.db.Create(&category).Error; err != nil {
		log.Error("创建课程分类失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建课程分类失败"))
		return
	}

	log.Info("创建课程分类成功", "id", category.ID, "name", category.Name)
	code.AutoResponse(c, category, nil)
}

// UpdateCourseCategory 更新课程分类
func (h *CourseCategoryHandler) UpdateCourseCategory(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程分类ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateCourseCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新课程分类参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询课程分类是否存在
	var category model.CourseCategory
	if err := h.db.First(&category, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程分类不存在"))
		} else {
			log.Error("查询课程分类失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程分类失败"))
		}
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.SortOrder != nil {
		updates["sort_order"] = *req.SortOrder
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if err := h.db.Model(&category).Updates(updates).Error; err != nil {
		log.Error("更新课程分类失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新课程分类失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.First(&category, id)

	log.Info("更新课程分类成功", "id", id)
	code.AutoResponse(c, category, nil)
}

// DeleteCourseCategory 删除课程分类
func (h *CourseCategoryHandler) DeleteCourseCategory(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程分类ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 检查是否有子分类
	var childCount int64
	h.db.Model(&model.CourseCategory{}).Where("parent_id = ?", id).Count(&childCount)
	if childCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该分类下还有子分类，无法删除"))
		return
	}

	// 检查是否有关联的课程
	var courseCount int64
	h.db.Model(&model.Course{}).Where("category_id = ?", id).Count(&courseCount)
	if courseCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该分类下还有课程，无法删除"))
		return
	}

	// 删除课程分类
	if err := h.db.Delete(&model.CourseCategory{}, id).Error; err != nil {
		log.Error("删除课程分类失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除课程分类失败"))
		return
	}

	log.Info("删除课程分类成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}
