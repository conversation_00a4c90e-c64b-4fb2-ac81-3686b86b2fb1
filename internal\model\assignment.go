package model

import (
	"time"

	"gorm.io/gorm"
)

// UserStoreAssignment 用户门店分配（员工在哪个门店工作）
type UserStoreAssignment struct {
	BaseModel
	UserID     string    `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	StoreID    uint      `json:"store_id" gorm:"not null;comment:门店ID"`
	AssignedBy string    `json:"assigned_by" gorm:"type:char(36);not null;comment:分配人ID"`
	AssignedAt time.Time `json:"assigned_at" gorm:"not null;comment:分配时间"`
	Status     int       `json:"status" gorm:"type:smallint;default:1;comment:状态 1:有效 0:无效"`
	Remark     string    `json:"remark" gorm:"type:varchar(500);comment:分配备注"`

	// 关联
	User           User  `json:"user" gorm:"foreignKey:UserID"`
	Store          Store `json:"store" gorm:"foreignKey:StoreID"`
	AssignedByUser User  `json:"assigned_by_user" gorm:"foreignKey:AssignedBy"`
}

// MemberSalesAssignment 会员销售分配（特定会员分配给特定销售）
type MemberSalesAssignment struct {
	BaseModel
	MemberID   string    `json:"member_id" gorm:"type:char(36);not null;comment:会员ID"`
	SalesID    string    `json:"sales_id" gorm:"type:char(36);not null;comment:销售ID"`
	StoreID    uint      `json:"store_id" gorm:"not null;comment:门店ID"`
	AssignedBy string    `json:"assigned_by" gorm:"type:char(36);not null;comment:分配人ID"`
	AssignedAt time.Time `json:"assigned_at" gorm:"not null;comment:分配时间"`
	Remark     string    `json:"remark" gorm:"type:varchar(500);comment:分配备注"`
	Status     int       `json:"status" gorm:"type:smallint;default:1;comment:状态 1:有效 0:无效"`

	// 关联
	Member         User  `json:"member" gorm:"foreignKey:MemberID"`
	Sales          User  `json:"sales" gorm:"foreignKey:SalesID"`
	Store          Store `json:"store" gorm:"foreignKey:StoreID"`
	AssignedByUser User  `json:"assigned_by_user" gorm:"foreignKey:AssignedBy"`
}

// TableName 指定表名
func (UserStoreAssignment) TableName() string {
	return "user_store_assignments"
}

func (MemberSalesAssignment) TableName() string {
	return "member_sales_assignments"
}

// IsActive 检查分配是否有效
func (usa *UserStoreAssignment) IsActive() bool {
	return usa.Status == 1
}

func (msa *MemberSalesAssignment) IsActive() bool {
	return msa.Status == 1
}

// GetUserStoreIDs 获取用户所在的门店ID列表
func GetUserStoreIDs(db *gorm.DB, userID string) ([]uint, error) {
	var assignments []UserStoreAssignment
	err := db.Where("user_id = ? AND status = 1", userID).Find(&assignments).Error
	if err != nil {
		return nil, err
	}

	var storeIDs []uint
	for _, assignment := range assignments {
		storeIDs = append(storeIDs, assignment.StoreID)
	}
	return storeIDs, nil
}

// GetSalesAssignedMembers 获取销售分配的会员ID列表
func GetSalesAssignedMembers(db *gorm.DB, salesID string) ([]string, error) {
	var assignments []MemberSalesAssignment
	err := db.Where("sales_id = ? AND status = 1", salesID).Find(&assignments).Error
	if err != nil {
		return nil, err
	}

	var memberIDs []string
	for _, assignment := range assignments {
		memberIDs = append(memberIDs, assignment.MemberID)
	}
	return memberIDs, nil
}

// GetStoreMembers 获取门店的所有会员ID列表（通过会员卡关联）
func GetStoreMembers(db *gorm.DB, storeIDs []uint) ([]string, error) {
	if len(storeIDs) == 0 {
		return []string{}, nil
	}

	var memberIDs []string
	err := db.Model(&MembershipCard{}).
		Where("store_id IN ?", storeIDs).
		Distinct("user_id").
		Pluck("user_id", &memberIDs).Error

	return memberIDs, err
}
