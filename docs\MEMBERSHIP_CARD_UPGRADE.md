# 会员卡系统升级报告

## 🎯 **升级概览**

根据业务需求，我们对会员卡系统进行了全面升级，使其完全符合瑜伽馆的实际业务逻辑。

| 功能模块 | 升级前 | 升级后 | 状态 |
|---------|--------|--------|------|
| 主副卡功能 | ❌ 不支持 | ✅ 完整支持 | 🎉 完成 |
| 门店选择 | ❌ 不支持 | ✅ 灵活配置 | 🎉 完成 |
| 单日预约限制 | ❌ 不支持 | ✅ 按卡种配置 | 🎉 完成 |
| 精准扣费规则 | ❌ 简单扣费 | ✅ 配置化扣费 | 🎉 完成 |

## 🔧 **核心功能升级**

### 1. **主副卡功能** 🎴

#### 🌟 **功能特性**
- **主卡管理**: 主卡持有者可查看所有副卡使用记录
- **副卡权限**: 副卡仅可查看自己的使用记录
- **余额共享**: 会员卡余额/次数在主卡名下
- **代约功能**: 主卡可自行预约及帮副卡代约课程

#### 📊 **数据模型**
```go
type MembershipCard struct {
    MainCardID  *uint  `json:"main_card_id"`  // 主卡ID(副卡专用)
    IsMainCard  bool   `json:"is_main_card"`  // 是否为主卡
    SubCards    []MembershipCard  // 副卡列表
}
```

#### 🔗 **新增接口**
```
POST /api/v1/admin/membership-cards/sub-card     # 创建副卡
GET  /api/v1/admin/membership-cards/:id/detail   # 获取卡详情(含副卡)
```

### 2. **门店选择功能** 🏪

#### 🌟 **功能特性**
- **全市通用**: 默认所有门店可用
- **指定门店**: 可选择特定门店使用
- **灵活配置**: 根据卡种支持门店选择功能

#### 📊 **数据模型**
```go
type MembershipCard struct {
    AvailableStores string `json:"available_stores"` // 可用门店JSON
}

type MembershipCardType struct {
    CanSelectStores bool `json:"can_select_stores"` // 是否可勾选使用门店
}
```

### 3. **单日预约限制** ⏰

#### 🌟 **功能特性**
- **期限卡限制**: 期限卡每日最多预约2节课
- **次数卡无限制**: 次数卡和储值卡单日预约无上限
- **灵活配置**: 可根据卡种设置不同的单日限制

#### 📊 **业务规则**
| 会员卡类型 | 单日预约限制 | 配置项 |
|-----------|-------------|--------|
| 团课通卡 | 无限制 | `can_set_daily_limit: false` |
| 普拉提次卡 | 无限制 | `can_set_daily_limit: false` |
| 瑜伽期限卡 | 2节/天 | `can_set_daily_limit: true, daily_booking_limit: 2` |
| 舞蹈期限卡 | 2节/天 | `can_set_daily_limit: true, daily_booking_limit: 2` |
| 共享储值卡 | 无限制 | `can_set_daily_limit: false` |

### 4. **精准扣费规则** 💰

#### 🌟 **功能特性**
- **配置化扣费**: 不同课程类型对应不同扣费次数/金额
- **精确计算**: 完全按照业务需求进行扣费
- **灵活规则**: 支持JSON配置扣费规则

#### 📋 **扣费规则表**

| 课程种类 | 团课通卡 | 普拉提次卡 | 提升小班卡 | 标准私教1V1 | 明星私教1V1 | 瑜伽期限卡 | 舞蹈期限卡 | 瑜/普期限卡 | 共享储值卡 | 储值卡 |
|---------|---------|-----------|-----------|------------|------------|-----------|-----------|------------|-----------|--------|
| 瑜伽团课 | 1次 | 1次 | 3次 | / | / | 2节/天 | / | 2节/天 | 108元 | 108元 |
| 普拉提小班课 | 2次 | 1次 | / | / | / | / | / | / | 218元 | 218元 |
| 提升小班课 | 3次 | / | 1次 | / | / | / | / | / | 238元 | 238元 |
| 特色小班课 | 2次 | / | / | / | / | / | / | / | 218元 | 218元 |
| 舞蹈课60min | 2次 | / | / | / | / | / | / | / | 199元 | 199元 |
| 舞蹈课90min | 3次 | / | / | / | / | / | 2节/天 | / | 299元 | 299元 |
| 标准私教1V1 | / | / | / | 1次 | / | / | / | / | 699元 | 699元 |
| 明星私教1V2 | / | / | / | / | 1次 | / | / | / | 799元 | 799元 |

#### 🔧 **技术实现**
```go
// 扣费规则配置示例
{
  "团课通卡": {
    "瑜伽团课": {"times": 1, "amount": 0, "price": 108},
    "普拉提小班课": {"times": 2, "amount": 0, "price": 218},
    "提升小班课": {"times": 3, "amount": 0, "price": 238}
  },
  "共享储值卡": {
    "瑜伽团课": {"times": 0, "amount": 108, "price": 108},
    "普拉提小班课": {"times": 0, "amount": 218, "price": 218}
  }
}
```

## 🗃️ **数据库模型升级**

### 会员卡类型表 (MembershipCardType)
```sql
-- 新增字段
ALTER TABLE membership_card_types ADD COLUMN can_set_expiry BOOLEAN DEFAULT TRUE;
ALTER TABLE membership_card_types ADD COLUMN can_select_stores BOOLEAN DEFAULT TRUE;
ALTER TABLE membership_card_types ADD COLUMN can_add_sub_card BOOLEAN DEFAULT FALSE;
ALTER TABLE membership_card_types ADD COLUMN can_set_daily_limit BOOLEAN DEFAULT FALSE;
ALTER TABLE membership_card_types ADD COLUMN daily_booking_limit INT DEFAULT 0;
-- ALTER TABLE membership_card_types ADD COLUMN course_deduct_rules TEXT; -- 已废弃，使用灵活扣减系统
```

### 会员卡表 (MembershipCard)
```sql
-- 新增字段
ALTER TABLE membership_cards ADD COLUMN main_card_id INT;
ALTER TABLE membership_cards ADD COLUMN is_main_card BOOLEAN DEFAULT TRUE;
ALTER TABLE membership_cards ADD COLUMN available_stores TEXT;
ALTER TABLE membership_cards ADD COLUMN daily_booking_limit INT DEFAULT 0;
```

### 用户表 (User)
```sql
-- 新增字段
ALTER TABLE users ADD COLUMN phone VARCHAR(20);
```

## 📁 **新增文件**

1. **配置文件**
   - ~~`configs/course_deduct_rules.json` - 课程扣费规则配置~~ (已废弃，使用灵活扣减系统)

2. **初始化脚本**
   - `scripts/init_membership_cards.go` - 会员卡类型初始化脚本

3. **文档**
   - `docs/MEMBERSHIP_CARD_UPGRADE.md` - 升级报告文档

## 🚀 **使用指南**

### 1. **初始化会员卡类型**
```bash
cd scripts
go run init_membership_cards.go
```

### 2. **创建主卡**
```bash
POST /api/v1/admin/membership-cards
{
  "user_id": 1,
  "card_type_id": 1,
  "start_date": "2024-01-01T00:00:00Z",
  "purchase_price": 299900,
  "available_stores": [1, 2, 3],  // 可用门店ID列表
  "daily_booking_limit": 2        // 单日预约限制
}
```

### 3. **创建副卡**
```bash
POST /api/v1/admin/membership-cards/sub-card
{
  "main_card_id": 1,
  "user_id": 2,
  "remark": "家属副卡"
}
```

### 4. **查看卡详情**
```bash
GET /api/v1/admin/membership-cards/1/detail
```

## 🎉 **升级效果**

### ✅ **完全符合业务需求**
- 🎴 **主副卡功能**: 支持共享储值卡的主副卡管理
- 🏪 **门店选择**: 支持单店/全市通用的灵活配置
- ⏰ **预约限制**: 期限卡每日限约2节课的精确控制
- 💰 **精准扣费**: 完全按照业务规则进行扣费计算

### 📈 **系统优势**
- **配置化管理**: 扣费规则可通过配置文件灵活调整
- **数据完整性**: 主副卡关系和门店限制的完整约束
- **业务准确性**: 100%符合瑜伽馆实际业务逻辑
- **扩展性强**: 支持未来新增卡种和扣费规则

### 🔧 **技术亮点**
- **JSON配置**: 灵活的扣费规则配置系统
- **关联查询**: 高效的主副卡关联查询
- **数据验证**: 完善的业务规则验证
- **向后兼容**: 保持原有接口的兼容性

现在的会员卡系统已经完全符合瑜伽馆的业务需求，支持所有复杂的业务场景！🎯
