package model

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"fmt"
	"strings"

	"yogaga/internal/enum"

	"github.com/lib/pq"
	"golang.org/x/crypto/argon2"
	"gorm.io/gorm"
)

// User represents a user account in the system.
type User struct {
	UUIDModel
	Username       string              `json:"username" gorm:"type:varchar(50);uniqueIndex:idx_username_access;not null;comment:用户名"`
	PlatformAccess enum.PlatformAccess `json:"platform_access" gorm:"type:int;uniqueIndex:idx_username_access;default:1;not null;comment:平台访问权限位掩码"`
	Password       string              `json:"-" gorm:"type:varchar(255);comment:密码"`
	Email          string              `json:"email" gorm:"type:varchar(100);uniqueIndex;comment:邮箱"`
	Phone          string              `json:"phone" gorm:"type:varchar(20);index;comment:手机号"`
	IsActive       bool                `json:"is_active" gorm:"default:true;comment:用户是否激活"`
	IsCoach        bool                `json:"is_coach" gorm:"default:false;comment:是否为教练"`

	// 微信小程序相关字段
	WeChatOpenID  *string     `json:"wechat_openid" gorm:"type:varchar(100);uniqueIndex;comment:微信小程序 OpenID"`
	WeChatUnionID *string     `json:"wechat_unionid" gorm:"type:varchar(100);index;comment:微信 UnionID"`
	NickName      string      `json:"nick_name" gorm:"type:varchar(100);comment:昵称"`
	AvatarID      *string     `json:"avatar_id" gorm:"type:char(36);comment:头像文件ID，关联files表"`
	Gender        enum.Gender `json:"gender" gorm:"type:smallint;default:0;comment:性别 0:未知 1:男 2:女"`
	Country       string      `json:"country" gorm:"type:varchar(50);comment:国家"`
	Province      string      `json:"province" gorm:"type:varchar(50);comment:省份"`

	// 教练相关字段（当用户角色为教练时使用）
	CoachDescription string         `json:"coach_description" gorm:"type:text;comment:教练简介"`
	CoachImageIDs    pq.StringArray `json:"coach_image_ids" gorm:"type:text[];comment:教练详情图片文件ID数组"`
	Experience       int            `json:"experience" gorm:"type:int;default:0;comment:从业年限"`
	Speciality       string         `json:"speciality" gorm:"type:varchar(255);comment:专业特长"`
	IsHomepage       bool           `json:"is_homepage" gorm:"default:false;comment:是否首页显示"`
	SortOrder        int            `json:"sort_order" gorm:"type:int;default:0;comment:排序"`
	City             string         `json:"city" gorm:"type:varchar(50);comment:城市"`
	Language         string         `json:"language" gorm:"type:varchar(20);comment:语言"`

	// 关联
	Avatar *File  `json:"avatar,omitempty" gorm:"foreignKey:AvatarID;references:ID"`
	Roles  []Role `json:"roles" gorm:"many2many:user_roles;"`
}

// CanAccessPlatform 检查用户是否可以访问指定平台
func (u *User) CanAccessPlatform(platform enum.Platform) bool {
	return u.PlatformAccess.HasAccess(platform)
}

// GetAccessiblePlatforms 获取用户可访问的平台列表
func (u *User) GetAccessiblePlatforms() []enum.Platform {
	return u.PlatformAccess.GetAccessiblePlatforms()
}

// HasRole 检查用户是否拥有指定角色ID
func (u *User) HasRole(roleID uint) bool {
	for _, role := range u.Roles {
		if role.ID == roleID {
			return true
		}
	}
	return false
}

// 身份判断方法 - 基于固定的角色ID，高效简洁
func (u *User) IsSuperAdmin() bool {
	return u.HasRole(1) // 超级管理员 ID = 1
}

func (u *User) IsDirector() bool {
	return u.HasRole(2) // 总监 ID = 2
}

func (u *User) IsManager() bool {
	return u.HasRole(3) // 店长 ID = 3
}

func (u *User) IsSales() bool {
	return u.HasRole(4) // 销售 ID = 4
}

func (u *User) IsCoachRole() bool {
	return u.HasRole(5) // 教练 ID = 5
}

func (u *User) IsReceptionist() bool {
	return u.HasRole(6) // 前台 ID = 6
}

// IsMember 检查是否是小程序会员用户
func (u *User) IsMember() bool {
	// 会员用户的特征：只能访问微信小程序，且不是员工
	return (u.PlatformAccess&enum.PlatformAccessWechat) != 0 && !u.IsStaff()
}

// 兼容性方法
func (u *User) IsAdmin() bool {
	return u.IsSuperAdmin() // 兼容旧代码
}

// SetPlatformAccess 设置用户平台访问权限
func (u *User) SetPlatformAccess(platforms ...enum.Platform) {
	u.PlatformAccess = enum.PlatformAccessNone
	for _, platform := range platforms {
		u.PlatformAccess = u.PlatformAccess.AddAccess(platform)
	}
}

// AddPlatformAccess 添加平台访问权限
func (u *User) AddPlatformAccess(platform enum.Platform) {
	if u.CanAccessPlatform(platform) {
		return // 已经有权限了
	}
	u.PlatformAccess = u.PlatformAccess.AddAccess(platform)
}

// RemovePlatformAccess 移除平台访问权限
func (u *User) RemovePlatformAccess(platform enum.Platform) {
	u.PlatformAccess = u.PlatformAccess.RemoveAccess(platform)
}

// SetCoachPlatformAccess 为教练设置双平台访问权限
func (u *User) SetCoachPlatformAccess() {
	u.PlatformAccess = enum.PlatformAccessWechat | enum.PlatformAccessAdmin // 可以访问小程序和管理端
}

// Argon2Params holds the parameters for Argon2 hashing.
type Argon2Params struct {
	memory      uint32
	iterations  uint32
	parallelism uint8
	saltLength  uint32
	keyLength   uint32
}

// DefaultParams provides recommended default parameters for Argon2id.
var DefaultParams = &Argon2Params{
	memory:      64 * 1024,
	iterations:  3,
	parallelism: 2,
	saltLength:  16,
	keyLength:   32,
}

// BeforeCreate is a GORM hook that automatically hashes the user's password using Argon2id.
func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	// 调用UUIDModel的BeforeCreate来生成UUID
	if err := u.UUIDModel.BeforeCreate(tx); err != nil {
		return err
	}

	// 只有当密码不为空时才进行哈希处理（微信用户可能没有密码）
	if u.Password != "" {
		hashedPassword, err := u.generatePasswordHash(u.Password)
		if err != nil {
			return err
		}
		u.Password = hashedPassword
	}

	// 为微信用户设置默认用户名（如果没有设置）
	if u.Username == "" && u.WeChatOpenID != nil && *u.WeChatOpenID != "" {
		openID := *u.WeChatOpenID
		if len(openID) >= 8 {
			u.Username = "wx_" + openID[len(openID)-8:] // 使用 OpenID 后8位作为默认用户名
		} else {
			u.Username = "wx_" + openID
		}
	}

	return
}

// generatePasswordHash creates an Argon2id hash of the password.
func (u *User) generatePasswordHash(password string) (string, error) {
	p := DefaultParams
	salt := make([]byte, p.saltLength)
	if _, err := rand.Read(salt); err != nil {
		return "", err
	}

	hash := argon2.IDKey([]byte(password), salt, p.iterations, p.memory, p.parallelism, p.keyLength)

	// Base64 encode the salt and hash for storage
	b64Salt := base64.RawStdEncoding.EncodeToString(salt)
	b64Hash := base64.RawStdEncoding.EncodeToString(hash)

	// Return a formatted string for storage
	// format: $argon2id$v=19$m=65536,t=3,p=2$somesalt$somehash
	return fmt.Sprintf("$argon2id$v=%d$m=%d,t=%d,p=%d$%s$%s", argon2.Version, p.memory, p.iterations, p.parallelism, b64Salt, b64Hash), nil
}

// CheckPassword compares a plaintext password with the stored Argon2id hash.
func (u *User) CheckPassword(password string) (bool, error) {
	parts := strings.Split(u.Password, "$")
	if len(parts) != 6 {
		return false, fmt.Errorf("invalid stored password format")
	}

	var p Argon2Params
	_, err := fmt.Sscanf(parts[3], "m=%d,t=%d,p=%d", &p.memory, &p.iterations, &p.parallelism)
	if err != nil {
		return false, err
	}

	salt, err := base64.RawStdEncoding.DecodeString(parts[4])
	if err != nil {
		return false, err
	}
	p.saltLength = uint32(len(salt))

	decodedHash, err := base64.RawStdEncoding.DecodeString(parts[5])
	if err != nil {
		return false, err
	}
	p.keyLength = uint32(len(decodedHash))

	comparisonHash := argon2.IDKey([]byte(password), salt, p.iterations, p.memory, p.parallelism, p.keyLength)

	return subtle.ConstantTimeCompare(decodedHash, comparisonHash) == 1, nil
}

// 组合身份判断方法
func (u *User) IsStaff() bool {
	// 员工：超级管理员、总监、店长、销售、教练、前台
	return u.IsSuperAdmin() || u.IsDirector() || u.IsManager() ||
		u.IsSales() || u.IsCoachRole() || u.IsReceptionist()
}

func (u *User) IsManagement() bool {
	// 管理层：超级管理员、总监、店长
	return u.IsSuperAdmin() || u.IsDirector() || u.IsManager()
}

func (u *User) CanAccessAdmin() bool {
	// 可以访问管理后台：所有员工
	return u.IsStaff()
}

func (u *User) CanAccessWechat() bool {
	// 可以访问小程序：教练和普通会员
	return (u.PlatformAccess & enum.PlatformAccessWechat) != 0
}

// GetRoleNames 获取用户角色名称列表
func (u *User) GetRoleNames() []string {
	var names []string
	for _, role := range u.Roles {
		names = append(names, role.Name)
	}
	return names
}

// GetHighestRoleID 获取用户最高权限角色ID（数字越小权限越高）
func (u *User) GetHighestRoleID() uint {
	if len(u.Roles) == 0 {
		return 0 // 无角色
	}

	minID := u.Roles[0].ID
	for _, role := range u.Roles {
		if role.ID < minID {
			minID = role.ID
		}
	}
	return minID
}

// GetWeChatOpenID 获取微信OpenID（处理指针类型）
func (u *User) GetWeChatOpenID() string {
	if u.WeChatOpenID == nil {
		return ""
	}
	return *u.WeChatOpenID
}

// HasWeChatOpenID 检查是否有微信OpenID
func (u *User) HasWeChatOpenID() bool {
	return u.WeChatOpenID != nil && *u.WeChatOpenID != ""
}

// SetWeChatOpenID 设置微信OpenID
func (u *User) SetWeChatOpenID(openID string) {
	if openID == "" {
		u.WeChatOpenID = nil
	} else {
		u.WeChatOpenID = &openID
	}
}
