package handler

import (
	"fmt"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// CourseHandler 课程处理器
type CourseHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewCourseHandler 创建课程处理器实例
func NewCourseHandler(db *gorm.DB, storage storage.Storage) *CourseHandler {
	return &CourseHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetCourses 获取课程列表
func (h *CourseHandler) GetCourses(c *gin.Context) {
	var req dto.CourseListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Course{}).Preload("Store").Preload("ClassRoom").Preload("CategoryRef")

	// 教练筛选 - 使用PostgreSQL数组查询
	if req.CoachID != "" {
		query = query.Where("? = ANY(coach_ids)", req.CoachID)
	}

	// 门店筛选
	if req.StoreID != "" {
		if storeIDInt := cast.ToUint(req.StoreID); storeIDInt > 0 {
			query = query.Where("store_id = ?", storeIDInt)
		}
	}

	// 教室筛选
	if req.ClassRoomID != "" {
		if classRoomIDInt := cast.ToUint(req.ClassRoomID); classRoomIDInt > 0 {
			query = query.Where("classroom_id = ?", classRoomIDInt)
		}
	}

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 类型筛选
	if req.Type != "" {
		if typeInt := cast.ToInt(req.Type); typeInt > 0 {
			query = query.Where("type = ?", typeInt)
		}
	}

	// 时间范围筛选
	if req.StartDate != "" {
		query = query.Where("start_time >= ?", req.StartDate)
	}

	if req.EndDate != "" {
		query = query.Where("start_time <= ?", req.EndDate)
	}

	// 课程级别筛选
	if req.Level != "" {
		if levelInt := cast.ToInt(req.Level); levelInt > 0 {
			query = query.Where("level = ?", levelInt)
		}
	}

	// 课程分类筛选
	if req.CategoryID != "" {
		if categoryIDInt := cast.ToUint(req.CategoryID); categoryIDInt > 0 {
			query = query.Where("category_id = ?", categoryIDInt)
		}
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var courses []model.Course
	err := query.Order("created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&courses).Error

	if err != nil {
		log.Error("查询课程失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     courses,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetCourse 获取课程详情
func (h *CourseHandler) GetCourse(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var course model.Course
	if err := h.db.Preload("Store").Preload("ClassRoom").Preload("CategoryRef").First(&course, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		} else {
			log.Error("查询课程失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		}
		return
	}

	code.AutoResponse(c, course, nil)
}

// CreateCourse 创建课程（分离式创建，只创建基本信息）
func (h *CourseHandler) CreateCourse(c *gin.Context) {
	var req dto.CreateCourseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建课程参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证课程时长合理性
	if req.Duration < 15 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程时长不能少于15分钟"))
		return
	}
	if req.Duration > 300 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程时长不能超过5小时"))
		return
	}

	// 验证门店是否存在
	var store model.Store
	if err := h.db.First(&store, req.StoreID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		return
	}

	// 检查同一门店下课程名称是否重复
	var nameCount int64
	h.db.Model(&model.Course{}).
		Where("store_id = ? AND name = ? AND status != 0", req.StoreID, req.Name).
		Count(&nameCount)
	if nameCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该门店下已存在同名课程"))
		return
	}

	// 验证课程分类是否存在
	if req.CategoryID > 0 {
		var category model.CourseCategory
		if err := h.db.First(&category, req.CategoryID).Error; err != nil {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程分类不存在"))
			return
		}
	}

	// 创建课程（分离式创建，只包含基本信息）
	course := model.Course{
		Name:        req.Name,
		Description: req.Description,
		Duration:    req.Duration,
		Capacity:    req.Capacity,
		Price:       float64(req.Price),
		Level:       req.Level,
		CategoryID:  req.CategoryID,
		StoreID:     req.StoreID,
		Type:        enum.CourseType(req.Type),
		Status:      enum.CourseStatusNormal, // 默认启用
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Create(&course).Error; err != nil {
		tx.Rollback()
		log.Error("创建课程失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建课程失败"))
		return
	}

	// 注意：扣减规则现在使用灵活扣减系统，不再在课程创建时直接创建
	// 需要通过灵活扣减管理接口单独配置

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交创建课程事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseError, "创建课程失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Store").Preload("CategoryRef").First(&course, course.ID)

	log.Info("创建课程成功", "id", course.ID, "name", course.Name)
	code.AutoResponse(c, course, nil)
}

// UpdateCourse 更新课程
func (h *CourseHandler) UpdateCourse(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateCourseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新课程参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询课程
	var course model.Course
	if err := h.db.First(&course, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		return
	}

	// 检查课程是否已开始（开始后不允许修改）
	if time.Now().After(course.StartTime) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程已开始，无法修改"))
		return
	}

	// 分离式更新：不包含教练验证，教练在排课时指定

	// 验证门店是否存在
	if req.StoreID != nil {
		var store model.Store
		if err := h.db.First(&store, *req.StoreID).Error; err != nil {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
			return
		}
	}

	// 验证教室
	if req.ClassRoomID != nil {
		storeID := course.StoreID
		if req.StoreID != nil {
			storeID = *req.StoreID
		}

		var classroom model.ClassRoom
		if err := h.db.Where("id = ? AND store_id = ?", *req.ClassRoomID, storeID).First(&classroom).Error; err != nil {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教室不存在或不属于该门店"))
			return
		}

		// 检查教室容量
		capacity := course.Capacity
		if req.Capacity != nil {
			capacity = *req.Capacity
		}
		if capacity > classroom.Capacity {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, fmt.Sprintf("课程容量不能超过教室容量(%d人)", classroom.Capacity)))
			return
		}
	}

	// 分离式更新：不包含时间和教练相关的验证

	// 更新字段（分离式更新，只包含基本信息）
	updates := make(map[string]any)
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.Duration != nil {
		updates["duration"] = *req.Duration
	}
	if req.Capacity != nil {
		updates["capacity"] = *req.Capacity
	}
	if req.Price != nil {
		updates["price"] = float64(*req.Price)
	}
	if req.StoreID != nil {
		updates["store_id"] = *req.StoreID
	}
	if req.Type != nil {
		updates["type"] = *req.Type
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	// 处理多教练更新
	if len(req.CoachIDs) > 0 {
		// 验证所有教练是否存在且为活跃状态
		var validCoaches []model.User
		err := h.db.Where("id IN ? AND is_coach = ? AND is_active = ?", req.CoachIDs, true, true).Find(&validCoaches).Error
		if err != nil {
			log.Error("验证教练失败", "coach_ids", req.CoachIDs, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "验证教练失败"))
			return
		}

		if len(validCoaches) != len(req.CoachIDs) {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "部分教练不存在或未激活"))
			return
		}

		// 设置教练信息
		updates["coach_ids"] = pq.StringArray(req.CoachIDs) // 转换为pq.StringArray
	}

	if err := h.db.Model(&course).Updates(updates).Error; err != nil {
		log.Error("更新课程失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新课程失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("Store").Preload("ClassRoom").Preload("CategoryRef").First(&course, id)

	log.Info("更新课程成功", "id", id)
	code.AutoResponse(c, course, nil)
}

// DeleteCourse 删除课程
func (h *CourseHandler) DeleteCourse(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 检查是否有关联的排课
	var scheduleCount int64
	h.db.Model(&model.ClassSchedule{}).Where("course_id = ?", id).Count(&scheduleCount)
	if scheduleCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该课程下还有排课记录，无法删除"))
		return
	}

	// 删除课程
	if err := h.db.Delete(&model.Course{}, id).Error; err != nil {
		log.Error("删除课程失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除课程失败"))
		return
	}

	log.Info("删除课程成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// ScheduleCourses 课程排课（基于已创建的课程模板进行排课）
func (h *CourseHandler) ScheduleCourses(c *gin.Context) {
	var req dto.CourseSchedulingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定排课参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 解析开始和结束日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		log.Error("解析开始日期失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		log.Error("解析结束日期失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}

	// 解析开始时间
	startTime, err := time.Parse("15:04", req.StartTime)
	if err != nil {
		log.Error("解析开始时间失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始时间格式错误"))
		return
	}

	// 验证日期范围
	if endDate.Before(startDate) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期不能早于开始日期"))
		return
	}

	// 获取所有课程模板信息
	courseTemplates := make(map[string]*model.Course)
	for _, schedule := range req.WeeklySchedules {
		if _, exists := courseTemplates[schedule.CourseID]; !exists {
			// 将字符串CourseID转换为整数
			courseID := cast.ToUint(schedule.CourseID)
			if courseID == 0 {
				log.Error("课程ID格式错误", "course_id", schedule.CourseID)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, fmt.Sprintf("课程ID格式错误: %s", schedule.CourseID)))
				return
			}

			var course model.Course
			if err := h.db.Where("id = ? AND store_id = ?", courseID, req.StoreID).First(&course).Error; err != nil {
				log.Error("课程模板不存在", "course_id", schedule.CourseID, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, fmt.Sprintf("课程模板 %s 不存在", schedule.CourseID)))
				return
			}
			courseTemplates[schedule.CourseID] = &course
		}
	}

	// 生成课程实例
	var scheduledCourses []model.Course
	var conflicts []dto.ConflictInfo

	// 遍历日期范围
	for date := startDate; !date.After(endDate); date = date.AddDate(0, 0, 1) {
		weekday := int(date.Weekday()) // 0=Sunday, 1=Monday, ..., 6=Saturday

		// 检查是否有该星期的排课安排
		for _, schedule := range req.WeeklySchedules {
			if schedule.Weekday == weekday {
				template := courseTemplates[schedule.CourseID]

				// 计算具体的开始和结束时间
				courseStartTime := time.Date(date.Year(), date.Month(), date.Day(),
					startTime.Hour(), startTime.Minute(), 0, 0, date.Location())
				courseEndTime := courseStartTime.Add(time.Duration(template.Duration) * time.Minute)

				// 检查教练冲突（使用PostgreSQL数组查询）
				hasConflict := false
				for _, coachID := range schedule.CoachIDs {
					var conflictCount int64
					h.db.Model(&model.Course{}).
						Where("store_id = ?", req.StoreID).
						Where("? = ANY(coach_ids)", coachID).
						Where("(start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?)",
							courseEndTime, courseStartTime, courseStartTime, courseEndTime).
						Count(&conflictCount)

					if conflictCount > 0 {
						conflicts = append(conflicts, dto.ConflictInfo{
							Date:         date.Format("2006-01-02"),
							Time:         fmt.Sprintf("%s-%s", courseStartTime.Format("15:04"), courseEndTime.Format("15:04")),
							ConflictType: "coach_busy",
							Details:      fmt.Sprintf("教练在该时间段已有其他课程安排"),
						})
						hasConflict = true
						break
					}
				}

				if hasConflict {
					continue
				}

				// 检查教室冲突（如果指定了教室）
				if schedule.ClassRoomID != nil {
					var conflictCount int64
					h.db.Model(&model.Course{}).
						Where("classroom_id = ? AND store_id = ?", *schedule.ClassRoomID, req.StoreID).
						Where("(start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?)",
							courseEndTime, courseStartTime, courseStartTime, courseEndTime).
						Count(&conflictCount)

					if conflictCount > 0 {
						conflicts = append(conflicts, dto.ConflictInfo{
							Date:         date.Format("2006-01-02"),
							Time:         fmt.Sprintf("%s-%s", courseStartTime.Format("15:04"), courseEndTime.Format("15:04")),
							ConflictType: "classroom_occupied",
							Details:      "指定教室在该时间段已被占用",
						})
						continue
					}
				}

				// 创建课程实例
				course := model.Course{
					Name:        template.Name,
					Description: template.Description,
					StartTime:   courseStartTime,
					EndTime:     courseEndTime,
					Duration:    template.Duration,
					Capacity:    template.Capacity,
					Price:       template.Price,
					Level:       template.Level,
					CategoryID:  template.CategoryID,
					StoreID:     req.StoreID,
					ClassRoomID: schedule.ClassRoomID,
					Type:        template.Type,
					Status:      enum.CourseStatusPublished,
				}

				// 设置多教练
				if len(schedule.CoachIDs) > 0 {
					course.CoachIDs = schedule.CoachIDs // 使用pq.StringArray类型
				} else {
					log.Error("排课配置缺少教练信息", "schedule", schedule)
					code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "排课配置缺少教练信息"))
					return
				}

				scheduledCourses = append(scheduledCourses, course)
			}
		}
	}

	// 如果有冲突，返回冲突信息
	if len(conflicts) > 0 {
		response := dto.BatchCreateConflictResponse{
			Message:   "部分课程排课失败，存在冲突",
			Conflicts: conflicts,
		}
		code.AutoResponse(c, response, code.NewErrCodeMsg(code.BusinessError, "存在时间冲突"))
		return
	}

	// 批量创建课程实例
	if len(scheduledCourses) > 0 {
		if err := h.db.Transaction(func(tx *gorm.DB) error {
			// 创建课程
			if err := tx.Create(&scheduledCourses).Error; err != nil {
				log.Error("数据库创建课程失败", "courses_count", len(scheduledCourses), "error", err)
				// 打印第一个课程的详细信息用于调试
				if len(scheduledCourses) > 0 {
					log.Error("第一个课程详情", "course", scheduledCourses[0])
				}
				return err
			}

			// 教练信息已经在创建课程时设置，无需额外处理

			return nil
		}); err != nil {
			log.Error("批量创建课程失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseError, "创建课程失败"))
			return
		}
	}

	// 返回成功响应
	response := dto.CourseSchedulingResponse{
		Message:          "课程排课成功",
		ScheduledCount:   len(scheduledCourses),
		ScheduledCourses: scheduledCourses,
	}

	code.AutoResponse(c, response, nil)
}

// BatchCreateCourses 批量创建课程（一步完成：课程创建+排课）
func (h *CourseHandler) BatchCreateCourses(c *gin.Context) {
	// 创建BatchCourseHandler实例并调用其方法
	batchHandler := NewBatchCourseHandler(h.db)
	batchHandler.BatchCreateCourses(c)
}

// BookCourse 预约课程（已废弃，请使用统一预订系统）
func (h *CourseHandler) BookCourse(c *gin.Context) {
	log.Warn("使用了已废弃的预订接口，请使用统一预订系统")

	response := dto.MessageResponse{
		Message: "此接口已废弃，请使用统一预订接口：POST /api/v1/app/bookings",
	}
	code.AutoResponse(c, response, code.NewErrCodeMsg(code.InvalidParams, "接口已废弃"))
}

// GetCourseSupportedCardTypes 获取课程支持的会员卡类型
// 注意：此功能现在通过灵活扣减系统实现，请使用相关接口查询
func (h *CourseHandler) GetCourseSupportedCardTypes(c *gin.Context) {
	log.Warn("使用了已废弃的获取课程支持会员卡类型接口")

	response := dto.MessageResponse{
		Message: "此接口已废弃，请使用灵活扣减系统相关接口查询课程支持的会员卡类型",
	}

	code.AutoResponse(c, response, nil)
}
