#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试交易记录的详细信息，验证会员卡信息和操作员名称
"""

import requests
import json

class TransactionDetailTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_recent_cards_with_transactions(self) -> list:
        """获取最近有交易记录的会员卡"""
        try:
            params = {"page": 1, "page_size": 10}
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    cards = result.get('data', {}).get('list', [])
                    # 返回最近创建的几张卡
                    return cards[:5]
            return []
        except Exception as e:
            print(f"❌ 获取会员卡异常: {e}")
            return []
    
    def check_transaction_details(self, card_id: int, card_number: str):
        """检查指定会员卡的交易记录详情"""
        print(f"\n📋 检查会员卡 {card_number} (ID: {card_id}) 的交易记录详情...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    transactions = result.get('data', {}).get('list', [])
                    print(f"✅ 找到 {len(transactions)} 条交易记录")
                    
                    if len(transactions) == 0:
                        print("⚠️ 该卡片没有交易记录")
                        return True
                    
                    print("\n📝 交易记录详细信息:")
                    print("-" * 100)
                    print(f"{'时间':<20} {'类型':<10} {'操作员':<15} {'会员卡':<20} {'原因':<15} {'备注'}")
                    print("-" * 100)
                    
                    issues_found = []
                    
                    for i, transaction in enumerate(transactions):
                        # 基本信息
                        created_at = transaction.get('created_at', 'N/A')[:19]  # 只取日期时间部分
                        trans_type = transaction.get('transaction_type', 'N/A')
                        operator_name = transaction.get('operator_name', 'N/A')
                        reason = transaction.get('reason', 'N/A')
                        remarks = transaction.get('remarks', 'N/A')
                        
                        # 会员卡信息
                        card_info = transaction.get('card', {})
                        if card_info:
                            card_display = f"{card_info.get('card_number', 'N/A')}"
                            card_type_info = card_info.get('card_type', {})
                            if card_type_info:
                                card_display += f" ({card_type_info.get('name', 'N/A')})"
                        else:
                            card_display = "❌ 无卡片信息"
                            issues_found.append(f"交易记录 {i+1}: 缺少会员卡信息")
                        
                        # 检查操作员名称
                        if operator_name in ['N/A', '<nil>', '', None]:
                            operator_display = "❌ 无操作员"
                            issues_found.append(f"交易记录 {i+1}: 操作员名称为空")
                        else:
                            operator_display = operator_name
                        
                        # 显示记录
                        print(f"{created_at:<20} {trans_type:<10} {operator_display:<15} {card_display:<20} {reason:<15} {remarks[:30]}")
                        
                        # 显示详细的JSON信息（用于调试）
                        if i == 0:  # 只显示第一条记录的详细信息
                            print(f"\n🔍 第一条记录的详细JSON信息:")
                            print(f"  operator_name: '{operator_name}'")
                            print(f"  operator_id: '{transaction.get('operator_id', 'N/A')}'")
                            print(f"  card: {json.dumps(card_info, indent=2, ensure_ascii=False) if card_info else 'null'}")
                            
                            # 检查 operator 关联信息
                            operator_info = transaction.get('operator', {})
                            if operator_info:
                                print(f"  operator (关联): {json.dumps(operator_info, indent=2, ensure_ascii=False)}")
                            else:
                                print(f"  operator (关联): null")
                    
                    print("-" * 100)
                    
                    # 总结问题
                    if issues_found:
                        print(f"\n⚠️ 发现 {len(issues_found)} 个问题:")
                        for issue in issues_found:
                            print(f"  - {issue}")
                        return False
                    else:
                        print(f"\n✅ 所有交易记录信息完整")
                        return True
                        
                else:
                    print(f"❌ 获取交易记录失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取交易记录失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取交易记录异常: {e}")
            return False
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始测试交易记录详细信息...")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取最近的会员卡
        cards = self.get_recent_cards_with_transactions()
        if not cards:
            print("❌ 没有找到会员卡")
            return False
        
        print(f"\n📋 找到 {len(cards)} 张会员卡，开始检查交易记录...")
        
        # 3. 检查每张卡的交易记录
        all_success = True
        for card in cards:
            card_id = card.get('id')
            card_number = card.get('card_number')
            
            success = self.check_transaction_details(card_id, card_number)
            if not success:
                all_success = False
        
        print("\n" + "=" * 80)
        print("📊 测试结果:")
        if all_success:
            print("✅ 所有交易记录信息完整，包含会员卡信息和操作员名称")
        else:
            print("❌ 部分交易记录存在问题")
        
        return all_success

def main():
    """主函数"""
    tester = TransactionDetailTester()
    success = tester.run_test()
    
    if success:
        print("\n🏆 交易记录详细信息测试成功！")
        exit(0)
    else:
        print("\n💥 交易记录详细信息测试失败！")
        exit(1)

if __name__ == "__main__":
    main()
