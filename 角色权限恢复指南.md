# 角色权限恢复指南

## 🎯 问题描述

前端不小心删除了一些`role_permissions`表的分配，需要恢复到初始化状态。

## 🛠️ 解决方案

我已经为你准备了两种恢复方法：

### 方法1：Python脚本（推荐）

**使用步骤**：
1. 安装Python依赖：
   ```bash
   pip install psycopg2-binary pyyaml
   ```
2. 在项目根目录运行：
   ```bash
   python restore_role_permissions.py
   ```

**优点**：
- ✅ 自动化执行
- ✅ 包含错误处理和事务
- ✅ 显示详细进度
- ✅ 自动验证结果
- ✅ 跨平台兼容

### 方法2：直接执行SQL

**使用步骤**：
```bash
psql -h ************ -p 5432 -U yogaga -d yogaga -f restore_role_permissions.sql
```

**输入密码**：`QjZ2xyD42bPYKTyx`

## 📊 恢复内容详情

### 角色权限分配

| 角色 | 权限数量 | 主要权限范围 |
|------|----------|--------------|
| **超级管理员** | 47个 | 所有权限，包括用户管理、角色管理、权限管理等 |
| **店长** | 29个 | 门店运营、课程管理、预约管理、会员卡管理 |
| **销售** | 20个 | 会员卡销售、客户服务、预约协助 |
| **前台** | 17个 | 预约管理、日常接待、基础查询 |
| **教练** | 15个 | 课程相关、预约查看、基础功能 |

### 权限分组

#### 超级管理员权限（ID: 1）
```
用户管理: 1,2,3,4,5
角色管理: 11,12,13,14,15,16,17
权限管理: 21,22,23,24,25
菜单管理: 31,32,33,34,35
门店管理: 41,42,43,44
课程管理: 51,52,53,54
预约管理: 61,62,63,64
会员卡管理: 71,72,73,74
文件管理: 81,82,83,84,85,86
数据统计: 91,92,93
灵活扣减: 101,102,103,104,105,106,107,108,109,110,111,112
```

#### 店长权限（ID: 2）
```
基础查询: 4,5,14,24,25,34,35,44
课程管理: 51,52,53,54
预约管理: 61,62,63,64
会员卡管理: 71,72,73,74
文件管理: 81,82,83,84,85,86
数据统计: 91,92,93
灵活扣减: 101,105,106,111,112
```

#### 销售权限（ID: 3）
```
基础查询: 4,5,24,25,34,35,44,54
预约管理: 61,62,64
会员卡管理: 71,72,74
数据统计: 91,92
灵活扣减: 101,105,106,112
```

#### 前台权限（ID: 4）
```
基础查询: 4,5,24,25,34,35,44,54
预约管理: 61,62,64
会员卡查询: 74
数据统计: 91
灵活扣减: 101,105,106,112
```

#### 教练权限（ID: 5）
```
基础查询: 4,5,24,25,34,35,44,54
预约管理: 61,64
数据统计: 91
灵活扣减: 101,105,106,112
```

## 🔍 执行内容

### 1. 清理现有数据
- 清空`role_permissions`表
- 清空`casbin_rule`表中的权限策略（保留角色继承）

### 2. 恢复角色权限关联
- 重新插入所有角色的权限分配
- 按照初始化配置精确恢复

### 3. 恢复Casbin策略
- 重新生成所有角色的权限策略
- 确保权限检查正常工作

### 4. 验证结果
- 显示每个角色的权限数量
- 确认恢复完整性

## ⚠️ 注意事项

1. **备份建议**：虽然只恢复角色权限，但建议执行前备份数据库
2. **应用重启**：恢复完成后建议重启应用，确保Casbin缓存更新
3. **权限验证**：恢复后测试各角色的权限是否正常工作
4. **只恢复角色权限**：不会影响用户数据、角色数据、权限数据等其他内容

## 🚀 执行步骤

1. **准备环境**
   ```bash
   # 确保PostgreSQL客户端已安装
   psql --version
   ```

2. **执行恢复**
   ```bash
   # 推荐方法
   python restore_role_permissions.py
   ```

3. **验证结果**
   ```sql
   -- 检查角色权限数量
   SELECT
       r.name as role_name,
       COUNT(rp.permission_id) as permission_count
   FROM roles r
   LEFT JOIN role_permissions rp ON r.id = rp.role_id
   GROUP BY r.id, r.name
   ORDER BY r.id;
   ```

4. **重启应用**
   ```bash
   # 重启应用确保权限生效
   ./yogaga.exe
   ```

## ✅ 预期结果

执行成功后，你应该看到：
```
role_name    | permission_count
-------------|------------------
超级管理员   | 47
店长         | 29
销售         | 20
前台         | 17
教练         | 15
```

现在你可以使用PowerShell脚本快速恢复角色权限分配了！🎉
