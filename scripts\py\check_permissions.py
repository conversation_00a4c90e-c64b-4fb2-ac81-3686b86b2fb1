#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限检查脚本
检查路由中定义的权限和数据库中配置的权限是否一致
"""

import re
import os
import sys

def extract_permissions_from_router():
    """从路由文件中提取所有权限"""
    # 从scripts/py目录运行时，需要回到项目根目录
    router_file = "../../routers/router.go"
    if not os.path.exists(router_file):
        # 如果从项目根目录运行
        router_file = "routers/router.go"

    permissions = set()

    if not os.path.exists(router_file):
        print(f"❌ 路由文件不存在: {router_file}")
        return permissions

    with open(router_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 匹配 CheckPermissionByKey("权限名", enforcer) 模式
    pattern = r'CheckPermissionByKey\("([^"]+)"'
    matches = re.findall(pattern, content)

    for match in matches:
        permissions.add(match)

    return permissions

def extract_permissions_from_sql():
    """从SQL文件中提取已配置的权限"""
    permissions = set()

    # 查找所有SQL文件
    sql_files = []
    for root, dirs, files in os.walk("."):
        for file in files:
            if file.endswith('.sql'):
                sql_files.append(os.path.join(root, file))

    for sql_file in sql_files:
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 匹配权限插入语句
            # INSERT INTO permissions ... VALUES ('权限名', ...)
            pattern = r"INSERT\s+INTO\s+permissions.*?VALUES\s*\(\s*['\"]([^'\"]+)['\"]"
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)

            for match in matches:
                permissions.add(match)

        except Exception as e:
            print(f"⚠️  读取SQL文件失败: {sql_file}, 错误: {e}")

    return permissions

def extract_permissions_from_go():
    """从Go代码中提取已配置的权限"""
    permissions = set()

    # 查找auto_init.go文件
    auto_init_file = "../../internal/bootstrap/auto_init.go"
    if not os.path.exists(auto_init_file):
        # 如果从项目根目录运行
        auto_init_file = "internal/bootstrap/auto_init.go"

    if not os.path.exists(auto_init_file):
        return permissions

    try:
        with open(auto_init_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 匹配权限配置中的Key字段
        # {ID: 1, Name: "创建用户", Key: "user:create", Method: "POST", Description: "创建新用户"},
        pattern = r'Key:\s*"([^"]+)"'
        matches = re.findall(pattern, content)

        for match in matches:
            permissions.add(match)

    except Exception as e:
        print(f"⚠️  读取Go文件失败: {auto_init_file}, 错误: {e}")

    return permissions

def generate_missing_permissions_sql(missing_permissions):
    """生成缺失权限的SQL语句"""
    if not missing_permissions:
        return ""

    sql_lines = [
        "-- 缺失的权限配置",
        "-- 请根据实际需要调整权限描述和分组",
        ""
    ]

    for perm in sorted(missing_permissions):
        # 根据权限名推断描述和分组
        parts = perm.split(':')
        if len(parts) >= 2:
            module = parts[0]
            action = parts[1]

            # 模块名映射
            module_map = {
                'user': '用户管理',
                'role': '角色管理',
                'permission': '权限管理',
                'store': '门店管理',
                'classroom': '教室管理',
                'coach': '教练管理',
                'course': '课程管理',
                'category': '分类管理',
                'booking': '预约管理',
                'schedule': '排课管理',
                'menu': '菜单管理',
                'system': '系统管理'
            }

            # 操作名映射
            action_map = {
                'list': '查看列表',
                'get': '查看详情',
                'create': '创建',
                'update': '更新',
                'delete': '删除',
                'manage': '管理'
            }

            module_name = module_map.get(module, module)
            action_name = action_map.get(action, action)
            description = f"{module_name} - {action_name}"
            group_name = module_name
        else:
            description = perm
            group_name = "其他"

        sql_lines.append(f"INSERT IGNORE INTO permissions (name, description, group_name, created_at, updated_at)")
        sql_lines.append(f"VALUES ('{perm}', '{description}', '{group_name}', NOW(), NOW());")
        sql_lines.append("")

    return "\n".join(sql_lines)

def check_database_permissions():
    """检查数据库中的权限配置（需要数据库连接）"""
    try:
        import mysql.connector
        import os

        # 从环境变量或配置文件读取数据库连接信息
        # 这里使用默认配置，实际使用时需要根据项目配置调整
        config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'root',
            'database': 'yogaga'
        }

        conn = mysql.connector.connect(**config)
        cursor = conn.cursor()

        # 查询所有权限
        cursor.execute("SELECT name FROM permissions")
        db_permissions = set(row[0] for row in cursor.fetchall())

        # 查询角色权限分配
        cursor.execute("""
            SELECT r.name, p.name
            FROM roles r
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
        """)
        role_permissions = {}
        for role_name, perm_name in cursor.fetchall():
            if role_name not in role_permissions:
                role_permissions[role_name] = set()
            role_permissions[role_name].add(perm_name)

        cursor.close()
        conn.close()

        return db_permissions, role_permissions

    except ImportError:
        print("⚠️  mysql-connector-python 未安装，跳过数据库检查")
        return set(), {}
    except Exception as e:
        print(f"⚠️  数据库连接失败: {e}")
        return set(), {}

def main():
    print("🔍 开始检查权限配置...")
    print("=" * 60)

    # 提取路由中的权限
    router_permissions = extract_permissions_from_router()
    print(f"📋 路由中发现的权限数量: {len(router_permissions)}")

    # 提取SQL中已配置的权限
    sql_permissions = extract_permissions_from_sql()
    print(f"📋 SQL文件中已配置的权限数量: {len(sql_permissions)}")

    # 提取Go代码中已配置的权限
    go_permissions = extract_permissions_from_go()
    print(f"📋 Go代码中已配置的权限数量: {len(go_permissions)}")

    # 检查数据库中的权限
    db_permissions, role_permissions = check_database_permissions()
    if db_permissions:
        print(f"📋 数据库中已存在的权限数量: {len(db_permissions)}")

    print("\n" + "=" * 60)

    # 找出缺失的权限（优先使用数据库数据，然后Go代码，最后SQL文件）
    existing_permissions = db_permissions if db_permissions else (go_permissions if go_permissions else sql_permissions)
    missing_permissions = router_permissions - existing_permissions

    if missing_permissions:
        print(f"❌ 发现 {len(missing_permissions)} 个缺失的权限:")
        for perm in sorted(missing_permissions):
            print(f"   - {perm}")

        print("\n" + "=" * 60)
        print("🔧 生成缺失权限的SQL语句:")
        print()

        sql_content = generate_missing_permissions_sql(missing_permissions)
        print(sql_content)

        # 保存到文件
        output_file = "scripts/missing_permissions.sql"
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(sql_content)
        print(f"💾 SQL语句已保存到: {output_file}")

    else:
        print("✅ 所有路由权限都已正确配置!")

    # 找出多余的权限
    extra_permissions = existing_permissions - router_permissions
    if extra_permissions:
        print(f"\n⚠️  发现 {len(extra_permissions)} 个可能多余的权限:")
        for perm in sorted(extra_permissions):
            print(f"   - {perm}")
        print("   (这些权限在SQL中配置了但在路由中未使用)")

    # 显示角色权限分配情况
    if role_permissions:
        print(f"\n📊 角色权限分配情况:")
        for role_name, perms in sorted(role_permissions.items()):
            print(f"   {role_name}: {len(perms)} 个权限")
            if role_name == 'admin':
                missing_admin_perms = router_permissions - perms
                if missing_admin_perms:
                    print(f"     ⚠️  admin角色缺少权限: {sorted(missing_admin_perms)}")

    print("\n" + "=" * 60)
    print("📊 权限检查完成!")

if __name__ == "__main__":
    main()
