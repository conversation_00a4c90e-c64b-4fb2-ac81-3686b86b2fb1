#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试门店更新接口
用于验证修复后的 image_ids 数组字段更新功能
"""

import requests
import json
import sys
import uuid
from typing import Dict, Any, Optional

class StoreUpdateTester:
    def __init__(self, base_url: str = "http://localhost:9095", username: str = "admin", password: str = "admin123"):
        self.base_url = base_url
        self.username = username
        self.password = password
        self.access_token = None
        self.session = requests.Session()

    def login(self) -> bool:
        """登录获取访问令牌"""
        login_url = f"{self.base_url}/api/v1/public/admin/login"  # 修正登录URL
        login_data = {
            "username": self.username,
            "password": self.password
        }

        try:
            print(f"🔐 正在登录... {login_url}")
            response = self.session.post(login_url, json=login_data)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    self.access_token = result["data"].get("access_token")
                    if self.access_token:
                        # 设置默认请求头
                        self.session.headers.update({
                            "Authorization": f"Bearer {self.access_token}",
                            "Content-Type": "application/json"
                        })
                        print("✅ 登录成功")
                        return True

            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return False

        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False

    def get_stores(self) -> Optional[list]:
        """获取门店列表"""
        try:
            print("📋 获取门店列表...")
            url = f"{self.base_url}/api/v1/admin/stores"
            response = self.session.get(url)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    stores = result["data"].get("list", [])
                    print(f"✅ 获取到 {len(stores)} 个门店")
                    return stores

            print(f"❌ 获取门店列表失败: {response.status_code} - {response.text}")
            return None

        except Exception as e:
            print(f"❌ 获取门店列表异常: {e}")
            return None

    def create_test_store(self) -> Optional[int]:
        """创建测试门店"""
        try:
            print("🏪 创建测试门店...")
            url = f"{self.base_url}/api/v1/admin/stores"

            # 生成测试用的文件ID（UUID格式）
            test_image_ids = [str(uuid.uuid4()), str(uuid.uuid4())]

            store_data = {
                "name": "测试门店-更新测试",
                "address": "测试地址123号",
                "phone": "13800138000",
                "latitude": 39.9042,
                "longitude": 116.4074,
                "image_ids": test_image_ids,
                "description": "这是一个用于测试更新功能的门店",
                "sort_order": 999
            }

            response = self.session.post(url, json=store_data)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    store_id = result["data"].get("id")
                    print(f"✅ 创建测试门店成功，ID: {store_id}")
                    print(f"   原始 image_ids: {test_image_ids}")
                    return store_id

            print(f"❌ 创建测试门店失败: {response.status_code} - {response.text}")
            return None

        except Exception as e:
            print(f"❌ 创建测试门店异常: {e}")
            return None

    def update_store(self, store_id: int) -> bool:
        """更新门店信息，重点测试 image_ids 字段"""
        try:
            print(f"🔄 更新门店 ID: {store_id}...")
            url = f"{self.base_url}/api/v1/admin/stores/{store_id}"

            # 生成新的测试文件ID
            new_image_ids = [str(uuid.uuid4()), str(uuid.uuid4()), str(uuid.uuid4())]

            update_data = {
                "name": "测试门店-已更新",
                "address": "更新后的地址456号",
                "phone": "13900139000",
                "latitude": 40.0042,
                "longitude": 116.5074,
                "image_ids": new_image_ids,  # 这是关键测试字段
                "description": "更新后的门店描述",
                "sort_order": 888,
                "status": 1
            }

            print(f"   新的 image_ids: {new_image_ids}")
            response = self.session.put(url, json=update_data)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print("✅ 门店更新成功")

                    # 验证更新结果
                    if "data" in result:
                        updated_store = result["data"]
                        actual_image_ids = updated_store.get("image_ids", [])
                        print(f"   实际 image_ids: {actual_image_ids}")

                        # 检查是否正确更新
                        if actual_image_ids == new_image_ids:
                            print("✅ image_ids 字段更新验证成功")
                            return True
                        else:
                            print("⚠️ image_ids 字段更新验证失败")
                            print(f"   期望: {new_image_ids}")
                            print(f"   实际: {actual_image_ids}")

                    return True
                else:
                    print(f"❌ 更新失败，错误码: {result.get('code')}, 消息: {result.get('message')}")

            print(f"❌ 门店更新失败: {response.status_code} - {response.text}")
            return False

        except Exception as e:
            print(f"❌ 门店更新异常: {e}")
            return False

    def get_store_detail(self, store_id: int) -> Optional[Dict[str, Any]]:
        """获取门店详情"""
        try:
            print(f"🔍 获取门店详情 ID: {store_id}...")
            url = f"{self.base_url}/api/v1/admin/stores/{store_id}"
            response = self.session.get(url)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200 and "data" in result:
                    store = result["data"]
                    print("✅ 获取门店详情成功")
                    print(f"   门店名称: {store.get('name')}")
                    print(f"   image_ids: {store.get('image_ids')}")
                    return store

            print(f"❌ 获取门店详情失败: {response.status_code} - {response.text}")
            return None

        except Exception as e:
            print(f"❌ 获取门店详情异常: {e}")
            return None

    def delete_test_store(self, store_id: int) -> bool:
        """删除测试门店"""
        try:
            print(f"🗑️ 删除测试门店 ID: {store_id}...")
            url = f"{self.base_url}/api/v1/admin/stores/{store_id}"
            response = self.session.delete(url)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print("✅ 测试门店删除成功")
                    return True

            print(f"❌ 删除测试门店失败: {response.status_code} - {response.text}")
            return False

        except Exception as e:
            print(f"❌ 删除测试门店异常: {e}")
            return False

    def run_test(self) -> bool:
        """运行完整测试流程"""
        print("🚀 开始门店更新接口测试")
        print("=" * 50)

        # 1. 登录
        if not self.login():
            return False

        # 2. 创建测试门店
        store_id = self.create_test_store()
        if not store_id:
            return False

        try:
            # 3. 获取创建后的门店详情
            self.get_store_detail(store_id)

            # 4. 更新门店（重点测试 image_ids 字段）
            update_success = self.update_store(store_id)

            # 5. 再次获取门店详情验证更新结果
            self.get_store_detail(store_id)

            return update_success

        finally:
            # 6. 清理：删除测试门店
            self.delete_test_store(store_id)

def main():
    """主函数"""
    print("🧪 门店更新接口测试工具")
    print("用于验证修复后的 PostgreSQL 数组字段更新功能")
    print()

    # 创建测试器实例
    tester = StoreUpdateTester()

    # 运行测试
    success = tester.run_test()

    print()
    print("=" * 50)
    if success:
        print("🎉 测试通过！门店更新接口工作正常")
        sys.exit(0)
    else:
        print("❌ 测试失败！请检查接口或修复代码")
        sys.exit(1)

if __name__ == "__main__":
    main()
