package storage

import (
	"context"
	"io"
	"mime"
	"net/url"
	"path/filepath"
	"time"

	"github.com/charmbracelet/log"
	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

type MinioClient struct {
	client     *minio.Client
	bucketName string
}

type MinioClientOptions struct {
	UseSSL bool
	Region string
}

// NewMinioClient 创建MinIO客户端
func NewMinioClient(endpoint, accessKeyID, accessKeySecret, bucketName string, opts *MinioClientOptions) (*MinioClient, error) {
	if opts == nil {
		opts = &MinioClientOptions{
			UseSSL: false,
			// Region: "us-east-1",
		}
	}

	// 初始化MinIO客户端
	minioClient, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(accessKeyID, accessKeySecret, ""),
		Secure: opts.UseSSL,
		Region: opts.Region,
	})
	if err != nil {
		log.Error("new minio client err = ", err)
		return nil, err
	}

	client := &MinioClient{
		client:     minioClient,
		bucketName: bucketName,
	}

	// 确保bucket存在
	ctx := context.Background()
	exists, err := minioClient.BucketExists(ctx, bucketName)
	if err != nil {
		log.Error("bucket = ", bucketName, " bucket exists err = ", err)
		return nil, err
	}
	if !exists {
		err = minioClient.MakeBucket(ctx, bucketName, minio.MakeBucketOptions{Region: opts.Region})
		if err != nil {
			log.Error("make bucket err = ", err)
			return nil, err
		}
	}

	// 设置bucket的访问策略，允许public目录下的文件公开访问
	policy := `{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Principal": {"AWS": ["*"]},
				"Action": ["s3:GetObject"],
				"Resource": ["arn:aws:s3:::` + bucketName + `/public/*"]
			}
		]
	}`
	err = minioClient.SetBucketPolicy(ctx, bucketName, policy)
	if err != nil {
		log.Error("set bucket policy err = ", err)
		return nil, err
	}

	return client, nil
}

// getContentType 根据文件扩展名获取正确的 Content-Type
func getContentType(filename string) string {
	ext := filepath.Ext(filename)
	contentType := mime.TypeByExtension(ext)
	if contentType == "" {
		// 如果无法识别，返回默认的二进制类型
		return "application/octet-stream"
	}
	return contentType
}

// GetUploadPresignedURL 获取上传接口的预请求url
func (m *MinioClient) GetPresignedUploadURL(ctx context.Context, objectName string, expires time.Duration) (*url.URL, error) {
	return m.client.PresignedPutObject(ctx, m.bucketName, objectName, expires)
}

// GetDownloadPresignedURL 获取下载接口的预请求url
func (m *MinioClient) GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error) {
	return m.client.PresignedGetObject(ctx, m.bucketName, objectName, expires, reqParams)
}

func (m *MinioClient) Delete(key string) error {
	return m.client.RemoveObject(context.Background(), m.bucketName, key, minio.RemoveObjectOptions{})
}

func (m *MinioClient) Exists(key string) (bool, error) {
	_, err := m.client.StatObject(context.Background(), m.bucketName, key, minio.StatObjectOptions{})
	if err != nil {
		return false, err
	}
	return true, nil
}

func (m *MinioClient) List(prefix string, limit int) ([]FileInfo, error) {
	objects := m.client.ListObjects(context.Background(), m.bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: false,
	})
	files := make([]FileInfo, 0)
	for object := range objects {
		files = append(files, FileInfo{
			Key:  object.Key,
			Size: object.Size,
		})
	}
	return files, nil
}

// GetBucketName 根据访问类型获取bucket名称（现在统一使用一个bucket）
func (m *MinioClient) GetBucketName(isPublic bool) string {
	return m.bucketName
}

func (m *MinioClient) Upload(key string, file io.Reader) (minio.UploadInfo, error) {
	// 根据文件扩展名设置正确的 Content-Type
	contentType := getContentType(key)

	info, err := m.client.PutObject(context.Background(), m.bucketName, key, file, -1, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return minio.UploadInfo{}, err
	}
	return info, nil
}

// UploadToSpecificBucket 上传文件到指定bucket
func (m *MinioClient) UploadToSpecificBucket(bucketName, key string, file io.Reader) (minio.UploadInfo, error) {
	// 根据文件扩展名设置正确的 Content-Type
	contentType := getContentType(key)

	info, err := m.client.PutObject(context.Background(), bucketName, key, file, -1, minio.PutObjectOptions{
		ContentType: contentType,
	})
	if err != nil {
		return minio.UploadInfo{}, err
	}
	return info, nil
}
