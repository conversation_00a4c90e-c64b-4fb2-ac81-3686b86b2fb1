package handler

import (
	"yogaga/internal/dto"
	"yogaga/pkg/code"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BatchCourseHandler 批量课程处理器
type BatchCourseHandler struct {
	db *gorm.DB
}

// NewBatchCourseHandler 创建批量课程处理器实例
func NewBatchCourseHandler(db *gorm.DB) *BatchCourseHandler {
	return &BatchCourseHandler{
		db: db,
	}
}

// BatchCreateCourses 批量创建课程（兼容接口，暂时未实现）
func (h *BatchCourseHandler) BatchCreateCourses(c *gin.Context) {
	response := dto.MessageResponse{
		Message: "批量创建接口正在重构中，请使用分离式创建：先创建课程，再进行排课",
	}
	code.AutoResponse(c, response, code.NewErrCodeMsg(code.InvalidParams, "接口暂未实现"))
}

// GetBatchCreatePreview 获取批量创建预览（暂时未实现）
func (h *BatchCourseHandler) GetBatchCreatePreview(c *gin.Context) {
	response := dto.MessageResponse{
		Message: "批量创建预览接口正在重构中，请使用分离式创建：先创建课程，再进行排课",
	}
	code.AutoResponse(c, response, code.NewErrCodeMsg(code.InvalidParams, "接口暂未实现"))
}
