package model

import "yogaga/internal/enum"

// ClassRoom 教室模型
type ClassRoom struct {
	BaseModel
	StoreID     uint                 `json:"store_id" gorm:"not null;comment:门店ID"`
	Name        string               `json:"name" gorm:"type:varchar(50);not null;comment:教室名称"`
	Capacity    int                  `json:"capacity" gorm:"type:int;default:20;comment:教室容量"`
	Equipment   string               `json:"equipment" gorm:"type:text;comment:设备描述"`
	Description string               `json:"description" gorm:"type:text;comment:教室描述"`
	Status      enum.ClassRoomStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:可用 0:维护"`

	// 关联
	Store *Store `json:"store,omitempty" gorm:"foreignKey:StoreID"`
}
