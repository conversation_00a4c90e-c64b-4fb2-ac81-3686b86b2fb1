package main

import (
	"encoding/json"
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"io/ioutil"
	"log"
	"os"
	"regexp"
	"strings"
)

// RouteInfo 路由信息结构
type RouteInfo struct {
	Method   string `json:"method"`
	Path     string `json:"path"`
	Handler  string `json:"handler"`
	Function string `json:"function"`
}

// APIInfo API文档接口信息结构
type APIInfo struct {
	Name     string `json:"name"`
	Method   string `json:"method"`
	Endpoint string `json:"endpoint"`
}

// HoppscotchCollection Hoppscotch集合结构
type HoppscotchCollection struct {
	Folders []HoppscotchFolder `json:"folders"`
}

// HoppscotchFolder 文件夹结构
type HoppscotchFolder struct {
	Name     string              `json:"name"`
	Requests []HoppscotchRequest `json:"requests"`
}

// HoppscotchRequest 请求结构
type HoppscotchRequest struct {
	Name     string `json:"name"`
	Method   string `json:"method"`
	Endpoint string `json:"endpoint"`
}

// ConsistencyReport 一致性检查报告
type ConsistencyReport struct {
	TotalRoutes      int              `json:"total_routes"`
	TotalAPIs        int              `json:"total_apis"`
	MatchedCount     int              `json:"matched_count"`
	ConsistencyRate  float64          `json:"consistency_rate"`
	MissingInAPI     []RouteInfo      `json:"missing_in_api"`
	MissingInRouter  []APIInfo        `json:"missing_in_router"`
	MethodMismatches []MethodMismatch `json:"method_mismatches"`
	Recommendations  []string         `json:"recommendations"`
	CheckTime        string           `json:"check_time"`
}

// MethodMismatch 方法不匹配结构
type MethodMismatch struct {
	Path         string `json:"path"`
	RouterMethod string `json:"router_method"`
	APIMethod    string `json:"api_method"`
}

func main() {
	fmt.Println("🔍 Admin API 一致性自动检查工具")
	fmt.Println("================================")

	// 1. 提取路由器中的admin路由
	fmt.Println("📋 步骤1: 提取路由器中的admin路由...")
	routes, err := extractAdminRoutes("routers/router.go")
	if err != nil {
		log.Fatalf("提取路由失败: %v", err)
	}
	fmt.Printf("✅ 提取到 %d 个admin路由\n", len(routes))

	// 2. 提取API文档中的接口
	fmt.Println("📋 步骤2: 提取API文档中的接口...")
	apis, err := extractAPIEndpoints("api-collections/admin/admin_api.json")
	if err != nil {
		log.Fatalf("提取API接口失败: %v", err)
	}
	fmt.Printf("✅ 提取到 %d 个API接口\n", len(apis))

	// 3. 进行一致性比较
	fmt.Println("📋 步骤3: 进行一致性比较...")
	report := compareConsistency(routes, apis)

	// 4. 生成报告
	fmt.Println("📋 步骤4: 生成检查报告...")
	err = generateReport(report)
	if err != nil {
		log.Fatalf("生成报告失败: %v", err)
	}

	// 5. 输出结果摘要
	fmt.Println("\n🎯 检查结果摘要:")
	fmt.Printf("总路由数: %d\n", report.TotalRoutes)
	fmt.Printf("总API数: %d\n", report.TotalAPIs)
	fmt.Printf("匹配数: %d\n", report.MatchedCount)
	fmt.Printf("一致性率: %.1f%%\n", report.ConsistencyRate)
	fmt.Printf("缺失在API文档中: %d\n", len(report.MissingInAPI))
	fmt.Printf("缺失在路由器中: %d\n", len(report.MissingInRouter))
	fmt.Printf("方法不匹配: %d\n", len(report.MethodMismatches))

	if report.ConsistencyRate >= 95.0 {
		fmt.Println("🎉 一致性检查通过！")
	} else if report.ConsistencyRate >= 85.0 {
		fmt.Println("⚠️  一致性需要改进")
	} else {
		fmt.Println("❌ 一致性检查失败，需要立即修复")
		os.Exit(1)
	}

	fmt.Println("\n📄 详细报告已保存到: scripts/consistency_report.json")
}

// extractAdminRoutes 从路由器文件中提取admin路由
func extractAdminRoutes(filename string) ([]RouteInfo, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	fset := token.NewFileSet()
	node, err := parser.ParseFile(fset, filename, content, parser.ParseComments)
	if err != nil {
		return nil, err
	}

	var routes []RouteInfo

	// 查找admin路由组
	ast.Inspect(node, func(n ast.Node) bool {
		if call, ok := n.(*ast.CallExpr); ok {
			if sel, ok := call.Fun.(*ast.SelectorExpr); ok {
				// 查找路由定义模式
				if isRouteMethod(sel.Sel.Name) && len(call.Args) >= 2 {
					if lit, ok := call.Args[0].(*ast.BasicLit); ok {
						path := strings.Trim(lit.Value, "\"")
						// 只处理admin路由
						if strings.Contains(path, "/admin/") {
							route := RouteInfo{
								Method: strings.ToUpper(sel.Sel.Name),
								Path:   "/api/v1" + path,
							}
							routes = append(routes, route)
						}
					}
				}
			}
		}
		return true
	})

	return routes, nil
}

// extractAPIEndpoints 从API文档中提取接口信息
func extractAPIEndpoints(filename string) ([]APIInfo, error) {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	var collection HoppscotchCollection
	err = json.Unmarshal(content, &collection)
	if err != nil {
		return nil, err
	}

	var apis []APIInfo
	for _, folder := range collection.Folders {
		for _, request := range folder.Requests {
			// 清理endpoint，移除变量
			endpoint := cleanEndpoint(request.Endpoint)
			api := APIInfo{
				Name:     request.Name,
				Method:   strings.ToUpper(request.Method),
				Endpoint: endpoint,
			}
			apis = append(apis, api)
		}
	}

	return apis, nil
}

// compareConsistency 比较路由和API的一致性
func compareConsistency(routes []RouteInfo, apis []APIInfo) *ConsistencyReport {
	report := &ConsistencyReport{
		TotalRoutes: len(routes),
		TotalAPIs:   len(apis),
		CheckTime:   "2025-07-29 12:00:00",
	}

	// 创建映射表便于查找
	routeMap := make(map[string]RouteInfo)
	apiMap := make(map[string]APIInfo)

	for _, route := range routes {
		key := route.Method + ":" + route.Path
		routeMap[key] = route
	}

	for _, api := range apis {
		key := api.Method + ":" + api.Endpoint
		apiMap[key] = api
	}

	// 查找匹配和不匹配
	matched := 0
	for key, route := range routeMap {
		if _, exists := apiMap[key]; exists {
			matched++
		} else {
			// 检查是否是方法不匹配
			pathOnly := strings.Split(key, ":")[1]
			methodMismatch := false
			for apiKey, api := range apiMap {
				if strings.Split(apiKey, ":")[1] == pathOnly {
					report.MethodMismatches = append(report.MethodMismatches, MethodMismatch{
						Path:         pathOnly,
						RouterMethod: route.Method,
						APIMethod:    api.Method,
					})
					methodMismatch = true
					break
				}
			}
			if !methodMismatch {
				report.MissingInAPI = append(report.MissingInAPI, route)
			}
		}
	}

	// 查找API文档中存在但路由器中不存在的接口
	for key, api := range apiMap {
		if _, exists := routeMap[key]; !exists {
			// 检查是否已经在方法不匹配中处理过
			pathOnly := strings.Split(key, ":")[1]
			alreadyHandled := false
			for _, mismatch := range report.MethodMismatches {
				if mismatch.Path == pathOnly {
					alreadyHandled = true
					break
				}
			}
			if !alreadyHandled {
				report.MissingInRouter = append(report.MissingInRouter, api)
			}
		}
	}

	report.MatchedCount = matched
	report.ConsistencyRate = float64(matched) / float64(len(routes)) * 100

	// 生成建议
	report.Recommendations = generateRecommendations(report)

	return report
}

// generateReport 生成检查报告
func generateReport(report *ConsistencyReport) error {
	reportJSON, err := json.MarshalIndent(report, "", "  ")
	if err != nil {
		return err
	}

	return ioutil.WriteFile("scripts/consistency_report.json", reportJSON, 0644)
}

// 辅助函数
func isRouteMethod(method string) bool {
	methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH"}
	for _, m := range methods {
		if strings.ToUpper(method) == m {
			return true
		}
	}
	return false
}

func cleanEndpoint(endpoint string) string {
	// 移除 <<baseURL>> 变量
	endpoint = strings.Replace(endpoint, "<<baseURL>>", "", 1)

	// 移除其他变量，如 <<yoga_basic_course_id>>
	re := regexp.MustCompile(`<<[^>]+>>`)
	endpoint = re.ReplaceAllStringFunc(endpoint, func(match string) string {
		// 将变量替换为路径参数格式
		if strings.Contains(match, "_id") {
			return ":id"
		}
		return ":param"
	})

	return endpoint
}

func generateRecommendations(report *ConsistencyReport) []string {
	var recommendations []string

	if len(report.MissingInAPI) > 0 {
		recommendations = append(recommendations,
			fmt.Sprintf("建议在API文档中添加 %d 个缺失的接口", len(report.MissingInAPI)))
	}

	if len(report.MissingInRouter) > 0 {
		recommendations = append(recommendations,
			fmt.Sprintf("建议检查路由器中是否缺少 %d 个接口的实现", len(report.MissingInRouter)))
	}

	if len(report.MethodMismatches) > 0 {
		recommendations = append(recommendations,
			fmt.Sprintf("建议修复 %d 个HTTP方法不匹配的问题", len(report.MethodMismatches)))
	}

	if report.ConsistencyRate < 95.0 {
		recommendations = append(recommendations, "建议定期运行此检查工具以保持一致性")
		recommendations = append(recommendations, "建议将此脚本集成到CI/CD流程中")
	}

	if report.ConsistencyRate >= 95.0 {
		recommendations = append(recommendations, "当前一致性良好，建议保持定期检查")
	}

	return recommendations
}
