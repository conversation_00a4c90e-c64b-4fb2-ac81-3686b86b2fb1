# -----------------------------------------------------------------------------
# STAGE 1: 构建阶段 (Builder)
# -----------------------------------------------------------------------------
FROM golang:1.24-alpine AS builder

# 设置环境变量
ENV GOOS=linux GOARCH=amd64 CGO_ENABLED=0
ENV GOPROXY=https://goproxy.cn,direct

# 设置构建阶段的工作目录
WORKDIR /app

# 优化依赖缓存
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建参数
ARG COMMIT_HASH
ARG BUILD_TIME
ARG PLATFORM
# 设定应用名称变量, 默认为 app
ARG APP_NAME=app
ARG CONFIG_FILE=config.example.yaml
ARG APP_PORT=9095

# 编译 Go 应用, 输出到独立的 /build 目录
RUN go build -trimpath -ldflags="\
    -w -s \
    -X 'main.CommitHash=${COMMIT_HASH}' \
    -X 'main.BuildTime=${BUILD_TIME}' \
    -X 'main.Platform=${PLATFORM}' \
    -X 'main.AppName=${APP_NAME}' \
    " -o /build/${APP_NAME} .

# -----------------------------------------------------------------------------
# STAGE 2: 运行阶段 (Final)
# -----------------------------------------------------------------------------
FROM alpine:latest

# 再次声明 ARG 以在当前阶段使用
ARG APP_NAME=app
ARG CONFIG_FILE=config.example.yaml

# 安装必要的运行时依赖:
# ca-certificates: 用于 Go 程序进行 HTTPS 调用
# tzdata: 用于设置正确的时区
# curl: 用于健康检查
RUN apk --no-cache add ca-certificates tzdata curl

# 设置时区
ENV TZ=Asia/Shanghai

# --- 安全与 FHS 目录结构设置 ---

# 1. 创建非 root 用户和组
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# 2. 创建 FHS 标准配置目录并设置权限
# 这是约定用于挂载配置文件的位置
RUN mkdir -p /etc/${APP_NAME} && \
    chown appuser:appgroup /etc/${APP_NAME}

# --- 复制构建产物 ---

# 3. 复制二进制文件到 FHS 标准位置
COPY --from=builder /build/${APP_NAME} /usr/local/bin/${APP_NAME}

# 根据构建时传入的 CONFIG_FILE 参数复制配置文件，并重命名为标准名称
COPY configs/${CONFIG_FILE} /etc/${APP_NAME}/config.yaml

# 复制 Casbin 模型配置文件
COPY configs/rbac_model.conf /etc/${APP_NAME}/rbac_model.conf

# --- 运行时配置 ---

# 切换到非 root 用户
USER appuser

# 将构建参数 APP_NAME 设为环境变量，以便在 ENTRYPOINT 中动态使用
ENV APP_NAME=${APP_NAME}

# EXPOSE: 声明应用监听的端口 (最佳实践, 用于文档化)
EXPOSE ${APP_PORT}

# 健康检查
HEALTHCHECK --interval=15s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:${APP_PORT}/healthz || exit 1

# 启动命令
# 使用 shell form + exec, 以支持变量替换并确保应用能正确处理信号 (PID 1)
ENTRYPOINT exec /usr/local/bin/${APP_NAME}
# 默认传递 --config 参数给 ENTRYPOINT
CMD ["--config", "/etc/${APP_NAME}/config.yaml"]