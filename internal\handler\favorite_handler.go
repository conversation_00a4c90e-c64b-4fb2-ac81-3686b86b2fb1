package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// FavoriteHandler 收藏处理器
type FavoriteHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewFavoriteHandler 创建收藏处理器实例
func NewFavoriteHandler(db *gorm.DB, storage storage.Storage) *FavoriteHandler {
	return &FavoriteHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetFavorites 获取我的收藏列表
func (h *FavoriteHandler) GetFavorites(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 类型筛选
	favoriteType := c.Query("type")

	// 构建查询条件
	query := h.db.Model(&model.Favorite{}).Where("user_id = ?", userID)

	if favoriteType != "" {
		// 验证收藏类型是否有效
		fType := enum.FavoriteType(favoriteType)
		if fType.IsValid() {
			query = query.Where("favorite_type = ?", favoriteType)
		}
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var favorites []model.Favorite
	err := query.Order("created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&favorites).Error

	if err != nil {
		log.Error("查询收藏失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询收藏失败"))
		return
	}

	// 构建响应，包含关联数据
	var favoriteList []dto.FavoriteItem
	for _, favorite := range favorites {
		item := dto.FavoriteItem{
			ID:           favorite.ID,
			FavoriteType: string(favorite.FavoriteType),
			TargetID:     favorite.TargetID,
			CreatedAt:    favorite.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		// 根据类型获取详细信息
		switch favorite.FavoriteType {
		case "course":
			if courseInfo := h.getCourseInfo(favorite.TargetID); courseInfo != nil {
				item.CourseInfo = courseInfo
			}
		case "coach":
			if coachInfo := h.getCoachInfo(favorite.TargetID); coachInfo != nil {
				item.CoachInfo = coachInfo
			}
		case "store":
			if storeInfo := h.getStoreInfo(favorite.TargetID); storeInfo != nil {
				item.StoreInfo = storeInfo
			}
		}

		favoriteList = append(favoriteList, item)
	}

	// 构建响应
	response := dto.PageResponse{
		List:     favoriteList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// AddFavorite 添加收藏
func (h *FavoriteHandler) AddFavorite(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.AddFavoriteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定添加收藏参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证目标是否存在
	if !h.validateTarget(req.FavoriteType, req.TargetID) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "收藏目标不存在"))
		return
	}

	// 检查是否已收藏
	var count int64
	h.db.Model(&model.Favorite{}).
		Where("user_id = ? AND favorite_type = ? AND target_id = ?", userID, req.FavoriteType, req.TargetID).
		Count(&count)

	if count > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "已经收藏过了"))
		return
	}

	// 创建收藏记录
	favorite := model.Favorite{
		UserID:       userID.(string),
		FavoriteType: enum.FavoriteType(req.FavoriteType),
		TargetID:     req.TargetID,
	}

	if err := h.db.Create(&favorite).Error; err != nil {
		log.Error("添加收藏失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "添加收藏失败"))
		return
	}

	log.Info("添加收藏成功", "user_id", userID, "type", req.FavoriteType, "target_id", req.TargetID)
	response := dto.MessageResponse{Message: "收藏成功"}
	code.AutoResponse(c, response, nil)
}

// RemoveFavorite 取消收藏
func (h *FavoriteHandler) RemoveFavorite(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("收藏ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 删除收藏记录
	result := h.db.Where("id = ? AND user_id = ?", id, userID).Delete(&model.Favorite{})
	if result.Error != nil {
		log.Error("取消收藏失败", "id", id, "error", result.Error)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "取消收藏失败"))
		return
	}

	if result.RowsAffected == 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "收藏记录不存在"))
		return
	}

	log.Info("取消收藏成功", "id", id, "user_id", userID)
	response := dto.MessageResponse{Message: "取消收藏成功"}
	code.AutoResponse(c, response, nil)
}

// CheckFavorite 检查是否已收藏
func (h *FavoriteHandler) CheckFavorite(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	favoriteType := c.Query("type")
	targetID := c.Query("target_id")

	if favoriteType == "" || targetID == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "参数不完整"))
		return
	}

	// 检查是否已收藏
	var count int64
	h.db.Model(&model.Favorite{}).
		Where("user_id = ? AND favorite_type = ? AND target_id = ?", userID, favoriteType, targetID).
		Count(&count)

	response := dto.FavoriteCheckResponse{
		IsFavorited: count > 0,
	}

	code.AutoResponse(c, response, nil)
}

// validateTarget 验证收藏目标是否存在
func (h *FavoriteHandler) validateTarget(favoriteType string, targetID string) bool {
	switch favoriteType {
	case "course":
		var count int64
		h.db.Model(&model.Course{}).Where("id = ?", targetID).Count(&count)
		return count > 0
	case "coach":
		var count int64
		h.db.Model(&model.User{}).Where("id = ?", targetID).Count(&count)
		return count > 0
	case "store":
		var count int64
		h.db.Model(&model.Store{}).Where("id = ?", targetID).Count(&count)
		return count > 0
	default:
		return false
	}
}

// getCourseInfo 获取课程信息
func (h *FavoriteHandler) getCourseInfo(courseID string) *dto.FavoriteCourseInfo {
	var course model.Course
	if err := h.db.Preload("Store").First(&course, courseID).Error; err != nil {
		return nil
	}

	// 获取教练信息
	var coachName string
	if len(course.CoachIDs) > 0 {
		var coach model.User
		if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
			coachName = getCoachName(&coach)
		}
	}

	return &dto.FavoriteCourseInfo{
		ID:          course.ID,
		Name:        course.Name,
		Description: course.Description,
		Duration:    course.Duration,
		Price:       course.Price,
		CoachName:   coachName,
		StoreName:   course.Store.Name,
	}
}

// getCoachInfo 获取教练信息
func (h *FavoriteHandler) getCoachInfo(coachID string) *dto.FavoriteCoachInfo {
	var coach model.User
	if err := h.db.Where("id = ?", coachID).First(&coach).Error; err != nil {
		return nil
	}

	// 获取头像URL
	avatarURL := ""
	if coach.AvatarID != nil && *coach.AvatarID != "" {
		avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
	}

	return &dto.FavoriteCoachInfo{
		ID:          coach.ID.String(),
		Name:        getCoachName(&coach),
		Avatar:      avatarURL,
		Experience:  coach.Experience,
		Speciality:  coach.Speciality,
		Description: coach.CoachDescription,
	}
}

// getStoreInfo 获取门店信息
func (h *FavoriteHandler) getStoreInfo(storeID string) *dto.FavoriteStoreInfo {
	var store model.Store
	if err := h.db.First(&store, storeID).Error; err != nil {
		return nil
	}

	return &dto.FavoriteStoreInfo{
		ID:          store.ID,
		Name:        store.Name,
		Address:     store.Address,
		Phone:       store.Phone,
		Images:      store.ImageIDs, // 使用新的ImageIDs字段
		Description: store.Description,
	}
}
