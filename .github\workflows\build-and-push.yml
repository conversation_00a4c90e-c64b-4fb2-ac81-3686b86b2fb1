# CI/CD 工作流：构建并推送 Docker 镜像
# 当一个 "v*" 格式的 tag 被推送到仓库时，此工作流将被触发
name: Build and Push to Aliyun ACR

# 当代码被推送到 main 分支时，此工作流将被触发
on:
  push:
    branches:
      - "main"

# 设置环境变量，方便在多个步骤中复用
env:
  DOCKER_REGISTRY: registry.cn-shanghai.aliyuncs.com
  ALIYUN_NAMESPACE: initcool
  IMAGE_NAME: ${{ github.repository }}

jobs:
  build-and-push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    steps:
      # 步骤 1: 检出代码 (使用您指定的 Gitea Action)
      - name: Checkout
        uses: https://gitea.com/actions/checkout@v4
        with:
          fetch-depth: 0

      # 新增步骤: 获取构建版本和时间信息
      - name: Get build version and time
        id: vars
        run: |
          echo "sha_short=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
          echo "build_time=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT

      # 步骤 2: 提取 Docker 元数据
      # 根据提交的 SHA 生成唯一标签, 并在 main 分支上生成 'latest' 标签
      - name: Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.DOCKER_REGISTRY }}/${{ env.ALIYUN_NAMESPACE }}/${{ github.event.repository.name }}
          tags: |
            type=sha,format=short,prefix=
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/main' }}

      # 新增步骤: 打印调试信息
      - name: Print debug information
        run: |
          echo "Short SHA: ${{ steps.vars.outputs.sha_short }}"
          echo "Build Time: ${{ steps.vars.outputs.build_time }}"

      # 步骤 3: 设置 QEMU, 以支持多平台构建
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      # 步骤 4: 设置 Docker Buildx
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # 步骤 5: 登录到阿里云容器镜像服务 (ACR)
      - name: Login to Aliyun Docker Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.DOCKER_REGISTRY }}
          username: ${{ secrets.DOCKER_USERNAME }} # 从 Secrets 读取用户名
          password: ${{ secrets.DOCKER_PASSWORD }} # 需要在 Secrets 中配置

      # 步骤 6: 构建并推送到 ACR
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./deploy/Dockerfile # 指定 Dockerfile 的位置
          platforms: linux/amd64 #,linux/arm64 # 根据需要启用多平台构建
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            APP_NAME=${{ github.event.repository.name }}
            COMMIT_HASH=${{ steps.vars.outputs.sha_short }}
            BUILD_TIME=${{ steps.vars.outputs.build_time }}
            PLATFORM=linux/amd64
            APP_PORT=9095