// ===== 完整的会员卡管理API请求示例 =====
// 使用说明：复制对应的JSON部分到Postman的Body中，选择raw -> JSON格式

// ===== 1. 创建会员卡类型 =====
// 接口：POST /api/v1/admin/membership-types
// 认证：需要管理员Token
// 说明：先创建会员卡类型，然后才能开具会员卡

// 1.1 次数卡类型示例
{
  "name": "10次团课卡",                    // 卡类型名称，必填，最大100字符
  "category": "times",                     // 卡类别，必填，可选值：times(次数卡)/period(期限卡)/balance(储值卡)
  "scope": "group",                        // 适用范围，必填，可选值：group(团课)/private(私教)/small(小班)/universal(通用)
  "description": "适用于团课的10次卡",      // 描述，可选，最大500字符
  "validity_days": 90,                     // 有效期天数，0表示永久有效
  "times": 10,                            // 次数，次数卡必填，其他类型填0
  "amount": 0,                            // 金额，储值卡必填(分为单位)，其他类型填0
  "price": 19900,                         // 售价，必填，单位：分(199元=19900分)
  "discount": 1.0,                        // 默认折扣，0-1之间，1.0表示无折扣
  "shareable": false,                     // 是否可共享(主副卡功能)
  "can_set_expiry": true,                 // 开卡时是否可设置有效期
  "can_select_stores": true,              // 开卡时是否可选择门店
  "can_add_sub_card": false,              // 是否支持副卡
  "can_set_daily_limit": true,            // 开卡时是否可设置每日预约限制
  "daily_booking_limit": 1,               // 默认每日预约限制，0表示无限制
  "applicable_courses": "团课类课程",      // 适用课程说明
  "sort_order": 1                         // 排序权重，数字越小越靠前
}

// 1.2 期限卡类型示例
{
  "name": "3个月无限次团课卡",
  "category": "period",                    // 期限卡
  "scope": "group",
  "description": "3个月内无限次团课",
  "validity_days": 90,                     // 3个月
  "times": 0,                             // 期限卡次数填0
  "amount": 0,                            // 期限卡金额填0
  "price": 59900,                         // 599元
  "discount": 1.0,
  "shareable": false,
  "can_set_expiry": false,                // 期限卡通常不允许自定义有效期
  "can_select_stores": true,
  "can_add_sub_card": false,
  "can_set_daily_limit": true,
  "daily_booking_limit": 2,               // 每天最多2节课
  "applicable_courses": "所有团课",
  "sort_order": 2
}

// 1.3 储值卡类型示例
{
  "name": "1000元储值卡",
  "category": "balance",                   // 储值卡
  "scope": "universal",                    // 通用，可用于所有课程类型
  "description": "储值1000元，按次扣费",
  "validity_days": 365,                    // 1年有效期
  "times": 0,                             // 储值卡次数填0
  "amount": 100000,                       // 储值金额，单位：分(1000元=100000分)
  "price": 100000,                        // 售价等于储值金额
  "discount": 1.0,
  "shareable": true,                      // 储值卡通常支持共享
  "can_set_expiry": true,
  "can_select_stores": false,             // 储值卡通常全店通用
  "can_add_sub_card": true,               // 支持副卡
  "can_set_daily_limit": false,           // 储值卡通常不限制每日预约
  "daily_booking_limit": 0,
  "applicable_courses": "所有课程类型",
  "sort_order": 3
}

// ===== 2. 创建会员卡实例 =====
// 接口：POST /api/v1/admin/membership-cards
// 认证：需要管理员Token
// 说明：为用户开具具体的会员卡

// 2.1 开次数卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-************",  // 用户ID，36位UUID格式，必填
  "card_type_id": 1,                                   // 会员卡类型ID，必填，需要先创建卡类型
  "start_date": "2024-01-15T00:00:00Z",              // 开始日期，必填，ISO 8601格式
  "purchase_price": 19900,                            // 实际支付价格，必填，单位：分
  "discount": 0.9,                                    // 折扣，可选，0.9表示9折
  "payment_method": "wechat_pay",                     // 支付方式，可选，wechat_pay/alipay/cash/membership_card
  "available_stores": [1, 2, 3],                     // 可用门店ID列表，可选，空数组表示全部门店
  "daily_booking_limit": 1,                          // 每日预约限制，可选，0表示无限制
  "remark": "新用户优惠开卡"                          // 备注，可选，最大500字符
}

// 2.2 开期限卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "card_type_id": 2,                                  // 期限卡类型ID
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 53910,                           // 599元的9折 = 539.1元 = 53910分
  "discount": 0.9,
  "payment_method": "alipay",
  "available_stores": [1, 2],                        // 限制只能在门店1和2使用
  "daily_booking_limit": 2,
  "remark": "春节促销活动，9折优惠"
}

// 2.3 开储值卡示例
{
  "user_id": "550e8400-e29b-41d4-a716-446655440002",
  "card_type_id": 3,                                  // 储值卡类型ID
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 100000,                          // 1000元
  "discount": 1.0,                                   // 储值卡通常不打折
  "payment_method": "cash",
  "available_stores": [],                            // 空数组表示全部门店可用
  "daily_booking_limit": 0,                         // 无限制
  "remark": "VIP客户储值"
}

// ===== 3. 获取会员卡类型列表 =====
// 接口：GET /api/v1/admin/membership-types
// 参数：通过URL参数传递
// 示例：GET /api/v1/admin/membership-types?page=1&page_size=10&category=times&status=1

// ===== 4. 获取会员卡列表 =====
// 接口：GET /api/v1/admin/membership-cards
// 参数：通过URL参数传递
// 示例：GET /api/v1/admin/membership-cards?page=1&page_size=10&user_id=xxx&status=active

// ===== 5. 管理员登录获取Token =====
// 接口：POST /api/v1/public/admin/login
// 说明：先登录获取Token，然后在其他接口的Header中使用
{
  "username": "admin",                               // 管理员用户名
  "password": "admin123"                             // 管理员密码
}

// ===== 常见参数说明 =====
/*
会员卡类别 (category):
- "times": 次数卡，按次数扣费
- "period": 期限卡，在有效期内无限次使用
- "balance": 储值卡，按金额扣费

适用范围 (scope):
- "group": 团课专用
- "private": 私教专用  
- "small": 小班课专用
- "universal": 通用卡，所有课程类型都可用

支付方式 (payment_method):
- "wechat_pay": 微信支付
- "alipay": 支付宝
- "cash": 现金
- "membership_card": 会员卡支付

价格单位说明:
- 所有价格都以"分"为单位
- 100元 = 10000分
- 避免小数点问题

日期格式:
- 推荐使用: "2024-01-15T00:00:00Z"
- 也可使用: "2024-01-15"

UUID格式:
- 用户ID必须是36位UUID: "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
- 可通过用户管理接口获取真实的用户ID
*/

// ===== Postman使用步骤 =====
/*
1. 先调用登录接口获取Token
2. 在后续请求的Headers中添加：
   Authorization: Bearer <your_token>
   Content-Type: application/json
3. 复制对应的JSON到Body中
4. 发送请求
*/
