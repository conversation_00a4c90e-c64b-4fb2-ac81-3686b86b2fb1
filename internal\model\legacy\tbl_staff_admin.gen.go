// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblStaffAdmin = "tbl_staff_admin"

// TblStaffAdmin mapped from table <tbl_staff_admin>
type TblStaffAdmin struct {
	ID       int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID   int32     `gorm:"column:shop_id" json:"shop_id"`
	RealName string    `gorm:"column:real_name" json:"real_name"`
	Mobile   string    `gorm:"column:mobile" json:"mobile"`
	Pwd      string    `gorm:"column:pwd;not null" json:"pwd"`
	Logtime  time.Time `gorm:"column:logtime" json:"logtime"`
	Status   bool      `gorm:"column:status;not null;default:1" json:"status"`
	Auths    string    `gorm:"column:auths" json:"auths"`
}

// TableName TblStaffAdmin's table name
func (*TblStaffAdmin) TableName() string {
	return TableNameTblStaffAdmin
}
