package handler

import (
	"strconv"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ShareHandler 分享处理器
type ShareHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewShareHandler 创建分享处理器实例
func NewShareHandler(db *gorm.DB, storage storage.Storage) *ShareHandler {
	return &ShareHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// ShareCourse 分享课程
func (h *ShareHandler) ShareCourse(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	courseIDStr := c.Param("id")
	courseID := cast.ToUint(courseIDStr)
	if courseID == 0 {
		log.Error("课程ID格式错误", "id", courseIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程ID格式错误"))
		return
	}

	// 查询课程信息
	var course model.Course
	err := h.db.Preload("Store").First(&course, courseID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		} else {
			log.Error("查询课程失败", "course_id", courseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		}
		return
	}

	// 记录分享行为（可选）
	h.recordShareAction(userID.(uint), "course", uint(courseID))

	// 构建分享信息
	shareInfo := dto.ShareResponse{
		Type:        "course",
		Title:       course.Name,
		Description: course.Description,
		Image:       "/images/default-course.jpg", // 使用默认课程图片
		URL:         h.generateShareURL("course", uint(courseID)),
		ShareText: h.generateShareText("course", course.Name, func() string {
			if len(course.CoachIDs) > 0 {
				var coach model.User
				if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
					return getCoachName(&coach)
				}
			}
			return ""
		}(), course.Store.Name),
	}

	log.Info("课程分享成功", "user_id", userID, "course_id", courseID)
	code.AutoResponse(c, shareInfo, nil)
}

// ShareCoach 分享教练
func (h *ShareHandler) ShareCoach(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	coachIDStr := c.Param("id")
	coachID := cast.ToUint(coachIDStr)
	if coachID == 0 {
		log.Error("教练ID格式错误", "id", coachIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "教练ID格式错误"))
		return
	}

	// 查询教练信息
	var coach model.User
	err := h.db.Where("id = ?", coachID).First(&coach).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		} else {
			log.Error("查询教练失败", "coach_id", coachID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练失败"))
		}
		return
	}

	// 记录分享行为
	h.recordShareAction(userID.(uint), "coach", uint(coachID))

	// 获取头像URL
	avatarURL := ""
	if coach.AvatarID != nil && *coach.AvatarID != "" {
		avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
	}

	// 构建分享信息
	shareInfo := dto.ShareResponse{
		Type:        "coach",
		Title:       getCoachName(&coach),
		Description: coach.CoachDescription,
		Image:       avatarURL,
		URL:         h.generateShareURL("coach", uint(coachID)),
		ShareText:   h.generateShareText("coach", getCoachName(&coach), "", ""),
	}

	log.Info("教练分享成功", "user_id", userID, "coach_id", coachID)
	code.AutoResponse(c, shareInfo, nil)
}

// ShareStore 分享门店
func (h *ShareHandler) ShareStore(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	storeIDStr := c.Param("id")
	storeID := cast.ToUint(storeIDStr)
	if storeID == 0 {
		log.Error("门店ID格式错误", "id", storeIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "门店ID格式错误"))
		return
	}

	// 查询门店信息
	var store model.Store
	err := h.db.First(&store, storeID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		} else {
			log.Error("查询门店失败", "store_id", storeID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询门店失败"))
		}
		return
	}

	// 记录分享行为
	h.recordShareAction(userID.(uint), "store", uint(storeID))

	// 构建分享信息
	shareInfo := dto.ShareResponse{
		Type:        "store",
		Title:       store.Name,
		Description: store.Description,
		Image:       h.getStoreMainImage(store.ImageIDs), // 使用新的ImageIDs字段
		URL:         h.generateShareURL("store", uint(storeID)),
		ShareText:   h.generateShareText("store", store.Name, "", store.Address),
	}

	log.Info("门店分享成功", "user_id", userID, "store_id", storeID)
	code.AutoResponse(c, shareInfo, nil)
}

// GetShareStats 获取分享统计
func (h *ShareHandler) GetShareStats(c *gin.Context) {
	shareType := c.Query("type")
	targetIDStr := c.Query("target_id")

	if shareType == "" || targetIDStr == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "参数不完整"))
		return
	}

	targetID := cast.ToUint(targetIDStr)
	if targetID == 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "目标ID格式错误"))
		return
	}

	// 查询分享次数
	var shareCount int64
	h.db.Model(&model.ShareRecord{}).
		Where("share_type = ? AND target_id = ?", shareType, targetID).
		Count(&shareCount)

	// 查询今日分享次数
	var todayShareCount int64
	h.db.Model(&model.ShareRecord{}).
		Where("share_type = ? AND target_id = ? AND DATE(created_at) = CURDATE()", shareType, targetID).
		Count(&todayShareCount)

	stats := dto.ShareStatsResponse{
		TotalShares: int(shareCount),
		TodayShares: int(todayShareCount),
	}

	code.AutoResponse(c, stats, nil)
}

// recordShareAction 记录分享行为
func (h *ShareHandler) recordShareAction(userID uint, shareType string, targetID uint) {
	shareRecord := model.ShareRecord{
		UserID:    userID,
		ShareType: shareType,
		TargetID:  targetID,
	}

	if err := h.db.Create(&shareRecord).Error; err != nil {
		log.Error("记录分享行为失败", "user_id", userID, "type", shareType, "target_id", targetID, "error", err)
	}
}

// generateShareURL 生成分享链接
func (h *ShareHandler) generateShareURL(shareType string, targetID uint) string {
	// 这里可以根据实际需求生成小程序页面路径或H5页面链接
	switch shareType {
	case "course":
		return "/pages/course/detail?id=" + strconv.Itoa(int(targetID))
	case "coach":
		return "/pages/coach/detail?id=" + strconv.Itoa(int(targetID))
	case "store":
		return "/pages/store/detail?id=" + strconv.Itoa(int(targetID))
	default:
		return "/pages/index/index"
	}
}

// generateShareText 生成分享文案
func (h *ShareHandler) generateShareText(shareType, name, coachName, address string) string {
	switch shareType {
	case "course":
		if coachName != "" {
			return "推荐一个超棒的瑜伽课程：" + name + "，教练：" + coachName + "，快来一起练习吧！"
		}
		return "推荐一个超棒的瑜伽课程：" + name + "，快来一起练习吧！"
	case "coach":
		return "推荐一位专业的瑜伽教练：" + name + "，经验丰富，快来体验吧！"
	case "store":
		if address != "" {
			return "推荐一家优质的瑜伽馆：" + name + "，地址：" + address + "，环境很棒哦！"
		}
		return "推荐一家优质的瑜伽馆：" + name + "，环境很棒哦！"
	default:
		return "分享来自Yogaga瑜伽馆的精彩内容！"
	}
}

// getStoreMainImage 获取门店主图
func (h *ShareHandler) getStoreMainImage(imageIDs []string) string {
	// 从文件ID数组中获取第一张图片
	if len(imageIDs) == 0 {
		return "/images/default-store.jpg"
	}

	// 使用URLConverter生成预签名URL
	return h.urlConverter.ConvertFileIDToURL(imageIDs[0])
}
