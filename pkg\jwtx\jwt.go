package jwtx

import (
	"errors"
	"time"
	"yogaga/configs"
	"yogaga/internal/model"

	"github.com/golang-jwt/jwt/v5"
)

// Service handles JWT generation and validation.
type Service struct {
	accessSecret  []byte
	refreshSecret []byte
	accessExp     time.Duration
	refreshExp    time.Duration
}

// CustomClaims contains the standard JWT claims and custom fields.
type CustomClaims struct {
	UserID   string   `json:"user_id"` // 使用string以支持UUID
	Username string   `json:"username"`
	Roles    []string `json:"roles"`
	jwt.RegisteredClaims
}

// NewService creates a new JWT Service.
func NewService(cfg configs.JwtAuth) *Service {
	return &Service{
		accessSecret:  []byte(cfg.AccessSecret),
		refreshSecret: []byte(cfg.RefreshSecret),
		accessExp:     time.Duration(cfg.AccessExpire) * time.Second,
		refreshExp:    time.Duration(cfg.RefreshExpire) * time.Second,
	}
}

// GenerateTokens creates both access and refresh tokens for a user.
func (s *Service) GenerateTokens(user *model.User) (accessToken string, refreshToken string, err error) {
	// Generate Access Token
	accessToken, err = s.generateAccessToken(user)
	if err != nil {
		return "", "", err
	}

	// Generate Refresh Token
	refreshToken, err = s.generateRefreshToken(user)
	if err != nil {
		return "", "", err
	}

	return accessToken, refreshToken, nil
}

// GetAccessExpireSeconds returns the access token expiration time in seconds
func (s *Service) GetAccessExpireSeconds() int64 {
	return int64(s.accessExp.Seconds())
}

// GetRefreshExpireSeconds returns the refresh token expiration time in seconds
func (s *Service) GetRefreshExpireSeconds() int64 {
	return int64(s.refreshExp.Seconds())
}

// generateAccessToken creates a new access token.
func (s *Service) generateAccessToken(user *model.User) (string, error) {
	roleNames := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roleNames[i] = role.Name
	}

	claims := CustomClaims{
		UserID:   user.ID.String(), // 将UUID转换为字符串
		Username: user.Username,
		Roles:    roleNames,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.accessExp)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "yogaga-api",
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.accessSecret)
}

// generateRefreshToken creates a new refresh token.
func (s *Service) generateRefreshToken(user *model.User) (string, error) {
	claims := jwt.RegisteredClaims{
		Subject:   user.ID.String(), // 将用户UUID转换为字符串
		ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.refreshExp)),
		IssuedAt:  jwt.NewNumericDate(time.Now()),
		NotBefore: jwt.NewNumericDate(time.Now()),
		Issuer:    "yogaga-api-refresh",
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.refreshSecret)
}

// ParseToken validates and parses a token string, returning the custom claims.
func (s *Service) ParseToken(tokenStr string) (*CustomClaims, error) {
	token, err := jwt.ParseWithClaims(tokenStr, &CustomClaims{}, func(token *jwt.Token) (interface{}, error) {
		return s.accessSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*CustomClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, errors.New("invalid token")
}

// ParseRefreshToken validates and parses a refresh token string, returning the user ID.
func (s *Service) ParseRefreshToken(tokenStr string) (string, error) {
	token, err := jwt.ParseWithClaims(tokenStr, &jwt.RegisteredClaims{}, func(token *jwt.Token) (interface{}, error) {
		return s.refreshSecret, nil
	})

	if err != nil {
		return "", err
	}

	if claims, ok := token.Claims.(*jwt.RegisteredClaims); ok && token.Valid {
		// 检查 issuer 是否正确
		if claims.Issuer != "yogaga-api-refresh" {
			return "", errors.New("invalid refresh token issuer")
		}

		// 返回 Subject 字符串 (用户UUID)
		if claims.Subject == "" {
			return "", errors.New("empty user ID in refresh token")
		}

		return claims.Subject, nil
	}

	return "", errors.New("invalid refresh token")
}
