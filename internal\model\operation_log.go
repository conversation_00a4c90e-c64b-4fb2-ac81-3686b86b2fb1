package model

import (
	"time"
)

// OperationLog 操作日志模型
type OperationLog struct {
	BaseModel
	UserID        string    `json:"user_id" gorm:"type:varchar(36);not null;comment:操作用户ID(UUID)"`
	Username      string    `json:"username" gorm:"type:varchar(50);not null;comment:操作用户名"`
	UserType      string    `json:"user_type" gorm:"type:varchar(20);default:'admin';comment:用户类型 admin/user"`
	Module        string    `json:"module" gorm:"type:varchar(50);not null;comment:操作模块"`
	Action        string    `json:"action" gorm:"type:varchar(50);not null;comment:操作动作"`
	Description   string    `json:"description" gorm:"type:varchar(500);not null;comment:操作描述"`
	ResourceType  string    `json:"resource_type" gorm:"type:varchar(50);comment:资源类型"`
	ResourceID    string    `json:"resource_id" gorm:"type:varchar(36);comment:资源ID(UUID)"`
	RequestMethod string    `json:"request_method" gorm:"type:varchar(10);comment:请求方法"`
	RequestURL    string    `json:"request_url" gorm:"type:varchar(500);comment:请求URL"`
	RequestIP     string    `json:"request_ip" gorm:"type:varchar(45);comment:请求IP"`
	UserAgent     string    `json:"user_agent" gorm:"type:varchar(500);comment:用户代理"`
	RequestData   string    `json:"request_data" gorm:"type:text;comment:请求数据"`
	ResponseData  string    `json:"response_data" gorm:"type:text;comment:响应数据"`
	Status        string    `json:"status" gorm:"type:varchar(20);default:'success';comment:操作状态 success/failed"`
	ErrorMessage  string    `json:"error_message" gorm:"type:text;comment:错误信息"`
	Duration      int64     `json:"duration" gorm:"comment:执行时长(毫秒)"`
	OperatedAt    time.Time `json:"operated_at" gorm:"not null;comment:操作时间"`

	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operation_logs"
}

// LogLevel 日志级别
type LogLevel string

const (
	LogLevelInfo     LogLevel = "info"     // 一般操作
	LogLevelWarning  LogLevel = "warning"  // 警告操作
	LogLevelError    LogLevel = "error"    // 错误操作
	LogLevelCritical LogLevel = "critical" // 关键操作
)

// OperationModule 操作模块枚举
type OperationModule string

const (
	ModuleUser           OperationModule = "user"            // 用户管理
	ModuleRole           OperationModule = "role"            // 角色管理
	ModulePermission     OperationModule = "permission"      // 权限管理
	ModuleMenu           OperationModule = "menu"            // 菜单管理
	ModuleStore          OperationModule = "store"           // 门店管理
	ModuleCourse         OperationModule = "course"          // 课程管理
	ModuleSchedule       OperationModule = "schedule"        // 排课管理
	ModuleBooking        OperationModule = "booking"         // 预约管理
	ModuleMembershipCard OperationModule = "membership_card" // 会员卡管理
	ModuleCoach          OperationModule = "coach"           // 教练管理
	ModuleReport         OperationModule = "report"          // 报表管理
	ModuleSystem         OperationModule = "system"          // 系统管理
)

// OperationAction 操作动作枚举
type OperationAction string

const (
	ActionCreate OperationAction = "create" // 创建
	ActionUpdate OperationAction = "update" // 更新
	ActionDelete OperationAction = "delete" // 删除
	ActionView   OperationAction = "view"   // 查看
	ActionLogin  OperationAction = "login"  // 登录
	ActionLogout OperationAction = "logout" // 登出
	ActionExport OperationAction = "export" // 导出
	ActionImport OperationAction = "import" // 导入
)

// GetModuleName 获取模块中文名称
func (m OperationModule) GetModuleName() string {
	switch m {
	case ModuleUser:
		return "用户管理"
	case ModuleRole:
		return "角色管理"
	case ModulePermission:
		return "权限管理"
	case ModuleMenu:
		return "菜单管理"
	case ModuleStore:
		return "门店管理"
	case ModuleCourse:
		return "课程管理"
	case ModuleSchedule:
		return "排课管理"
	case ModuleBooking:
		return "预约管理"
	case ModuleMembershipCard:
		return "会员卡管理"
	case ModuleCoach:
		return "教练管理"
	case ModuleReport:
		return "报表管理"
	case ModuleSystem:
		return "系统管理"
	default:
		return "未知模块"
	}
}

// GetActionName 获取动作中文名称
func (a OperationAction) GetActionName() string {
	switch a {
	case ActionCreate:
		return "创建"
	case ActionUpdate:
		return "更新"
	case ActionDelete:
		return "删除"
	case ActionView:
		return "查看"
	case ActionLogin:
		return "登录"
	case ActionLogout:
		return "登出"
	case ActionExport:
		return "导出"
	case ActionImport:
		return "导入"
	default:
		return "未知操作"
	}
}

// IsSuccess 检查操作是否成功
func (ol *OperationLog) IsSuccess() bool {
	return ol.Status == "success"
}

// GetStatusText 获取状态文本
func (ol *OperationLog) GetStatusText() string {
	switch ol.Status {
	case "success":
		return "成功"
	case "failed":
		return "失败"
	default:
		return "未知"
	}
}
