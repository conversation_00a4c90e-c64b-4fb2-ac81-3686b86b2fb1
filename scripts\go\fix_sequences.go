package main

import (
	"fmt"
	"log"
	"os"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 需要修复序列的表名列表
var tables = []string{
	"menus",
	"users",
	"roles",
	"permissions",
	"stores",
	"course_categories",
	"courses",
	"class_rooms",
	"class_schedules",
	"membership_card_types",
	"membership_cards",
	"bookings",
	"booking_queues",
	"booking_applications",
	"banners",
	"coach_banners",
	"favorites",
	"operation_logs",
	"admin_notifications",
	"membership_card_transactions",
	"membership_card_leaves",
	"course_type_configs",
	"flexible_deduction_rules",
	"user_store_assignments",
	"member_sales_assignments",
	"resource_assignments",
	"migration_status",
	"files",
}

func main() {
	fmt.Println("🔧 开始修复PostgreSQL序列问题...")

	// 从环境变量获取数据库连接信息
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("DB_USER", "postgres")
	dbPassword := getEnv("DB_PASSWORD", "")
	dbName := getEnv("DB_NAME", "yogaga")

	if dbPassword == "" {
		fmt.Print("请输入数据库密码: ")
		fmt.Scanln(&dbPassword)
	}

	// 构建数据库连接字符串
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable TimeZone=Asia/Shanghai",
		dbHost, dbPort, dbUser, dbPassword, dbName)

	// 连接数据库
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatalf("❌ 连接数据库失败: %v", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 修复每个表的序列
	for _, tableName := range tables {
		err := fixSequence(db, tableName)
		if err != nil {
			fmt.Printf("⚠️ 修复表 %s 的序列失败: %v\n", tableName, err)
		} else {
			fmt.Printf("✅ 修复表 %s 的序列成功\n", tableName)
		}
	}

	// 显示修复结果
	fmt.Println("\n📊 序列修复结果:")
	showSequenceStatus(db)

	fmt.Println("\n🎉 序列修复完成！")
}

// fixSequence 修复指定表的序列
func fixSequence(db *gorm.DB, tableName string) error {
	sequenceName := tableName + "_id_seq"
	
	// 检查表是否存在
	var exists bool
	err := db.Raw("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = ?)", tableName).Scan(&exists).Error
	if err != nil {
		return fmt.Errorf("检查表是否存在失败: %w", err)
	}
	
	if !exists {
		fmt.Printf("⚠️ 表 %s 不存在，跳过\n", tableName)
		return nil
	}

	// 检查序列是否存在
	err = db.Raw("SELECT EXISTS (SELECT FROM information_schema.sequences WHERE sequence_name = ?)", sequenceName).Scan(&exists).Error
	if err != nil {
		return fmt.Errorf("检查序列是否存在失败: %w", err)
	}
	
	if !exists {
		fmt.Printf("⚠️ 序列 %s 不存在，跳过\n", sequenceName)
		return nil
	}

	// 修复序列
	sql := fmt.Sprintf("SELECT setval('%s', COALESCE((SELECT MAX(id) FROM %s), 1), true)", sequenceName, tableName)
	err = db.Exec(sql).Error
	if err != nil {
		return fmt.Errorf("修复序列失败: %w", err)
	}

	return nil
}

// showSequenceStatus 显示序列状态
func showSequenceStatus(db *gorm.DB) {
	type SequenceInfo struct {
		SchemaName   string `json:"schemaname"`
		TableName    string `json:"tablename"`
		SequenceName string `json:"sequencename"`
		LastValue    int64  `json:"last_value"`
	}

	var sequences []SequenceInfo
	err := db.Raw(`
		SELECT 
			schemaname,
			tablename,
			sequencename,
			last_value
		FROM pg_sequences 
		WHERE schemaname = 'public'
		ORDER BY tablename
	`).Scan(&sequences).Error

	if err != nil {
		fmt.Printf("❌ 查询序列状态失败: %v\n", err)
		return
	}

	fmt.Printf("%-25s %-30s %s\n", "表名", "序列名", "当前值")
	fmt.Println("─────────────────────────────────────────────────────────────────")
	
	for _, seq := range sequences {
		fmt.Printf("%-25s %-30s %d\n", seq.TableName, seq.SequenceName, seq.LastValue)
	}
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
