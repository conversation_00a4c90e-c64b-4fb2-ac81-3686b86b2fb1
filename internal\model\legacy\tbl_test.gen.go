// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblTest = "tbl_test"

// TblTest mapped from table <tbl_test>
type TblTest struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	C          string    `gorm:"column:c" json:"c"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IP         string    `gorm:"column:ip" json:"ip"`
	I          string    `gorm:"column:i" json:"i"`
	M          string    `gorm:"column:m" json:"m"`
	P          string    `gorm:"column:p" json:"p"`
	G          string    `gorm:"column:g" json:"g"`
}

// TableName TblTest's table name
func (*TblTest) TableName() string {
	return TableNameTblTest
}
