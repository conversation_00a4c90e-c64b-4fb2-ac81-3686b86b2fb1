#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试刷新token功能并修复UUID查询问题
"""

import requests
import time
import json

class RefreshTokenTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.access_token = None
        self.refresh_token = None

    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录获取access_token和refresh_token"""
        print("🔐 正在登录系统...")

        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {
            "username": username,
            "password": password
        }

        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    data = result.get('data', {})
                    self.access_token = data.get('access_token')
                    self.refresh_token = data.get('refresh_token')

                    if self.access_token and self.refresh_token:
                        print("✅ 登录成功")
                        print(f"   Access Token: {self.access_token[:20]}...")
                        print(f"   Refresh Token: {self.refresh_token[:20]}...")
                        return True
                    else:
                        print("❌ 未获取到完整的token信息")
                        print(f"   响应数据: {data}")
                        return False

            print(f"❌ 登录失败: {response.text}")
            return False

        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False

    def test_refresh_token(self) -> bool:
        """测试刷新token功能"""
        print("\n🔄 测试刷新token功能...")

        if not self.refresh_token:
            print("❌ 没有refresh_token，无法测试")
            return False

        refresh_url = f"{self.base_url}/api/v1/public/auth/refresh"

        # 测试不同的请求方式
        test_cases = [
            {
                "name": "使用Authorization头",
                "headers": {"Authorization": f"Bearer {self.refresh_token}"},
                "data": {}
            },
            {
                "name": "使用JSON body",
                "headers": {"Content-Type": "application/json"},
                "data": {"refresh_token": self.refresh_token}
            },
            {
                "name": "使用表单数据",
                "headers": {"Content-Type": "application/x-www-form-urlencoded"},
                "data": {"refresh_token": self.refresh_token}
            }
        ]

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 测试方案 {i}: {test_case['name']}")

            try:
                if test_case['name'] == "使用表单数据":
                    response = requests.post(
                        refresh_url,
                        headers=test_case['headers'],
                        data=test_case['data']
                    )
                else:
                    response = requests.post(
                        refresh_url,
                        headers=test_case['headers'],
                        json=test_case['data'] if test_case['data'] else None
                    )

                print(f"   状态码: {response.status_code}")
                print(f"   响应: {response.text}")

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        new_access_token = result.get('data', {}).get('access_token')
                        new_refresh_token = result.get('data', {}).get('refresh_token')

                        if new_access_token:
                            print(f"   ✅ 刷新成功!")
                            print(f"   新Access Token: {new_access_token[:20]}...")
                            if new_refresh_token:
                                print(f"   新Refresh Token: {new_refresh_token[:20]}...")
                            return True
                        else:
                            print(f"   ❌ 响应中没有新的access_token")
                    else:
                        print(f"   ❌ 业务错误: {result}")
                else:
                    print(f"   ❌ HTTP错误: {response.status_code}")

            except Exception as e:
                print(f"   ❌ 请求异常: {str(e)}")

        return False

    def test_with_expired_token(self) -> bool:
        """测试使用过期或无效token的情况"""
        print("\n🧪 测试无效token情况...")

        refresh_url = f"{self.base_url}/api/v1/public/auth/refresh"

        # 测试无效token
        invalid_tokens = [
            "invalid_token_123",
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid",
            "",
            None
        ]

        for i, invalid_token in enumerate(invalid_tokens, 1):
            print(f"\n📋 测试无效token {i}: {invalid_token}")

            try:
                if invalid_token is None:
                    response = requests.post(refresh_url, json={})
                else:
                    response = requests.post(
                        refresh_url,
                        json={"refresh_token": invalid_token}
                    )

                print(f"   状态码: {response.status_code}")
                print(f"   响应: {response.text}")

                # 期望这些请求都应该失败
                if response.status_code != 200:
                    print(f"   ✅ 正确拒绝了无效token")
                else:
                    result = response.json()
                    if result.get('code') != 0:
                        print(f"   ✅ 正确返回了错误码")
                    else:
                        print(f"   ⚠️  意外成功了，可能有安全问题")

            except Exception as e:
                print(f"   ❌ 请求异常: {str(e)}")

        return True

    def test_user_lookup_fix(self) -> bool:
        """测试用户查找修复"""
        print("\n🔍 测试用户查找功能...")

        if not self.access_token:
            print("❌ 没有access_token，无法测试")
            return False

        # 使用access_token访问需要认证的接口
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }

        # 测试获取用户信息
        user_info_url = f"{self.base_url}/api/v1/admin/users/profile"

        try:
            response = requests.get(user_info_url, headers=headers)
            print(f"   获取用户信息 - 状态码: {response.status_code}")
            print(f"   响应: {response.text}")

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    user_data = result.get('data', {})
                    user_id = user_data.get('id')
                    username = user_data.get('username')

                    print(f"   ✅ 用户信息获取成功")
                    print(f"   用户ID: {user_id}")
                    print(f"   用户名: {username}")

                    # 验证用户ID格式
                    if user_id and len(str(user_id)) == 36 and '-' in str(user_id):
                        print(f"   ✅ 用户ID格式正确 (UUID)")
                        return True
                    else:
                        print(f"   ⚠️  用户ID格式可能有问题: {user_id}")
                        return False
                else:
                    print(f"   ❌ 业务错误: {result}")
            else:
                print(f"   ❌ HTTP错误: {response.status_code}")

        except Exception as e:
            print(f"   ❌ 请求异常: {str(e)}")

        return False

    def run_all_tests(self) -> dict:
        """运行所有测试"""
        print("🚀 开始刷新token功能测试...")
        print("=" * 60)

        results = {}

        # 1. 登录测试
        results['login'] = self.login()
        if not results['login']:
            print("\n❌ 登录失败，无法继续测试")
            return results

        # 2. 刷新token测试
        results['refresh_token'] = self.test_refresh_token()

        # 3. 无效token测试
        results['invalid_token'] = self.test_with_expired_token()

        # 4. 用户查找测试
        results['user_lookup'] = self.test_user_lookup_fix()

        # 输出测试结果
        print("\n📊 测试结果汇总:")
        print("=" * 60)

        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name:<20}: {status}")

        # 分析问题
        print("\n🔍 问题分析:")
        print("=" * 60)

        if not results.get('refresh_token', False):
            print("❌ 刷新token功能存在问题")
            print("   可能的原因:")
            print("   1. UUID用户ID在SQL查询中格式错误")
            print("   2. 数据库字段类型不匹配")
            print("   3. 查询语句中缺少引号")
            print("\n💡 建议修复:")
            print("   1. 检查用户查询SQL语句")
            print("   2. 确保UUID字段使用正确的数据类型")
            print("   3. 在SQL查询中正确处理UUID格式")
        else:
            print("✅ 刷新token功能正常")

        return results

def create_sql_fix_suggestions():
    """生成SQL修复建议"""
    print("\n🔧 SQL修复建议:")
    print("=" * 60)

    print("问题: UUID在SQL查询中被当作数字处理")
    print("错误示例: WHERE user_id = 5550b113-1cea-462f-a7d9-1b58c76bebcc")
    print("正确示例: WHERE user_id = '5550b113-1cea-462f-a7d9-1b58c76bebcc'")

    print("\n修复方案:")
    print("1. 在Go代码中确保UUID参数被正确引用:")
    print("   错误: db.Where(\"user_id = ?\", userID)")
    print("   正确: db.Where(\"user_id = ?\", userID) // GORM会自动处理")

    print("\n2. 检查数据库字段类型:")
    print("   确保user_id字段类型为UUID或VARCHAR(36)")

    print("\n3. 如果使用原生SQL，确保参数化查询:")
    print("   错误: fmt.Sprintf(\"SELECT * FROM users WHERE id = %s\", userID)")
    print("   正确: db.Raw(\"SELECT * FROM users WHERE id = ?\", userID)")

if __name__ == "__main__":
    # 创建测试器实例
    tester = RefreshTokenTester()

    # 运行所有测试
    results = tester.run_all_tests()

    # 生成修复建议
    create_sql_fix_suggestions()

    print("\n🏁 测试完成")
