# 用户角色管理最佳实践

## 🎯 设计原则

### 1. 角色分类策略

**后台员工用户（需要分配 role_id）：**
- 超级管理员 (ID: 1) - 系统最高权限
- 总监 (ID: 2) - 运营管理权限
- 店长 (ID: 3) - 门店管理权限
- 销售 (ID: 4) - 销售相关权限
- 教练 (ID: 5) - 课程教学权限
- 前台 (ID: 6) - 基础服务权限

**小程序会员用户（不分配 role_id）：**
- 通过 `PlatformAccess` 字段控制访问权限
- 无需复杂的角色层级管理
- 简化权限控制逻辑

### 2. 权限控制策略

```go
// 后台员工 - 基于角色ID的精确权限控制
func (u *User) IsSuperAdmin() bool { return u.HasRole(1) }
func (u *User) IsManager() bool { return u.HasRole(3) }
func (u *User) IsSales() bool { return u.HasRole(4) }

// 会员用户 - 基于平台访问权限
func (u *User) IsMember() bool {
    return (u.PlatformAccess & enum.PlatformAccessWechat) != 0 && !u.IsStaff()
}

// 组合判断
func (u *User) IsStaff() bool {
    return u.IsSuperAdmin() || u.IsDirector() || u.IsManager() || 
           u.IsSales() || u.IsCoachRole() || u.IsReceptionist()
}
```

## 🚀 性能优势

### 基于固定ID的身份判断

**优势：**
- ✅ **性能极高** - 直接数字比较，O(n)复杂度（n为用户角色数量）
- ✅ **稳定可靠** - 不依赖角色名称，避免字符串匹配错误
- ✅ **易于维护** - 代码简洁，逻辑清晰
- ✅ **多语言支持** - 角色名称可以本地化，不影响逻辑

**对比旧方法：**
```go
// ❌ 旧方法 - 基于字符串匹配（性能差，易出错）
func (u *User) IsManager() bool {
    for _, role := range u.Roles {
        if role.Name == "manager" || role.Name == "店长" ||
           strings.Contains(strings.ToLower(role.Name), "manager") {
            return true
        }
    }
    return false
}

// ✅ 新方法 - 基于固定ID（性能优，稳定）
func (u *User) IsManager() bool {
    return u.HasRole(3) // 店长 ID = 3
}
```

## 📊 用户类型设计

### 1. 后台管理用户

```go
// 创建后台用户示例
user := model.User{
    Username:       "manager",
    PlatformAccess: enum.PlatformAccessAdmin, // 只能访问管理后台
    Email:          "<EMAIL>",
    IsActive:       true,
    Roles:          []model.Role{{ID: 3}}, // 分配店长角色
}
```

**特点：**
- 必须分配具体的 role_id
- 通过角色控制细粒度权限
- 只能访问管理后台

### 2. 教练用户（特殊情况）

```go
// 创建教练用户示例
user := model.User{
    Username:       "coach1",
    PlatformAccess: enum.PlatformAccessAdmin | enum.PlatformAccessWechat, // 双平台访问
    Email:          "<EMAIL>",
    IsActive:       true,
    IsCoach:        true,
    WeChatOpenID:   &openID, // 需要微信OpenID
    Roles:          []model.Role{{ID: 5}}, // 分配教练角色
}
```

**特点：**
- 既是员工又是小程序用户
- 可以访问管理后台和小程序
- 需要微信OpenID用于小程序功能

### 3. 小程序会员用户

```go
// 创建会员用户示例
user := model.User{
    Username:       "member1",
    PlatformAccess: enum.PlatformAccessWechat, // 只能访问小程序
    WeChatOpenID:   &openID, // 必须有微信OpenID
    IsActive:       true,
    // 不分配 Roles，通过平台权限控制
}
```

**特点：**
- 不分配 role_id
- 只能访问小程序
- 必须有微信OpenID
- 权限通过平台访问控制

## 🔧 微信OpenID管理

### 指针类型设计

```go
// 用户模型中的微信字段
type User struct {
    WeChatOpenID  *string `json:"wechat_openid" gorm:"type:varchar(100);uniqueIndex"`
    WeChatUnionID *string `json:"wechat_unionid" gorm:"type:varchar(100);index"`
}

// 辅助方法
func (u *User) GetWeChatOpenID() string {
    if u.WeChatOpenID == nil { return "" }
    return *u.WeChatOpenID
}

func (u *User) HasWeChatOpenID() bool {
    return u.WeChatOpenID != nil && *u.WeChatOpenID != ""
}

func (u *User) SetWeChatOpenID(openID string) {
    if openID == "" {
        u.WeChatOpenID = nil
    } else {
        u.WeChatOpenID = &openID
    }
}
```

**设计原因：**
- 使用指针类型避免空字符串的唯一索引冲突
- 只有小程序用户和教练需要微信OpenID
- 后台管理用户不需要微信OpenID

## 📋 最佳实践总结

### ✅ 推荐做法

1. **后台员工** - 分配具体的 role_id (1-6)
2. **小程序会员** - 不分配 role_id，通过平台权限控制
3. **教练用户** - 特殊处理，既有角色又有微信权限
4. **身份判断** - 使用基于固定ID的高效方法
5. **微信OpenID** - 使用指针类型，避免唯一索引冲突

### ❌ 避免做法

1. **不要**为所有用户都分配角色
2. **不要**使用字符串匹配进行身份判断
3. **不要**为会员用户创建复杂的角色层级
4. **不要**在微信OpenID字段使用空字符串

### 🎯 权限控制流程

```go
// 权限验证示例
func CheckPermission(user *model.User, action string) bool {
    // 1. 检查是否是员工
    if !user.IsStaff() {
        return false // 非员工无管理权限
    }
    
    // 2. 基于角色ID进行精确权限控制
    switch action {
    case "user:create":
        return user.IsSuperAdmin() // 只有超级管理员可以创建用户
    case "course:create":
        return user.IsManager() || user.IsSuperAdmin() // 店长及以上可以创建课程
    case "booking:create":
        return user.IsStaff() // 所有员工都可以创建预约
    default:
        return false
    }
}
```

## 🔄 数据迁移建议

如果需要从旧系统迁移：

1. **清空现有用户数据**
2. **重新初始化角色权限系统**
3. **按新规则创建用户**
4. **更新所有身份判断代码**

使用提供的脚本：
```bash
# 1. 强制清理所有数据
go run cmd/scripts/force_clean/main.go

# 2. 重新初始化角色权限
go run cmd/scripts/clean_reset_roles_permissions/main.go

# 3. 创建测试用户（仅后台用户）
go run cmd/scripts/init_test_users/main.go

# 4. 验证身份判断功能
go run cmd/scripts/test_user_roles/main.go
```

这种设计既保证了性能，又简化了权限管理，是企业级应用的最佳实践。
