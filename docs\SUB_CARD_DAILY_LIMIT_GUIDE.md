# 副卡和单日限制功能完善指南

## 📋 **功能概览**

本次完善为瑜伽馆会员卡系统添加了两个核心功能：
1. **增强副卡管理** - 完善的副卡权限控制和管理功能
2. **单日预约限制** - 灵活的单日预约次数限制机制

---

## 🎴 **副卡功能完善**

### **核心特性**

#### 1. **权限分级管理**
- **主卡权限**：可预约、查看所有记录、管理副卡、代约功能
- **副卡权限**：可预约、仅查看自己记录、无管理权限

#### 2. **智能权限验证**
```go
// 获取会员卡权限
GET /api/v1/admin/sub-cards/cards/{card_id}/permission

// 响应示例
{
  "can_book": true,
  "can_view_all": true,
  "can_manage_cards": true
}
```

#### 3. **副卡统计信息**
```go
// 获取副卡列表（带统计）
GET /api/v1/admin/sub-cards/cards/{card_id}/stats

// 响应示例
{
  "sub_cards": [
    {
      "id": 123,
      "card_number": "SC001",
      "user_name": "张三",
      "user_phone": "13800138001",
      "booking_count": 15,
      "last_booking": "2024-01-15 14:30:00"
    }
  ],
  "total": 1
}
```

#### 4. **副卡生命周期管理**
```bash
# 停用副卡
PUT /api/v1/admin/sub-cards/{sub_card_id}/disable
{
  "reason": "用户要求暂停使用"
}

# 启用副卡
PUT /api/v1/admin/sub-cards/{sub_card_id}/enable

# 副卡转主卡
POST /api/v1/admin/sub-cards/{sub_card_id}/transfer-to-main
{
  "new_card_type_id": 5
}
```

### **业务场景示例**

#### 场景1：家庭共享储值卡
```
主卡：妈妈（张女士）
副卡：女儿（小张）、儿子（小李）

权限分配：
- 妈妈：可以查看所有人的预约记录，可以帮孩子们预约课程
- 孩子们：只能看到自己的预约记录，可以自己预约课程
```

#### 场景2：企业团体卡
```
主卡：HR部门
副卡：员工A、员工B、员工C

管理方式：
- HR可以统一管理所有员工的预约情况
- 员工离职时可以停用对应副卡
- 新员工入职时可以添加新副卡
```

---

## 📅 **单日限制功能完善**

### **核心特性**

#### 1. **多维度限制检查**
- **卡种级别限制**：在会员卡类型中设置默认单日限制
- **卡片级别限制**：为特定会员卡设置个性化限制
- **课程类型区分**：期限卡按课程类型限制，其他卡种全局限制

#### 2. **实时限制查询**
```go
// 检查单日预约限制
POST /api/v1/admin/sub-cards/daily-limit/check
{
  "card_id": 123,
  "course_id": 456
}

// 响应示例
{
  "can_book": true,
  "current_count": 1,
  "limit_count": 2,
  "remaining_count": 1,
  "message": "今日已预约 1 节，限制 2 节"
}
```

#### 3. **限制信息展示**
```go
// 获取会员卡限制信息
GET /api/v1/admin/sub-cards/cards/{card_id}/daily-limit

// 响应示例
{
  "card_id": 123,
  "card_number": "YG001",
  "card_type_name": "瑜伽期限卡",
  "daily_limit": 2,
  "has_limit": true,
  "is_main_card": true,
  "can_set_daily_limit": true
}
```

### **限制规则说明**

#### 1. **期限卡限制规则**
- 按课程类型分别计算（瑜伽课、舞蹈课分开统计）
- 每日最多预约2节同类型课程
- 副卡与主卡分别计算限制

#### 2. **次数卡/储值卡限制规则**
- 全局计算（所有课程类型合并统计）
- 通常无单日限制或限制较宽松
- 副卡共享主卡的限制配额

#### 3. **副卡限制共享机制**
```
共享储值卡示例：
- 主卡：每日限制3节课
- 副卡A：每日限制3节课
- 副卡B：每日限制3节课

说明：每张副卡都有独立的单日限制，不与主卡共享
```

---

## 🔧 **技术实现亮点**

### **1. 服务分层设计**
```
SubCardService      - 副卡权限管理
DailyLimitService   - 单日限制检查
UnifiedBookingService - 统一预约处理（集成上述服务）
```

### **2. 权限验证流程**
```
预约请求 → 副卡权限验证 → 单日限制检查 → 扣减规则匹配 → 创建预约
```

### **3. 数据库优化**
- 高效的预约统计查询
- 智能的权限关联查询
- 灵活的限制配置存储

---

## 📱 **前端集成建议**

### **1. 副卡管理界面**
```
会员卡详情页面：
├── 基本信息
├── 余额信息
├── 副卡管理 (仅主卡显示)
│   ├── 副卡列表
│   ├── 添加副卡
│   ├── 副卡状态管理
│   └── 使用统计
└── 预约记录 (根据权限显示)
```

### **2. 预约界面优化**
```
课程预约页面：
├── 课程信息
├── 会员卡选择
├── 单日限制提示 ⭐新增
│   └── "今日已预约 1/2 节瑜伽课"
├── 代约选择 (主卡用户) ⭐新增
└── 确认预约
```

### **3. 状态提示优化**
```javascript
// 预约前检查示例
const checkDailyLimit = async (cardId, courseId) => {
  const response = await api.post('/sub-cards/daily-limit/check', {
    card_id: cardId,
    course_id: courseId
  });

  if (!response.can_book) {
    showWarning(response.message);
    return false;
  }

  if (response.remaining_count <= 1) {
    showInfo(`今日还可预约 ${response.remaining_count} 节课`);
  }

  return true;
};
```

---

## 🎯 **业务价值**

### **1. 提升用户体验**
- **家庭用户**：一卡多人使用，方便管理
- **企业用户**：统一管理员工健身福利
- **个人用户**：清晰的预约限制提示

### **2. 优化运营管理**
- **精准控制**：避免用户过度预约影响其他会员
- **数据洞察**：详细的使用统计帮助优化课程安排
- **灵活配置**：不同卡种可设置不同的限制策略

### **3. 增强系统稳定性**
- **权限隔离**：副卡用户无法查看其他人信息
- **限制保护**：防止系统资源被恶意占用
- **事务安全**：完整的权限验证和限制检查

---

## 🚀 **部署建议**

### **1. 数据迁移**
```sql
-- 为现有会员卡设置默认单日限制
UPDATE membership_cards
SET daily_booking_limit = (
  SELECT daily_booking_limit
  FROM membership_card_types
  WHERE id = membership_cards.card_type_id
)
WHERE daily_booking_limit = 0;
```

### **2. 配置检查**
- 确认所有卡种的 `can_add_sub_card` 和 `can_set_daily_limit` 配置
- 验证期限卡的 `daily_booking_limit` 设置为2
- 检查储值卡的副卡支持配置

### **3. 功能测试**
- 副卡创建和权限验证
- 单日限制检查和计数
- 预约流程完整性测试
- 权限边界测试

---

## 📞 **技术支持**

如有任何问题或需要进一步定制，请联系开发团队。

**功能特点**：
✅ 完整的副卡权限管理
✅ 灵活的单日预约限制
✅ 智能的权限验证机制
✅ 详细的使用统计信息
✅ 简单易用的API接口
