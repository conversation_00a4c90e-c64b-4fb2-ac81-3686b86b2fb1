package handler

import (
	"context"
	"net/url"
	"time"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

// BatchLoadDirectFileURLs 批量加载文件的直接访问URL（MinIO预签名URL）
// 这是核心优化函数，将N+1查询优化为2次操作：1次查文件元数据 + 批量生成预签名URL
func BatchLoadDirectFileURLs(ctx context.Context, db *gorm.DB, storage interface {
	GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error)
}, fileIDs []string) (map[string]string, error) {
	if len(fileIDs) == 0 {
		return make(map[string]string), nil
	}

	// 第1次查询：批量获取所有文件元数据
	var files []model.File
	err := db.Where("id IN ? AND status = ?", fileIDs, model.StatusActive).Find(&files).Error
	if err != nil {
		return nil, err
	}

	// 第2次操作：批量生成预签名URL（直接访问MinIO，无需后台重定向）
	urlMap := make(map[string]string)
	for _, file := range files {
		// 生成1小时有效期的预签名URL，前端可直接访问
		presignedURL, err := storage.GetPresignedDownloadURL(ctx, file.ObjectName, time.Hour, nil)
		if err != nil {
			log.Error("生成预签名URL失败", "file_id", file.ID, "object_name", file.ObjectName, "error", err)
			continue
		}
		urlMap[file.ID.String()] = presignedURL.String()
	}

	return urlMap, nil
}

// BuildDirectFileURLsArray 将文件ID数组转换为预签名URL数组
func BuildDirectFileURLsArray(ctx context.Context, db *gorm.DB, storage interface {
	GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error)
}, fileIDs []string) []string {
	if len(fileIDs) == 0 {
		return []string{}
	}

	urlMap, err := BatchLoadDirectFileURLs(ctx, db, storage, fileIDs)
	if err != nil {
		log.Error("批量生成文件URL失败", "error", err)
		return []string{}
	}

	// 按原始顺序返回URL数组
	urls := make([]string, 0, len(fileIDs))
	for _, fileID := range fileIDs {
		if url, exists := urlMap[fileID]; exists {
			urls = append(urls, url)
		}
	}

	return urls
}

// getCoachName 获取教练姓名的公共函数
func getCoachName(coach *model.User) string {
	if coach == nil {
		return ""
	}

	// 优先使用昵称，如果没有则使用用户名
	if coach.NickName != "" {
		return coach.NickName
	}

	return coach.Username
}

// URLConverter 统一的文件URL转换接口
type URLConverter interface {
	// ConvertFileIDToURL 将单个文件ID转换为预签名URL
	ConvertFileIDToURL(fileID string) string
	// ConvertFileIDsToURLs 将文件ID数组转换为预签名URL数组
	ConvertFileIDsToURLs(fileIDs []string) []string
	// ConvertStringArrayToURLs 将pq.StringArray转换为预签名URL数组
	ConvertStringArrayToURLs(fileIDs interface{}) []string
}

// DefaultURLConverter 默认的URL转换器实现
type DefaultURLConverter struct {
	db      *gorm.DB
	storage interface {
		GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error)
	}
}

// NewURLConverter 创建URL转换器
func NewURLConverter(db *gorm.DB, storage interface {
	GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error)
}) URLConverter {
	return &DefaultURLConverter{
		db:      db,
		storage: storage,
	}
}

// ConvertFileIDToURL 将单个文件ID转换为预签名URL
func (c *DefaultURLConverter) ConvertFileIDToURL(fileID string) string {
	if fileID == "" || c.db == nil || c.storage == nil {
		return ""
	}

	urls := c.ConvertFileIDsToURLs([]string{fileID})
	if len(urls) > 0 {
		return urls[0]
	}
	return ""
}

// ConvertFileIDsToURLs 将文件ID数组转换为预签名URL数组
func (c *DefaultURLConverter) ConvertFileIDsToURLs(fileIDs []string) []string {
	if len(fileIDs) == 0 || c.db == nil || c.storage == nil {
		return []string{}
	}

	urlMap, err := BatchLoadDirectFileURLs(context.Background(), c.db, c.storage, fileIDs)
	if err != nil {
		log.Error("批量生成文件URL失败", "error", err)
		return []string{}
	}

	urls := make([]string, 0, len(fileIDs))
	for _, fileID := range fileIDs {
		if url, exists := urlMap[fileID]; exists {
			urls = append(urls, url)
		}
	}

	return urls
}

// ConvertStringArrayToURLs 将pq.StringArray转换为预签名URL数组
func (c *DefaultURLConverter) ConvertStringArrayToURLs(fileIDs interface{}) []string {
	switch v := fileIDs.(type) {
	case []string:
		return c.ConvertFileIDsToURLs(v)
	case pq.StringArray:
		return c.ConvertFileIDsToURLs([]string(v))
	case nil:
		return []string{}
	default:
		return []string{}
	}
}

// 临时兼容函数，用于其他handler的过渡
// 这些函数将在后续版本中被移除，请使用URLConverter接口

// getAvatarURL 临时兼容函数
func getAvatarURL(user *model.User) string {
	// 返回空字符串，需要各handler自行实现URL转换
	return ""
}

// getBannerImageURL 临时兼容函数
func getBannerImageURL(banner *model.Banner) string {
	// 返回空字符串，需要各handler自行实现URL转换
	return ""
}

// getStoreImageURLs 临时兼容函数
func getStoreImageURLs(store *model.Store) []string {
	// 返回空数组，需要各handler自行实现URL转换
	return []string{}
}
