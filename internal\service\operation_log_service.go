package service

import (
	"encoding/json"
	"fmt"
	"time"
	"yogaga/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OperationLogService 操作日志服务
type OperationLogService struct {
	db *gorm.DB
}

// NewOperationLogService 创建操作日志服务实例
func NewOperationLogService(db *gorm.DB) *OperationLogService {
	return &OperationLogService{
		db: db,
	}
}

// LogOperation 记录操作日志
func (s *OperationLogService) LogOperation(c *gin.Context, userID string, username string, module model.OperationModule, action model.OperationAction, description string, resourceType string, resourceID string, requestData interface{}, responseData interface{}, err error) {
	// 序列化请求数据
	var requestDataStr string
	if requestData != nil {
		if data, jsonErr := json.Marshal(requestData); jsonErr == nil {
			requestDataStr = string(data)
		}
	}

	// 序列化响应数据
	var responseDataStr string
	if responseData != nil {
		if data, jsonErr := json.Marshal(responseData); jsonErr == nil {
			responseDataStr = string(data)
		}
	}

	// 确定操作状态
	status := "success"
	errorMessage := ""
	if err != nil {
		status = "failed"
		errorMessage = err.Error()
	}

	// 创建日志记录
	log := model.OperationLog{
		UserID:        userID,
		Username:      username,
		UserType:      "admin", // 默认为管理员操作
		Module:        string(module),
		Action:        string(action),
		Description:   description,
		ResourceType:  resourceType,
		ResourceID:    resourceID,
		RequestMethod: c.Request.Method,
		RequestURL:    c.Request.URL.String(),
		RequestIP:     c.ClientIP(),
		UserAgent:     c.Request.UserAgent(),
		RequestData:   requestDataStr,
		ResponseData:  responseDataStr,
		Status:        status,
		ErrorMessage:  errorMessage,
		OperatedAt:    time.Now(),
	}

	// 异步保存日志（避免影响主业务）
	go func() {
		if err := s.db.Create(&log).Error; err != nil {
			// 日志保存失败，可以记录到文件或其他地方
			fmt.Printf("保存操作日志失败: %v\n", err)
		}
	}()
}

// LogUserOperation 记录用户操作（小程序端）
func (s *OperationLogService) LogUserOperation(c *gin.Context, userID string, username string, module model.OperationModule, action model.OperationAction, description string, resourceType string, resourceID string) {
	log := model.OperationLog{
		UserID:        userID,
		Username:      username,
		UserType:      "user", // 小程序用户
		Module:        string(module),
		Action:        string(action),
		Description:   description,
		ResourceType:  resourceType,
		ResourceID:    resourceID,
		RequestMethod: c.Request.Method,
		RequestURL:    c.Request.URL.String(),
		RequestIP:     c.ClientIP(),
		UserAgent:     c.Request.UserAgent(),
		Status:        "success",
		OperatedAt:    time.Now(),
	}

	// 异步保存日志
	go func() {
		if err := s.db.Create(&log).Error; err != nil {
			fmt.Printf("保存用户操作日志失败: %v\n", err)
		}
	}()
}

// GetOperationLogs 获取操作日志列表
func (s *OperationLogService) GetOperationLogs(page, pageSize int, userID string, module, action, status string, startDate, endDate time.Time) ([]model.OperationLog, int64, error) {
	var logs []model.OperationLog
	var total int64

	// 构建查询
	query := s.db.Model(&model.OperationLog{}).Preload("User")

	// 筛选条件
	if userID != "" {
		query = query.Where("user_id = ?", userID)
	}
	if module != "" {
		query = query.Where("module = ?", module)
	}
	if action != "" {
		query = query.Where("action = ?", action)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if !startDate.IsZero() {
		query = query.Where("operated_at >= ?", startDate)
	}
	if !endDate.IsZero() {
		query = query.Where("operated_at <= ?", endDate)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("operated_at DESC").Find(&logs).Error; err != nil {
		return nil, 0, err
	}

	return logs, total, nil
}

// GetOperationLogDetail 获取操作日志详情
func (s *OperationLogService) GetOperationLogDetail(logID string) (*model.OperationLog, error) {
	var log model.OperationLog
	err := s.db.Preload("User").First(&log, logID).Error
	if err != nil {
		return nil, err
	}
	return &log, nil
}

// DeleteOperationLog 删除操作日志（仅超级管理员）
func (s *OperationLogService) DeleteOperationLog(logID string) error {
	return s.db.Where("id = ?", logID).Delete(&model.OperationLog{}).Error
}

// CleanOldLogs 清理旧日志（保留指定天数）
func (s *OperationLogService) CleanOldLogs(keepDays int) error {
	cutoffDate := time.Now().AddDate(0, 0, -keepDays)
	return s.db.Where("operated_at < ?", cutoffDate).Delete(&model.OperationLog{}).Error
}

// GetOperationStats 获取操作统计
func (s *OperationLogService) GetOperationStats(startDate, endDate time.Time) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总操作数
	var totalCount int64
	query := s.db.Model(&model.OperationLog{})
	if !startDate.IsZero() {
		query = query.Where("operated_at >= ?", startDate)
	}
	if !endDate.IsZero() {
		query = query.Where("operated_at <= ?", endDate)
	}
	query.Count(&totalCount)
	stats["total_count"] = totalCount

	// 成功/失败统计
	var successCount, failedCount int64
	query.Where("status = ?", "success").Count(&successCount)
	query.Where("status = ?", "failed").Count(&failedCount)
	stats["success_count"] = successCount
	stats["failed_count"] = failedCount

	// 按模块统计
	var moduleStats []map[string]interface{}
	s.db.Model(&model.OperationLog{}).
		Select("module, COUNT(*) as count").
		Where("operated_at >= ? AND operated_at <= ?", startDate, endDate).
		Group("module").
		Order("count DESC").
		Find(&moduleStats)
	stats["module_stats"] = moduleStats

	// 按用户统计（Top 10）
	var userStats []map[string]interface{}
	s.db.Model(&model.OperationLog{}).
		Select("username, COUNT(*) as count").
		Where("operated_at >= ? AND operated_at <= ?", startDate, endDate).
		Group("username").
		Order("count DESC").
		Limit(10).
		Find(&userStats)
	stats["user_stats"] = userStats

	return stats, nil
}
