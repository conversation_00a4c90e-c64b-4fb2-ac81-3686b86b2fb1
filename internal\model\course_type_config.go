package model

// CourseTypeConfig 课程类型配置表
// 完全可配置的课程类型，不写死在代码中
type CourseTypeConfig struct {
	BaseModel
	TypeCode    string `json:"type_code" gorm:"type:varchar(50);uniqueIndex;not null;comment:课程类型代码，如yoga_group"`
	TypeName    string `json:"type_name" gorm:"type:varchar(100);not null;comment:课程类型名称，如瑜伽团课"`
	Category    string `json:"category" gorm:"type:varchar(50);not null;comment:课程大类，如group/private/small"`
	Description string `json:"description" gorm:"type:text;comment:课程类型描述"`
	SortOrder   int    `json:"sort_order" gorm:"type:int;default:0;comment:排序"`
	Status      int    `json:"status" gorm:"type:int;default:1;comment:状态 1:启用 2:停用"`
}

// TableName 指定表名
func (CourseTypeConfig) TableName() string {
	return "course_type_configs"
}

// IsEnabled 检查是否启用
func (c *CourseTypeConfig) IsEnabled() bool {
	return c.Status == 1
}
