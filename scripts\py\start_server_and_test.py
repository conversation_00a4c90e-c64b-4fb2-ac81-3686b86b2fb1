#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动服务器并测试门店更新功能
"""

import subprocess
import time
import requests
import os
import sys
from pathlib import Path

def check_go_installed():
    """检查 Go 是否安装"""
    try:
        result = subprocess.run(['go', 'version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Go 已安装: {result.stdout.strip()}")
            return True
        else:
            print("❌ Go 未安装或不在 PATH 中")
            return False
    except FileNotFoundError:
        print("❌ Go 未安装或不在 PATH 中")
        return False

def start_server():
    """启动 Go 服务器"""
    # 切换到项目根目录
    project_root = Path(__file__).parent.parent.parent
    os.chdir(project_root)
    
    print(f"📁 切换到项目目录: {project_root}")
    
    # 检查 main.go 是否存在
    if not Path("main.go").exists():
        print("❌ 未找到 main.go 文件")
        return None
    
    print("🚀 启动 Go 服务器...")
    try:
        # 启动服务器进程
        process = subprocess.Popen(
            ['go', 'run', 'main.go'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):  # 最多等待30秒
            try:
                response = requests.get("http://localhost:9095/health", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功")
                    return process
            except:
                pass
            
            time.sleep(1)
            print(f"   等待中... ({i+1}/30)")
        
        print("❌ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        return None

def run_store_test():
    """运行门店测试"""
    print("\n" + "="*50)
    print("🧪 开始门店更新测试")
    print("="*50)
    
    # 导入并运行测试
    try:
        from quick_store_test import test_store_update
        return test_store_update()
    except ImportError:
        print("❌ 无法导入测试模块")
        return False

def main():
    """主函数"""
    print("🔧 门店更新接口自动测试工具")
    print("此工具将自动启动服务器并运行测试")
    print()
    
    # 1. 检查 Go 环境
    if not check_go_installed():
        print("\n请先安装 Go 语言环境：https://golang.org/dl/")
        return False
    
    # 2. 启动服务器
    server_process = start_server()
    if not server_process:
        return False
    
    try:
        # 3. 运行测试
        test_success = run_store_test()
        
        print("\n" + "="*50)
        if test_success:
            print("🎉 测试完成！门店更新接口工作正常")
        else:
            print("❌ 测试失败！请检查错误信息")
        
        return test_success
        
    finally:
        # 4. 清理：停止服务器
        print("\n🛑 停止服务器...")
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
            print("✅ 服务器已停止")
        except subprocess.TimeoutExpired:
            server_process.kill()
            print("⚠️ 强制停止服务器")

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
