$BaseUrl = "http://127.0.0.1:9095"

Write-Host "=== 小程序端免登录接口测试 ===" -ForegroundColor Green
Write-Host ""

# 测试健康检查
Write-Host "1. 测试健康检查接口" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/healthz" -Method Get
    Write-Host "✅ 健康检查成功" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试课程列表
Write-Host "2. 测试课程列表接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/courses?page=1&page_size=10" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 课程列表获取成功" -ForegroundColor Green
        Write-Host "   数据总数: $($response.data.total)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 课程列表获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 课程列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试教练列表
Write-Host "3. 测试教练列表接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/coaches?page=1&page_size=10" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 教练列表获取成功" -ForegroundColor Green
        Write-Host "   数据条数: $($response.data.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 教练列表获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 教练列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试门店列表
Write-Host "4. 测试门店列表接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/stores?page=1&page_size=10" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 门店列表获取成功" -ForegroundColor Green
        Write-Host "   数据条数: $($response.data.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 门店列表获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 门店列表请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试轮播图
Write-Host "5. 测试轮播图接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/banners" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 轮播图获取成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 轮播图获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 轮播图请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试门店详情
Write-Host "6. 测试门店详情接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/stores/1" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 门店详情获取成功" -ForegroundColor Green
        Write-Host "   门店名称: $($response.data.name)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 门店详情获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 门店详情请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# 测试课程分类
Write-Host "7. 测试课程分类接口（免登录）" -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$BaseUrl/api/v1/app/course-categories" -Method Get
    if ($response.code -eq 0) {
        Write-Host "✅ 课程分类获取成功" -ForegroundColor Green
        Write-Host "   分类数量: $($response.data.Count)" -ForegroundColor Gray
    } else {
        Write-Host "❌ 课程分类获取失败: $($response.msg)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 课程分类请求失败: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

Write-Host "=== 测试完成 ===" -ForegroundColor Green
