package model

// FileStatus 定义文件状态的枚举类型
type FileStatus string

const (
	StatusPendingUpload FileStatus = "pending_upload"
	StatusActive        FileStatus = "active"
	StatusArchived      FileStatus = "archived"
)

// File 用于存储文件元数据的数据库模型
// 采用元数据与对象分离的最佳实践
type File struct {
	*UUIDModel
	ObjectName       string     `json:"object_name" gorm:"type:varchar(512);not null;uniqueIndex;comment:在MinIO中的唯一对象名"`
	BucketName       string     `json:"bucket_name" gorm:"type:varchar(100);not null;comment:MinIO存储桶名称"`
	OriginalFilename string     `json:"original_filename" gorm:"type:varchar(255);comment:用户上传时的原始文件名"`
	FileName         string     `json:"file_name" gorm:"type:varchar(255);comment:显示文件名"`
	FileSize         int64      `json:"file_size" gorm:"comment:文件大小 (bytes)"`
	ContentType      string     `json:"content_type" gorm:"type:varchar(100);comment:文件的MIME类型"`
	Purpose          string     `json:"purpose" gorm:"type:varchar(50);comment:文件用途 (avatar, banner, course, store, document)"`
	UploaderID       string     `json:"uploader_id" gorm:"type:char(36);index;comment:上传者用户ID（UUID）"`
	Status           FileStatus `json:"status" gorm:"type:varchar(20);default:'active';comment:文件状态"`

	// 关联上传者
	Uploader *User `json:"uploader,omitempty" gorm:"foreignKey:UploaderID"`
}
