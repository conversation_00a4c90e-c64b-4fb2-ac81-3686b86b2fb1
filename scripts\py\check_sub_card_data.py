#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查和修复副卡数据一致性脚本
"""

import requests
import json

class SubCardDataChecker:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_all_cards(self) -> list:
        """获取所有会员卡"""
        print("\n📋 获取所有会员卡...")
        
        try:
            params = {
                "page": 1,
                "page_size": 100
            }
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    cards = result.get('data', {}).get('list', [])
                    print(f"✅ 获取到 {len(cards)} 张会员卡")
                    return cards
                else:
                    print(f"❌ 获取会员卡失败: {result.get('msg')}")
                    return []
            else:
                print(f"❌ 获取会员卡失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取会员卡异常: {e}")
            return []
    
    def analyze_card_data(self, cards: list):
        """分析会员卡数据"""
        print("\n🔍 分析会员卡数据...")
        
        main_cards = []
        sub_cards = []
        problematic_cards = []
        
        for card in cards:
            card_id = card.get('id')
            card_number = card.get('card_number')
            is_main_card = card.get('is_main_card')
            main_card_id = card.get('main_card_id')
            remaining_amount = card.get('remaining_amount', 0)
            remaining_times = card.get('remaining_times', 0)
            purchase_price = card.get('purchase_price', 0)
            
            print(f"\n📄 卡片 {card_id} ({card_number}):")
            print(f"   是否主卡: {is_main_card}")
            print(f"   主卡ID: {main_card_id}")
            print(f"   剩余金额: {remaining_amount / 100}元")
            print(f"   剩余次数: {remaining_times}")
            print(f"   购买价格: {purchase_price / 100}元")
            
            if main_card_id is not None:
                # 这应该是副卡
                sub_cards.append(card)
                if is_main_card:
                    print("   ⚠️ 问题：副卡的 is_main_card 应该为 false")
                    problematic_cards.append({
                        'card': card,
                        'issue': 'is_main_card_should_be_false'
                    })
                
                if remaining_amount > 0:
                    print("   ⚠️ 问题：副卡的 remaining_amount 应该为 0")
                    problematic_cards.append({
                        'card': card,
                        'issue': 'remaining_amount_should_be_zero'
                    })
                
                if remaining_times > 0:
                    print("   ⚠️ 问题：副卡的 remaining_times 应该为 0")
                    problematic_cards.append({
                        'card': card,
                        'issue': 'remaining_times_should_be_zero'
                    })
                
                if purchase_price > 0:
                    print("   ⚠️ 问题：副卡的 purchase_price 应该为 0")
                    problematic_cards.append({
                        'card': card,
                        'issue': 'purchase_price_should_be_zero'
                    })
            else:
                # 这应该是主卡
                main_cards.append(card)
                if not is_main_card:
                    print("   ⚠️ 问题：主卡的 is_main_card 应该为 true")
                    problematic_cards.append({
                        'card': card,
                        'issue': 'is_main_card_should_be_true'
                    })
        
        print(f"\n📊 数据统计:")
        print(f"   主卡数量: {len(main_cards)}")
        print(f"   副卡数量: {len(sub_cards)}")
        print(f"   问题卡片数量: {len(problematic_cards)}")
        
        if problematic_cards:
            print(f"\n⚠️ 发现的问题:")
            for item in problematic_cards:
                card = item['card']
                issue = item['issue']
                print(f"   卡片 {card['id']} ({card['card_number']}): {issue}")
        
        return main_cards, sub_cards, problematic_cards
    
    def get_card_detail(self, card_id: int):
        """获取卡片详情"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {})
            return None
        except Exception as e:
            print(f"❌ 获取卡片详情异常: {e}")
            return None
    
    def check_sub_card_relationships(self, sub_cards: list):
        """检查副卡与主卡的关联关系"""
        print(f"\n🔗 检查副卡关联关系...")
        
        for sub_card in sub_cards:
            card_id = sub_card['id']
            main_card_id = sub_card['main_card_id']
            
            print(f"\n🔍 检查副卡 {card_id} 的主卡关联...")
            
            # 获取副卡详情
            sub_detail = self.get_card_detail(card_id)
            if sub_detail:
                print(f"   副卡详情获取成功")
                print(f"   是否主卡: {sub_detail.get('is_main_card')}")
                print(f"   剩余金额: {sub_detail.get('remaining_amount', 0) / 100}元")
                
                # 检查主卡信息
                if main_card_id:
                    main_detail = self.get_card_detail(main_card_id)
                    if main_detail:
                        print(f"   主卡 {main_card_id} 详情:")
                        print(f"     剩余金额: {main_detail.get('remaining_amount', 0) / 100}元")
                        print(f"     副卡数量: {len(main_detail.get('sub_cards', []))}")
                    else:
                        print(f"   ❌ 无法获取主卡 {main_card_id} 的详情")
    
    def run_check(self):
        """运行完整检查"""
        print("🚀 开始副卡数据一致性检查...")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取所有会员卡
        cards = self.get_all_cards()
        if not cards:
            return False
        
        # 3. 分析数据
        main_cards, sub_cards, problematic_cards = self.analyze_card_data(cards)
        
        # 4. 检查副卡关联关系
        if sub_cards:
            self.check_sub_card_relationships(sub_cards)
        
        print("\n" + "=" * 80)
        if problematic_cards:
            print("⚠️ 发现数据一致性问题，建议修复")
            print("\n💡 修复建议:")
            print("1. 副卡的 is_main_card 应该设置为 false")
            print("2. 副卡的 remaining_amount 和 remaining_times 应该为 0")
            print("3. 副卡的 purchase_price 应该为 0")
            print("4. 副卡应该从主卡共享余额和次数")
        else:
            print("✅ 副卡数据一致性检查通过！")
        
        return True

def main():
    """主函数"""
    checker = SubCardDataChecker()
    success = checker.run_check()
    
    if success:
        print("\n🏆 副卡数据检查完成！")
        exit(0)
    else:
        print("\n💥 副卡数据检查失败！")
        exit(1)

if __name__ == "__main__":
    main()
