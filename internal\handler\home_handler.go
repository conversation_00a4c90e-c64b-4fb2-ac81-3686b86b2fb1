package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// HomeHandler 首页处理器
type HomeHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewHomeHandler 创建首页处理器实例
func NewHomeHandler(db *gorm.DB, storage storage.Storage) *HomeHandler {
	return &HomeHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetHomeData 获取首页数据
func (h *HomeHandler) GetHomeData(c *gin.Context) {
	// 获取轮播图
	banners, err := h.getBanners()
	if err != nil {
		log.Error("获取轮播图失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取首页数据失败"))
		return
	}

	// 获取精选课程
	featuredCourses, err := h.getFeaturedCourses()
	if err != nil {
		log.Error("获取精选课程失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取首页数据失败"))
		return
	}

	// 获取推荐教练
	featuredCoaches, err := h.getFeaturedCoaches()
	if err != nil {
		log.Error("获取推荐教练失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取首页数据失败"))
		return
	}

	// 获取门店信息
	stores, err := h.getNearbyStores()
	if err != nil {
		log.Error("获取门店信息失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取首页数据失败"))
		return
	}

	// 构建首页数据
	homeData := dto.HomeData{
		Banners:         banners,
		FeaturedCourses: featuredCourses,
		FeaturedCoaches: featuredCoaches,
		NearbyStores:    stores,
	}

	code.AutoResponse(c, homeData, nil)
}

// getBanners 获取轮播图
func (h *HomeHandler) getBanners() ([]dto.Banner, error) {
	var banners []model.Banner
	err := h.db.Where("status = ? AND position = ?", 1, "home").
		Order("sort_order ASC, created_at DESC").
		Find(&banners).Error

	if err != nil {
		return nil, err
	}

	// 转换为DTO
	var bannerList []dto.Banner
	for _, banner := range banners {
		bannerDTO := dto.Banner{
			ID:          banner.ID,
			Title:       banner.Title,
			Image:       h.urlConverter.ConvertFileIDToURL(banner.FileID),
			LinkURL:     banner.LinkURL,
			Description: banner.Description,
			SortOrder:   banner.SortOrder,
		}
		bannerList = append(bannerList, bannerDTO)
	}

	return bannerList, nil
}

// getFeaturedCourses 获取精选课程
func (h *HomeHandler) getFeaturedCourses() ([]dto.FeaturedCourse, error) {
	var courses []model.Course
	err := h.db.Preload("Coach").Preload("Store").
		Where("status = 1").
		Order("created_at DESC").
		Limit(6).
		Find(&courses).Error

	if err != nil {
		return nil, err
	}

	var featuredCourses []dto.FeaturedCourse
	for _, course := range courses {
		featuredCourse := dto.FeaturedCourse{
			ID:          course.ID,
			Name:        course.Name,
			Description: course.Description,
			Duration:    course.Duration,
			Price:       course.Price,
			CoachName: func() string {
				if len(course.CoachIDs) > 0 {
					var coach model.User
					if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
						return getCoachName(&coach)
					}
				}
				return ""
			}(),
			StoreName: course.Store.Name,
			StartTime: course.StartTime.Format("2006-01-02 15:04:05"),
		}
		featuredCourses = append(featuredCourses, featuredCourse)
	}

	return featuredCourses, nil
}

// getFeaturedCoaches 获取推荐教练
func (h *HomeHandler) getFeaturedCoaches() ([]dto.FeaturedCoach, error) {
	var coaches []model.User
	err := h.db.Where("is_coach = ? AND is_homepage = ? AND is_active = ?", true, true, true).
		Order("sort_order ASC").
		Limit(4).
		Find(&coaches).Error

	if err != nil {
		return nil, err
	}

	var featuredCoaches []dto.FeaturedCoach
	for _, coach := range coaches {
		var avatarURL string
		if coach.AvatarID != nil {
			avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
		}

		featuredCoach := dto.FeaturedCoach{
			ID:          coach.ID.String(),
			Name:        getCoachName(&coach),
			Avatar:      avatarURL,
			Experience:  coach.Experience,
			Speciality:  coach.Speciality,
			Description: coach.CoachDescription,
		}
		featuredCoaches = append(featuredCoaches, featuredCoach)
	}

	return featuredCoaches, nil
}

// getNearbyStores 获取附近门店（优化版本，批量生成图片URL）
func (h *HomeHandler) getNearbyStores() ([]dto.NearbyStore, error) {
	var stores []model.Store
	err := h.db.Where("status = 1").
		Order("sort_order ASC").
		Limit(3).
		Find(&stores).Error

	if err != nil {
		return nil, err
	}

	// 收集所有图片ID
	var allImageIDs []string
	for _, store := range stores {
		allImageIDs = append(allImageIDs, store.ImageIDs...)
	}

	var nearbyStores []dto.NearbyStore
	for _, store := range stores {
		nearbyStore := dto.NearbyStore{
			ID:          store.ID,
			Name:        store.Name,
			Address:     store.Address,
			Phone:       store.Phone,
			Images:      h.urlConverter.ConvertStringArrayToURLs(store.ImageIDs),
			Description: store.Description,
		}
		nearbyStores = append(nearbyStores, nearbyStore)
	}

	return nearbyStores, nil
}

// GetBanners 获取轮播图列表
func (h *HomeHandler) GetBanners(c *gin.Context) {
	banners, err := h.getBanners()
	if err != nil {
		log.Error("获取轮播图失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取轮播图失败"))
		return
	}

	code.AutoResponse(c, banners, nil)
}

// GetAds 获取广告弹窗
func (h *HomeHandler) GetAds(c *gin.Context) {
	// 这里可以从数据库查询广告，暂时返回模拟数据
	ads := []dto.Advertisement{
		{
			ID:       1,
			Title:    "新用户福利",
			Content:  "新用户注册即送体验课程一节",
			Image:    "/images/ad1.jpg",
			LinkType: "membership",
			LinkID:   1,
			ShowType: "popup",
			Status:   1,
		},
	}

	code.AutoResponse(c, ads, nil)
}

// GetFeaturedContent 获取精选内容
func (h *HomeHandler) GetFeaturedContent(c *gin.Context) {
	// 获取精选课程
	featuredCourses, err := h.getFeaturedCourses()
	if err != nil {
		log.Error("获取精选课程失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取精选内容失败"))
		return
	}

	// 获取推荐教练
	featuredCoaches, err := h.getFeaturedCoaches()
	if err != nil {
		log.Error("获取推荐教练失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取精选内容失败"))
		return
	}

	featuredContent := dto.FeaturedContent{
		Courses: featuredCourses,
		Coaches: featuredCoaches,
	}

	code.AutoResponse(c, featuredContent, nil)
}
