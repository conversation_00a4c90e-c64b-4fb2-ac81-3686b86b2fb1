package handler

import (
	"fmt"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// FlexibleDeductionHandler 灵活扣减处理器
type FlexibleDeductionHandler struct {
	db      *gorm.DB
	service *service.FlexibleDeductionService
}

// NewFlexibleDeductionHandler 创建灵活扣减处理器
func NewFlexibleDeductionHandler(db *gorm.DB) *FlexibleDeductionHandler {
	return &FlexibleDeductionHandler{
		db:      db,
		service: service.NewFlexibleDeductionService(db),
	}
}

// GetCourseTypeConfigs 获取课程类型配置列表
func (h *FlexibleDeductionHandler) GetCourseTypeConfigs(c *gin.Context) {
	var configs []model.CourseTypeConfig

	query := h.db.Model(&model.CourseTypeConfig{})

	// 状态筛选
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	if err := query.Order("sort_order ASC, id ASC").Find(&configs).Error; err != nil {
		log.Error("查询课程类型配置失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程类型配置失败"))
		return
	}

	code.AutoResponse(c, configs, nil)
}

// CreateCourseTypeConfig 创建课程类型配置
func (h *FlexibleDeductionHandler) CreateCourseTypeConfig(c *gin.Context) {
	var req model.CourseTypeConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 检查类型代码是否重复
	var existing model.CourseTypeConfig
	if err := h.db.Where("type_code = ?", req.TypeCode).First(&existing).Error; err == nil {
		log.Warn("课程类型代码已存在", "type_code", req.TypeCode)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程类型代码已存在"))
		return
	}

	if err := h.db.Create(&req).Error; err != nil {
		log.Error("创建课程类型配置失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建课程类型配置失败"))
		return
	}

	log.Info("创建课程类型配置成功", "type_code", req.TypeCode, "type_name", req.TypeName)
	code.AutoResponse(c, req, nil)
}

// GetFlexibleDeductionRules 获取灵活扣减规则列表
func (h *FlexibleDeductionHandler) GetFlexibleDeductionRules(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.Err(c, code.InvalidParams, "请求参数错误")
		return
	}
	req.SetDefaults()

	var rules []model.FlexibleDeductionRule

	query := h.db.Model(&model.FlexibleDeductionRule{}).
		Preload("CardType").
		Preload("Course").
		Preload("CourseTypeConf")

	// 会员卡类型筛选
	if cardTypeID := c.Query("card_type_id"); cardTypeID != "" {
		query = query.Where("card_type_id = ?", cardTypeID)
	}

	var total int64
	query.Count(&total)

	if err := query.Order("priority DESC, id DESC").
		Offset(req.GetOffset()).Limit(req.PageSize).Find(&rules).Error; err != nil {
		log.Error("查询扣减规则失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询扣减规则失败"))
		return
	}

	response := dto.PageResponse{
		List:     rules,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// CreateFlexibleDeductionRule 创建灵活扣减规则
func (h *FlexibleDeductionHandler) CreateFlexibleDeductionRule(c *gin.Context) {
	var req model.FlexibleDeductionRule
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证会员卡类型是否存在
	var cardType model.MembershipCardType
	if err := h.db.First(&cardType, req.CardTypeID).Error; err != nil {
		log.Error("会员卡类型不存在", "card_type_id", req.CardTypeID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "会员卡类型不存在"))
		return
	}

	if err := h.db.Create(&req).Error; err != nil {
		log.Error("创建扣减规则失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建扣减规则失败"))
		return
	}

	// 重新查询包含关联数据
	h.db.Preload("CardType").Preload("Course").Preload("CourseTypeConf").First(&req, req.ID)

	log.Info("创建扣减规则成功", "rule_id", req.ID, "rule_name", req.RuleName)
	code.AutoResponse(c, req, nil)
}

// TestDeductionRule 测试扣减规则
func (h *FlexibleDeductionHandler) TestDeductionRule(c *gin.Context) {
	var req service.DeductionContext
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	result, err := h.service.FindMatchingRule(req)
	if err != nil {
		log.Error("测试扣减规则失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "测试扣减规则失败"))
		return
	}

	code.AutoResponse(c, result, nil)
}

// UpdateCourseTypeConfig 更新课程类型配置
func (h *FlexibleDeductionHandler) UpdateCourseTypeConfig(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的课程类型配置ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的课程类型配置ID"))
		return
	}

	var req model.CourseTypeConfig
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 检查记录是否存在
	var existing model.CourseTypeConfig
	if err := h.db.First(&existing, id).Error; err != nil {
		log.Error("课程类型配置不存在", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程类型配置不存在"))
		return
	}

	// 检查类型代码是否重复（排除自己）
	var duplicate model.CourseTypeConfig
	if err := h.db.Where("type_code = ? AND id != ?", req.TypeCode, id).First(&duplicate).Error; err == nil {
		log.Warn("课程类型代码已存在", "type_code", req.TypeCode)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程类型代码已存在"))
		return
	}

	// 更新记录
	req.ID = id
	if err := h.db.Save(&req).Error; err != nil {
		log.Error("更新课程类型配置失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新课程类型配置失败"))
		return
	}

	log.Info("更新课程类型配置成功", "id", id, "type_code", req.TypeCode)
	code.AutoResponse(c, req, nil)
}

// DeleteCourseTypeConfig 删除课程类型配置
func (h *FlexibleDeductionHandler) DeleteCourseTypeConfig(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的课程类型配置ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的课程类型配置ID"))
		return
	}

	// 检查是否有关联的扣减规则
	var ruleCount int64
	h.db.Model(&model.FlexibleDeductionRule{}).Where("course_type_code = (SELECT type_code FROM course_type_configs WHERE id = ?)", id).Count(&ruleCount)
	if ruleCount > 0 {
		log.Warn("课程类型配置有关联的扣减规则，无法删除", "id", id, "rule_count", ruleCount)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该课程类型配置有关联的扣减规则，无法删除"))
		return
	}

	if err := h.db.Delete(&model.CourseTypeConfig{}, id).Error; err != nil {
		log.Error("删除课程类型配置失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除课程类型配置失败"))
		return
	}

	log.Info("删除课程类型配置成功", "id", id)
	code.AutoResponse(c, map[string]any{"message": "删除成功"}, nil)
}

// GetFlexibleDeductionRuleDetail 获取灵活扣减规则详情
func (h *FlexibleDeductionHandler) GetFlexibleDeductionRuleDetail(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的扣减规则ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的扣减规则ID"))
		return
	}

	var rule model.FlexibleDeductionRule
	if err := h.db.Preload("CardType").Preload("Course").Preload("CourseTypeConf").First(&rule, id).Error; err != nil {
		log.Error("查询扣减规则详情失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "扣减规则不存在"))
		return
	}

	code.AutoResponse(c, rule, nil)
}

// UpdateFlexibleDeductionRule 更新灵活扣减规则
func (h *FlexibleDeductionHandler) UpdateFlexibleDeductionRule(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的扣减规则ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的扣减规则ID"))
		return
	}

	var req model.FlexibleDeductionRule
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 检查记录是否存在
	var existing model.FlexibleDeductionRule
	if err := h.db.First(&existing, id).Error; err != nil {
		log.Error("扣减规则不存在", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "扣减规则不存在"))
		return
	}

	// 验证会员卡类型是否存在
	var cardType model.MembershipCardType
	if err := h.db.First(&cardType, req.CardTypeID).Error; err != nil {
		log.Error("会员卡类型不存在", "card_type_id", req.CardTypeID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "会员卡类型不存在"))
		return
	}

	// 更新记录
	req.ID = id
	if err := h.db.Save(&req).Error; err != nil {
		log.Error("更新扣减规则失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新扣减规则失败"))
		return
	}

	// 重新查询包含关联数据
	h.db.Preload("CardType").Preload("Course").Preload("CourseTypeConf").First(&req, req.ID)

	log.Info("更新扣减规则成功", "id", id, "rule_name", req.RuleName)
	code.AutoResponse(c, req, nil)
}

// DeleteFlexibleDeductionRule 删除灵活扣减规则
func (h *FlexibleDeductionHandler) DeleteFlexibleDeductionRule(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的扣减规则ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的扣减规则ID"))
		return
	}

	if err := h.db.Delete(&model.FlexibleDeductionRule{}, id).Error; err != nil {
		log.Error("删除扣减规则失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除扣减规则失败"))
		return
	}

	log.Info("删除扣减规则成功", "id", id)
	code.AutoResponse(c, map[string]any{"message": "删除成功"}, nil)
}

// ToggleFlexibleDeductionRuleStatus 切换灵活扣减规则状态
func (h *FlexibleDeductionHandler) ToggleFlexibleDeductionRuleStatus(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		log.Error("无效的扣减规则ID", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的扣减规则ID"))
		return
	}

	var rule model.FlexibleDeductionRule
	if err := h.db.First(&rule, id).Error; err != nil {
		log.Error("扣减规则不存在", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "扣减规则不存在"))
		return
	}

	// 切换状态
	newStatus := 1
	if rule.Status == 1 {
		newStatus = 2
	}

	if err := h.db.Model(&rule).Update("status", newStatus).Error; err != nil {
		log.Error("更新扣减规则状态失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新状态失败"))
		return
	}

	statusText := "启用"
	if newStatus == 2 {
		statusText = "停用"
	}

	log.Info("切换扣减规则状态成功", "id", id, "new_status", newStatus)
	code.AutoResponse(c, map[string]any{
		"id":      id,
		"status":  newStatus,
		"message": fmt.Sprintf("规则已%s", statusText),
	}, nil)
}

// GetCardSupportedCourseTypes 获取会员卡支持的课程类型
func (h *FlexibleDeductionHandler) GetCardSupportedCourseTypes(c *gin.Context) {
	cardTypeID := cast.ToUint(c.Param("card_type_id"))
	if cardTypeID == 0 {
		log.Error("无效的会员卡类型ID", "card_type_id", c.Param("card_type_id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的会员卡类型ID"))
		return
	}

	courseTypes, err := h.service.GetSupportedCourseTypes(uint(cardTypeID))
	if err != nil {
		log.Error("查询支持的课程类型失败", "card_type_id", cardTypeID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询支持的课程类型失败"))
		return
	}

	// 获取课程类型详细信息
	var configs []model.CourseTypeConfig
	if len(courseTypes) > 0 {
		h.db.Where("type_code IN ?", courseTypes).Find(&configs)
	}

	response := map[string]any{
		"card_type_id":   cardTypeID,
		"course_types":   courseTypes,
		"course_configs": configs,
	}

	code.AutoResponse(c, response, nil)
}
