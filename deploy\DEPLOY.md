# Yogaga 项目容器化部署指南

本文档旨在为 `Yogaga` Go Gin 项目提供一套完整的、符合生产最佳实践的容器化部署方案。

## 1. 先决条件

- [Docker Engine](https://docs.docker.com/engine/install/)
- [Docker Compose](https://docs.docker.com/compose/install/) (推荐)

## 2. 核心理念：符合 FHS 的不可变镜像

我们遵循云原生十二要素应用原则，并结合 FHS (文件系统层次结构标准) 进行容器化：

- **不可变镜像**: 镜像一旦构建，就不再更改。所有环境特定的配置都在运行时提供。
- **配置与镜像分离**: 敏感信息（如密码）和环境配置（如数据库地址）不会打包进镜像。
- **FHS 标准目录**:
  - 二进制文件位于: `/usr/local/bin/yogaga`
  - 配置文件挂载点: `/etc/yogaga`

## 3. 准备配置文件

在您的服务器上创建一个配置目录，并放入 `config.yaml` 和 `rbac_model.conf`。

```bash
mkdir -p /data/yogaga/config
cd /data/yogaga/config
# 从您的项目或代码仓库中复制 config.yaml 和 rbac_model.conf 到这里
```

目录结构应如下所示：

```
/data/yogaga
└── config
    ├── config.yaml
    └── rbac_model.conf
```

## 4. 构建 Docker 镜像

### 4.1. 使用构建脚本（推荐）

我们提供了一个便捷的构建脚本：

```bash
# 构建最新版本
./deploy/build.sh

# 构建指定标签版本
./deploy/build.sh v1.0.0
```

### 4.2. 手动构建

在项目根目录下，使用以下命令构建镜像。此命令与 CI/CD 流程完全一致。

```bash
# 获取项目名 (仓库名)
APP_NAME=$(basename -s .git `git config --get remote.origin.url`)

# 构建命令
docker build \
    --build-arg APP_NAME=${APP_NAME} \
    --build-arg COMMIT_HASH=$(git rev-parse --short HEAD) \
    --build-arg BUILD_TIME=$(date -u +"%Y-%m-%dT%H:%M:%SZ") \
    --build-arg PLATFORM=$(go version | awk '{print $4}') \
    --build-arg APP_PORT=9095 \
    -t ${APP_NAME}:latest \
    -f deploy/Dockerfile .
```

## 5. 运行容器

### 5.1. 使用 `docker run`

将您在宿主机上的配置目录挂载到容器内的 `/etc/yogaga`。

```bash
# 确保将 /data/yogaga/config 替换为您的真实路径
docker run -d \
  -p 9095:9095 \
  -v /data/yogaga/config:/etc/yogaga \
  --name yogaga-app \
  yogaga:latest
```

### 5.2. 使用 `docker-compose` (生产环境推荐)

我们提供了多个 docker-compose 模板：

#### 5.2.1. 仅应用服务（推荐用于已有基础设施的环境）

```bash
# 复制简化版 docker-compose 文件
cp deploy/docker-compose.simple.yml docker-compose.yml

# 编辑配置（如需要）
vim docker-compose.yml
```

#### 5.2.2. 完整服务栈（包含数据库、Redis、MinIO）

```bash
# 复制完整版 docker-compose 文件
cp deploy/docker-compose.example.yml docker-compose.yml

# 编辑配置，特别是密码等敏感信息
vim docker-compose.yml
```

#### 5.2.3. 自定义 docker-compose.yml

在您的部署目录（例如 `/data/yogaga/`）下创建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  app:
    image: yogaga:latest # 使用您构建的镜像, 或直接从镜像仓库拉取
    container_name: yogaga_app
    restart: always
    ports:
      - "9095:9095"
    volumes:
      # 将宿主机的整个 config 目录挂载到容器的 /etc/yogaga
      - ./config:/etc/yogaga:ro
      - ./logs:/app/logs
    environment:
      - APP_ENV=prod
    networks:
      - yogaga-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9095/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  yogaga-net:
    driver: bridge
```

**对应的部署目录结构:**
```
/data/yogaga/
├── docker-compose.yml
└── config/
    ├── config.yaml
    └── rbac_model.conf
```

**启动服务:**

```bash
cd /data/yogaga
docker-compose up -d
```

## 6. 验证部署

### 6.1. 检查容器状态

```bash
# 查看容器运行状态
docker ps

# 查看应用日志
docker logs yogaga-app

# 或使用 docker-compose
docker-compose logs -f app
```

### 6.2. 健康检查

容器内置了健康检查，您可以通过以下方式验证：

```bash
# 检查健康状态
docker inspect yogaga-app | grep -A 10 "Health"

# 手动测试健康检查端点
curl http://localhost:9095/healthz
```

## 7. 配置文件说明

### 7.1. 必需的配置文件

在挂载的配置目录中，您需要提供以下文件：

1. **config.yaml** - 主配置文件
2. **rbac_model.conf** - Casbin RBAC 模型配置文件

### 7.2. config.yaml 配置要点

**重要配置项说明：**

```yaml
APP:
  Name: yogaga
  Port: 9095  # 容器内端口，需与 Dockerfile EXPOSE 一致

Database:
  Host: "mysql-prod"  # 数据库服务名或IP
  # 其他数据库配置...

Casbin:
  ModelPath: "/etc/yogaga/rbac_model.conf"  # 容器内路径，不要修改

Log:
  Level: warn  # 生产环境建议使用 warn 或 error
  Output: [file]  # 生产环境建议只输出到文件
```

### 7.3. rbac_model.conf 配置

该文件定义了 Casbin 的 RBAC 模型，内容如下：

```ini
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
```

## 8. 生产环境最佳实践

### 8.1. 安全配置

1. **修改默认密钥和密码**：
   - JWT 密钥 (`JwtAuth.AccessSecret`, `JwtAuth.RefreshSecret`)
   - 数据库密码 (`Database.Password`)
   - MinIO 访问密钥 (`Storage.AccessKeyID`, `Storage.AccessKeySecret`)

2. **使用环境变量或 Docker Secrets**：
   ```bash
   # 使用环境变量覆盖敏感配置
   docker run -d \
     -p 9095:9095 \
     -v /data/yogaga/config:/etc/yogaga \
     -e DATABASE_PASSWORD=your_secure_password \
     -e JWT_ACCESS_SECRET=your_jwt_secret \
     --name yogaga-app \
     yogaga:latest
   ```

### 8.2. 资源限制

在生产环境中，建议为容器设置资源限制：

```yaml
# docker-compose.yml 中添加
services:
  app:
    # ... 其他配置
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
```

### 8.3. 日志管理

```yaml
# docker-compose.yml 中添加日志配置
services:
  app:
    # ... 其他配置
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

### 8.4. 网络安全

```yaml
# 使用自定义网络隔离服务
networks:
  yogaga-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

## 9. 故障排除

### 9.1. 常见问题

1. **配置文件未找到**：
   - 确保配置文件路径正确挂载到 `/etc/yogaga`
   - 检查文件权限，确保容器内的 `appuser` 可以读取

2. **数据库连接失败**：
   - 检查数据库服务是否运行
   - 验证网络连通性
   - 确认数据库配置正确

3. **Casbin 初始化失败**：
   - 确保 `rbac_model.conf` 文件存在且格式正确
   - 检查文件路径配置

### 9.2. 调试模式

如需调试，可以临时启用调试日志：

```bash
# 临时修改配置文件中的日志级别为 debug
# 然后重启容器
docker-compose restart app
```

## 10. 更新和维护

### 10.1. 应用更新

```bash
# 1. 构建新镜像
docker build -t yogaga:v1.1.0 -f deploy/Dockerfile .

# 2. 更新 docker-compose.yml 中的镜像版本
# 3. 重新部署
docker-compose down
docker-compose up -d
```

### 10.2. 配置更新

```bash
# 修改配置文件后，重启容器即可
docker-compose restart app
```

## 11. 监控和告警

建议集成以下监控方案：

- **健康检查**: 容器内置 `/healthz` 端点
- **日志监控**: 使用 ELK Stack 或类似方案
- **性能监控**: 使用 Prometheus + Grafana
- **告警**: 基于健康检查和关键指标设置告警

---

**注意**: 本部署指南假设您已经有相应的数据库、Redis、MinIO 等依赖服务。如需完整的服务栈部署，请参考项目的 `deploy/docker-compose.example.yml` 文件。

## 12. 部署文件说明

项目 `deploy/` 目录包含以下文件：

- `Dockerfile` - Docker 镜像构建文件
- `build.sh` - 镜像构建脚本
- `docker-compose.simple.yml` - 仅应用服务的 compose 文件
- `docker-compose.example.yml` - 包含完整服务栈的 compose 文件
- `DEPLOY.md` - 本部署指南

根据您的需求选择合适的部署方式。