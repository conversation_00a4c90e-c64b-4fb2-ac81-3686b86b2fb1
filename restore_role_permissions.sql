-- 角色权限分配恢复脚本
-- 使用方法: psql -h 8.153.85.120 -p 5432 -U yogaga -d yogaga -f restore_role_permissions.sql

BEGIN;

-- 清理现有的角色权限关联
DELETE FROM role_permissions;

-- 清理<PERSON><PERSON>bin中的权限策略（保留角色继承）
DELETE FROM casbin_rule WHERE ptype = 'p';

-- 恢复角色权限分配

-- 1. 超级管理员 (ID: 1) - 拥有所有权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
(1, 11), (1, 12), (1, 13), (1, 14), (1, 15), (1, 16), (1, 17),
(1, 21), (1, 22), (1, 23), (1, 24), (1, 25),
(1, 31), (1, 32), (1, 33), (1, 34), (1, 35),
(1, 41), (1, 42), (1, 43), (1, 44),
(1, 51), (1, 52), (1, 53), (1, 54),
(1, 61), (1, 62), (1, 63), (1, 64),
(1, 71), (1, 72), (1, 73), (1, 74),
(1, 81), (1, 82), (1, 83), (1, 84), (1, 85), (1, 86),
(1, 91), (1, 92), (1, 93),
(1, 101), (1, 102), (1, 103), (1, 104), (1, 105), (1, 106), (1, 107), (1, 108), (1, 109), (1, 110), (1, 111), (1, 112);

-- 2. 店长 (ID: 2) - 门店管理权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(2, 4), (2, 5), (2, 14), (2, 24), (2, 25), (2, 34), (2, 35), (2, 44),
(2, 51), (2, 52), (2, 53), (2, 54),
(2, 61), (2, 62), (2, 63), (2, 64),
(2, 71), (2, 72), (2, 73), (2, 74),
(2, 81), (2, 82), (2, 83), (2, 84), (2, 85), (2, 86),
(2, 91), (2, 92), (2, 93),
(2, 101), (2, 105), (2, 106), (2, 111), (2, 112);

-- 3. 销售 (ID: 3) - 销售相关权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(3, 4), (3, 5), (3, 24), (3, 25), (3, 34), (3, 35), (3, 44), (3, 54),
(3, 61), (3, 62), (3, 64),
(3, 71), (3, 72), (3, 74),
(3, 91), (3, 92),
(3, 101), (3, 105), (3, 106), (3, 112);

-- 4. 前台 (ID: 4) - 前台接待权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(4, 4), (4, 5), (4, 24), (4, 25), (4, 34), (4, 35), (4, 44), (4, 54),
(4, 61), (4, 62), (4, 64), (4, 74), (4, 91),
(4, 101), (4, 105), (4, 106), (4, 112);

-- 5. 教练 (ID: 5) - 教练相关权限
INSERT INTO role_permissions (role_id, permission_id) VALUES
(5, 4), (5, 5), (5, 24), (5, 25), (5, 34), (5, 35), (5, 44), (5, 54),
(5, 61), (5, 64), (5, 91),
(5, 101), (5, 105), (5, 106), (5, 112);

-- 恢复Casbin策略
-- 超级管理员策略
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '超级管理员', 'user:create', 'POST'),
('p', '超级管理员', 'user:update_roles', 'PUT'),
('p', '超级管理员', 'user:delete', 'DELETE'),
('p', '超级管理员', 'user:list', 'GET'),
('p', '超级管理员', 'user:detail', 'GET'),
('p', '超级管理员', 'role:create', 'POST'),
('p', '超级管理员', 'role:update', 'PUT'),
('p', '超级管理员', 'role:delete', 'DELETE'),
('p', '超级管理员', 'role:list', 'GET'),
('p', '超级管理员', 'role:update_permissions', 'PUT'),
('p', '超级管理员', 'role:get', 'GET'),
('p', '超级管理员', 'role:get_permissions', 'GET'),
('p', '超级管理员', 'permission:create', 'POST'),
('p', '超级管理员', 'permission:update', 'PUT'),
('p', '超级管理员', 'permission:delete', 'DELETE'),
('p', '超级管理员', 'permission:list', 'GET'),
('p', '超级管理员', 'permission:view', 'GET'),
('p', '超级管理员', 'menu:create', 'POST'),
('p', '超级管理员', 'menu:update', 'PUT'),
('p', '超级管理员', 'menu:delete', 'DELETE'),
('p', '超级管理员', 'menu:list', 'GET'),
('p', '超级管理员', 'menu:get', 'GET'),
('p', '超级管理员', 'store:create', 'POST'),
('p', '超级管理员', 'store:update', 'PUT'),
('p', '超级管理员', 'store:delete', 'DELETE'),
('p', '超级管理员', 'store:list', 'GET'),
('p', '超级管理员', 'course:create', 'POST'),
('p', '超级管理员', 'course:update', 'PUT'),
('p', '超级管理员', 'course:delete', 'DELETE'),
('p', '超级管理员', 'course:list', 'GET'),
('p', '超级管理员', 'booking:create', 'POST'),
('p', '超级管理员', 'booking:update', 'PUT'),
('p', '超级管理员', 'booking:cancel', 'PUT'),
('p', '超级管理员', 'booking:list', 'GET'),
('p', '超级管理员', 'membership:create', 'POST'),
('p', '超级管理员', 'membership:update', 'PUT'),
('p', '超级管理员', 'membership:delete', 'DELETE'),
('p', '超级管理员', 'membership:list', 'GET'),
('p', '超级管理员', 'file:upload', 'POST'),
('p', '超级管理员', 'file:delete', 'DELETE'),
('p', '超级管理员', 'file:list', 'GET'),
('p', '超级管理员', 'file:get_presigned_upload_url', 'GET'),
('p', '超级管理员', 'file:get_presigned_download_url', 'GET'),
('p', '超级管理员', 'file:proxy_upload', 'POST'),
('p', '超级管理员', 'dashboard:view', 'GET'),
('p', '超级管理员', 'report:view', 'GET'),
('p', '超级管理员', 'data:export', 'POST'),
('p', '超级管理员', 'flexible:course_types:list', 'GET'),
('p', '超级管理员', 'flexible:course_types:create', 'POST'),
('p', '超级管理员', 'flexible:course_types:update', 'PUT'),
('p', '超级管理员', 'flexible:course_types:delete', 'DELETE'),
('p', '超级管理员', 'flexible:deduction_rules:list', 'GET'),
('p', '超级管理员', 'flexible:deduction_rules:detail', 'GET'),
('p', '超级管理员', 'flexible:deduction_rules:create', 'POST'),
('p', '超级管理员', 'flexible:deduction_rules:update', 'PUT'),
('p', '超级管理员', 'flexible:deduction_rules:delete', 'DELETE'),
('p', '超级管理员', 'flexible:deduction_rules:toggle', 'PUT'),
('p', '超级管理员', 'flexible:test_deduction', 'POST'),
('p', '超级管理员', 'flexible:card_supported_courses', 'GET');

-- 店长策略（部分权限）
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '店长', 'user:list', 'GET'),
('p', '店长', 'user:detail', 'GET'),
('p', '店长', 'role:list', 'GET'),
('p', '店长', 'permission:list', 'GET'),
('p', '店长', 'permission:view', 'GET'),
('p', '店长', 'menu:list', 'GET'),
('p', '店长', 'menu:get', 'GET'),
('p', '店长', 'store:list', 'GET'),
('p', '店长', 'course:create', 'POST'),
('p', '店长', 'course:update', 'PUT'),
('p', '店长', 'course:delete', 'DELETE'),
('p', '店长', 'course:list', 'GET'),
('p', '店长', 'booking:create', 'POST'),
('p', '店长', 'booking:update', 'PUT'),
('p', '店长', 'booking:cancel', 'PUT'),
('p', '店长', 'booking:list', 'GET'),
('p', '店长', 'membership:create', 'POST'),
('p', '店长', 'membership:update', 'PUT'),
('p', '店长', 'membership:delete', 'DELETE'),
('p', '店长', 'membership:list', 'GET'),
('p', '店长', 'file:upload', 'POST'),
('p', '店长', 'file:delete', 'DELETE'),
('p', '店长', 'file:list', 'GET'),
('p', '店长', 'file:get_presigned_upload_url', 'GET'),
('p', '店长', 'file:get_presigned_download_url', 'GET'),
('p', '店长', 'file:proxy_upload', 'POST'),
('p', '店长', 'dashboard:view', 'GET'),
('p', '店长', 'report:view', 'GET'),
('p', '店长', 'data:export', 'POST'),
('p', '店长', 'flexible:course_types:list', 'GET'),
('p', '店长', 'flexible:deduction_rules:list', 'GET'),
('p', '店长', 'flexible:deduction_rules:detail', 'GET'),
('p', '店长', 'flexible:test_deduction', 'POST'),
('p', '店长', 'flexible:card_supported_courses', 'GET');

-- 销售策略
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '销售', 'user:list', 'GET'),
('p', '销售', 'user:detail', 'GET'),
('p', '销售', 'permission:list', 'GET'),
('p', '销售', 'permission:view', 'GET'),
('p', '销售', 'menu:list', 'GET'),
('p', '销售', 'menu:get', 'GET'),
('p', '销售', 'store:list', 'GET'),
('p', '销售', 'course:list', 'GET'),
('p', '销售', 'booking:create', 'POST'),
('p', '销售', 'booking:update', 'PUT'),
('p', '销售', 'booking:list', 'GET'),
('p', '销售', 'membership:create', 'POST'),
('p', '销售', 'membership:update', 'PUT'),
('p', '销售', 'membership:list', 'GET'),
('p', '销售', 'dashboard:view', 'GET'),
('p', '销售', 'report:view', 'GET'),
('p', '销售', 'flexible:course_types:list', 'GET'),
('p', '销售', 'flexible:deduction_rules:list', 'GET'),
('p', '销售', 'flexible:deduction_rules:detail', 'GET'),
('p', '销售', 'flexible:card_supported_courses', 'GET');

-- 前台策略
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '前台', 'user:list', 'GET'),
('p', '前台', 'user:detail', 'GET'),
('p', '前台', 'permission:list', 'GET'),
('p', '前台', 'permission:view', 'GET'),
('p', '前台', 'menu:list', 'GET'),
('p', '前台', 'menu:get', 'GET'),
('p', '前台', 'store:list', 'GET'),
('p', '前台', 'course:list', 'GET'),
('p', '前台', 'booking:create', 'POST'),
('p', '前台', 'booking:update', 'PUT'),
('p', '前台', 'booking:list', 'GET'),
('p', '前台', 'membership:list', 'GET'),
('p', '前台', 'dashboard:view', 'GET'),
('p', '前台', 'flexible:course_types:list', 'GET'),
('p', '前台', 'flexible:deduction_rules:list', 'GET'),
('p', '前台', 'flexible:deduction_rules:detail', 'GET'),
('p', '前台', 'flexible:card_supported_courses', 'GET');

-- 教练策略
INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES
('p', '教练', 'user:list', 'GET'),
('p', '教练', 'user:detail', 'GET'),
('p', '教练', 'permission:list', 'GET'),
('p', '教练', 'permission:view', 'GET'),
('p', '教练', 'menu:list', 'GET'),
('p', '教练', 'menu:get', 'GET'),
('p', '教练', 'store:list', 'GET'),
('p', '教练', 'course:list', 'GET'),
('p', '教练', 'booking:create', 'POST'),
('p', '教练', 'booking:list', 'GET'),
('p', '教练', 'dashboard:view', 'GET'),
('p', '教练', 'flexible:course_types:list', 'GET'),
('p', '教练', 'flexible:deduction_rules:list', 'GET'),
('p', '教练', 'flexible:deduction_rules:detail', 'GET'),
('p', '教练', 'flexible:card_supported_courses', 'GET');

COMMIT;

-- 验证恢复结果
SELECT 
    r.name as role_name,
    COUNT(rp.permission_id) as permission_count
FROM roles r
LEFT JOIN role_permissions rp ON r.id = rp.role_id
GROUP BY r.id, r.name
ORDER BY r.id;
