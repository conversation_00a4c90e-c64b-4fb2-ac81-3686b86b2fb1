#!/usr/bin/env python3
"""
直接测试预签名URL的Content-Type
"""

import requests

def test_url_content_type(url):
    """测试URL的Content-Type"""
    print(f"🔗 测试URL: {url}")

    try:
        # 发送HEAD请求检查Content-Type
        response = requests.head(url, timeout=10)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            content_type = response.headers.get('Content-Type', 'Not Set')
            print(f"Content-Type: {content_type}")

            if 'image/jpeg' in content_type:
                print("✅ Content-Type正确设置为image/jpeg")
            elif 'application/octet-stream' in content_type:
                print("❌ Content-Type仍然是application/octet-stream")
            else:
                print(f"⚠️  Content-Type异常: {content_type}")

        elif response.status_code == 403:
            print("❌ 403 Forbidden - 可能是文件已过期或权限问题")
        else:
            print(f"❌ 请求失败: {response.status_code}")

    except Exception as e:
        print(f"❌ 请求异常: {e}")

def main():
    # 测试最新的预签名URL
    test_url = "http://************:9000/starter-test/document/2025/07/22/3f09b2b5-8293-4ca5-b7f7-a0555827c13f.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=8TKr8b47Yk8eH7XzaxfS%2F20250722%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250722T060139Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&response-content-disposition=attachment%3B%20filename%3D%22test_image.jpg%22&X-Amz-Signature=43d3bfa78b7c498dc33dbab31bf30a7ed72d99b7d06ef7892eb7854d2050436b"

    test_url_content_type(test_url)

if __name__ == "__main__":
    main()
