#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建会员用户脚本
为测试会员卡功能创建一个会员用户
"""

import requests
import time

def create_member_user():
    """创建会员用户"""
    base_url = "http://localhost:9095"
    
    # 1. 登录获取token
    print("🔐 正在登录系统...")
    login_url = f"{base_url}/api/v1/public/admin/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    session = requests.Session()
    response = session.post(login_url, json=login_data)
    
    if response.status_code != 200:
        print(f"❌ 登录失败: {response.text}")
        return False
    
    result = response.json()
    if result.get('code') != 0:
        print(f"❌ 登录失败: {result}")
        return False
    
    token = result.get('data', {}).get('access_token')
    if not token:
        print("❌ 未获取到token")
        return False
    
    session.headers.update({
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    })
    print("✅ 登录成功")
    
    # 2. 创建会员用户
    print("👤 正在创建会员用户...")
    timestamp = int(time.time())
    
    member_user = {
        "username": f"member_user_{timestamp}",
        "email": f"member_{timestamp}@example.com",
        "password": "123456",
        "role_ids": [],  # 普通用户，不分配角色
        "phone": f"138{timestamp % 100000000:08d}",
        "nickname": "测试会员用户",
        "gender": "unknown",
        "birthday": "1990-01-01",
        "address": "测试地址"
    }
    
    create_url = f"{base_url}/api/v1/admin/users"
    response = session.post(create_url, json=member_user)
    
    if response.status_code != 200:
        print(f"❌ 创建用户失败: {response.text}")
        return False
    
    result = response.json()
    if result.get('code') != 0:
        print(f"❌ 创建用户失败: {result}")
        return False
    
    user_id = result.get('data', {}).get('id')
    print(f"✅ 成功创建会员用户")
    print(f"   用户ID: {user_id}")
    print(f"   用户名: {member_user['username']}")
    print(f"   邮箱: {member_user['email']}")
    print(f"   手机: {member_user.get('phone', 'N/A')}")
    
    return {
        'user_id': user_id,
        'username': member_user['username'],
        'email': member_user['email'],
        'phone': member_user.get('phone')
    }

if __name__ == "__main__":
    print("🚀 开始创建会员用户...")
    result = create_member_user()
    
    if result:
        print("\n🎉 会员用户创建成功！")
        print("现在可以使用这个用户进行会员卡测试了。")
    else:
        print("\n❌ 会员用户创建失败")
