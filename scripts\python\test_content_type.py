#!/usr/bin/env python3
"""
测试文件上传后的Content-Type是否正确设置
"""

import requests
import json
import os
import tempfile
from PIL import Image

# API配置
BASE_URL = "http://127.0.0.1:9095"
LOGIN_URL = f"{BASE_URL}/api/v1/public/admin/login"
UPLOAD_URL = f"{BASE_URL}/api/v1/admin/files/upload"

def login():
    """管理员登录"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    response = requests.post(LOGIN_URL, json=login_data)
    if response.status_code == 200:
        result = response.json()
        if result.get("code") == 0:  # 修复：code应该是0，不是200
            return result["data"]["access_token"]

    raise Exception(f"登录失败: {response.text}")

def create_test_image():
    """创建一个测试图片文件"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (100, 100), color='red')

    # 保存到临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    img.save(temp_file.name, 'JPEG')
    temp_file.close()

    return temp_file.name

def upload_file(token, file_path):
    """上传文件"""
    headers = {
        "Authorization": f"Bearer {token}"
    }

    with open(file_path, 'rb') as f:
        files = {
            'file': ('test_image.jpg', f, 'image/jpeg')
        }
        data = {
            'purpose': 'document'
        }

        response = requests.post(UPLOAD_URL, headers=headers, files=files, data=data)
        return response

def test_presigned_url_content_type(file_id):
    """测试预签名URL的Content-Type"""
    # 构造文件访问URL
    file_url = f"{BASE_URL}/files/{file_id}"

    # 发送HEAD请求检查Content-Type
    response = requests.head(file_url, allow_redirects=False)
    print(f"文件访问URL: {file_url}")
    print(f"状态码: {response.status_code}")
    print(f"Content-Type: {response.headers.get('Content-Type', 'Not Set')}")

    # 如果是重定向，获取重定向的URL
    if response.status_code in [301, 302, 307, 308]:
        redirect_url = response.headers.get('Location')
        if redirect_url:
            print(f"重定向到: {redirect_url}")

            # 检查重定向URL的Content-Type
            redirect_response = requests.head(redirect_url, timeout=10)
            print(f"重定向URL状态码: {redirect_response.status_code}")
            print(f"重定向URL Content-Type: {redirect_response.headers.get('Content-Type', 'Not Set')}")

            return redirect_response.headers.get('Content-Type')

    return response.headers.get('Content-Type')

def main():
    try:
        print("🔐 正在登录...")
        token = login()
        print("✅ 登录成功")

        print("\n📷 创建测试图片...")
        test_image_path = create_test_image()
        print(f"✅ 测试图片创建: {test_image_path}")

        print("\n📤 上传测试图片...")
        upload_response = upload_file(token, test_image_path)

        if upload_response.status_code == 200:
            result = upload_response.json()
            if result.get("code") == 0:  # 修复：code应该是0，不是200
                file_id = result["data"]["file_id"]  # 修复：字段名是file_id
                print(f"✅ 文件上传成功，文件ID: {file_id}")

                print("\n🔍 测试预签名URL的Content-Type...")
                content_type = test_presigned_url_content_type(file_id)

                print(f"\n📊 测试结果:")
                print(f"文件ID: {file_id}")
                print(f"Content-Type: {content_type}")

                if content_type and 'image/jpeg' in content_type:
                    print("🎉 Content-Type设置正确！")
                elif content_type and 'application/octet-stream' in content_type:
                    print("❌ Content-Type仍然是application/octet-stream")
                else:
                    print(f"⚠️  Content-Type异常: {content_type}")

            else:
                print(f"❌ 上传失败: {result}")
        else:
            print(f"❌ 上传请求失败: {upload_response.status_code} - {upload_response.text}")

        # 清理临时文件
        os.unlink(test_image_path)

    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    main()
