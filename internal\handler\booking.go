package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// BookingHandler 预约处理器
type BookingHandler struct {
	db                    *gorm.DB
	bookingService        *service.BookingService
	unifiedBookingService *service.UnifiedBookingService
	notificationService   *service.NotificationService
	urlConverter          URLConverter
}

// NewBookingHandler 创建预约处理器实例
func NewBookingHandler(db *gorm.DB, bookingService *service.BookingService, notificationService *service.NotificationService, storage storage.Storage) *BookingHandler {
	return &BookingHandler{
		db:                    db,
		bookingService:        bookingService,
		unifiedBookingService: service.NewUnifiedBookingService(db),
		notificationService:   notificationService,
		urlConverter:          NewURLConverter(db, storage),
	}
}

// CreateBooking 创建预约（统一处理，支持立即预订和排队）
func (h *BookingHandler) CreateBooking(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.BookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定预约请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到创建预约请求", "user_id", userID, "schedule_id", req.ScheduleID, "people_count", req.PeopleCount)

	// 使用统一预订服务处理
	bookingReq := service.BookingRequest{
		UserID:           userID.(string),
		CourseID:         req.ScheduleID, // 将ScheduleID映射为CourseID
		MembershipCardID: req.MembershipCardID,
		PeopleCount:      req.PeopleCount,
	}

	result, err := h.unifiedBookingService.ProcessBooking(bookingReq)
	if err != nil {
		log.Error("预订处理失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, result.Message))
		return
	}

	if result.Success {
		if result.BookingID > 0 {
			// 立即预订成功
			response := dto.BookingSuccessResponse{
				BookingID:       result.BookingID,
				Message:         result.Message,
				DeductedAmount:  result.DeductedAmount,
				DeductedTimes:   result.DeductedTimes,
				RemainingAmount: result.RemainingAmount,
				RemainingTimes:  result.RemainingTimes,
			}
			log.Info("预订成功", "booking_id", result.BookingID, "user_id", userID)
			code.AutoResponse(c, response, nil)
		} else {
			// 加入排队
			response := dto.BookingQueueResponse{
				QueuePosition: result.QueuePosition,
				Message:       result.Message,
			}
			log.Info("加入排队", "queue_position", result.QueuePosition, "user_id", userID)
			code.AutoResponse(c, response, nil)
		}
	} else {
		// 预订失败
		log.Error("预订失败", "message", result.Message, "user_id", userID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, result.Message))
	}
}

// GetBookingStatus 查询预约状态
func (h *BookingHandler) GetBookingStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	taskID := c.Param("task_id")
	if taskID == "" {
		log.Error("任务ID不能为空")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "任务ID不能为空"))
		return
	}

	// 查询预约申请
	var application model.BookingApplication
	err := h.db.Preload("Booking.Schedule.Course").
		Preload("Booking.Schedule.Coach").
		Preload("Booking.Schedule.Store").
		Where("task_id = ? AND user_id = ?", taskID, userID).
		First(&application).Error

	if err != nil {
		log.Error("查询预约申请失败", "task_id", taskID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约申请不存在"))
		return
	}

	// 构建响应
	response := dto.BookingStatusResponse{
		TaskID:        application.TaskID,
		Status:        string(application.Status),
		Result:        application.Result,
		BookingID:     application.BookingID,
		QueuePosition: application.QueuePosition,
		ErrorCode:     application.ErrorCode,
		CreatedAt:     application.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     application.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 如果预约成功，添加预约详情
	if application.Status == "completed" && application.Booking != nil {
		bookingInfo := dto.BookingInfo{
			ID:            application.Booking.ID,
			BookingNumber: application.Booking.BookingNumber,
			ScheduleInfo: dto.ScheduleInfo{
				ID:           application.Booking.Schedule.ID,
				CourseName:   application.Booking.Schedule.Course.Name,
				CoachName:    getCoachName(application.Booking.Schedule.Coach),
				StoreName:    application.Booking.Schedule.Store.Name,
				StoreAddress: application.Booking.Schedule.Store.Address,
				StartTime:    application.Booking.Schedule.StartTime.Format("2006-01-02 15:04:05"),
				EndTime:      application.Booking.Schedule.EndTime.Format("2006-01-02 15:04:05"),
				Duration:     application.Booking.Schedule.Course.Duration,
				MaxCapacity:  application.Booking.Schedule.MaxCapacity,
				CurrentCount: application.Booking.Schedule.CurrentCount,
			},
			PeopleCount: application.Booking.PeopleCount,
			Status:      int(application.Booking.Status),
			StatusText:  h.getStatusText(int(application.Booking.Status)),
			CanCancel:   h.bookingService.CanCancelBooking(application.Booking),
			CanRate:     application.Booking.Status == 3 && application.Booking.Rating == 0,
			Rating:      application.Booking.Rating,
			Comment:     application.Booking.Comment,
			CreatedAt:   application.Booking.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		response.BookingInfo = &bookingInfo
	}

	code.AutoResponse(c, response, nil)
}

// buildBookingResponse 构建预约响应
func (h *BookingHandler) buildBookingResponse(booking *model.Booking, card *model.MembershipCard) dto.BookingResponse {
	// 查询课程详情
	var schedule model.ClassSchedule
	h.db.Preload("Course").Preload("Coach").Preload("Store").First(&schedule, booking.ScheduleID)

	return dto.BookingResponse{
		BookingNumber: booking.BookingNumber,
		ScheduleInfo: dto.ScheduleInfo{
			ID:           schedule.ID,
			CourseName:   schedule.Course.Name,
			CoachName:    getCoachName(schedule.Coach),
			StoreName:    schedule.Store.Name,
			StoreAddress: schedule.Store.Address,
			StartTime:    schedule.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:      schedule.EndTime.Format("2006-01-02 15:04:05"),
			Duration:     schedule.Course.Duration,
			MaxCapacity:  schedule.MaxCapacity,
			CurrentCount: schedule.CurrentCount,
		},
		MembershipCard: dto.MembershipCardInfo{
			ID:              card.ID,
			CardNumber:      card.CardNumber,
			CardTypeName:    card.CardType.Name,
			Category:        string(card.CardType.Category),
			RemainingTimes:  card.RemainingTimes,
			RemainingAmount: card.RemainingAmount,
			EndDate:         card.EndDate.Format("2006-01-02"),
		},
		DeductInfo: dto.DeductInfo{
			DeductTimes:  booking.DeductTimes,
			DeductAmount: booking.DeductAmount,
			ActualAmount: booking.ActualAmount,
		},
		Status: int(booking.Status),
	}
}

// GetBookingList 获取预约列表
func (h *BookingHandler) GetBookingList(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.BookingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定预约列表请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 设置默认值
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Booking{}).Where("user_id = ?", userID)

	// 状态筛选
	if req.Status > 0 {
		query = query.Where("status = ?", req.Status)
	}

	// 类型筛选
	switch req.Type {
	case "upcoming":
		// 即将上课的
		query = query.Joins("JOIN class_schedules cs ON bookings.schedule_id = cs.id").
			Where("cs.start_time > ? AND bookings.status IN (1, 2)", time.Now())
	case "history":
		// 历史记录
		query = query.Joins("JOIN class_schedules cs ON bookings.schedule_id = cs.id").
			Where("cs.start_time <= ? OR bookings.status IN (3, 4, 5)", time.Now())
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var bookings []model.Booking
	err := query.Preload("Schedule.Course").Preload("Schedule.Coach").Preload("Schedule.Store").
		Order("created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&bookings).Error

	if err != nil {
		log.Error("查询预约列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约列表失败"))
		return
	}

	// 获取统计信息
	totalClasses, totalDays, upcomingClasses, err := h.bookingService.GetBookingStats(userID.(uint))
	if err != nil {
		log.Error("获取预约统计失败", "error", err)
		// 统计失败不影响主要功能，设置默认值
		totalClasses, totalDays, upcomingClasses = 0, 0, 0
	}

	// 构建响应
	response := dto.BookingListResponse{
		List:     h.buildBookingInfoList(bookings),
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		Statistics: dto.UserBookingStats{
			TotalClasses:    totalClasses,
			TotalDays:       totalDays,
			UpcomingClasses: upcomingClasses,
		},
	}

	code.AutoResponse(c, response, nil)
}

// buildBookingInfoList 构建预约信息列表
func (h *BookingHandler) buildBookingInfoList(bookings []model.Booking) []dto.BookingInfo {
	var list []dto.BookingInfo

	for _, booking := range bookings {
		info := dto.BookingInfo{
			ID:            booking.ID,
			BookingNumber: booking.BookingNumber,
			ScheduleInfo: dto.ScheduleInfo{
				ID:           booking.Schedule.ID,
				CourseName:   booking.Schedule.Course.Name,
				CoachName:    getCoachName(booking.Schedule.Coach),
				StoreName:    booking.Schedule.Store.Name,
				StoreAddress: booking.Schedule.Store.Address,
				StartTime:    booking.Schedule.StartTime.Format("2006-01-02 15:04:05"),
				EndTime:      booking.Schedule.EndTime.Format("2006-01-02 15:04:05"),
				Duration:     booking.Schedule.Course.Duration,
				MaxCapacity:  booking.Schedule.MaxCapacity,
				CurrentCount: booking.Schedule.CurrentCount,
			},
			PeopleCount: booking.PeopleCount,
			Status:      int(booking.Status),
			StatusText:  h.getStatusText(int(booking.Status)),
			CanCancel:   h.bookingService.CanCancelBooking(&booking),
			CanRate:     booking.Status == 3 && booking.Rating == 0, // 已完成且未评价
			Rating:      booking.Rating,
			Comment:     booking.Comment,
			CreatedAt:   booking.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		list = append(list, info)
	}

	return list
}

// getStatusText 获取状态文本
func (h *BookingHandler) getStatusText(status int) string {
	bookingStatus := enum.BookingStatus(status)
	// 返回英文状态值，前端负责国际化处理
	return bookingStatus.String()
}

// GetCancelBookingInfo 获取取消预约信息
func (h *BookingHandler) GetCancelBookingInfo(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	bookingIDStr := c.Param("id")
	bookingID := cast.ToUint(bookingIDStr)
	if bookingID == 0 {
		log.Error("预约ID格式错误", "id", bookingIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "预约ID格式错误"))
		return
	}

	// 查询预约详情
	var booking model.Booking
	err := h.db.Preload("Schedule.Course").Preload("Schedule.Coach").Preload("Schedule.Store").
		Preload("MembershipCard.CardType").
		Where("id = ? AND user_id = ?", bookingID, userID).
		First(&booking).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约不存在"))
		} else {
			log.Error("查询预约详情失败", "booking_id", bookingID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约详情失败"))
		}
		return
	}

	// 检查是否可以取消
	canCancel := h.bookingService.CanCancelBooking(&booking)

	// 构建退款信息
	refundMethod := "会员卡退款"
	refundAmount := booking.DeductAmount
	refundTimes := booking.DeductTimes
	cancelMessage := ""

	if !canCancel {
		cancelMessage = "开课前2小时内不可取消预约"
	} else {
		// 检查课程是否火爆（预约人数接近满员）
		occupancyRate := float64(booking.Schedule.CurrentCount) / float64(booking.Schedule.MaxCapacity)
		if occupancyRate >= 0.8 {
			cancelMessage = "当前课程火爆，确定要取消吗？"
		}
	}

	// 构建响应
	response := dto.CancelBookingInfoResponse{
		BookingID:     booking.ID,
		CourseName:    booking.Schedule.Course.Name,
		CoachName:     getCoachName(booking.Schedule.Coach),
		StoreName:     booking.Schedule.Store.Name,
		StartTime:     booking.Schedule.StartTime.Format("2006-01-02 15:04"),
		PeopleCount:   booking.PeopleCount,
		RefundMethod:  refundMethod,
		RefundAmount:  refundAmount,
		RefundTimes:   refundTimes,
		CanCancel:     canCancel,
		CancelMessage: cancelMessage,
	}

	log.Info("获取取消预约信息成功", "user_id", userID, "booking_id", bookingID)
	code.AutoResponse(c, response, nil)
}

// CancelBooking 取消预约
func (h *BookingHandler) CancelBooking(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.CancelBookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定取消预约请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到取消预约请求", "user_id", userID, "booking_id", req.BookingID)

	// 查询预约记录
	var booking model.Booking
	err := h.db.Where("id = ? AND user_id = ?", req.BookingID, userID).First(&booking).Error
	if err != nil {
		log.Error("查询预约记录失败", "booking_id", req.BookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约记录不存在"))
		return
	}

	// 检查是否可以取消
	if !h.bookingService.CanCancelBooking(&booking) {
		log.Warn("预约不可取消", "booking_id", req.BookingID, "status", booking.Status)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该预约不可取消"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新预约状态
	now := time.Now()
	updates := map[string]interface{}{
		"status":        4, // 已取消
		"cancel_time":   &now,
		"cancel_reason": req.Reason,
	}

	if err := tx.Model(&booking).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("更新预约状态失败", "booking_id", req.BookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "取消预约失败"))
		return
	}

	// 2. 退款处理
	if err := h.bookingService.RefundBooking(&booking); err != nil {
		tx.Rollback()
		log.Error("退款处理失败", "booking_id", req.BookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "退款处理失败"))
		return
	}

	// 3. 发送取消预约微信通知
	go func() {
		if h.notificationService != nil {
			err := h.notificationService.SendBookingCancelNotification(booking.ID, req.Reason)
			if err != nil {
				log.Error("发送取消预约通知失败", "booking_id", booking.ID, "error", err)
			} else {
				log.Info("取消预约通知发送成功", "booking_id", booking.ID)
			}
		}
	}()

	// 4. 处理排队通知
	go func() {
		if err := h.bookingService.ProcessQueueNotification(booking.ScheduleID); err != nil {
			log.Error("处理排队通知失败", "schedule_id", booking.ScheduleID, "error", err)
		}
	}()

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "取消预约失败"))
		return
	}

	log.Info("取消预约成功", "booking_id", req.BookingID)
	response := dto.MessageResponse{Message: "预约已取消"}
	code.AutoResponse(c, response, nil)
}

// RateBooking 评价预约
func (h *BookingHandler) RateBooking(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.RateBookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定评价请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到评价预约请求", "user_id", userID, "booking_id", req.BookingID)

	// 查询预约记录
	var booking model.Booking
	err := h.db.Where("id = ? AND user_id = ?", req.BookingID, userID).First(&booking).Error
	if err != nil {
		log.Error("查询预约记录失败", "booking_id", req.BookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约记录不存在"))
		return
	}

	// 检查是否可以评价
	if booking.Status != 3 {
		log.Warn("预约状态不允许评价", "booking_id", req.BookingID, "status", booking.Status)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该预约不可评价"))
		return
	}

	// 更新评价
	updates := map[string]interface{}{
		"rating":  req.Rating,
		"comment": req.Comment,
	}

	if err := h.db.Model(&booking).Updates(updates).Error; err != nil {
		log.Error("更新评价失败", "booking_id", req.BookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "评价失败"))
		return
	}

	log.Info("评价预约成功", "booking_id", req.BookingID, "rating", req.Rating)
	response := dto.MessageResponse{Message: "评价成功"}
	code.AutoResponse(c, response, nil)
}

// GetBookingDetail 获取预约详情
func (h *BookingHandler) GetBookingDetail(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	bookingIDStr := c.Param("id")
	bookingID := cast.ToUint(bookingIDStr)
	if bookingID == 0 {
		log.Error("预约ID格式错误", "booking_id", bookingIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "预约ID格式错误"))
		return
	}

	// 查询预约详情
	var booking model.Booking
	err := h.db.Preload("Schedule.Course").Preload("Schedule.Coach").Preload("Schedule.Store").
		Preload("MembershipCard.CardType").
		Where("id = ? AND user_id = ?", bookingID, userID).
		First(&booking).Error

	if err != nil {
		log.Error("查询预约详情失败", "booking_id", bookingID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约记录不存在"))
		return
	}

	// 构建详情响应
	response := h.buildBookingDetailResponse(&booking)
	code.AutoResponse(c, response, nil)
}

// buildBookingDetailResponse 构建预约详情响应
func (h *BookingHandler) buildBookingDetailResponse(booking *model.Booking) dto.BookingDetailResponse {
	// 判断课程状态
	now := time.Now()
	var courseStatus string
	if now.Before(booking.Schedule.StartTime) {
		courseStatus = "课程未开始"
	} else {
		courseStatus = "课程已结束"
	}

	return dto.BookingDetailResponse{
		ID:            booking.ID,
		BookingNumber: booking.BookingNumber,
		Status:        int(booking.Status),
		StatusText:    h.getStatusText(int(booking.Status)),
		PeopleCount:   booking.PeopleCount,
		CreatedAt:     booking.CreatedAt.Format("2006-01-02 15:04:05"),
		Course: dto.CourseDetailInfo{
			ID:        booking.Schedule.Course.ID,
			Name:      booking.Schedule.Course.Name,
			StartTime: booking.Schedule.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:   booking.Schedule.EndTime.Format("2006-01-02 15:04:05"),
			Duration:  booking.Schedule.Course.Duration,
			Coach: dto.BookingCoachInfo{
				ID:     booking.Schedule.Coach.ID.String(),
				Name:   getCoachName(booking.Schedule.Coach),
				Avatar: h.getCoachAvatarURL(booking.Schedule.Coach),
			},
			Store: dto.BookingStoreDetailInfo{
				ID:      booking.Schedule.Store.ID,
				Name:    booking.Schedule.Store.Name,
				Address: booking.Schedule.Store.Address,
				Phone:   booking.Schedule.Store.Phone,
			},
			CourseStatus: courseStatus,
		},
		Order: dto.OrderInfo{
			BookingNumber: booking.BookingNumber,
			TotalPrice:    int(booking.Schedule.Course.Price * 100), // 转换为分
			ActualAmount:  booking.ActualAmount,
			PaymentTime:   booking.CreatedAt.Format("2006-01-02 15:04:05"),
			PaymentMethod: h.getPaymentMethod(booking),
			DeductTimes:   booking.DeductTimes,
			DeductAmount:  booking.DeductAmount,
		},
		MembershipCard: dto.BookingMembershipCardInfo{
			ID:       booking.MembershipCard.ID,
			Name:     booking.MembershipCard.CardNumber,
			CardType: booking.MembershipCard.CardType.Name,
		},
		CanCancel: h.bookingService.CanCancelBooking(booking),
		CanRate:   booking.Status == 3 && booking.Rating == 0, // 已完成且未评价
		Rating:    booking.Rating,
		Comment:   booking.Comment,
	}
}

// getPaymentMethod 获取支付方式描述
func (h *BookingHandler) getPaymentMethod(booking *model.Booking) string {
	if booking.MembershipCard.ID > 0 {
		return booking.MembershipCard.CardType.Name + "会员卡支付"
	}
	return "其他支付方式"
}

// getCoachAvatarURL 获取教练头像URL
func (h *BookingHandler) getCoachAvatarURL(coach *model.User) string {
	if coach == nil || coach.AvatarID == nil || *coach.AvatarID == "" {
		return ""
	}
	return h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
}
