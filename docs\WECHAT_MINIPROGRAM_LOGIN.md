# 微信小程序登录接口文档

## 📋 概述

本文档描述了微信小程序登录功能的实现，使用了 `silenceper/wechat` 官方SDK，包括配置、API接口和使用方法。

## 🛠️ 技术栈

- **微信SDK**: `github.com/silenceper/wechat/v2` - 官方推荐的微信开发SDK
- **缓存**: 内存缓存 (可扩展为Redis缓存)
- **认证**: JWT令牌认证
- **权限**: 基于角色的访问控制 (RBAC)

## 🔧 配置

### 1. 微信小程序配置

在 `config.yaml` 中添加微信小程序配置：

```yaml
WeChat:
  AppID: "your_wechat_mini_program_appid"     # 微信小程序 AppID
  AppSecret: "your_wechat_mini_program_secret" # 微信小程序 AppSecret
```

### 2. 数据库模型扩展

User模型已扩展支持微信小程序用户：

```go
type User struct {
    BaseModel
    Username string `json:"username"`
    Platform string `json:"platform"` // "admin" 或 "app"

    // 微信小程序相关字段
    WeChatOpenID  string `json:"wechat_openid"`  // 微信小程序 OpenID
    WeChatUnionID string `json:"wechat_unionid"` // 微信 UnionID
    NickName      string `json:"nick_name"`      // 昵称
    AvatarUrl     string `json:"avatar_url"`     // 头像URL
    Gender        int    `json:"gender"`         // 性别 0:未知 1:男 2:女
    Country       string `json:"country"`        // 国家
    Province      string `json:"province"`       // 省份
    City          string `json:"city"`           // 城市
    Language      string `json:"language"`       // 语言

    Roles []Role `json:"roles"`
}
```

## 📡 API 接口

### 1. 微信小程序登录

**接口**: `POST /api/v1/app/wechat/login`

**描述**: 使用微信小程序的 code 进行登录

**请求参数**:
```json
{
  "code": "微信小程序登录凭证"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "Bearer",
    "user_info": {
      "id": 1,
      "username": "wx_12345678",
      "platform": "app",
      "roles": ["user"]
    }
  }
}
```

### 2. 更新用户信息

**接口**: `PUT /api/v1/app/wechat/userinfo`

**描述**: 更新微信用户的个人信息

**请求头**:
```
Authorization: Bearer {access_token}
```

**请求参数**:
```json
{
  "nick_name": "用户昵称",
  "avatar_url": "头像URL",
  "gender": 1,
  "country": "中国",
  "province": "广东省",
  "city": "深圳市",
  "language": "zh_CN"
}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "message": "用户信息更新成功"
  }
}
```

### 3. 获取用户信息

**接口**: `GET /api/v1/app/wechat/userinfo`

**描述**: 获取当前用户信息

**请求头**:
```
Authorization: Bearer {access_token}
```

**响应示例**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "username": "wx_12345678",
    "platform": "app",
    "roles": ["user"]
  }
}
```

### 4. 扩展功能

App端专注于业务功能，不需要菜单管理。可以根据业务需求添加其他接口：

```
GET  /api/v1/app/profile     # 获取用户详细资料
PUT  /api/v1/app/profile     # 更新用户资料
POST /api/v1/app/feedback    # 用户反馈
GET  /api/v1/app/orders      # 用户订单
```

## 🔄 登录流程

### 1. 微信小程序端

```javascript
// 1. 获取微信登录凭证
wx.login({
  success: function(res) {
    if (res.code) {
      // 2. 发送 code 到后端
      wx.request({
        url: 'https://your-api.com/api/v1/app/wechat/login',
        method: 'POST',
        data: {
          code: res.code
        },
        success: function(response) {
          if (response.data.code === 0) {
            // 3. 保存 token
            wx.setStorageSync('access_token', response.data.data.access_token);
            wx.setStorageSync('refresh_token', response.data.data.refresh_token);

            // 4. 登录成功，跳转到主页
            wx.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      });
    }
  }
});
```

### 2. 后端处理流程

1. **接收 code**: 从小程序接收登录凭证
2. **调用微信接口**: 使用 code 换取 openid 和 session_key
3. **查找或创建用户**: 根据 openid 查找用户，不存在则创建
4. **分配默认角色**: 为新用户分配 "user" 角色
5. **生成 JWT**: 生成访问令牌和刷新令牌
6. **返回响应**: 返回令牌和用户信息

## 🛡️ 安全特性

### 1. 用户验证
- 通过微信官方接口验证 code 的有效性
- 自动创建用户账户，无需手动注册
- 支持用户账户启用/禁用状态检查

### 2. 权限管理
- 新用户自动分配默认 "user" 角色
- 支持基于角色的权限控制
- 集成 Casbin 权限验证系统

### 3. 令牌安全
- 使用 JWT 进行身份认证
- 支持访问令牌和刷新令牌
- 令牌过期自动处理

## 🔧 开发配置

### 1. 获取微信小程序配置

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入小程序管理后台
3. 在 "开发" -> "开发管理" -> "开发设置" 中获取：
   - AppID (小程序ID)
   - AppSecret (小程序密钥)

### 2. 配置服务器域名

在微信小程序后台配置服务器域名：
- request合法域名: `https://your-api-domain.com`

## 🧪 测试

### 1. 使用微信开发者工具测试

```javascript
// 在小程序中测试登录
Page({
  onLoad: function() {
    this.wechatLogin();
  },

  wechatLogin: function() {
    wx.login({
      success: (res) => {
        console.log('微信登录 code:', res.code);
        // 调用后端登录接口
        this.callLoginAPI(res.code);
      }
    });
  },

  callLoginAPI: function(code) {
    wx.request({
      url: 'http://localhost:9095/api/v1/app/wechat/login',
      method: 'POST',
      data: { code: code },
      success: (res) => {
        console.log('登录响应:', res.data);
      }
    });
  }
});
```

### 2. 使用 curl 测试

```bash
# 测试登录接口
curl -X POST "http://localhost:9095/api/v1/app/wechat/login" \
  -H "Content-Type: application/json" \
  -d '{"code": "your_wechat_code"}'

# 测试获取用户信息
curl -X GET "http://localhost:9095/api/v1/app/wechat/userinfo" \
  -H "Authorization: Bearer your_access_token"
```

## 🎉 特性总结

- ✅ **无缝登录**: 支持微信小程序一键登录
- ✅ **自动注册**: 新用户自动创建账户
- ✅ **角色管理**: 集成 RBAC 权限系统
- ✅ **用户信息**: 支持微信用户信息同步
- ✅ **安全认证**: JWT 令牌认证机制
- ✅ **菜单权限**: 基于角色的菜单访问控制

## 🔧 SDK优势

使用 `silenceper/wechat` SDK 的优势：

### 1. 官方推荐
- 微信官方推荐的Go语言SDK
- 持续维护和更新
- 完整的API覆盖

### 2. 简化开发
```go
// 之前：手动HTTP请求
resp, err := http.Get("https://api.weixin.qq.com/sns/jscode2session?...")

// 现在：SDK封装
result, err := miniProgram.GetAuth().Code2Session(code)
```

### 3. 内置缓存
- 自动管理access_token
- 支持内存和Redis缓存
- 避免频繁请求微信接口

### 4. 错误处理
- 统一的错误处理机制
- 详细的错误信息
- 自动重试机制

## 📦 依赖管理

```bash
# 安装微信SDK
go get github.com/silenceper/wechat/v2

# 查看版本
go list -m github.com/silenceper/wechat/v2
```

这样的实现为微信小程序提供了完整的用户认证和权限管理解决方案！
