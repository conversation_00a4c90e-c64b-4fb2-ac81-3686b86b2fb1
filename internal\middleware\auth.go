package middleware

import (
	"strings"
	"yogaga/pkg/code"
	"yogaga/pkg/jwtx"

	"github.com/gin-gonic/gin"
)

const (
	// ContextUserIDKey is the key for user ID in the context.
	ContextUserIDKey = "user_id"
	// ContextRolesKey is the key for user roles in the context.
	ContextRolesKey = "roles"
)

// AuthMiddleware creates a middleware that validates the JWT token.
func AuthMiddleware(jwtSvc *jwtx.Service) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "未登录"))
			c.Abort()
			return
		}

		parts := strings.Split(authHeader, " ")
		if len(parts) != 2 || parts[0] != "Bearer" {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "认证格式必须为: Bearer {token}"))
			c.Abort()
			return
		}

		tokenStr := parts[1]
		claims, err := jwtSvc.ParseToken(tokenStr)
		if err != nil {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "无效的认证令牌"))
			c.Abort()
			return
		}

		// Set user info into context for subsequent handlers
		c.Set(ContextUserIDKey, claims.UserID)
		c.Set("username", claims.Username) // 添加用户名到 context
		c.Set(ContextRolesKey, claims.Roles)

		// 设置角色信息供权限中间件使用
		if len(claims.Roles) > 0 {
			c.Set("roles", claims.Roles)
		} else {
			// 如果没有角色，设置为访客
			c.Set("roles", []string{"guest"})
		}

		c.Next()
	}
}
