# 课程创建和排课API文档

## 概述

本文档描述了分离式课程创建和排课系统的API接口。系统将课程信息管理和排课安排分离，提供更灵活的课程管理方式。

## 业务流程

```
1. 创建课程模板 → 2. 基于模板排课 → 3. 生成课程实例
```

## API接口

### 1. 创建课程模板

**接口地址：** `POST /api/v1/admin/courses`

**功能描述：** 创建课程模板，包含课程的所有元信息

**请求参数：**
```json
{
  "name": "瑜伽基础",
  "description": "适合初学者的基础瑜伽课程，注重体式正位和呼吸配合",
  "course_schedules": [
    {
      "start_time": "2025-07-21T12:00:00Z",
      "end_time": "2025-07-21T13:00:00Z"
    }
  ],
  "duration": 60,
  "capacity": 20,
  "price": 10800,
  "level": 1,
  "category_id": 1,
  "coach_ids": [],
  "store_id": 1,
  "type": 1
}
```

**响应示例：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "course-template-001",
    "name": "瑜伽基础",
    "description": "适合初学者的基础瑜伽课程，注重体式正位和呼吸配合",
    "duration": 60,
    "capacity": 20,
    "price": 10800,
    "level": 1,
    "status": "normal",
    "created_at": "2025-07-17T10:00:00Z"
  }
}
```

### 2. 课程排课

**接口地址：** `POST /api/v1/admin/courses/schedule`

**功能描述：** 基于已创建的课程模板进行排课安排

**请求参数：**
```json
{
  "start_date": "2025-07-17",
  "end_date": "2025-07-27",
  "start_time": "12:00",
  "store_id": 1,
  "weekly_schedules": [
    {
      "weekday": 1,
      "course_id": "course-template-001",
      "coach_ids": ["coach-aa-uuid"],
      "classroom_id": 1
    },
    {
      "weekday": 3,
      "course_id": "course-template-002",
      "coach_ids": ["coach-bb-uuid", "coach-dd-uuid"],
      "classroom_id": 2
    },
    {
      "weekday": 0,
      "course_id": "course-template-003",
      "coach_ids": ["coach-bb-uuid", "coach-ee-uuid"],
      "classroom_id": 3
    }
  ]
}
```

**参数说明：**
- `start_date`: 排课开始日期
- `end_date`: 排课结束日期
- `start_time`: 统一开始时间
- `weekday`: 星期几 (0=周日, 1=周一, ..., 6=周六)
- `course_id`: 课程模板ID
- `coach_ids`: 教练ID数组，支持多教练
- `classroom_id`: 教室ID（可选）

**成功响应：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "message": "课程排课成功",
    "scheduled_count": 5,
    "scheduled_courses": [
      {
        "id": "course-instance-001",
        "name": "阴瑜伽",
        "start_time": "2025-07-20T12:00:00Z",
        "end_time": "2025-07-20T13:30:00Z",
        "duration": 90,
        "capacity": 12,
        "status": "published"
      },
      {
        "id": "course-instance-002",
        "name": "瑜伽基础",
        "start_time": "2025-07-21T12:00:00Z",
        "end_time": "2025-07-21T13:00:00Z",
        "duration": 60,
        "capacity": 20,
        "status": "published"
      }
    ]
  }
}
```

**冲突响应：**
```json
{
  "code": 40000,
  "message": "存在时间冲突",
  "data": {
    "message": "部分课程排课失败，存在冲突",
    "conflicts": [
      {
        "date": "2025-07-21",
        "time": "12:00-13:00",
        "conflict_type": "coach_busy",
        "details": "教练在该时间段已有其他课程安排"
      },
      {
        "date": "2025-07-23",
        "time": "12:00-13:15",
        "conflict_type": "classroom_occupied",
        "details": "指定教室在该时间段已被占用"
      }
    ]
  }
}
```

### 3. 批量创建课程（兼容接口）

**接口地址：** `POST /api/v1/admin/courses/batch`

**功能描述：** 一步完成课程创建和排课（向后兼容）

**请求参数：**
```json
{
  "start_date": "2025-07-17",
  "end_date": "2025-07-27",
  "start_time": "12:00",
  "weekly_schedules": [
    {
      "weekday": 1,
      "course_name": "瑜伽基础",
      "description": "适合初学者的基础瑜伽课程",
      "coach_ids": ["coach-aa-uuid"],
      "duration": 60
    }
  ],
  "global_description": "2025年7月瑜伽课程系列",
  "capacity": 20,
  "price": 10800,
  "level": 2,
  "category_id": 1,
  "store_id": 1,
  "type": 1,
  "deduction_rules": [
    {
      "card_type": "储值卡",
      "deduction_type": "amount",
      "amount": 10800,
      "description": "108元/节"
    }
  ]
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 10000 | 请求参数错误 |
| 20000 | 数据库错误 |
| 40000 | 业务逻辑错误（如时间冲突） |

## 冲突类型说明

| 冲突类型 | 说明 |
|----------|------|
| coach_busy | 教练时间冲突 |
| classroom_occupied | 教室占用冲突 |

## 使用示例

### 完整流程示例

1. **创建瑜伽基础课程模板**
```bash
curl -X POST http://localhost:8080/api/v1/admin/courses \
  -H 'Content-Type: application/json' \
  -d '{
    "name": "瑜伽基础",
    "description": "适合初学者的基础瑜伽课程",
    "duration": 60,
    "capacity": 20,
    "price": 10800,
    "level": 1,
    "category_id": 1,
    "store_id": 1,
    "type": 1
  }'
```

2. **基于模板进行排课**
```bash
curl -X POST http://localhost:8080/api/v1/admin/courses/schedule \
  -H 'Content-Type: application/json' \
  -d '{
    "start_date": "2025-07-17",
    "end_date": "2025-07-27",
    "start_time": "12:00",
    "store_id": 1,
    "weekly_schedules": [
      {
        "weekday": 1,
        "course_id": "course-template-001",
        "coach_ids": ["coach-aa-uuid"]
      }
    ]
  }'
```

## 数据模型

### 课程模板 (Course Template)
- 包含课程的基本信息和属性
- 可以被多次用于排课
- 状态：template, active, inactive

### 课程实例 (Course Instance)
- 基于模板生成的具体课程
- 包含具体的时间、教练、教室信息
- 状态：scheduled, published, cancelled, completed

### 多教练支持
- 使用 CoachIDs 数组字段支持一节课多个教练
- 灵活的教练分配机制

## 注意事项

1. **时间计算**：系统会根据开始时间和课程时长自动计算结束时间
2. **冲突检测**：系统会自动检测教练和教室的时间冲突
3. **多教练支持**：一节课可以分配多个教练
4. **跨周期支持**：支持任意时间范围的排课安排
