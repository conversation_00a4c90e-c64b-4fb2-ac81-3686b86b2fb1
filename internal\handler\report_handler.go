package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ReportHandler struct {
	db *gorm.DB
}

func NewReportHandler(db *gorm.DB) *ReportHandler {
	return &ReportHandler{
		db: db,
	}
}

// GetCardSalesReport 获取会员卡销售报表
func (h *ReportHandler) GetCardSalesReport(c *gin.Context) {
	// 获取时间范围参数
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().AddDate(0, -1, 0).Format("2006-01-02") // 默认一个月前
	}
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02") // 默认今天
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	end = end.Add(24 * time.Hour) // 包含结束日期当天

	// 查询会员卡销售统计
	var cardSales []dto.CardSalesStat
	err = h.db.Model(&model.MembershipCard{}).
		Select("membership_types.name as card_type, COUNT(*) as count, SUM(membership_cards.price) as total_amount").
		Joins("JOIN membership_types ON membership_cards.type = membership_types.name").
		Where("membership_cards.created_at >= ? AND membership_cards.created_at < ?", start, end).
		Group("membership_types.name").
		Find(&cardSales).Error

	if err != nil {
		log.Error("查询会员卡销售统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询销售报表失败"))
		return
	}

	// 计算总计
	var totalCount int64
	var totalAmount float64
	for _, sale := range cardSales {
		totalCount += sale.Count
		totalAmount += sale.TotalAmount
	}

	response := dto.CardSalesReportResponse{
		StartDate:   startDate,
		EndDate:     endDate,
		CardSales:   cardSales,
		TotalCount:  totalCount,
		TotalAmount: totalAmount,
	}

	code.AutoResponse(c, response, nil)
}

// GetBookingReport 获取预约报表
func (h *ReportHandler) GetBookingReport(c *gin.Context) {
	// 获取时间范围参数
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().AddDate(0, -1, 0).Format("2006-01-02")
	}
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02")
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	end = end.Add(24 * time.Hour)

	// 查询预约统计
	var bookingStats []dto.BookingReportStat
	err = h.db.Model(&model.Booking{}).
		Select("DATE(created_at) as date, COUNT(*) as total_count, "+
			"SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as pending_count, "+
			"SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as confirmed_count, "+
			"SUM(CASE WHEN status = 3 THEN 1 ELSE 0 END) as completed_count, "+
			"SUM(CASE WHEN status = 4 THEN 1 ELSE 0 END) as cancelled_count, "+
			"SUM(amount) as total_amount").
		Where("created_at >= ? AND created_at < ?", start, end).
		Group("DATE(created_at)").
		Order("date ASC").
		Find(&bookingStats).Error

	if err != nil {
		log.Error("查询预约统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约报表失败"))
		return
	}

	// 计算总计
	var totalBookings, totalPending, totalConfirmed, totalCompleted, totalCancelled int64
	var totalRevenue float64
	for _, stat := range bookingStats {
		totalBookings += stat.TotalCount
		totalPending += stat.PendingCount
		totalConfirmed += stat.ConfirmedCount
		totalCompleted += stat.CompletedCount
		totalCancelled += stat.CancelledCount
		totalRevenue += stat.TotalAmount
	}

	response := dto.BookingReportResponse{
		StartDate:      startDate,
		EndDate:        endDate,
		DailyStats:     bookingStats,
		TotalBookings:  totalBookings,
		TotalPending:   totalPending,
		TotalConfirmed: totalConfirmed,
		TotalCompleted: totalCompleted,
		TotalCancelled: totalCancelled,
		TotalRevenue:   totalRevenue,
	}

	code.AutoResponse(c, response, nil)
}

// GetCourseReport 获取课程报表
func (h *ReportHandler) GetCourseReport(c *gin.Context) {
	// 获取时间范围参数
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().AddDate(0, -1, 0).Format("2006-01-02")
	}
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02")
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	end = end.Add(24 * time.Hour)

	// 查询课程统计
	var courseStats []dto.CourseReportStat
	err = h.db.Model(&model.Course{}).
		Select("courses.name as course_name, users.nick_name as coach_name, stores.name as store_name, "+
			"COUNT(bookings.id) as booking_count, "+
			"SUM(CASE WHEN bookings.status = 3 THEN 1 ELSE 0 END) as completed_count, "+
			"AVG(CASE WHEN bookings.rating > 0 THEN bookings.rating ELSE NULL END) as avg_rating").
		Joins("LEFT JOIN bookings ON courses.id = bookings.course_id").
		Joins("LEFT JOIN users ON courses.coach_id = users.id").
		Joins("LEFT JOIN stores ON courses.store_id = stores.id").
		Where("courses.start_time >= ? AND courses.start_time < ?", start, end).
		Group("courses.id, courses.name, users.nick_name, stores.name").
		Order("booking_count DESC").
		Find(&courseStats).Error

	if err != nil {
		log.Error("查询课程统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程报表失败"))
		return
	}

	response := dto.CourseReportResponse{
		StartDate:   startDate,
		EndDate:     endDate,
		CourseStats: courseStats,
	}

	code.AutoResponse(c, response, nil)
}

// GetCoachReport 获取教练报表
func (h *ReportHandler) GetCoachReport(c *gin.Context) {
	// 获取时间范围参数
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	if startDate == "" {
		startDate = time.Now().AddDate(0, -1, 0).Format("2006-01-02")
	}
	if endDate == "" {
		endDate = time.Now().Format("2006-01-02")
	}

	// 解析日期
	start, err := time.Parse("2006-01-02", startDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	end, err := time.Parse("2006-01-02", endDate)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	end = end.Add(24 * time.Hour)

	// 查询教练统计
	var coachStats []dto.CoachReportStat
	err = h.db.Model(&model.User{}).
		Select("users.nick_name as coach_name, "+
			"COUNT(DISTINCT courses.id) as course_count, "+
			"COUNT(bookings.id) as booking_count, "+
			"SUM(CASE WHEN bookings.status = 3 THEN 1 ELSE 0 END) as completed_count, "+
			"AVG(CASE WHEN bookings.rating > 0 THEN bookings.rating ELSE NULL END) as avg_rating, "+
			"SUM(CASE WHEN bookings.status = 3 THEN bookings.amount ELSE 0 END) as total_revenue").
		Joins("LEFT JOIN courses ON users.id = courses.coach_id").
		Joins("LEFT JOIN bookings ON courses.id = bookings.course_id").
		Where("users.is_coach = ? AND courses.start_time >= ? AND courses.start_time < ?", true, start, end).
		Group("users.id, users.nick_name").
		Order("booking_count DESC").
		Find(&coachStats).Error

	if err != nil {
		log.Error("查询教练统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练报表失败"))
		return
	}

	response := dto.CoachReportResponse{
		StartDate:  startDate,
		EndDate:    endDate,
		CoachStats: coachStats,
	}

	code.AutoResponse(c, response, nil)
}
