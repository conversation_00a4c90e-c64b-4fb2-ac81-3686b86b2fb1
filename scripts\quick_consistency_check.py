#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Admin API 快速一致性检查工具
用于快速验证路由器与API文档的基本一致性
"""

import json
import re
import sys
from datetime import datetime
from pathlib import Path

def extract_routes_from_router(router_file):
    """从路由器文件中提取admin路由"""
    routes = []

    try:
        with open(router_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # 查找admin路由定义的模式
        # 匹配类似 admin.GET("/users", handler.GetUsers) 的模式
        patterns = [
            r'admin\.(GET|POST|PUT|DELETE|PATCH)\s*\(\s*"([^"]+)"',  # admin.GET("/users", ...)
            r'(\w+Group)\.(GET|POST|PUT|DELETE|PATCH)\s*\(\s*"([^"]+)"',  # xxxGroup.GET("/path", ...)
        ]

        matches = []
        for pattern in patterns:
            if pattern == patterns[0]:  # admin pattern
                pattern_matches = re.findall(pattern, content, re.IGNORECASE)
                matches.extend(pattern_matches)
            else:  # group pattern
                group_matches = re.findall(pattern, content, re.IGNORECASE)
                # 只处理在admin路由组内的路由
                for group_name, method, path in group_matches:
                    # 检查这个group是否在admin路由组内定义
                    group_def_pattern = rf'{group_name}\s*:=\s*admin\.Group\s*\(\s*"([^"]+)"'
                    group_def_match = re.search(group_def_pattern, content)
                    if group_def_match:
                        base_path = group_def_match.group(1)
                        full_path = base_path + path
                        matches.append((method, full_path))

        for method, path in matches:
            full_path = f"/api/v1/admin{path}"
            routes.append({
                'method': method.upper(),
                'path': full_path
            })

    except Exception as e:
        print(f"❌ 读取路由器文件失败: {e}")
        return []

    return routes

def extract_apis_from_collection(api_file):
    """从API文档中提取接口信息"""
    apis = []

    try:
        with open(api_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 遍历所有文件夹和请求
        for folder in data.get('folders', []):
            for request in folder.get('requests', []):
                endpoint = request.get('endpoint', '')
                method = request.get('method', '').upper()
                name = request.get('name', '')

                # 清理endpoint，移除变量
                endpoint = clean_endpoint(endpoint)

                if endpoint and method:
                    apis.append({
                        'name': name,
                        'method': method,
                        'endpoint': endpoint
                    })

    except Exception as e:
        print(f"❌ 读取API文档失败: {e}")
        return []

    return apis

def clean_endpoint(endpoint):
    """清理endpoint，移除变量和baseURL"""
    # 移除 <<baseURL>>
    endpoint = endpoint.replace('<<baseURL>>', '')

    # 将变量替换为路径参数
    # <<user_id>> -> :id, <<course_id>> -> :id 等
    endpoint = re.sub(r'<<[^>]*_id>>', ':id', endpoint)
    endpoint = re.sub(r'<<[^>]+>>', ':param', endpoint)

    return endpoint

def compare_consistency(routes, apis):
    """比较一致性"""
    # 创建映射表
    route_keys = set()
    api_keys = set()

    for route in routes:
        key = f"{route['method']}:{route['path']}"
        route_keys.add(key)

    for api in apis:
        key = f"{api['method']}:{api['endpoint']}"
        api_keys.add(key)

    # 计算匹配情况
    matched = route_keys & api_keys
    missing_in_api = route_keys - api_keys
    missing_in_router = api_keys - route_keys

    total_routes = len(route_keys)
    matched_count = len(matched)
    consistency_rate = (matched_count / total_routes * 100) if total_routes > 0 else 0

    return {
        'total_routes': total_routes,
        'total_apis': len(api_keys),
        'matched_count': matched_count,
        'consistency_rate': consistency_rate,
        'missing_in_api': list(missing_in_api),
        'missing_in_router': list(missing_in_router),
        'matched': list(matched)
    }

def print_report(result):
    """打印检查报告"""
    print("🔍 Admin API 快速一致性检查")
    print("=" * 40)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    print("📊 检查结果摘要:")
    print(f"总路由数: {result['total_routes']}")
    print(f"总API数: {result['total_apis']}")
    print(f"匹配数: {result['matched_count']}")
    print(f"一致性率: {result['consistency_rate']:.1f}%")
    print()

    # 状态判断
    rate = result['consistency_rate']
    if rate >= 95:
        print("🎉 一致性检查通过！")
        status = "PASS"
    elif rate >= 85:
        print("⚠️  一致性需要改进")
        status = "WARNING"
    else:
        print("❌ 一致性检查失败，需要立即修复")
        status = "FAIL"

    print()

    # 显示详细问题
    if result['missing_in_api']:
        print(f"🔍 缺失在API文档中的路由 ({len(result['missing_in_api'])}个):")
        for missing in result['missing_in_api'][:5]:  # 只显示前5个
            print(f"  - {missing}")
        if len(result['missing_in_api']) > 5:
            print(f"  ... 还有 {len(result['missing_in_api']) - 5} 个")
        print()

    if result['missing_in_router']:
        print(f"🔍 缺失在路由器中的接口 ({len(result['missing_in_router'])}个):")
        for missing in result['missing_in_router'][:5]:  # 只显示前5个
            print(f"  - {missing}")
        if len(result['missing_in_router']) > 5:
            print(f"  ... 还有 {len(result['missing_in_router']) - 5} 个")
        print()

    # 改进建议
    print("💡 改进建议:")
    if result['missing_in_api']:
        print(f"  • 建议在API文档中添加 {len(result['missing_in_api'])} 个缺失的接口")
    if result['missing_in_router']:
        print(f"  • 建议检查路由器中是否缺少 {len(result['missing_in_router'])} 个接口的实现")
    if rate < 95:
        print("  • 建议定期运行此检查工具以保持一致性")
        print("  • 建议将此脚本集成到CI/CD流程中")
    else:
        print("  • 当前一致性良好，建议保持定期检查")

    return status

def main():
    """主函数"""
    # 检查必要文件
    router_file = Path("routers/router.go")
    api_file = Path("api-collections/admin/admin_api.json")

    if not router_file.exists():
        print(f"❌ 路由器文件不存在: {router_file}")
        sys.exit(1)

    if not api_file.exists():
        print(f"❌ API文档文件不存在: {api_file}")
        sys.exit(1)

    # 提取数据
    print("📋 提取路由器中的admin路由...")
    routes = extract_routes_from_router(router_file)
    print(f"✅ 提取到 {len(routes)} 个admin路由")

    print("📋 提取API文档中的接口...")
    apis = extract_apis_from_collection(api_file)
    print(f"✅ 提取到 {len(apis)} 个API接口")

    # 比较一致性
    print("📋 进行一致性比较...")
    result = compare_consistency(routes, apis)

    # 输出报告
    print()
    status = print_report(result)

    # 保存详细报告
    report_file = Path("scripts/quick_consistency_report.json")
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    print(f"\n📄 详细报告已保存到: {report_file}")

    # 根据状态设置退出码
    if status == "FAIL":
        sys.exit(1)
    elif status == "WARNING":
        sys.exit(0)  # 警告状态仍然允许通过
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
