# 简化版 Admin API 功能测试脚本

param(
    [string]$BaseURL = "http://127.0.0.1:9095"
)

$ErrorActionPreference = "Continue"
$AccessToken = ""
$TestResults = @()

Write-Host "🧪 Admin API 简化功能测试" -ForegroundColor Cyan
Write-Host "基础URL: $BaseURL" -ForegroundColor Gray
Write-Host "=" * 50

function Test-API {
    param(
        [string]$Name,
        [string]$URL,
        [string]$Method = "GET",
        [hashtable]$Headers = @{},
        [string]$Body = $null
    )
    
    Write-Host "测试: $Name" -ForegroundColor Yellow
    
    try {
        if ($Body) {
            $response = Invoke-RestMethod -Uri $URL -Method $Method -Headers $Headers -Body $Body -ContentType "application/json"
        } else {
            $response = Invoke-RestMethod -Uri $URL -Method $Method -Headers $Headers
        }
        
        if ($response.code -eq 0) {
            Write-Host "  ✅ PASS: $Name" -ForegroundColor Green
            $script:TestResults += @{ Name = $Name; Status = "PASS"; Message = "成功" }
            return $true
        } else {
            Write-Host "  ❌ FAIL: $Name - $($response.message)" -ForegroundColor Red
            $script:TestResults += @{ Name = $Name; Status = "FAIL"; Message = $response.message }
            return $false
        }
    }
    catch {
        Write-Host "  ❌ FAIL: $Name - $($_.Exception.Message)" -ForegroundColor Red
        $script:TestResults += @{ Name = $Name; Status = "FAIL"; Message = $_.Exception.Message }
        return $false
    }
}

# 1. 测试管理员登录
Write-Host "`n🔐 测试管理员登录..." -ForegroundColor Cyan

$loginData = @{
    username = "admin"
    password = "123456"
} | ConvertTo-Json

$loginSuccess = Test-API -Name "管理员登录" -URL "$BaseURL/api/v1/public/admin/login" -Method "POST" -Body $loginData

if ($loginSuccess) {
    try {
        $loginResponse = Invoke-RestMethod -Uri "$BaseURL/api/v1/public/admin/login" -Method POST -Body $loginData -ContentType "application/json"
        $AccessToken = $loginResponse.data.access_token
        Write-Host "  🔑 获取到访问令牌" -ForegroundColor Green
    }
    catch {
        Write-Host "  ❌ 无法获取访问令牌" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "  ❌ 登录失败，跳过后续测试" -ForegroundColor Red
    exit 1
}

# 2. 测试需要认证的接口
$authHeaders = @{ Authorization = "Bearer $AccessToken" }

Write-Host "`n📊 测试管理接口..." -ForegroundColor Cyan

# 测试仪表盘
Test-API -Name "仪表盘数据" -URL "$BaseURL/api/v1/admin/dashboard" -Headers $authHeaders

# 测试用户管理
Test-API -Name "用户列表" -URL "$BaseURL/api/v1/admin/users?page=1&page_size=5" -Headers $authHeaders

# 测试会员卡管理
Test-API -Name "会员卡列表" -URL "$BaseURL/api/v1/admin/membership-cards?page=1&page_size=5" -Headers $authHeaders

# 测试会员卡类型管理
Test-API -Name "会员卡类型列表" -URL "$BaseURL/api/v1/admin/membership-types?page=1&page_size=5" -Headers $authHeaders

# 测试门店管理
Test-API -Name "门店列表" -URL "$BaseURL/api/v1/admin/stores?page=1&page_size=5" -Headers $authHeaders

# 测试课程管理
Test-API -Name "课程列表" -URL "$BaseURL/api/v1/admin/courses?page=1&page_size=5" -Headers $authHeaders

# 测试新增的接口
Write-Host "`n🆕 测试新增接口..." -ForegroundColor Cyan

# 测试门店教室列表（假设门店ID为1）
Test-API -Name "门店教室列表" -URL "$BaseURL/api/v1/admin/stores/1/classrooms" -Headers $authHeaders

# 测试课程支持的卡类型（假设课程ID为1）
Test-API -Name "课程支持的卡类型" -URL "$BaseURL/api/v1/admin/courses/1/supported-card-types" -Headers $authHeaders

# 测试批量课程预览
$batchData = @{
    start_date = "2025-08-01"
    end_date = "2025-08-07"
    start_time = "09:00"
    weekly_schedules = @(
        @{
            weekday = 1
            course_name = "测试瑜伽课程"
            description = "测试用的瑜伽课程"
            coach_ids = @("test-coach-id")
            duration = 90
        }
    )
    global_description = "测试批量创建课程"
    capacity = 20
    price = 10800
    level = 1
    category_id = 1
    store_id = 1
    type = 1
} | ConvertTo-Json -Depth 3

Test-API -Name "批量课程预览" -URL "$BaseURL/api/v1/admin/batch-courses/preview" -Method "POST" -Headers $authHeaders -Body $batchData

# 生成测试报告
Write-Host "`n📄 测试结果摘要:" -ForegroundColor Cyan

$passCount = ($TestResults | Where-Object { $_.Status -eq "PASS" }).Count
$failCount = ($TestResults | Where-Object { $_.Status -eq "FAIL" }).Count
$totalCount = $TestResults.Count
$successRate = if ($totalCount -gt 0) { [math]::Round(($passCount / $totalCount) * 100, 1) } else { 0 }

Write-Host "总测试数: $totalCount" -ForegroundColor White
Write-Host "通过: $passCount" -ForegroundColor Green
Write-Host "失败: $failCount" -ForegroundColor Red
Write-Host "成功率: $successRate%" -ForegroundColor White

# 显示失败的测试
if ($failCount -gt 0) {
    Write-Host "`n❌ 失败的测试:" -ForegroundColor Red
    $TestResults | Where-Object { $_.Status -eq "FAIL" } | ForEach-Object {
        Write-Host "  - $($_.Name): $($_.Message)" -ForegroundColor Gray
    }
}

# 保存报告
$report = @{
    summary = @{
        total = $totalCount
        pass = $passCount
        fail = $failCount
        success_rate = $successRate
    }
    test_time = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    base_url = $BaseURL
    results = $TestResults
} | ConvertTo-Json -Depth 3

$report | Out-File -FilePath "scripts/simple_admin_api_test_report.json" -Encoding UTF8
Write-Host "`n📄 详细报告已保存到: scripts/simple_admin_api_test_report.json" -ForegroundColor Cyan

# 根据成功率设置退出码
if ($successRate -ge 80) {
    Write-Host "`n🎉 测试通过！" -ForegroundColor Green
    exit 0
} elseif ($successRate -ge 60) {
    Write-Host "`n⚠️  测试部分通过，需要关注" -ForegroundColor Yellow
    exit 0
} else {
    Write-Host "`n❌ 测试失败，需要修复" -ForegroundColor Red
    exit 1
}
