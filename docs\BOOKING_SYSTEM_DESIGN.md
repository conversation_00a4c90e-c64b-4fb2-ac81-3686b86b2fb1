# Yogaga预约系统设计文档

## 🎯 设计目标

设计一个**简单好用且满足商业化需求**的预约功能，核心原则：
- **用户体验简单**：3步完成预约
- **商业逻辑清晰**：智能会员卡选择和扣费
- **数据完整性**：事务保证和状态管理
- **扩展性强**：支持排队、评价、统计等功能

## 🏗️ 系统架构

```mermaid
graph TB
    A[用户发起预约] --> B[检查预约条件]
    B --> C[智能选择会员卡]
    C --> D[计算扣费]
    D --> E[创建预约记录]
    E --> F[更新课程人数]
    F --> G[扣除会员卡余额]
    G --> H[预约成功]
    
    I[取消预约] --> J[检查取消条件]
    J --> K[退款处理]
    K --> L[恢复课程人数]
    L --> M[通知排队用户]
```

## 📊 核心数据模型

### 1. 课程排期 (ClassSchedule)
```go
type ClassSchedule struct {
    StoreID      uint      // 门店ID
    CourseID     uint      // 课程ID
    CoachID      uint      // 教练ID
    StartTime    time.Time // 开始时间
    EndTime      time.Time // 结束时间
    MaxCapacity  int       // 最大容量
    CurrentCount int       // 当前预约人数
    Status       int       // 状态：1可预约 2已满员 3已开课 4已结束 5已取消
}
```

### 2. 会员卡 (MembershipCard)
```go
type MembershipCard struct {
    UserID         uint      // 用户ID
    CardTypeID     uint      // 卡种ID
    RemainingTimes int       // 剩余次数
    RemainingAmount int      // 剩余金额(分)
    StartDate      time.Time // 开始日期
    EndDate        time.Time // 结束日期
    Status         int       // 状态：1正常 2冻结 3过期 4用完
}
```

### 3. 预约记录 (Booking)
```go
type Booking struct {
    UserID         uint      // 用户ID
    ScheduleID     uint      // 课程排期ID
    MembershipCardID uint    // 使用的会员卡ID
    BookingNumber  string    // 预约单号
    PeopleCount    int       // 预约人数
    DeductTimes    int       // 扣除次数
    DeductAmount   int       // 扣除金额(分)
    Status         int       // 状态：1已预约 2已签到 3已完成 4已取消 5未到课
}
```

## 🔧 核心功能设计

### 1. 智能会员卡选择

#### 选择优先级
1. **期限卡优先**：有效期内的期限卡优先使用
2. **临期优先**：按有效期临近程度排序
3. **余额充足**：确保余额/次数足够

#### 实现逻辑
```go
func SelectBestCard(userID uint, scheduleID uint) (*MembershipCard, error) {
    // 查询用户有效会员卡，按优先级排序
    cards := db.Where("user_id = ? AND status = 1", userID).
        Where("(end_date IS NULL OR end_date > ?) AND (remaining_times > 0 OR remaining_amount > 0)", time.Now()).
        Order("CASE WHEN category = 'period' THEN 1 ELSE 2 END, end_date ASC").
        Find(&cards)
    
    // 选择第一张可用的卡
    for _, card := range cards {
        if canUseCard(card, scheduleID) {
            return card
        }
    }
}
```

### 2. 扣费计算逻辑

#### 不同卡种扣费规则
- **次数卡**：扣除次数，记录实际消费金额
- **期限卡**：不扣次数，只记录实际消费
- **储值卡**：扣除金额，支持共享使用

#### 计算公式
```go
func CalculateDeduction(card *MembershipCard, schedule *ClassSchedule, peopleCount int) {
    baseAmount := schedule.Price * peopleCount
    actualAmount := int(float64(baseAmount) * card.Discount)
    
    switch card.Category {
    case "times":
        deductTimes = peopleCount
        deductAmount = 0
    case "period":
        deductTimes = 0
        deductAmount = 0
    case "balance":
        deductTimes = 0
        deductAmount = actualAmount
    }
}
```

### 3. 预约流程控制

#### 预约前检查
1. **时间冲突检查**：同一时间段不能重复预约
2. **课程可用性**：检查课程状态和容量
3. **预约时限**：开课前2小时停止预约
4. **会员卡有效性**：检查卡片状态和余额

#### 事务保证
```go
func CreateBooking(req BookingRequest) error {
    tx := db.Begin()
    defer tx.Rollback()
    
    // 1. 创建预约记录
    // 2. 更新课程人数
    // 3. 扣除会员卡余额
    
    return tx.Commit()
}
```

### 4. 取消和退款机制

#### 取消条件
- 开课前2小时外可取消
- 已预约或已签到状态可取消
- 已完成或已取消状态不可取消

#### 退款处理
```go
func RefundBooking(booking *Booking) error {
    // 1. 恢复会员卡余额
    // 2. 恢复课程人数
    // 3. 触发排队通知
}
```

## 📱 API接口设计

### 1. 创建预约
```
POST /api/v1/app/bookings
{
  "schedule_id": 123,
  "membership_card_id": 456,  // 可选，不传则自动选择
  "people_count": 1
}
```

### 2. 预约列表
```
GET /api/v1/app/bookings?type=upcoming&page=1&page_size=10
```

### 3. 取消预约
```
DELETE /api/v1/app/bookings/cancel
{
  "booking_id": 123,
  "reason": "临时有事"
}
```

### 4. 评价预约
```
POST /api/v1/app/bookings/rate
{
  "booking_id": 123,
  "rating": 5,
  "comment": "教练很专业"
}
```

## 🎯 商业化特性

### 1. 会员卡优先级策略
- **促进期限卡销售**：期限卡优先使用
- **避免浪费**：临期卡优先消费
- **用户友好**：自动选择最优卡种

### 2. 排队机制
- **增加转化**：课程满员后开启排队
- **实时通知**：有空位时自动通知排队用户
- **商业机会**：排队数据可用于增开课程决策

### 3. 数据统计
- **用户画像**：上课次数、偏好分析
- **课程热度**：预约率、取消率统计
- **收入分析**：不同卡种的使用情况

### 4. 防刷机制
- **时间限制**：开课前2小时内不可预约/取消
- **冲突检查**：同一时间段不能重复预约
- **状态控制**：严格的状态流转控制

## 🔄 状态流转图

```mermaid
stateDiagram-v2
    [*] --> 已预约: 创建预约
    已预约 --> 已签到: 用户签到
    已预约 --> 已取消: 用户取消
    已签到 --> 已完成: 课程结束
    已签到 --> 未到课: 未参加课程
    已完成 --> [*]: 可评价
    已取消 --> [*]: 已退款
    未到课 --> [*]: 扣费完成
```

## 🚀 扩展功能

### 1. 微信消息推送
- 预约成功通知
- 开课前2小时提醒
- 取消预约确认
- 排队转预约通知

### 2. 地理位置验证
- 签到时验证距离门店300米内
- 防止代签到行为

### 3. 智能推荐
- 基于用户历史偏好推荐课程
- 根据时间习惯推荐时段

### 4. 会员权益
- VIP会员优先预约
- 积分奖励机制
- 生日特权等

## 💡 技术优势

### 1. 简单易用
- **3步预约**：选课程 → 确认卡种 → 完成预约
- **智能选择**：系统自动选择最优会员卡
- **状态清晰**：预约状态一目了然

### 2. 商业友好
- **收入最大化**：智能的卡种使用策略
- **用户粘性**：排队机制增加用户参与度
- **数据驱动**：完整的统计分析支持

### 3. 技术可靠
- **事务保证**：数据一致性保障
- **并发安全**：课程容量控制
- **扩展性强**：模块化设计便于功能扩展

这个设计既满足了用户的简单易用需求，又充分考虑了商业化运营的各种场景，是一个平衡用户体验和商业价值的优秀方案。
