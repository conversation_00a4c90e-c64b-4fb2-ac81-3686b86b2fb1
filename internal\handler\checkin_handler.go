package handler

import (
	"math"
	"strconv"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type CheckinHandler struct {
	db *gorm.DB
}

func NewCheckinHandler(db *gorm.DB) *CheckinHandler {
	return &CheckinHandler{
		db: db,
	}
}

// GenerateQRCode 生成签到二维码
func (h *CheckinHandler) GenerateQRCode(c *gin.Context) {
	courseIDStr := c.Param("course_id")
	courseID := cast.ToUint(courseIDStr)
	if courseID == 0 {
		log.Error("课程ID格式错误", "course_id", courseIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程ID格式错误"))
		return
	}

	// 验证课程是否存在
	var course model.Course
	err := h.db.Preload("Store").First(&course, courseID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		} else {
			log.Error("查询课程失败", "course_id", courseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		}
		return
	}

	// 检查课程时间（只能在课程开始前90分钟到课程结束后90分钟内生成二维码）
	now := time.Now()
	startTime := course.StartTime.Add(-90 * time.Minute)
	endTime := course.EndTime.Add(90 * time.Minute)

	if now.Before(startTime) || now.After(endTime) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "当前时间不在签到时间范围内"))
		return
	}

	// 生成签到码（使用课程ID + 时间戳）
	checkinCode := generateCheckinCode(uint(courseID), now)

	// 构建二维码数据
	qrData := dto.QRCodeData{
		Type:        "checkin",
		CourseID:    uint(courseID),
		CheckinCode: checkinCode,
		StoreID:     course.StoreID,
		Latitude:    course.Store.Latitude,
		Longitude:   course.Store.Longitude,
		ExpireTime:  endTime.Unix(),
	}

	response := dto.QRCodeResponse{
		QRData:     qrData,
		ExpireTime: endTime.Format("2006-01-02 15:04:05"),
	}

	code.AutoResponse(c, response, nil)
}

// CheckIn 扫码签到
func (h *CheckinHandler) CheckIn(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.CheckinRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定签到参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证二维码是否过期
	if time.Now().Unix() > req.QRData.ExpireTime {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "二维码已过期"))
		return
	}

	// 验证签到码
	expectedCode := generateCheckinCode(req.QRData.CourseID, time.Unix(req.QRData.ExpireTime, 0).Add(-90*time.Minute))
	if req.QRData.CheckinCode != expectedCode {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的签到码"))
		return
	}

	// 地理位置验证
	distance := calculateDistance(req.Latitude, req.Longitude, req.QRData.Latitude, req.QRData.Longitude)
	if distance > 300 { // 300米范围内
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "签到位置距离门店过远"))
		return
	}

	// 查询用户的预约记录
	var booking model.Booking
	err := h.db.Where("user_id = ? AND course_id = ? AND status IN (1, 2)", userID, req.QRData.CourseID).First(&booking).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "未找到有效的预约记录"))
		} else {
			log.Error("查询预约记录失败", "user_id", userID, "course_id", req.QRData.CourseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约记录失败"))
		}
		return
	}

	// 检查是否已经签到
	if booking.CheckinTime != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "您已经签到过了"))
		return
	}

	// 更新签到时间
	now := time.Now()
	err = h.db.Model(&booking).Updates(map[string]interface{}{
		"checkin_time": &now,
		"status":       2, // 已确认状态
	}).Error

	if err != nil {
		log.Error("更新签到时间失败", "booking_id", booking.ID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "签到失败"))
		return
	}

	log.Info("用户签到成功", "user_id", userID, "course_id", req.QRData.CourseID, "booking_id", booking.ID)

	response := dto.CheckinResponse{
		Message:     "签到成功",
		CheckinTime: now.Format("2006-01-02 15:04:05"),
	}

	code.AutoResponse(c, response, nil)
}

// GetCheckinStatus 获取签到状态
func (h *CheckinHandler) GetCheckinStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	courseIDStr := c.Param("course_id")
	courseID := cast.ToUint(courseIDStr)
	if courseID == 0 {
		log.Error("课程ID格式错误", "course_id", courseIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程ID格式错误"))
		return
	}

	// 查询预约记录
	var booking model.Booking
	err := h.db.Where("user_id = ? AND course_id = ?", userID, courseID).First(&booking).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "未找到预约记录"))
		} else {
			log.Error("查询预约记录失败", "user_id", userID, "course_id", courseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约记录失败"))
		}
		return
	}

	response := dto.CheckinStatusResponse{
		HasCheckedIn: booking.CheckinTime != nil,
		CheckinTime:  "",
	}

	if booking.CheckinTime != nil {
		response.CheckinTime = booking.CheckinTime.Format("2006-01-02 15:04:05")
	}

	code.AutoResponse(c, response, nil)
}

// generateCheckinCode 生成签到码
func generateCheckinCode(courseID uint, timestamp time.Time) string {
	// 简单的签到码生成算法（实际项目中可以使用更复杂的算法）
	return strconv.FormatInt(int64(courseID)*timestamp.Unix()%1000000, 10)
}

// calculateDistance 计算两点间距离（米）
func calculateDistance(lat1, lon1, lat2, lon2 float64) float64 {
	const R = 6371000 // 地球半径（米）

	lat1Rad := lat1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	deltaLat := (lat2 - lat1) * math.Pi / 180
	deltaLon := (lon2 - lon1) * math.Pi / 180

	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLon/2)*math.Sin(deltaLon/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	return R * c
}
