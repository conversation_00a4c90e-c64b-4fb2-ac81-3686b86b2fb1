#!/usr/bin/env node

/**
 * 角色创建测试脚本
 * 测试角色创建API是否正常工作
 */

const https = require('https');
const http = require('http');

// 配置
const config = {
    baseURL: 'http://localhost:9095',
    adminCredentials: {
        username: 'admin',
        password: 'admin123'
    }
};

// HTTP请求工具函数
function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const protocol = options.protocol === 'https:' ? https : http;

        const req = protocol.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });

            res.on('end', () => {
                try {
                    const result = {
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body ? JSON.parse(body) : null
                    };
                    resolve(result);
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        body: body
                    });
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(JSON.stringify(data));
        }

        req.end();
    });
}

// 登录获取token
async function login() {
    console.log('🔐 正在登录...');

    const url = new URL('/api/v1/public/admin/login', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options, config.adminCredentials);

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log('✅ 登录成功');
            return response.body.data.access_token;
        } else {
            console.error('❌ 登录失败:', response.body);
            return null;
        }
    } catch (error) {
        console.error('❌ 登录请求失败:', error.message);
        return null;
    }
}

// 获取角色列表
async function getRoles(token) {
    console.log('📋 获取角色列表...');

    const url = new URL('/api/v1/admin/roles', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'GET',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200 && response.body.code === 0) {
            const roles = Array.isArray(response.body.data) ? response.body.data : response.body.data.list || [];
            console.log(`✅ 获取角色列表成功，共 ${roles.length} 个角色`);

            // 显示现有角色
            roles.forEach(role => {
                console.log(`   - ID: ${role.id}, 名称: ${role.name}, 描述: ${role.description}`);
            });

            return roles;
        } else {
            console.error('❌ 获取角色列表失败:', response.body);
            return [];
        }
    } catch (error) {
        console.error('❌ 获取角色列表请求失败:', error.message);
        return [];
    }
}

// 创建角色
async function createRole(token, roleData) {
    console.log(`🆕 创建角色: ${roleData.name}`);

    const url = new URL('/api/v1/admin/roles', config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options, roleData);

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log(`✅ 角色创建成功: ${roleData.name}`);
            console.log(`   - 新角色ID: ${response.body.data.id}`);
            return response.body.data;
        } else {
            console.error(`❌ 角色创建失败: ${roleData.name}`, response.body);
            return null;
        }
    } catch (error) {
        console.error(`❌ 角色创建请求失败: ${roleData.name}`, error.message);
        return null;
    }
}

// 删除角色
async function deleteRole(token, roleId) {
    console.log(`🗑️  删除角色: ID ${roleId}`);

    const url = new URL(`/api/v1/admin/roles/${roleId}`, config.baseURL);
    const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: 'DELETE',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    };

    try {
        const response = await makeRequest(options);

        if (response.statusCode === 200 && response.body.code === 0) {
            console.log(`✅ 角色删除成功: ID ${roleId}`);
            return true;
        } else {
            console.error(`❌ 角色删除失败: ID ${roleId}`, response.body);
            return false;
        }
    } catch (error) {
        console.error(`❌ 角色删除请求失败: ID ${roleId}`, error.message);
        return false;
    }
}

// 主测试函数
async function main() {
    console.log('🚀 开始测试角色创建功能...');
    console.log('='.repeat(50));

    // 1. 登录
    const token = await login();
    if (!token) {
        console.error('❌ 无法获取访问令牌，测试终止');
        return;
    }

    // 2. 获取现有角色列表
    const existingRoles = await getRoles(token);

    // 3. 测试创建角色
    const testRoles = [
        { name: '小智', description: '这是测试' },
        { name: '测试角色1', description: '第一个测试角色' },
        { name: '测试角色2', description: '第二个测试角色' }
    ];

    const createdRoles = [];

    for (const roleData of testRoles) {
        const createdRole = await createRole(token, roleData);
        if (createdRole) {
            createdRoles.push(createdRole);
        }

        // 等待一秒避免请求过快
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 4. 再次获取角色列表验证
    console.log('\n📋 验证角色创建结果...');
    await getRoles(token);

    // 5. 清理测试数据（可选）
    console.log('\n🧹 清理测试数据...');
    for (const role of createdRoles) {
        await deleteRole(token, role.id);
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    console.log('\n🎉 角色创建测试完成！');
}

// 运行测试
main().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
});
