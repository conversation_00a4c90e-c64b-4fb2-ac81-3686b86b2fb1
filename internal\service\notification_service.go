package service

import (
	"encoding/json"
	"fmt"
	"time"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

// NotificationService 消息推送服务
type NotificationService struct {
	db            *gorm.DB
	wechatService *WeChatService
	asynqClient   *asynq.Client
}

// NewNotificationService 创建消息推送服务实例
func NewNotificationService(db *gorm.DB, wechatService *WeChatService, asynqClient *asynq.Client) *NotificationService {
	return &NotificationService{
		db:            db,
		wechatService: wechatService,
		asynqClient:   asynqClient,
	}
}

// ScheduleCourseReminder 安排开课提醒
func (s *NotificationService) ScheduleCourseReminder(courseID uint) error {
	// 查询课程信息
	var course model.Course
	err := s.db.Preload("Store").First(&course, courseID).Error
	if err != nil {
		log.Error("查询课程失败", "course_id", courseID, "error", err)
		return fmt.Errorf("查询课程失败: %w", err)
	}

	// 查询该课程的所有预约用户
	var bookings []model.Booking
	err = s.db.Preload("User").Where("course_id = ? AND status IN (1, 2)", courseID).Find(&bookings).Error
	if err != nil {
		log.Error("查询预约用户失败", "course_id", courseID, "error", err)
		return fmt.Errorf("查询预约用户失败: %w", err)
	}

	// 计算提醒时间（课程开始前2小时）
	reminderTime := course.StartTime.Add(-2 * time.Hour)

	// 为每个用户安排提醒任务
	for _, booking := range bookings {
		if !booking.User.HasWeChatOpenID() {
			continue // 跳过没有微信OpenID的用户
		}

		// 创建提醒任务
		payload := map[string]interface{}{
			"user_id":     booking.UserID,
			"course_id":   courseID,
			"course_name": course.Name,
			"start_time":  course.StartTime.Format("2006-01-02 15:04"),
			"store_name":  course.Store.Name,
			"openid":      booking.User.WeChatOpenID,
		}
		payloadBytes, _ := json.Marshal(payload)
		task := asynq.NewTask("course:reminder", payloadBytes)

		// 安排在提醒时间执行
		_, err = s.asynqClient.Enqueue(task, asynq.ProcessAt(reminderTime))
		if err != nil {
			log.Error("安排开课提醒任务失败", "user_id", booking.UserID, "course_id", courseID, "error", err)
			continue
		}

		log.Info("安排开课提醒任务成功", "user_id", booking.UserID, "course_id", courseID, "reminder_time", reminderTime)
	}

	return nil
}

// SendBookingCancelNotification 发送预约取消通知
func (s *NotificationService) SendBookingCancelNotification(bookingID uint, reason string) error {
	// 查询预约信息
	var booking model.Booking
	err := s.db.Preload("User").Preload("Course").Preload("Course.Store").First(&booking, bookingID).Error
	if err != nil {
		log.Error("查询预约信息失败", "booking_id", bookingID, "error", err)
		return fmt.Errorf("查询预约信息失败: %w", err)
	}

	if !booking.User.HasWeChatOpenID() {
		log.Warn("用户没有微信OpenID，跳过推送", "user_id", booking.UserID)
		return nil
	}

	// 发送取消通知
	err = s.wechatService.SendBookingCancelNotice(
		booking.User.GetWeChatOpenID(),
		booking.Course.Name,
		time.Now().Format("2006-01-02 15:04"),
		reason,
	)

	if err != nil {
		log.Error("发送预约取消通知失败", "booking_id", bookingID, "error", err)
		return fmt.Errorf("发送预约取消通知失败: %w", err)
	}

	log.Info("发送预约取消通知成功", "booking_id", bookingID, "user_id", booking.UserID)
	return nil
}

// ProcessQueueToBooking 处理排队转预约
func (s *NotificationService) ProcessQueueToBooking(courseID uint) error {
	// 查询课程信息
	var course model.Course
	err := s.db.First(&course, courseID).Error
	if err != nil {
		log.Error("查询课程失败", "course_id", courseID, "error", err)
		return fmt.Errorf("查询课程失败: %w", err)
	}

	// 查询当前预约数量
	var currentBookings int64
	err = s.db.Model(&model.Booking{}).Where("course_id = ? AND status IN (1, 2)", courseID).Count(&currentBookings).Error
	if err != nil {
		log.Error("查询当前预约数量失败", "course_id", courseID, "error", err)
		return fmt.Errorf("查询当前预约数量失败: %w", err)
	}

	// 检查是否有空余名额
	availableSpots := course.Capacity - int(currentBookings)
	if availableSpots <= 0 {
		return nil // 没有空余名额
	}

	// 查询排队用户（按排队时间排序）
	var queueUsers []model.BookingQueue
	err = s.db.Preload("User").Where("course_id = ? AND status = 1", courseID).
		Order("created_at ASC").Limit(availableSpots).Find(&queueUsers).Error
	if err != nil {
		log.Error("查询排队用户失败", "course_id", courseID, "error", err)
		return fmt.Errorf("查询排队用户失败: %w", err)
	}

	// 为排队用户发送通知
	for _, queueUser := range queueUsers {
		if !queueUser.User.HasWeChatOpenID() {
			continue
		}

		// 发送排队转预约通知
		err = s.wechatService.SendQueueNotification(
			queueUser.User.GetWeChatOpenID(),
			course.Name,
			time.Now().Add(10*time.Minute).Format("2006-01-02 15:04"), // 10分钟内预约
		)

		if err != nil {
			log.Error("发送排队转预约通知失败", "queue_id", queueUser.ID, "error", err)
			continue
		}

		// 更新排队状态为已通知
		err = s.db.Model(&queueUser).Update("status", 2).Error
		if err != nil {
			log.Error("更新排队状态失败", "queue_id", queueUser.ID, "error", err)
		}

		log.Info("发送排队转预约通知成功", "queue_id", queueUser.ID, "user_id", queueUser.UserID)
	}

	return nil
}

// ProcessCourseReminder 处理开课提醒任务
func (s *NotificationService) ProcessCourseReminder(payload map[string]interface{}) error {
	openID, ok := payload["openid"].(string)
	if !ok {
		return fmt.Errorf("无效的openid")
	}

	courseName, ok := payload["course_name"].(string)
	if !ok {
		return fmt.Errorf("无效的课程名称")
	}

	startTime, ok := payload["start_time"].(string)
	if !ok {
		return fmt.Errorf("无效的开始时间")
	}

	storeName, ok := payload["store_name"].(string)
	if !ok {
		return fmt.Errorf("无效的门店名称")
	}

	// 发送开课提醒
	err := s.wechatService.SendCourseReminder(openID, courseName, startTime, storeName)
	if err != nil {
		log.Error("发送开课提醒失败", "openid", openID, "error", err)
		return fmt.Errorf("发送开课提醒失败: %w", err)
	}

	log.Info("发送开课提醒成功", "openid", openID, "course_name", courseName)
	return nil
}

// CreateBookingQueue 创建预约排队记录
func (s *NotificationService) CreateBookingQueue(userID string, courseID uint) error {
	// 检查是否已经在排队
	var existingQueue model.BookingQueue
	err := s.db.Where("user_id = ? AND course_id = ? AND status = 1", userID, courseID).First(&existingQueue).Error
	if err == nil {
		return fmt.Errorf("用户已在排队中")
	}

	// 创建排队记录
	queue := model.BookingQueue{
		UserID:   userID,
		CourseID: courseID,
		Status:   1, // 排队中
	}

	err = s.db.Create(&queue).Error
	if err != nil {
		log.Error("创建排队记录失败", "user_id", userID, "course_id", courseID, "error", err)
		return fmt.Errorf("创建排队记录失败: %w", err)
	}

	log.Info("创建排队记录成功", "user_id", userID, "course_id", courseID, "queue_id", queue.ID)
	return nil
}

// GetQueuePosition 获取排队位置
func (s *NotificationService) GetQueuePosition(userID, courseID uint) (int, error) {
	var queue model.BookingQueue
	err := s.db.Where("user_id = ? AND course_id = ? AND status = 1", userID, courseID).First(&queue).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return 0, fmt.Errorf("未找到排队记录")
		}
		return 0, fmt.Errorf("查询排队记录失败: %w", err)
	}

	// 计算排队位置
	var position int64
	err = s.db.Model(&model.BookingQueue{}).
		Where("course_id = ? AND status = 1 AND created_at <= ?", courseID, queue.CreatedAt).
		Count(&position).Error
	if err != nil {
		return 0, fmt.Errorf("计算排队位置失败: %w", err)
	}

	return int(position), nil
}
