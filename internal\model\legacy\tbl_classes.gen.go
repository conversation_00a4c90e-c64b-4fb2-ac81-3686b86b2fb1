// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblClass = "tbl_classes"

// TblClass mapped from table <tbl_classes>
type TblClass struct {
	ID             int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID         int32     `gorm:"column:shop_id" json:"shop_id"`
	RoomID         int32     `gorm:"column:room_id;not null" json:"room_id"`
	ClassTpID      int32     `gorm:"column:classTp_id;not null" json:"classTp_id"`
	CoachID        int32     `gorm:"column:coach_id;not null" json:"coach_id"`
	CDate          time.Time `gorm:"column:c_date" json:"c_date"`
	CDate2         time.Time `gorm:"column:c_date2" json:"c_date2"`
	CTimeStart     time.Time `gorm:"column:c_time_start" json:"c_time_start"`
	CTimeEnd       time.Time `gorm:"column:c_time_end" json:"c_time_end"`
	Price          float64   `gorm:"column:price;not null;default:0.00" json:"price"`
	Points         int32     `gorm:"column:points;not null" json:"points"`
	IsFree         int32     `gorm:"column:is_free;not null;default:1" json:"is_free"`
	PayDetail      string    `gorm:"column:pay_detail" json:"pay_detail"`
	Title          string    `gorm:"column:title" json:"title"`
	Note           string    `gorm:"column:note" json:"note"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Picture        string    `gorm:"column:picture" json:"picture"`
	PictureOri     string    `gorm:"column:picture_ori" json:"picture_ori"`
	IsDelete       int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	IsCancel       bool      `gorm:"column:is_cancel;not null" json:"is_cancel"`
	AppointmentNum int32     `gorm:"column:appointment_num;not null" json:"appointment_num"`
	Status         bool      `gorm:"column:status;not null" json:"status"`
	PeopleIsfull   int32     `gorm:"column:people_isfull;not null" json:"people_isfull"`
	IsChkCancel    bool      `gorm:"column:is_chk_cancel" json:"is_chk_cancel"`
	Chktime        time.Time `gorm:"column:chktime" json:"chktime"`
	IsTimeopen     bool      `gorm:"column:is_timeopen;not null;default:1" json:"is_timeopen"`
	OpenHour       int32     `gorm:"column:open_hour;not null" json:"open_hour"`
	OpenTime       time.Time `gorm:"column:open_time" json:"open_time"`
	PeopleNum      int32     `gorm:"column:people_num;not null" json:"people_num"`
	QueneupNum     int32     `gorm:"column:queneup_num;not null" json:"queneup_num"`
}

// TableName TblClass's table name
func (*TblClass) TableName() string {
	return TableNameTblClass
}
