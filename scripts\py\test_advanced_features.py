#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会员卡高级功能测试脚本
测试转卡、充值、升级、请假、停用、延期等高级功能
"""

import requests
import json
import time
from datetime import datetime, timedelta
from test_membership_card import YogaSystemTester

class AdvancedFeatureTester(YogaSystemTester):
    def __init__(self, base_url: str = "http://localhost:9095"):
        super().__init__(base_url)

    def test_card_transfer(self) -> bool:
        """测试转卡功能"""
        print("\n🧪 测试转卡功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 创建转入用户
        transfer_user = {
            "username": f"transfer_user_{int(time.time())}",
            "phone": f"137{int(time.time()) % 100000000:08d}",
            "nickname": "转卡接收用户",
            "password": "123456"
        }

        result = self.api_request('POST', '/api/v1/admin/users', transfer_user)
        if not result['success']:
            print(f"❌ 创建转入用户失败: {result['error']}")
            return False

        transfer_user_id = result['data'].get('data', {}).get('id')
        print(f"✅ 成功创建转入用户，ID: {transfer_user_id}")

        # 2. 执行转卡
        transfer_data = {
            "target_user_phone": transfer_user['phone'],
            "transfer_fee": 5000,  # 50元手续费
            "remark": "Python测试转卡"
        }

        result = self.api_request('POST', f'/api/v1/admin/advanced-membership/transfer', transfer_data)
        if not result['success']:
            print(f"❌ 转卡失败: {result['error']}")
            return False

        print("✅ 转卡功能测试通过")
        return True

    def test_card_recharge(self) -> bool:
        """测试充值功能"""
        print("\n🧪 测试充值功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 获取充值前的卡片信息
        result = self.api_request('GET', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}')
        if not result['success']:
            print(f"❌ 获取卡片信息失败: {result['error']}")
            return False

        original_card = result['data'].get('data', {})
        original_times = original_card.get('remaining_times', 0)
        original_amount = original_card.get('remaining_amount', 0)

        print(f"📋 充值前: 剩余次数={original_times}, 剩余金额={original_amount}")

        # 2. 执行充值
        recharge_data = {
            "payment_method": "cash",
            "recharge_times": 10,
            "recharge_amount": 50000,  # 500元
            "actual_payment": 45000,   # 实收450元，9折
            "extend_validity": False,  # 沿用原有效期
            "remark": "Python测试充值"
        }

        result = self.api_request('POST', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}/recharge', recharge_data)
        if not result['success']:
            print(f"❌ 充值失败: {result['error']}")
            return False

        # 3. 验证充值结果
        result = self.api_request('GET', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}')
        if result['success']:
            updated_card = result['data'].get('data', {})
            new_times = updated_card.get('remaining_times', 0)
            new_amount = updated_card.get('remaining_amount', 0)

            print(f"📋 充值后: 剩余次数={new_times}, 剩余金额={new_amount}")

            if new_times > original_times or new_amount > original_amount:
                print("✅ 充值功能测试通过")
                return True
            else:
                print("❌ 充值后余额未增加")
                return False

        print("❌ 无法验证充值结果")
        return False

    def test_card_upgrade(self) -> bool:
        """测试升级功能"""
        print("\n🧪 测试升级功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 获取可升级的卡种
        result = self.api_request('GET', '/api/v1/admin/membership-card-types', {"status": 1})
        if not result['success']:
            print(f"❌ 获取卡种列表失败: {result['error']}")
            return False

        card_types = result['data'].get('data', {}).get('list', [])
        target_card_type = None

        for card_type in card_types:
            if card_type.get('id') != self.test_data['card_type_id']:
                target_card_type = card_type
                break

        if not target_card_type:
            print("ℹ️  没有可升级的卡种，跳过升级测试")
            return True

        # 2. 执行升级
        upgrade_data = {
            "new_card_type_id": target_card_type.get('id'),
            "upgrade_fee": 10000,  # 100元升级费
            "remark": "Python测试升级"
        }

        result = self.api_request('POST', f'/api/v1/admin/advanced-membership/upgrade', upgrade_data)
        if not result['success']:
            print(f"❌ 升级失败: {result['error']}")
            return False

        print(f"✅ 成功升级到: {target_card_type.get('name')}")
        print("✅ 升级功能测试通过")
        return True

    def test_card_leave(self) -> bool:
        """测试请假功能"""
        print("\n🧪 测试请假功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 申请请假
        leave_data = {
            "leave_days": 7,
            "reason": "Python测试请假",
            "remark": "测试请假功能"
        }

        result = self.api_request('POST', f'/api/v1/admin/advanced-membership/leave/apply', leave_data)
        if not result['success']:
            print(f"❌ 申请请假失败: {result['error']}")
            return False

        print("✅ 成功申请请假7天")

        # 2. 测试提前销假
        result = self.api_request('POST', f'/api/v1/admin/advanced-membership/leave/cancel', {
            "card_id": self.test_data['card_id'],
            "remark": "Python测试提前销假"
        })

        if result['success']:
            print("✅ 成功提前销假")

        print("✅ 请假功能测试通过")
        return True

    def test_card_freeze_unfreeze(self) -> bool:
        """测试停用/启用功能"""
        print("\n🧪 测试停用/启用功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 冻结会员卡
        freeze_data = {
            "reason": "Python测试冻结",
            "remark": "测试冻结功能"
        }

        result = self.api_request('POST', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}/freeze', freeze_data)
        if not result['success']:
            print(f"❌ 冻结会员卡失败: {result['error']}")
            return False

        print("✅ 成功冻结会员卡")

        # 2. 解冻会员卡
        unfreeze_data = {
            "remark": "Python测试解冻"
        }

        result = self.api_request('POST', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}/unfreeze', unfreeze_data)
        if not result['success']:
            print(f"❌ 解冻会员卡失败: {result['error']}")
            return False

        print("✅ 成功解冻会员卡")
        print("✅ 停用/启用功能测试通过")
        return True

    def test_card_extension(self) -> bool:
        """测试延期功能"""
        print("\n🧪 测试延期功能...")

        if not hasattr(self, 'test_data'):
            print("❌ 需要先运行开卡测试")
            return False

        # 1. 获取延期前的到期日期
        result = self.api_request('GET', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}')
        if not result['success']:
            print(f"❌ 获取卡片信息失败: {result['error']}")
            return False

        original_end_date = result['data'].get('data', {}).get('end_date')
        print(f"📋 延期前到期日期: {original_end_date}")

        # 2. 执行延期
        extension_data = {
            "extend_days": 30,
            "extension_fee": 0,
            "remark": "Python测试延期30天"
        }

        result = self.api_request('POST', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}/extend', extension_data)
        if not result['success']:
            print(f"❌ 延期失败: {result['error']}")
            return False

        # 3. 验证延期结果
        result = self.api_request('GET', f'/api/v1/admin/membership-cards/{self.test_data["card_id"]}')
        if result['success']:
            new_end_date = result['data'].get('data', {}).get('end_date')
            print(f"📋 延期后到期日期: {new_end_date}")

            if new_end_date > original_end_date:
                print("✅ 延期功能测试通过")
                return True
            else:
                print("❌ 延期后日期未变化")
                return False

        print("❌ 无法验证延期结果")
        return False

    def test_deduction_rules(self) -> bool:
        """测试扣减规则功能"""
        print("\n🧪 测试扣减规则功能...")

        # 1. 获取课程列表
        result = self.api_request('GET', '/api/v1/admin/courses')
        if not result['success']:
            print(f"❌ 获取课程列表失败: {result['error']}")
            return False

        courses = result['data'].get('data', {}).get('list', [])
        if not courses:
            print("ℹ️  没有课程，跳过扣减规则测试")
            return True

        # 2. 获取卡种列表
        result = self.api_request('GET', '/api/v1/admin/membership-card-types')
        if not result['success']:
            print(f"❌ 获取卡种列表失败: {result['error']}")
            return False

        card_types = result['data'].get('data', {}).get('list', [])
        if not card_types:
            print("❌ 没有卡种")
            return False

        # 3. 创建扣减规则
        rule_data = {
            "course_id": courses[0].get('id'),
            "card_type_id": card_types[0].get('id'),
            "deduction_type": "times",
            "deduction_times": 1,
            "description": "Python测试扣减规则"
        }

        result = self.api_request('POST', '/api/v1/admin/course-deduction-rules', rule_data)
        if not result['success']:
            print(f"❌ 创建扣减规则失败: {result['error']}")
            return False

        rule_id = result['data'].get('data', {}).get('id')
        print(f"✅ 成功创建扣减规则，ID: {rule_id}")

        # 4. 查询扣减规则
        result = self.api_request('GET', f'/api/v1/admin/courses/{courses[0].get("id")}/deduction-rules')
        if result['success']:
            rules = result['data'].get('data', {}).get('list', [])
            if any(rule.get('id') == rule_id for rule in rules):
                print("✅ 扣减规则查询成功")
            else:
                print("❌ 扣减规则查询失败")
                return False

        print("✅ 扣减规则功能测试通过")
        return True

    def run_advanced_tests(self) -> Dict[str, bool]:
        """运行高级功能测试"""
        print("🚀 开始会员卡高级功能测试...")
        print("=" * 60)

        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return {}

        # 先运行基础测试获取测试数据
        print("📋 运行基础测试获取测试数据...")
        if not self.test_card_creation():
            print("❌ 基础测试失败，无法继续高级测试")
            return {}

        # 高级功能测试计划
        tests = [
            ("转卡功能", self.test_card_transfer),
            ("充值功能", self.test_card_recharge),
            ("升级功能", self.test_card_upgrade),
            ("请假功能", self.test_card_leave),
            ("停用启用功能", self.test_card_freeze_unfreeze),
            ("延期功能", self.test_card_extension),
            ("扣减规则功能", self.test_deduction_rules),
        ]

        results = {}

        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name}测试异常: {str(e)}")
                results[test_name] = False

        # 输出测试结果
        print("\n📊 高级功能测试结果汇总:")
        print("=" * 60)

        pass_count = 0
        total_count = len(results)

        for test_name, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"{test_name:<20}: {status}")
            if passed:
                pass_count += 1

        print("=" * 60)
        print(f"总计: {pass_count}/{total_count} 通过 ({pass_count/total_count*100:.1f}%)")

        if pass_count == total_count:
            print("\n🎉 所有高级功能测试通过！")
            print("\n✨ 高级功能验证结果:")
            print("• 转卡功能 ✅")
            print("• 充值功能 ✅")
            print("• 升级功能 ✅")
            print("• 请假功能 ✅")
            print("• 停用启用功能 ✅")
            print("• 延期功能 ✅")
            print("• 扣减规则功能 ✅")
        else:
            print(f"\n⚠️  有 {total_count - pass_count} 个高级功能测试失败")

        return results

if __name__ == "__main__":
    # 创建高级功能测试器实例
    tester = AdvancedFeatureTester()

    # 运行所有高级功能测试
    results = tester.run_advanced_tests()
