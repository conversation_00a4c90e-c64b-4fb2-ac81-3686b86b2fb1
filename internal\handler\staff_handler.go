package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// StaffHandler 员工管理处理器（基于现有users表）
type StaffHandler struct {
	db *gorm.DB
}

// NewStaffHandler 创建员工管理处理器实例
func NewStaffHandler(db *gorm.DB) *StaffHandler {
	return &StaffHandler{
		db: db,
	}
}

// GetStaff 获取员工列表（管理员用户）
func (h *StaffHandler) GetStaff(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()
	roleID := c.Query("role_id")
	status := c.Query("status")
	search := c.Query("search")

	// 构建查询 - 查询可以访问管理端的用户
	query := h.db.Model(&model.User{}).
		Preload("Roles").
		Where("platform_access LIKE ?", "%"+string(enum.PlatformAdmin)+"%")

	// 角色筛选
	if roleID != "" {
		query = query.Joins("JOIN user_roles ON users.id = user_roles.user_id").
			Where("user_roles.role_id = ?", roleID)
	}

	// 状态筛选
	if status != "" {
		if status == "1" {
			query = query.Where("is_active = ?", true)
		} else {
			query = query.Where("is_active = ?", false)
		}
	}

	// 搜索条件
	if search != "" {
		query = query.Where("nick_name LIKE ? OR username LIKE ? OR phone LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 查询数据
	var staff []model.User
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&staff).Error

	if err != nil {
		log.Error("查询员工列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询员工列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     staff,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetActiveStaff 获取在职员工列表（用于销售选择等场景）
func (h *StaffHandler) GetActiveStaff(c *gin.Context) {
	roleType := c.Query("role_type") // sales, coach, manager等

	// 构建查询 - 查询可以访问管理端的激活用户
	query := h.db.Model(&model.User{}).
		Preload("Roles").
		Where("platform_access LIKE ? AND is_active = ?", "%"+string(enum.PlatformAdmin)+"%", true)

	// 角色类型筛选
	if roleType != "" {
		query = query.Joins("JOIN user_roles ON users.id = user_roles.user_id").
			Joins("JOIN roles ON user_roles.role_id = roles.id").
			Where("roles.name LIKE ?", "%"+roleType+"%")
	}

	var staff []model.User
	err := query.Order("nick_name ASC").Find(&staff).Error

	if err != nil {
		log.Error("查询在职员工失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询在职员工失败"))
		return
	}

	code.AutoResponse(c, staff, nil)
}

// UpdateStaffStatus 更新员工状态（在职/离职）
func (h *StaffHandler) UpdateStaffStatus(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("员工ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateStaffStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新员工状态参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询员工
	var user model.User
	if err := h.db.Where("id = ? AND platform_access LIKE ?", id, "%"+string(enum.PlatformAdmin)+"%").First(&user).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "员工不存在"))
		return
	}

	// 更新状态
	if err := h.db.Model(&user).Update("is_active", req.IsActive).Error; err != nil {
		log.Error("更新员工状态失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新员工状态失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Roles").First(&user, id)

	log.Info("更新员工状态成功", "id", id, "is_active", req.IsActive)
	code.AutoResponse(c, user, nil)
}

// GetCoaches 获取教练列表（基于现有users表的is_coach字段）
func (h *StaffHandler) GetCoaches(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()
	status := c.Query("status")
	search := c.Query("search")

	// 构建查询 - 查询教练用户（使用 is_coach 字段）
	query := h.db.Model(&model.User{}).Where("is_coach = ?", true)

	// 状态筛选
	if status != "" {
		if status == "1" {
			query = query.Where("is_active = ?", true)
		} else {
			query = query.Where("is_active = ?", false)
		}
	}

	// 搜索条件
	if search != "" {
		query = query.Where("nick_name LIKE ? OR username LIKE ? OR phone LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 查询数据
	var coaches []model.User
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("sort_order ASC, created_at DESC").
		Find(&coaches).Error

	if err != nil {
		log.Error("查询教练列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     coaches,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// UpdateCoachStatus 更新教练状态
func (h *StaffHandler) UpdateCoachStatus(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教练ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateStaffStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新教练状态参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询教练
	var coach model.User
	if err := h.db.Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("users.id = ? AND (roles.name LIKE '%coach%' OR roles.name LIKE '%教练%')", id).
		First(&coach).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		return
	}

	// 更新状态
	if err := h.db.Model(&coach).Update("is_active", req.IsActive).Error; err != nil {
		log.Error("更新教练状态失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新教练状态失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Roles").First(&coach, id)

	log.Info("更新教练状态成功", "id", id, "is_active", req.IsActive)
	code.AutoResponse(c, coach, nil)
}

// GetSalesStaff 获取销售员工列表（用于会员开卡/充值场景）
func (h *StaffHandler) GetSalesStaff(c *gin.Context) {
	// 构建查询 - 查询可以访问管理端的激活销售角色用户
	query := h.db.Model(&model.User{}).
		Preload("Roles").
		Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("users.platform_access LIKE ? AND users.is_active = ? AND (roles.name LIKE '%sales%' OR roles.name LIKE '%销售%')",
			"%"+string(enum.PlatformAdmin)+"%", true)

	var salesStaff []model.User
	err := query.Order("users.nick_name ASC").Find(&salesStaff).Error

	if err != nil {
		log.Error("查询销售员工失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询销售员工失败"))
		return
	}

	code.AutoResponse(c, salesStaff, nil)
}
