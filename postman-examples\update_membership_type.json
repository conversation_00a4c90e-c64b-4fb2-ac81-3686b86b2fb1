// ===== 更新会员卡类型 API 请求示例 =====
// 接口：PUT /api/v1/admin/membership-types/{id}
// 认证：需要管理员Token
// 说明：增量更新指定ID的会员卡类型，只更新传入的字段，不传的字段保持原值

{
  "name": "15次团课卡",                    // 卡类型名称，可选，最大100字符
  "category": "times",                     // 卡类别，可选，可选值：times(次数卡)/period(期限卡)/balance(储值卡)
  "scope": "group",                        // 适用范围，可选，可选值：group(团课)/private(私教)/small(小班)/universal(通用)
  "description": "适用于团课的15次卡",      // 描述，可选，最大500字符
  "validity_days": 120,                    // 有效期天数，可选，0表示永久有效
  "times": 15,                            // 次数，可选，次数卡必填，其他类型填0
  "amount": 0,                            // 金额，可选，储值卡必填(分为单位)，其他类型填0
  "price": 29900,                         // 售价，可选，单位：分(299元=29900分)
  "discount": 1.0,                        // 默认折扣，可选，0-1之间，1.0表示无折扣
  "shareable": false,                     // 是否可共享(主副卡功能)，可选
  "can_set_expiry": true,                 // 开卡时是否可设置有效期，可选
  "can_select_stores": true,              // 开卡时是否可选择门店，可选
                                          // true: 可选择指定门店，不选择则全部门店可用
                                          // false: 不可选择，强制全部门店可用
  "can_add_sub_card": false,              // 是否支持副卡，可选
  "can_set_daily_limit": true,            // 开卡时是否可设置每日预约限制，可选
  "daily_booking_limit": 2,               // 默认每日预约限制，可选，0表示无限制
  "applicable_courses": "所有团课类课程",   // 适用课程说明，可选
  "sort_order": 5,                        // 排序权重，可选，数字越小越靠前
  "status": 1                             // 状态，可选，1:启用 0:停用
}

// ===== 不同更新场景示例 =====

// 1. 只更新价格和描述
{
  "price": 25900,                         // 259元
  "description": "限时优惠价格的15次团课卡"
}

// 2. 只更新状态（停用卡类型）
{
  "status": 0                             // 停用
}

// 3. 更新门店使用规则
{
  "can_select_stores": false,             // 改为不可选择门店，强制全部门店可用
  "description": "全门店通用的团课卡"
}

// 4. 更新次数和有效期
{
  "times": 20,                            // 改为20次
  "validity_days": 180,                   // 改为180天有效期
  "price": 35900,                         // 相应调整价格
  "name": "20次团课卡"
}

// 5. 开启副卡功能
{
  "can_add_sub_card": true,               // 允许添加副卡
  "shareable": true,                      // 允许共享使用
  "description": "支持主副卡共享的团课卡"
}

// 6. 设置每日预约限制
{
  "can_set_daily_limit": true,            // 允许设置每日限制
  "daily_booking_limit": 1,               // 默认每天最多1节课
  "description": "限制每日预约次数的团课卡"
}

// ===== 期限卡更新示例 =====
{
  "name": "6个月无限次团课卡",
  "category": "period",                    // 期限卡
  "validity_days": 180,                   // 6个月
  "times": 0,                             // 期限卡次数为0
  "amount": 0,                            // 期限卡金额为0
  "price": 89900,                         // 899元
  "can_set_expiry": false,                // 期限卡通常不允许自定义有效期
  "daily_booking_limit": 3,               // 每天最多3节课
  "description": "6个月内无限次团课，每天最多3节"
}

// ===== 储值卡更新示例 =====
{
  "name": "2000元储值卡",
  "category": "balance",                   // 储值卡
  "times": 0,                             // 储值卡次数为0
  "amount": 200000,                       // 储值2000元(200000分)
  "price": 200000,                        // 售价等于储值金额
  "can_select_stores": false,             // 储值卡通常全店通用
  "can_add_sub_card": true,               // 支持副卡
  "daily_booking_limit": 0,               // 无每日限制
  "description": "2000元储值卡，全店通用，支持副卡"
}

// ===== 重要说明 =====
/*
1. 字段类型说明：
   - 普通字段：直接传值，如 "name": "新名称"
   - 指针字段：传null表示不更新，传值表示更新
   - 布尔字段：true/false

2. 部分更新特性：
   - 只传需要更新的字段
   - 不传的字段保持原值不变
   - 传null的指针字段不会更新

3. 业务逻辑验证：
   - times卡：times > 0, amount = 0
   - period卡：times = 0, amount = 0, validity_days > 0
   - balance卡：times = 0, amount > 0

4. can_select_stores 逻辑：
   - true: 开卡时可选择指定门店，不选择则全部门店可用
   - false: 开卡时不可选择，强制全部门店可用

5. 状态管理：
   - 1: 启用（可用于开卡）
   - 0: 停用（不可用于开卡，但已开的卡仍有效）

6. 价格单位：
   - 所有价格以"分"为单位
   - 299元 = 29900分

7. 排序规则：
   - sort_order 数字越小越靠前
   - 相同数字按创建时间排序
*/
