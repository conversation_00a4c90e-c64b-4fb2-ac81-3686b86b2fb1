{
  // ===== 创建会员卡 API 请求示例 =====
  // 接口地址: POST /api/v1/admin/membership-cards
  // 需要认证: Bearer <PERSON> (管理员登录后获取)
  
  // 用户ID - 必填 (36位UUID格式)
  // 说明: 要为哪个用户开卡，需要是系统中已存在的用户ID
  // 格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
  "user_id": "550e8400-e29b-41d4-a716-************",
  
  // 会员卡类型ID - 必填 (数字)
  // 说明: 选择要开的卡类型，需要是系统中已存在的会员卡类型ID
  // 可通过 GET /api/v1/admin/membership-types 接口获取可用的卡类型
  "card_type_id": 1,
  
  // 开始日期 - 必填 (ISO 8601格式)
  // 说明: 会员卡的生效日期，通常是购买当天或指定的开始日期
  // 格式: YYYY-MM-DDTHH:mm:ssZ 或 YYYY-MM-DD
  "start_date": "2024-01-15T00:00:00Z",
  
  // 购买价格 - 必填 (整数，单位：分)
  // 说明: 用户实际支付的金额，以分为单位
  // 例如: 299元 = 29900分
  "purchase_price": 29900,
  
  // 折扣 - 可选 (小数，0-1之间)
  // 说明: 折扣比例，0.8表示8折，1.0表示无折扣
  // 默认: 1.0 (无折扣)
  "discount": 0.85,
  
  // 支付方式 - 可选 (字符串，最大50字符)
  // 说明: 记录用户的支付方式
  // 可选值: "wechat_pay"(微信支付), "alipay"(支付宝), "cash"(现金), "membership_card"(会员卡)
  "payment_method": "wechat_pay",
  
  // 可用门店列表 - 可选 (数字数组)
  // 说明: 限制该会员卡只能在指定门店使用，空数组或不传表示全部门店可用
  // 可通过 GET /api/v1/admin/stores 接口获取门店ID列表
  "available_stores": [1, 2, 3],
  
  // 单日预约课程上限 - 可选 (整数，>=0)
  // 说明: 限制用户每天最多可以预约多少节课，0表示无限制
  // 默认: 0 (无限制)
  "daily_booking_limit": 2,
  
  // 备注 - 可选 (字符串，最大500字符)
  // 说明: 开卡时的备注信息，如优惠活动、特殊说明等
  "remark": "新年促销活动开卡，享受8.5折优惠"
}

// ===== 不同类型会员卡的示例 =====

// 1. 次数卡示例 (times)
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "card_type_id": 1,  // 假设ID为1的是次数卡类型
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 19900,  // 199元
  "discount": 1.0,
  "payment_method": "wechat_pay",
  "available_stores": [],  // 全部门店可用
  "daily_booking_limit": 1,  // 每天最多约1节课
  "remark": "10次团课卡"
}

// 2. 期限卡示例 (period)
{
  "user_id": "550e8400-e29b-41d4-a716-446655440001",
  "card_type_id": 2,  // 假设ID为2的是期限卡类型
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 59900,  // 599元
  "discount": 0.9,  // 9折
  "payment_method": "alipay",
  "available_stores": [1, 2],  // 只能在门店1和2使用
  "daily_booking_limit": 2,  // 每天最多约2节课
  "remark": "3个月无限次团课卡，限工作日使用"
}

// 3. 储值卡示例 (balance)
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "card_type_id": 3,  // 假设ID为3的是储值卡类型
  "start_date": "2024-01-15T00:00:00Z",
  "purchase_price": 100000,  // 1000元
  "discount": 1.0,
  "payment_method": "cash",
  "available_stores": [],  // 全部门店可用
  "daily_booking_limit": 0,  // 无限制
  "remark": "储值1000元，可用于任意课程消费"
}

// ===== 常见错误和注意事项 =====

/*
1. 用户ID格式错误
   - 必须是36位UUID格式: xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx
   - 用户必须在系统中存在

2. 会员卡类型ID不存在
   - 需要先通过 GET /api/v1/admin/membership-types 获取有效的类型ID

3. 日期格式错误
   - 推荐使用 ISO 8601 格式: 2024-01-15T00:00:00Z
   - 也可以使用简化格式: 2024-01-15

4. 价格单位
   - 必须以分为单位，不能使用小数
   - 299.99元 应该写成 29999

5. 折扣范围
   - 必须在 0-1 之间
   - 0.8 表示8折，不是80%

6. 门店ID
   - 必须是系统中存在的门店ID
   - 可通过 GET /api/v1/admin/stores 获取

7. 认证要求
   - 需要在请求头中添加: Authorization: Bearer <your_token>
   - Token通过管理员登录接口获取
*/

// ===== 成功响应示例 =====
/*
{
  "code": 0,
  "message": "success",
  "data": {
    "id": "card-uuid-here",
    "user_id": "550e8400-e29b-41d4-a716-************",
    "card_type_id": 1,
    "card_number": "YG202401150001",
    "status": "active",
    "start_date": "2024-01-15T00:00:00Z",
    "end_date": "2024-04-15T23:59:59Z",
    "remaining_times": 10,
    "remaining_amount": 0,
    "purchase_price": 19900,
    "discount": 1.0,
    "payment_method": "wechat_pay",
    "available_stores": [],
    "daily_booking_limit": 1,
    "remark": "10次团课卡",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
*/
