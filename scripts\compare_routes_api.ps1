# 比较路由器中的admin路由与API文档的一致性
param(
    [string]$RouterPath = "routers/router.go",
    [string]$ApiDocPath = "api-collections/admin/admin_api.json"
)

Write-Host "🔍 开始比较路由器与API文档的一致性..." -ForegroundColor Green
Write-Host ""

# 读取路由器文件
if (-not (Test-Path $RouterPath)) {
    Write-Host "❌ 路由器文件不存在: $RouterPath" -ForegroundColor Red
    exit 1
}

$routerContent = Get-Content $RouterPath -Raw

# 读取API文档
if (-not (Test-Path $ApiDocPath)) {
    Write-Host "❌ API文档不存在: $ApiDocPath" -ForegroundColor Red
    exit 1
}

$apiDoc = Get-Content $ApiDocPath -Raw | ConvertFrom-Json

# 从路由器中提取admin路由
Write-Host "📋 从路由器中提取admin路由..." -ForegroundColor Yellow

# 定义路由提取模式
$routePatterns = @(
    # 直接admin路由
    'admin\.GET\("([^"]+)"',
    'admin\.POST\("([^"]+)"',
    'admin\.PUT\("([^"]+)"',
    'admin\.DELETE\("([^"]+)"',
    
    # 组路由
    '(\w+Group)\.GET\("([^"]+)"',
    '(\w+Group)\.POST\("([^"]+)"',
    '(\w+Group)\.PUT\("([^"]+)"',
    '(\w+Group)\.DELETE\("([^"]+)"'
)

$routerRoutes = @()

# 提取组定义
$groupMatches = [regex]::Matches($routerContent, '(\w+Group) := admin\.Group\("([^"]+)"\)')
$groups = @{}
foreach ($match in $groupMatches) {
    $groups[$match.Groups[1].Value] = $match.Groups[2].Value
}

Write-Host "发现的路由组:" -ForegroundColor Cyan
foreach ($group in $groups.GetEnumerator()) {
    Write-Host "  $($group.Key) -> $($group.Value)" -ForegroundColor Gray
}
Write-Host ""

# 提取所有路由
foreach ($pattern in $routePatterns) {
    $matches = [regex]::Matches($routerContent, $pattern)
    foreach ($match in $matches) {
        if ($match.Groups.Count -eq 2) {
            # 直接admin路由
            $method = if ($pattern -match 'GET') { "GET" } 
                     elseif ($pattern -match 'POST') { "POST" }
                     elseif ($pattern -match 'PUT') { "PUT" }
                     elseif ($pattern -match 'DELETE') { "DELETE" }
            $path = "/api/v1/admin" + $match.Groups[1].Value
            $routerRoutes += @{ Method = $method; Path = $path }
        } elseif ($match.Groups.Count -eq 3) {
            # 组路由
            $groupName = $match.Groups[1].Value
            $method = if ($pattern -match 'GET') { "GET" } 
                     elseif ($pattern -match 'POST') { "POST" }
                     elseif ($pattern -match 'PUT') { "PUT" }
                     elseif ($pattern -match 'DELETE') { "DELETE" }
            $routePath = $match.Groups[2].Value
            
            if ($groups.ContainsKey($groupName)) {
                $basePath = $groups[$groupName]
                $fullPath = "/api/v1/admin" + $basePath + $routePath
                $routerRoutes += @{ Method = $method; Path = $fullPath }
            }
        }
    }
}

Write-Host "📊 路由器中发现的admin路由 ($($routerRoutes.Count)个):" -ForegroundColor Yellow
$routerRoutes | Sort-Object Method, Path | ForEach-Object {
    Write-Host "  $($_.Method) $($_.Path)" -ForegroundColor Gray
}
Write-Host ""

# 从API文档中提取路由
Write-Host "📋 从API文档中提取路由..." -ForegroundColor Yellow

$apiRoutes = @()
function Extract-ApiRoutes($folder) {
    foreach ($request in $folder.requests) {
        $endpoint = $request.endpoint -replace "<<baseURL>>", ""
        $method = $request.method
        $apiRoutes += @{
            Method = $method
            Path = $endpoint
            Name = $request.name
        }
    }

    foreach ($subfolder in $folder.folders) {
        Extract-ApiRoutes $subfolder
    }
}

foreach ($folder in $apiDoc.folders) {
    Extract-ApiRoutes $folder
}

Write-Host "📊 API文档中发现的路由 ($($apiRoutes.Count)个):" -ForegroundColor Yellow
$apiRoutes | Sort-Object Method, Path | ForEach-Object {
    Write-Host "  $($_.Method) $($_.Path) - $($_.Name)" -ForegroundColor Gray
}
Write-Host ""

# 比较路由
Write-Host "🔍 开始比较路由..." -ForegroundColor Green
Write-Host ""

# 找出路由器中有但API文档中没有的路由
$missingInApi = @()
foreach ($routerRoute in $routerRoutes) {
    $found = $apiRoutes | Where-Object {
        $_.Method -eq $routerRoute.Method -and $_.Path -eq $routerRoute.Path
    }
    if (-not $found) {
        $missingInApi += $routerRoute
    }
}

# 找出API文档中有但路由器中没有的路由
$missingInRouter = @()
foreach ($apiRoute in $apiRoutes) {
    $found = $routerRoutes | Where-Object {
        $_.Method -eq $apiRoute.Method -and $_.Path -eq $apiRoute.Path
    }
    if (-not $found) {
        $missingInRouter += $apiRoute
    }
}

# 显示结果
Write-Host "📊 比较结果:" -ForegroundColor Green
Write-Host "  路由器中的路由: $($routerRoutes.Count)" -ForegroundColor Cyan
Write-Host "  API文档中的路由: $($apiRoutes.Count)" -ForegroundColor Cyan
Write-Host "  缺失在API文档中: $($missingInApi.Count)" -ForegroundColor Red
Write-Host "  缺失在路由器中: $($missingInRouter.Count)" -ForegroundColor Red
Write-Host ""

if ($missingInApi.Count -gt 0) {
    Write-Host "❌ 路由器中有但API文档中缺失的路由:" -ForegroundColor Red
    $missingInApi | Sort-Object Method, Path | ForEach-Object {
        Write-Host "  $($_.Method) $($_.Path)" -ForegroundColor Red
    }
    Write-Host ""
}

if ($missingInRouter.Count -gt 0) {
    Write-Host "❌ API文档中有但路由器中缺失的路由:" -ForegroundColor Red
    $missingInRouter | Sort-Object Method, Path | ForEach-Object {
        Write-Host "  $($_.Method) $($_.Path) - $($_.Name)" -ForegroundColor Red
    }
    Write-Host ""
}

if ($missingInApi.Count -eq 0 -and $missingInRouter.Count -eq 0) {
    Write-Host "✅ 所有路由都一致！" -ForegroundColor Green
} else {
    Write-Host "⚠️  发现不一致的路由，需要更新API文档。" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 建议操作:" -ForegroundColor Cyan
if ($missingInApi.Count -gt 0) {
    Write-Host "  1. 将缺失的路由添加到API文档中" -ForegroundColor Yellow
}
if ($missingInRouter.Count -gt 0) {
    Write-Host "  2. 从API文档中移除不存在的路由" -ForegroundColor Yellow
}
if ($missingInApi.Count -eq 0 -and $missingInRouter.Count -eq 0) {
    Write-Host "  无需操作，路由已同步" -ForegroundColor Green
}
