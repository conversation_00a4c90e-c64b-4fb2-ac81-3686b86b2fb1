# 🏃‍♀️ 课程功能实现文档

## 🎯 **功能概述**

课程功能支持免登录访问，用户可以根据日历日期显示各个门店的可报名课程，支持多维度筛选和门店收藏功能。

## 🔧 **技术实现**

### **核心接口（免登录）**

| 接口 | 方法 | 功能描述 |
|------|------|----------|
| `/api/v1/app/courses` | GET | 获取课程列表（支持多维度筛选） |
| `/api/v1/app/courses/:id` | GET | 获取课程详情 |
| `/api/v1/app/courses/date` | GET | 按日期查询课程 |
| `/api/v1/app/courses/calendar` | GET | 获取课程日历 |
| `/api/v1/app/course-categories` | GET | 获取课程分类树 |

### **数据模型增强**

#### Course 课程模型（新增字段）
```go
type Course struct {
    // 原有字段...
    Level       int               // 课程级别 1:初级 2:中级 3:高级
    CategoryID  uint              // 课程分类ID
    CategoryRef *CourseCategory   // 分类关联
}
```

## 📱 **功能特性**

### 1. **🗓️ 日历显示**

#### 获取课程日历
```bash
GET /api/v1/app/courses/calendar?month=2024-01

Response:
{
  "month": "2024-01",
  "calendar": {
    "2024-01-15": 3,  // 该日期有3个课程
    "2024-01-16": 5,  // 该日期有5个课程
    "2024-01-17": 2   // 该日期有2个课程
  }
}
```

#### 按日期查询课程
```bash
GET /api/v1/app/courses/date?date=2024-01-15&store_id=1&level=2

Response:
{
  "date": "2024-01-15",
  "courses": [
    {
      "id": 123,
      "name": "瑜伽基础课程",
      "start_time": "2024-01-15T09:00:00Z",
      "end_time": "2024-01-15T10:30:00Z",
      "coach": {
        "id": 456,
        "nick_name": "张老师"
      },
      "store": {
        "id": 1,
        "name": "Yogaga瑜伽馆(朝阳店)"
      },
      "level": 2,
      "category": {
        "id": 10,
        "name": "哈他瑜伽"
      },
      "available_spots": 8,
      "can_book": true
    }
  ],
  "total": 1
}
```

### 2. **🔍 多维度筛选**

#### 支持的筛选参数
- **门店筛选**：`store_id=1`
- **教练筛选**：`coach_id=456`
- **课程类型筛选**：`type=1` (1:团课 2:私教)
- **课程级别筛选**：`level=2` (1:初级 2:中级 3:高级)
- **课程分类筛选**：`category_id=10`
- **关键词搜索**：`keyword=瑜伽`
- **时间筛选**：`show_past=false` (默认只显示未来课程)

#### 综合筛选示例
```bash
GET /api/v1/app/courses?store_id=1&level=2&type=1&category_id=10&page=1&page_size=10

Response:
{
  "list": [
    {
      "id": 123,
      "name": "中级哈他瑜伽",
      "level": 2,
      "category": {
        "id": 10,
        "name": "哈他瑜伽"
      },
      "coach": {
        "id": 456,
        "nick_name": "张老师"
      },
      "store": {
        "id": 1,
        "name": "Yogaga瑜伽馆(朝阳店)"
      },
      "available_spots": 8,
      "can_book": true
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 10
}
```

### 3. **❤️ 门店收藏功能**

#### 收藏门店（需要登录）
```bash
POST /api/v1/app/favorites
Authorization: Bearer <token>

{
  "target_type": "store",
  "target_id": 1
}
```

#### 检查收藏状态
```bash
GET /api/v1/app/favorites/check?target_type=store&target_id=1
Authorization: Bearer <token>

Response:
{
  "is_favorited": true
}
```

### 4. **📋 课程分类树**

```bash
GET /api/v1/app/course-categories

Response:
{
  "categories": [
    {
      "id": 1,
      "name": "哈他瑜伽",
      "level": 1,
      "children": [
        {
          "id": 10,
          "name": "基础哈他",
          "level": 2
        },
        {
          "id": 11,
          "name": "进阶哈他",
          "level": 2
        }
      ]
    },
    {
      "id": 2,
      "name": "流瑜伽",
      "level": 1,
      "children": [
        {
          "id": 20,
          "name": "基础流瑜伽",
          "level": 2
        }
      ]
    }
  ]
}
```

## 🌟 **功能亮点**

### 1. **🔓 免登录访问**
- 所有课程查看功能都无需登录
- 降低用户使用门槛
- 提高课程曝光度

### 2. **📅 智能日历**
- 月视图显示有课程的日期
- 点击日期查看当天所有课程
- 支持筛选条件在日期查询中生效

### 3. **🎯 精准筛选**
- 5个维度的筛选条件
- 支持组合筛选
- 实时筛选结果

### 4. **📱 移动端优化**
- 响应式设计
- 快速加载
- 流畅的用户体验

## 🔄 **与其他功能的集成**

### **课程 + 收藏**
用户可以在浏览课程时收藏喜欢的门店：
1. 查看课程列表
2. 点击门店信息
3. 收藏门店（需要登录）

### **课程 + 预约**
用户看到心仪课程后可以直接预约：
1. 查看课程详情
2. 点击预约按钮（需要登录）
3. 选择会员卡支付

### **课程 + 分享**
用户可以分享课程给朋友：
1. 查看课程详情
2. 点击分享按钮（需要登录）
3. 生成分享链接和文案

## 📊 **数据流程**

```mermaid
graph TD
    A[用户访问课程页面] --> B[免登录查看课程列表]
    B --> C[选择筛选条件]
    C --> D[查看筛选结果]
    D --> E[点击日期查看当天课程]
    E --> F[查看课程详情]
    F --> G{用户是否登录?}
    G -->|是| H[可以预约/收藏/分享]
    G -->|否| I[引导用户登录]
    I --> J[登录后可以操作]
```

## 🎉 **总结**

课程功能的完善实现了需求中的所有要点：

- ✅ **免登录访问**：降低使用门槛
- ✅ **日历显示**：直观的时间视图
- ✅ **多维度筛选**：门店、教练、类型、级别、分类
- ✅ **门店收藏**：用户可以收藏喜欢的门店
- ✅ **点击日期显示排课**：完整的课程信息和报名入口

这为瑜伽馆的课程展示和用户体验提供了强大的支持！🚀
