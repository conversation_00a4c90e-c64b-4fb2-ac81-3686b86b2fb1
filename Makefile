# 变量定义
APP_NAME = starter
VERSION ?= $(shell git describe --tags --always)
BUILD_DIR = build

# 默认架构
ARCH ?= amd64

# 编译参数
LDFLAGS = -s -w \
	-X main.Version=$(VERSION) \
	-X main.BuildTime=$(shell date -u +%Y-%m-%d_%H:%M:%S)

# 默认目标
.PHONY: all
all: linux

# 创建构建目录
$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

# 构建 Linux 版本
.PHONY: linux
linux: $(BUILD_DIR)
	@echo "Building for Linux ($(ARCH))..."
	GOOS=linux GOARCH=$(ARCH) go build -ldflags="$(LDFLAGS)" \
		-o $(BUILD_DIR)/$(APP_NAME)-linux-$(ARCH)

# 构建 Windows 版本
.PHONY: windows
windows: $(BUILD_DIR)
	@echo "Building for Windows ($(ARCH))..."
	GOOS=windows GOARCH=$(ARCH) go build -ldflags="$(LDFLAGS)" \
		-o $(BUILD_DIR)/$(APP_NAME)-windows-$(ARCH).exe

# 构建 MacOS 版本
.PHONY: darwin
darwin: $(BUILD_DIR)
	@echo "Building for MacOS ($(ARCH))..."
	GOOS=darwin GOARCH=$(ARCH) go build -ldflags="$(LDFLAGS)" \
		-o $(BUILD_DIR)/$(APP_NAME)-darwin-$(ARCH)

# 构建所有平台 amd64 版本
.PHONY: all-arch
all-arch: linux windows darwin

# 构建所有平台 arm64 版本
.PHONY: all-arm64
all-arm64: ARCH=arm64
all-arm64: linux windows darwin

# 清理构建产物
.PHONY: clean
clean:
	rm -rf $(BUILD_DIR)

# 运行测试
.PHONY: test
test:
	go test -v ./...

# 运行程序
.PHONY: run
run:
	go run main.go

# 权限管理工具
.PHONY: sync analyze check-orphaned cleanup test-sync fix-admin fix-user add-user-perms tools-help

sync:
	@echo "🔄 同步数据库和 Casbin 策略..."
	cd scripts/tools && go run . sync

analyze:
	@echo "🔍 分析同步状态..."
	cd scripts/tools && go run . analyze

check-orphaned:
	@echo "🔍 检查孤立数据..."
	cd scripts/tools && go run . check-orphaned

cleanup:
	@echo "🧹 清理孤立数据..."
	cd scripts/tools && go run . cleanup

test-sync:
	@echo "🧪 全面功能测试..."
	cd scripts/tools && go run . test

fix-admin:
	@echo "🔧 修复管理员权限..."
	cd scripts/tools && go run . fix-admin

fix-user:
	@echo "🔧 修复用户权限..."
	cd scripts/tools && go run . fix-user

add-user-perms:
	@echo "🔧 添加缺失的用户权限..."
	cd scripts/tools && go run . add-user-perms

tools-help:
	@echo "🔧 权限管理工具详细说明:"
	@echo ""
	@echo "📊 状态检查工具:"
	@echo "  make sync         - 完整重建 Casbin 策略，确保数据库与 Casbin 完全同步"
	@echo "  make analyze      - 详细分析数据库与 Casbin 的同步状态，显示详细信息"
	@echo "  make check-orphaned - 检查数据库中的孤立数据（指向已删除记录的关联）"
	@echo ""
	@echo "🧹 数据清理工具:"
	@echo "  make cleanup      - 清理数据库中的孤立数据，确保数据完整性"
	@echo ""
	@echo "🧪 测试工具:"
	@echo "  make test-sync    - 全面测试同步功能，包括创建、更新、删除操作"
	@echo ""
	@echo "🔧 修复工具:"
	@echo "  make fix-admin    - 为 admin 角色分配所有权限"
	@echo "  make fix-user     - 为 admin 用户分配 admin 角色"
	@echo "  make add-user-perms - 添加缺失的用户相关权限"
	@echo ""
	@echo "💡 使用建议:"
	@echo "  1. 系统初始化后运行: make sync"
	@echo "  2. 发现权限问题时: make analyze → make check-orphaned → make cleanup → make sync"
	@echo "  3. 定期维护: make check-orphaned && make cleanup"
	@echo "  4. 功能测试: make test-sync"

# 帮助信息
.PHONY: help
help:
	@echo "Yogaga Project - 可用命令:"
	@echo ""
	@echo "🚀 项目构建:"
	@echo "  linux    - Build for Linux (ARCH=amd64/arm64)"
	@echo "  windows  - Build for Windows (ARCH=amd64/arm64)"
	@echo "  darwin   - Build for MacOS (ARCH=amd64/arm64)"
	@echo "  all-arch - Build for all platforms (amd64)"
	@echo "  all-arm64 - Build for all platforms (arm64)"
	@echo "  clean    - Clean build directory"
	@echo "  test     - Run tests"
	@echo "  run      - Run the application"
	@echo ""
	@echo "🔧 权限管理工具:"
	@echo "  sync         - 完整同步数据库和 Casbin 策略"
	@echo "  analyze      - 详细分析同步状态"
	@echo "  check-orphaned - 检查孤立数据"
	@echo "  cleanup      - 清理孤立数据"
	@echo "  test-sync    - 全面功能测试"
	@echo "  fix-admin    - 修复管理员权限"
	@echo "  fix-user     - 修复用户权限"
	@echo "  add-user-perms - 添加缺失的用户权限"
	@echo "  tools-help   - 显示工具详细帮助"
	@echo ""
	@echo "Examples:"
	@echo "  make linux ARCH=arm64   - Build Linux arm64 version"
	@echo "  make darwin ARCH=arm64  - Build MacOS arm64 version"
	@echo "  make all-arch          - Build all platforms in amd64"
	@echo "  make all-arm64         - Build all platforms in arm64"