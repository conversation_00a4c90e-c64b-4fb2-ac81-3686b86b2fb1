// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblShopRotate = "tbl_shop_rotate"

// TblShopRotate mapped from table <tbl_shop_rotate>
type TblShopRotate struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID     int32     `gorm:"column:shop_id;not null" json:"shop_id"`
	Title      string    `gorm:"column:title" json:"title"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Picture    string    `gorm:"column:picture" json:"picture"`
	PictureOri string    `gorm:"column:picture_ori" json:"picture_ori"`
	URL        string    `gorm:"column:url" json:"url"`
	Orderby    int32     `gorm:"column:orderby;not null" json:"orderby"`
	IsDelete   bool      `gorm:"column:is_delete;not null" json:"is_delete"`
}

// TableName TblShopRotate's table name
func (*TblShopRotate) TableName() string {
	return TableNameTblShopRotate
}
