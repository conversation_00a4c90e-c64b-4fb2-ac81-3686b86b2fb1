package configs

import (
	"time"
)

type DBDriver string

const (
	DriverMysql    DBDriver = "mysql"
	DriverSqlite   DBDriver = "sqlite3"
	DriverPostgres DBDriver = "postgres"
	DriverMssql    DBDriver = "mssql"
	DriverOracle   DBDriver = "oracle"
	DriverMongo    DBDriver = "mongo"
)

type Config struct {
	App      App
	Driver   DBDriver
	Database Database
	JwtAuth  JwtAuth
	Mongo    Mongo
	Redis    map[string]Redis
	Log      LogConfig
	Storage  Storage
	Casbin   Casbin
	WeChat   WeChat
}

// Config app config
type App struct {
	Port int
	Name string
}

// Config mysql config
type Database struct {
	UserName        string
	Password        string
	DBName          string
	Host            string
	Port            int
	TablePrefix     string
	Charset         string
	ParseTime       bool
	Loc             string
	ShowLog         bool
	MaxIdleConn     int
	MaxOpenConn     int
	ConnMaxLifeTime time.Duration
	SlowThreshold   time.Duration // 慢查询时长，默认500ms
}

// Config jwt config
type JwtAuth struct {
	AccessSecret  string `mapstructure:"AccessSecret"`
	AccessExpire  int    `mapstructure:"AccessExpire"`
	RefreshSecret string `mapstructure:"RefreshSecret"`
	RefreshExpire int    `mapstructure:"RefreshExpire"`
}

// Config MongoDB config
type Mongo struct {
	URI      string
	User     string
	Password string
	DB       string
}

// Redis配置结构
type Redis struct {
	Addr         string
	Password     string
	DB           int
	MinIdleConn  int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	PoolSize     int
	PoolTimeout  time.Duration
	EnableTrace  bool
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string        // 日志级别: debug, info, warn, error
	Output     []string      // 输出方式: console, file
	FileConfig FileLogConfig // 文件输出配置
}

// FileLogConfig 文件日志配置
type FileLogConfig struct {
	Path       string // 日志文件路径
	MaxSize    int    // 每个日志文件的最大大小（MB）
	MaxAge     int    // 日志文件保留天数
	MaxBackups int    // 保留的旧日志文件最大数量
	Compress   bool   // 是否压缩旧日志文件
}

type Storage struct {
	Type            string
	Endpoint        string
	AccessKeyID     string
	AccessKeySecret string
	BucketName      string
	UseSSL          bool
}

type Casbin struct {
	Enabled          bool
	ModelPath        string
	PolicyTable      string
	AutoLoadInterval int
	EnableLog        bool
	EnableAutoSave   bool
}

// WeChat 微信小程序配置
type WeChat struct {
	AppID                            string `mapstructure:"AppID"`                            // 小程序 AppID
	AppSecret                        string `mapstructure:"AppSecret"`                        // 小程序 AppSecret
	CourseReminderTemplateID         string `mapstructure:"CourseReminderTemplateID"`         // 开课提醒模板ID
	CancelNoticeTemplateID           string `mapstructure:"CancelNoticeTemplateID"`           // 取消通知模板ID
	QueueNotificationTemplateID      string `mapstructure:"QueueNotificationTemplateID"`      // 排队通知模板ID
	BookingConfirmationTemplateID    string `mapstructure:"BookingConfirmationTemplateID"`    // 预约成功通知模板ID
	CourseCancelTemplateID           string `mapstructure:"CourseCancelTemplateID"`           // 课程取消通知模板ID
	CardExpiryReminderTemplateID     string `mapstructure:"CardExpiryReminderTemplateID"`     // 会员卡到期提醒模板ID
	ValidityDeductReminderTemplateID string `mapstructure:"ValidityDeductReminderTemplateID"` // 有效期扣除提醒模板ID
}
