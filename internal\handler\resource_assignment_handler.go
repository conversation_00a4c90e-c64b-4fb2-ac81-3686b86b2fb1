package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// ResourceAssignmentHandler 资源分配处理器
type ResourceAssignmentHandler struct {
	db *gorm.DB
}

// NewResourceAssignmentHandler 创建资源分配处理器实例
func NewResourceAssignmentHandler(db *gorm.DB) *ResourceAssignmentHandler {
	return &ResourceAssignmentHandler{
		db: db,
	}
}

// AssignMembersToSales 分配会员给销售
func (h *ResourceAssignmentHandler) AssignMembersToSales(c *gin.Context) {
	var req dto.AssignMembersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定分配会员参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 获取当前用户ID
	currentUserID, exists := c.Get("user_id")
	if !exists {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 验证销售员工是否存在且为销售角色
	var salesStaff model.User
	err := h.db.Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("users.id = ? AND users.platform = ? AND users.is_active = ? AND (roles.name LIKE '%sales%' OR roles.name LIKE '%销售%')",
			req.SalesStaffID, enum.PlatformAdmin, true).
		First(&salesStaff).Error

	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "销售员工不存在或状态异常"))
		return
	}

	// 验证会员是否存在
	var members []model.User
	err = h.db.Where("id IN ? AND platform = ?", req.MemberIDs, enum.PlatformWechat).Find(&members).Error
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员失败"))
		return
	}

	if len(members) != len(req.MemberIDs) {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "部分会员不存在"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量创建资源分配记录
	var assignments []model.ResourceAssignment
	for _, memberID := range req.MemberIDs {
		// 检查是否已经分配
		var existingAssignment model.ResourceAssignment
		err := tx.Where("staff_id = ? AND resource_type = 'member' AND resource_id = ? AND status = 1",
			req.SalesStaffID, memberID).First(&existingAssignment).Error

		if err == nil {
			// 已经分配，跳过
			continue
		}

		assignment := model.ResourceAssignment{
			StaffID:      req.SalesStaffID,
			ResourceType: "member",
			ResourceID:   memberID,
			AssignedBy:   currentUserID.(string),
			AssignedAt:   time.Now(),
			Remark:       req.Remark,
			Status:       1,
		}
		assignments = append(assignments, assignment)
	}

	if len(assignments) > 0 {
		if err := tx.Create(&assignments).Error; err != nil {
			tx.Rollback()
			log.Error("批量分配会员失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "分配会员失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交分配事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "分配失败"))
		return
	}

	log.Info("分配会员成功", "sales_staff_id", req.SalesStaffID, "member_count", len(assignments))
	response := dto.MessageResponse{Message: "分配成功"}
	code.AutoResponse(c, response, nil)
}

// GetSalesMembers 获取销售负责的会员列表
func (h *ResourceAssignmentHandler) GetSalesMembers(c *gin.Context) {
	salesID := c.Param("sales_id")
	// 验证UUID格式
	if len(salesID) != 36 {
		log.Error("销售ID格式错误", "id", salesID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	search := c.Query("search")

	// 构建查询
	query := h.db.Model(&model.User{}).
		Joins("JOIN resource_assignments ON users.id = resource_assignments.resource_id").
		Where("resource_assignments.staff_id = ? AND resource_assignments.resource_type = 'member' AND resource_assignments.status = 1",
			salesID)

	// 搜索条件
	if search != "" {
		query = query.Where("users.nick_name LIKE ? OR users.phone LIKE ?",
			"%"+search+"%", "%"+search+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 查询数据
	var members []model.User
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("resource_assignments.assigned_at DESC").
		Find(&members).Error

	if err != nil {
		log.Error("查询销售会员列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询销售会员列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     members,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// TransferMember 转移会员给其他销售
func (h *ResourceAssignmentHandler) TransferMember(c *gin.Context) {
	var req dto.TransferMemberRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定转移会员参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 获取当前用户ID
	currentUserID, exists := c.Get("user_id")
	if !exists {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 验证目标销售员工
	var targetSalesStaff model.User
	err := h.db.Joins("JOIN user_roles ON users.id = user_roles.user_id").
		Joins("JOIN roles ON user_roles.role_id = roles.id").
		Where("users.id = ? AND users.platform = ? AND users.is_active = ? AND (roles.name LIKE '%sales%' OR roles.name LIKE '%销售%')",
			req.TargetSalesStaffID, enum.PlatformAdmin, true).
		First(&targetSalesStaff).Error

	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "目标销售员工不存在或状态异常"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 将原分配设为无效
	err = tx.Model(&model.ResourceAssignment{}).
		Where("staff_id = ? AND resource_type = 'member' AND resource_id = ? AND status = 1",
			req.FromSalesStaffID, req.MemberID).
		Update("status", 0).Error

	if err != nil {
		tx.Rollback()
		log.Error("取消原分配失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "转移失败"))
		return
	}

	// 创建新分配
	newAssignment := model.ResourceAssignment{
		StaffID:      req.TargetSalesStaffID,
		ResourceType: "member",
		ResourceID:   req.MemberID,
		AssignedBy:   currentUserID.(string),
		AssignedAt:   time.Now(),
		Remark:       req.Remark,
		Status:       1,
	}

	if err := tx.Create(&newAssignment).Error; err != nil {
		tx.Rollback()
		log.Error("创建新分配失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "转移失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交转移事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "转移失败"))
		return
	}

	log.Info("转移会员成功", "member_id", req.MemberID, "from", req.FromSalesStaffID, "to", req.TargetSalesStaffID)
	response := dto.MessageResponse{Message: "转移成功"}
	code.AutoResponse(c, response, nil)
}

// GetUnassignedMembers 获取未分配的会员列表
func (h *ResourceAssignmentHandler) GetUnassignedMembers(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()
	search := c.Query("search")

	// 构建查询 - 查询未分配的小程序用户
	query := h.db.Model(&model.User{}).
		Where("platform = ? AND id NOT IN (SELECT resource_id FROM resource_assignments WHERE resource_type = 'member' AND status = 1)",
			enum.PlatformWechat)

	// 搜索条件
	if search != "" {
		query = query.Where("nick_name LIKE ? OR phone LIKE ?",
			"%"+search+"%", "%"+search+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 查询数据
	var members []model.User
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&members).Error

	if err != nil {
		log.Error("查询未分配会员列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询未分配会员列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     members,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// RemoveMemberAssignment 移除会员分配
func (h *ResourceAssignmentHandler) RemoveMemberAssignment(c *gin.Context) {
	var req dto.RemoveMemberAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定移除分配参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 将分配设为无效
	err := h.db.Model(&model.ResourceAssignment{}).
		Where("staff_id = ? AND resource_type = 'member' AND resource_id = ? AND status = 1",
			req.SalesStaffID, req.MemberID).
		Update("status", 0).Error

	if err != nil {
		log.Error("移除会员分配失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "移除分配失败"))
		return
	}

	log.Info("移除会员分配成功", "sales_staff_id", req.SalesStaffID, "member_id", req.MemberID)
	response := dto.MessageResponse{Message: "移除成功"}
	code.AutoResponse(c, response, nil)
}
