package main

import (
	"fmt"
	"log"
	"yogaga/configs"
	"yogaga/global"
	"yogaga/internal/database"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔍 检查 users 表结构...")

	// 加载配置
	global.Config = configs.LoadConfig()

	// 连接数据库
	db := database.NewDB(*global.Config)

	// 检查 users 表是否存在
	if !db.Migrator().HasTable("users") {
		log.Fatal("❌ users 表不存在")
	}

	fmt.Println("✅ users 表存在")

	// 检查 wechat_openid 字段
	if db.Migrator().HasColumn("users", "wechat_openid") {
		fmt.Println("✅ wechat_openid 字段存在")
	} else {
		fmt.Println("❌ wechat_openid 字段不存在")
	}

	// 获取表的所有列信息
	fmt.Println("\n📋 users 表的所有字段:")
	rows, err := db.Raw("SELECT column_name, data_type, is_nullable FROM information_schema.columns WHERE table_name = 'users' AND table_schema = 'public' ORDER BY ordinal_position").Rows()
	if err != nil {
		log.Fatal("查询字段信息失败:", err)
	}
	defer rows.Close()

	for rows.Next() {
		var columnName, dataType, isNullable string
		if err := rows.Scan(&columnName, &dataType, &isNullable); err != nil {
			log.Fatal("扫描字段信息失败:", err)
		}
		fmt.Printf("   %s (%s) - nullable: %s\n", columnName, dataType, isNullable)
	}

	// 检查是否有微信相关字段
	fmt.Println("\n🔍 检查微信相关字段:")
	wechatFields := []string{"wechat_openid", "wechat_unionid", "nick_name"}
	for _, field := range wechatFields {
		if db.Migrator().HasColumn("users", field) {
			fmt.Printf("   ✅ %s 存在\n", field)
		} else {
			fmt.Printf("   ❌ %s 不存在\n", field)
		}
	}

	// 尝试查询一条记录看看实际的字段
	fmt.Println("\n📊 查询 users 表记录数:")
	var count int64
	db.Table("users").Count(&count)
	fmt.Printf("   总记录数: %d\n", count)

	if count > 0 {
		fmt.Println("\n📝 查看第一条记录的字段:")
		var result map[string]interface{}
		err := db.Table("users").Select("*").First(&result).Error
		if err != nil && err != gorm.ErrRecordNotFound {
			log.Printf("查询记录失败: %v", err)
		} else if err == nil {
			for key, value := range result {
				fmt.Printf("   %s: %v\n", key, value)
			}
		}
	}

	fmt.Println("\n🎉 检查完成!")
}
