package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"yogaga/internal/bootstrap"
	"yogaga/internal/config"
	"yogaga/internal/database"
	"yogaga/internal/model"

	"github.com/casbin/casbin/v2"
	"gorm.io/gorm"
)

// 使用bootstrap包中的数据结构

func main() {
	fmt.Println("🔄 开始恢复角色权限分配...")

	// 获取项目根目录
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("❌ 获取工作目录失败: %v", err)
	}

	// 查找config.yaml文件
	configPath := filepath.Join(workDir, "config.yaml")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		// 如果在当前目录找不到，尝试上级目录
		configPath = filepath.Join(workDir, "..", "..", "..", "config.yaml")
		if _, err := os.Stat(configPath); os.IsNotExist(err) {
			log.Fatalf("❌ 找不到config.yaml文件，请确保在项目根目录运行")
		}
	}

	fmt.Printf("📁 使用配置文件: %s\n", configPath)

	// 加载配置
	cfg, err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.InitDB(cfg)
	if err != nil {
		log.Fatalf("❌ 数据库连接失败: %v", err)
	}

	// 初始化Casbin
	enforcer, err := bootstrap.InitCasbin(cfg, db)
	if err != nil {
		log.Fatalf("❌ 初始化Casbin失败: %v", err)
	}

	// 执行恢复
	if err := restoreRolePermissions(db, enforcer); err != nil {
		log.Fatalf("❌ 恢复角色权限失败: %v", err)
	}

	fmt.Println("✅ 角色权限分配恢复完成！")
}

func restoreRolePermissions(db *gorm.DB, enforcer *casbin.Enforcer) error {
	fmt.Println("🔄 开始恢复角色权限分配...")

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 清理现有的角色权限关联
	fmt.Println("🧹 清理现有角色权限关联...")
	if err := tx.Exec("DELETE FROM role_permissions").Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("清理角色权限关联失败: %v", err)
	}

	// 清理Casbin中的权限策略（保留角色继承）
	fmt.Println("🧹 清理Casbin权限策略...")
	if _, err := enforcer.RemoveFilteredPolicy(0, "", "", ""); err != nil {
		tx.Rollback()
		return fmt.Errorf("清理Casbin策略失败: %v", err)
	}

	totalAssignments := 0
	totalPolicies := 0

	// 获取角色数据
	autoRoles := bootstrap.GetAutoRoles()
	autoPermissions := bootstrap.GetAutoPermissions()

	// 重新分配角色权限
	for _, roleInfo := range autoRoles {
		fmt.Printf("🔄 处理角色: %s (ID: %d)\n", roleInfo.Name, roleInfo.ID)

		// 查找角色
		var role model.Role
		if err := tx.First(&role, roleInfo.ID).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("查找角色 %s 失败: %v", roleInfo.Name, err)
		}

		// 收集权限
		var permissions []model.Permission
		for _, permID := range roleInfo.PermissionIDs {
			var permission model.Permission
			if err := tx.First(&permission, permID).Error; err != nil {
				fmt.Printf("⚠️ 权限 %d 不存在，跳过\n", permID)
				continue
			}
			permissions = append(permissions, permission)
		}

		// 关联权限到角色
		if err := tx.Model(&role).Association("Permissions").Replace(permissions); err != nil {
			tx.Rollback()
			return fmt.Errorf("关联权限到角色 %s 失败: %v", roleInfo.Name, err)
		}

		fmt.Printf("✅ 角色 %s 分配了 %d 个权限\n", roleInfo.Name, len(permissions))
		totalAssignments += len(permissions)

		// 创建Casbin策略
		for _, permission := range permissions {
			// 从权限初始化数据中查找对应的Method
			method := "GET" // 默认方法
			for _, perm := range autoPermissions {
				if perm.ID == permission.ID {
					method = perm.Method
					break
				}
			}

			if _, err := enforcer.AddPolicy(roleInfo.Name, permission.Key, method); err != nil {
				tx.Rollback()
				return fmt.Errorf("添加Casbin策略失败: %v", err)
			}
			totalPolicies++
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	// 保存Casbin策略
	if err := enforcer.SavePolicy(); err != nil {
		return fmt.Errorf("保存Casbin策略失败: %v", err)
	}

	fmt.Printf("✅ 恢复完成: %d 个角色权限分配, %d 个Casbin策略\n", totalAssignments, totalPolicies)
	return nil
}
