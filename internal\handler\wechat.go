package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"
	"yogaga/pkg/jwtx"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// WeChatHandler 微信小程序相关处理器
type WeChatHandler struct {
	db            *gorm.DB
	jwtSvc        *jwtx.Service
	wechatService *service.WeChatService
}

// NewWeChatHandler 创建微信处理器实例
func NewWeChatHandler(db *gorm.DB, jwtSvc *jwtx.Service, wechatService *service.WeChatService) *WeChatHandler {
	return &WeChatHandler{
		db:            db,
		jwtSvc:        jwtSvc,
		wechatService: wechatService,
	}
}

// Login 微信小程序登录
func (h *WeChatHandler) Login(c *gin.Context) {
	var req dto.WeChatLoginRequest
	if err := c.Should<PERSON>(&req); err != nil {
		log.Error("绑定微信登录请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到微信小程序登录请求", "code", req.Code)

	// 1. 调用微信接口获取 openid 和 session_key
	sessionResp, err := h.wechatService.Code2Session(req.Code)
	if err != nil {
		log.Error("微信登录失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "微信登录失败"))
		return
	}

	// 2. 查找或创建用户
	var user model.User
	err = h.db.Where("wechat_openid = ?", sessionResp.OpenID).First(&user).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 用户不存在，创建新用户
			user = model.User{
				Username:       sessionResp.OpenID, // 使用 OpenID 作为用户名
				PlatformAccess: enum.PlatformAccessWechat,
				WeChatOpenID:   &sessionResp.OpenID,
				WeChatUnionID:  &sessionResp.UnionID,
				IsActive:       true,
			}

			// 为新用户分配默认角色（如果存在）
			var defaultRole model.Role
			if err := h.db.Where("name = ?", "user").First(&defaultRole).Error; err == nil {
				user.Roles = []model.Role{defaultRole}
			}

			if err := h.db.Create(&user).Error; err != nil {
				log.Error("创建微信用户失败", "openid", sessionResp.OpenID, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建用户失败"))
				return
			}

			log.Info("创建新微信用户成功", "user_id", user.ID, "openid", sessionResp.OpenID)
		} else {
			log.Error("查询微信用户失败", "openid", sessionResp.OpenID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询用户失败"))
			return
		}
	}

	// 3. 检查用户是否激活
	if !user.IsActive {
		log.Warn("微信用户账户已禁用", "user_id", user.ID, "openid", sessionResp.OpenID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserDisabled, "用户账户已禁用"))
		return
	}

	// 4. 预加载用户角色信息
	if err := h.db.Preload("Roles").First(&user, user.ID).Error; err != nil {
		log.Error("加载用户角色失败", "user_id", user.ID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "加载用户信息失败"))
		return
	}

	// 5. 生成 JWT 令牌
	accessToken, refreshToken, err := h.jwtSvc.GenerateTokens(&user)
	if err != nil {
		log.Error("生成令牌失败", "user_id", user.ID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "登录失败"))
		return
	}

	// 6. 构建角色名称列表
	roleNames := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roleNames[i] = role.Name
	}

	// 7. 构建用户信息
	platforms := user.GetAccessiblePlatforms()
	platformStrs := make([]string, len(platforms))
	for i, p := range platforms {
		platformStrs[i] = string(p)
	}

	userInfo := dto.UserInfo{
		ID:             user.ID.String(),
		Username:       user.Username,
		PlatformAccess: int(user.PlatformAccess),
		Platforms:      platformStrs,
		Roles:          roleNames,
	}

	// 8. 构建登录响应
	response := dto.LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		UserInfo:     &userInfo,
	}

	log.Info("微信用户登录成功", "user_id", user.ID, "openid", sessionResp.OpenID, "roles", roleNames)
	code.AutoResponse(c, response, nil)
}

// UpdateUserInfo 更新微信用户信息
func (h *WeChatHandler) UpdateUserInfo(c *gin.Context) {
	// 从 JWT 中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.WeChatUserInfo
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定用户信息更新请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查找用户
	var user model.User
	if err := h.db.First(&user, userID).Error; err != nil {
		log.Error("查找用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNotFound, "用户不存在"))
		return
	}

	// 更新用户信息
	updates := map[string]interface{}{
		"nick_name":  req.NickName,
		"avatar_url": req.Avatar,
		"gender":     req.Gender,
		"country":    req.Country,
		"province":   req.Province,
		"city":       req.City,
		"language":   req.Language,
	}

	if err := h.db.Model(&user).Updates(updates).Error; err != nil {
		log.Error("更新用户信息失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新用户信息失败"))
		return
	}

	log.Info("更新微信用户信息成功", "user_id", userID, "nick_name", req.NickName)
	response := dto.MessageResponse{Message: "用户信息更新成功"}
	code.AutoResponse(c, response, nil)
}

// GetUserInfo 获取当前用户信息
func (h *WeChatHandler) GetUserInfo(c *gin.Context) {
	// 从 JWT 中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查找用户
	var user model.User
	if err := h.db.Preload("Roles").First(&user, userID).Error; err != nil {
		log.Error("查找用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNotFound, "用户不存在"))
		return
	}

	// 构建角色名称列表
	roleNames := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roleNames[i] = role.Name
	}

	// 构建用户信息响应
	platforms := user.GetAccessiblePlatforms()
	platformStrs := make([]string, len(platforms))
	for i, p := range platforms {
		platformStrs[i] = string(p)
	}

	userInfo := dto.UserInfo{
		ID:             user.ID.String(),
		Username:       user.Username,
		PlatformAccess: int(user.PlatformAccess),
		Platforms:      platformStrs,
		Roles:          roleNames,
	}

	code.AutoResponse(c, userInfo, nil)
}
