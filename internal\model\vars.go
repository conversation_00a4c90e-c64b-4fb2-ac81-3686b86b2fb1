package model

import (
	"time"

	"yogaga/internal/database"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

var db = database.DB

var ErrNotFound = gorm.ErrRecordNotFound

type BaseModel struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

type UUIDModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:char(36);primary_key"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}

func (u *UUIDModel) BeforeCreate(tx *gorm.DB) (err error) {
	if u.ID == uuid.Nil {
		u.ID = uuid.New()
	}
	return
}

// MigrationStatus 数据库迁移状态表
type MigrationStatus struct {
	BaseModel
	Name        string     `json:"name" gorm:"type:varchar(100);uniqueIndex;not null;comment:迁移名称"`
	Version     string     `json:"version" gorm:"type:varchar(50);not null;comment:迁移版本"`
	Status      string     `json:"status" gorm:"type:varchar(20);not null;default:'pending';comment:迁移状态:pending,running,completed,failed"`
	Description string     `json:"description" gorm:"type:text;comment:迁移描述"`
	StartedAt   *time.Time `json:"started_at" gorm:"comment:开始时间"`
	CompletedAt *time.Time `json:"completed_at" gorm:"comment:完成时间"`
	ErrorMsg    string     `json:"error_msg" gorm:"type:text;comment:错误信息"`
}
