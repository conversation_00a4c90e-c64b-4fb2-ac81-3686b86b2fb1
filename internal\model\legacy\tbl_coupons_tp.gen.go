// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblCouponsTp = "tbl_coupons_tp"

// TblCouponsTp mapped from table <tbl_coupons_tp>
type TblCouponsTp struct {
	ID           int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID       int32     `gorm:"column:shop_id" json:"shop_id"`
	ShopIds      string    `gorm:"column:shop_ids" json:"shop_ids"`
	Title        string    `gorm:"column:title" json:"title"`
	Tp           bool      `gorm:"column:tp;default:1;comment:1-储值卡 2-次数卡 3-期限卡" json:"tp"` // 1-储值卡 2-次数卡 3-期限卡
	Note         string    `gorm:"column:note" json:"note"`
	CreateTime   time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete     int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	Terms        string    `gorm:"column:terms" json:"terms"`
	Picture      string    `gorm:"column:picture" json:"picture"`
	PictureOri   string    `gorm:"column:picture_ori" json:"picture_ori"`
	AmountOver   float64   `gorm:"column:amount_over;not null;default:0.00" json:"amount_over"`
	AmountReduce float64   `gorm:"column:amount_reduce;not null;default:0.00" json:"amount_reduce"`
}

// TableName TblCouponsTp's table name
func (*TblCouponsTp) TableName() string {
	return TableNameTblCouponsTp
}
