package bootstrap

import (
	"fmt"
	"log"

	"yogaga/internal/model"

	"gorm.io/gorm"
)

// MigrateFlexibleSystem 迁移灵活扣减系统
func MigrateFlexibleSystem(db *gorm.DB) error {
	log.Println("🔄 开始迁移灵活扣减系统...")

	// 1. 创建课程类型配置表
	if err := db.AutoMigrate(&model.CourseTypeConfig{}); err != nil {
		return fmt.Errorf("创建课程类型配置表失败: %w", err)
	}
	log.Println("✅ 课程类型配置表创建成功")

	// 2. 创建灵活扣减规则表
	if err := db.AutoMigrate(&model.FlexibleDeductionRule{}); err != nil {
		return fmt.Errorf("创建灵活扣减规则表失败: %w", err)
	}
	log.Println("✅ 灵活扣减规则表创建成功")

	// 3. 创建会员卡请假记录表
	if err := db.AutoMigrate(&model.MembershipCardLeave{}); err != nil {
		return fmt.Errorf("创建会员卡请假记录表失败: %w", err)
	}
	log.Println("✅ 会员卡请假记录表创建成功")

	// 4. 初始化基础数据
	if err := initFlexibleSystemData(db); err != nil {
		return fmt.Errorf("初始化灵活系统数据失败: %w", err)
	}

	log.Println("🎉 灵活扣减系统迁移完成！")
	return nil
}

// initFlexibleSystemData 初始化灵活系统基础数据
func initFlexibleSystemData(db *gorm.DB) error {
	log.Println("🔄 开始初始化灵活系统基础数据...")

	// 1. 初始化课程类型配置
	courseTypes := []model.CourseTypeConfig{
		{
			TypeCode:    "yoga_group",
			TypeName:    "瑜伽团课",
			Category:    "group",
			Description: "适合多人参与的瑜伽团课",
			SortOrder:   1,
			Status:      1,
		},
		{
			TypeCode:    "pilates_small",
			TypeName:    "普拉提小班课",
			Category:    "small",
			Description: "普拉提精品小班课程",
			SortOrder:   2,
			Status:      1,
		},
		{
			TypeCode:    "advanced_small",
			TypeName:    "提升小班课",
			Category:    "small",
			Description: "进阶提升小班课程",
			SortOrder:   3,
			Status:      1,
		},
		{
			TypeCode:    "special_small",
			TypeName:    "特色小班课",
			Category:    "small",
			Description: "特色主题小班课程",
			SortOrder:   4,
			Status:      1,
		},
		{
			TypeCode:    "dance_60",
			TypeName:    "舞蹈课60分钟",
			Category:    "group",
			Description: "60分钟舞蹈课程",
			SortOrder:   5,
			Status:      1,
		},
		{
			TypeCode:    "dance_90",
			TypeName:    "舞蹈课90分钟",
			Category:    "group",
			Description: "90分钟舞蹈课程",
			SortOrder:   6,
			Status:      1,
		},
		{
			TypeCode:    "private_std",
			TypeName:    "标准私教1V1",
			Category:    "private",
			Description: "标准一对一私教课程",
			SortOrder:   7,
			Status:      1,
		},
		{
			TypeCode:    "private_star_1v1",
			TypeName:    "明星私教1V1",
			Category:    "private",
			Description: "明星教练一对一私教课程",
			SortOrder:   8,
			Status:      1,
		},
		{
			TypeCode:    "private_star_1v2",
			TypeName:    "明星私教1V2",
			Category:    "private",
			Description: "明星教练一对二私教课程",
			SortOrder:   9,
			Status:      1,
		},
	}

	for _, courseType := range courseTypes {
		var existing model.CourseTypeConfig
		if err := db.Where("type_code = ?", courseType.TypeCode).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&courseType).Error; err != nil {
					return fmt.Errorf("创建课程类型配置失败 %s: %w", courseType.TypeCode, err)
				}
				log.Printf("✅ 创建课程类型配置: %s", courseType.TypeName)
			} else {
				return fmt.Errorf("查询课程类型配置失败 %s: %w", courseType.TypeCode, err)
			}
		}
	}

	// 2. 初始化示例扣减规则（基于你提供的表格）
	if err := initSampleDeductionRules(db); err != nil {
		return fmt.Errorf("初始化示例扣减规则失败: %w", err)
	}

	log.Println("✅ 灵活系统基础数据初始化完成")
	return nil
}

// initSampleDeductionRules 初始化示例扣减规则
func initSampleDeductionRules(db *gorm.DB) error {
	log.Println("🔄 开始初始化示例扣减规则...")

	// 示例：团课通卡的扣减规则
	sampleRules := []model.FlexibleDeductionRule{
		// 团课通卡规则
		{
			CardTypeID:         1, // 假设团课通卡ID为1
			CourseTypeCode:     "yoga_group",
			DeductionType:      "times",
			DeductionTimes:     1,
			PerPersonDeduction: true,
			Priority:           100,
			RuleName:           "团课通卡-瑜伽团课",
			Description:        "团课通卡预约瑜伽团课扣1次",
			Status:             1,
		},
		{
			CardTypeID:         1,
			CourseTypeCode:     "pilates_small",
			DeductionType:      "times",
			DeductionTimes:     2,
			PerPersonDeduction: true,
			Priority:           100,
			RuleName:           "团课通卡-普拉提小班课",
			Description:        "团课通卡预约普拉提小班课扣2次",
			Status:             1,
		},
		{
			CardTypeID:         1,
			CourseTypeCode:     "advanced_small",
			DeductionType:      "times",
			DeductionTimes:     3,
			PerPersonDeduction: true,
			Priority:           100,
			RuleName:           "团课通卡-提升小班课",
			Description:        "团课通卡预约提升小班课扣3次",
			Status:             1,
		},
		// 储值卡规则
		{
			CardTypeID:         10, // 假设储值卡ID为10
			CourseTypeCode:     "yoga_group",
			DeductionType:      "amount",
			DeductionAmount:    10800, // 108元
			PerPersonDeduction: true,
			Priority:           50,
			RuleName:           "储值卡-瑜伽团课",
			Description:        "储值卡预约瑜伽团课扣108元",
			Status:             1,
		},
		{
			CardTypeID:         10,
			CourseTypeCode:     "pilates_small",
			DeductionType:      "amount",
			DeductionAmount:    21800, // 218元
			PerPersonDeduction: true,
			Priority:           50,
			RuleName:           "储值卡-普拉提小班课",
			Description:        "储值卡预约普拉提小班课扣218元",
			Status:             1,
		},
		// 期限卡规则
		{
			CardTypeID:     6, // 假设瑜伽期限卡ID为6
			CourseTypeCode: "yoga_group",
			DeductionType:  "period",
			DailyLimit:     2,
			Priority:       80,
			RuleName:       "瑜伽期限卡-瑜伽团课",
			Description:    "瑜伽期限卡每日限约2节瑜伽团课",
			Status:         1,
		},
	}

	for _, rule := range sampleRules {
		var existing model.FlexibleDeductionRule
		if err := db.Where("card_type_id = ? AND course_type_code = ? AND rule_name = ?",
			rule.CardTypeID, rule.CourseTypeCode, rule.RuleName).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&rule).Error; err != nil {
					log.Printf("⚠️  创建示例扣减规则失败 %s: %v", rule.RuleName, err)
					// 不返回错误，继续创建其他规则
					continue
				}
				log.Printf("✅ 创建示例扣减规则: %s", rule.RuleName)
			}
		}
	}

	log.Println("✅ 示例扣减规则初始化完成")
	return nil
}
