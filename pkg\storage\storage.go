package storage

import (
	"context"
	"errors"
	"io"
	"net/url"
	"time"
	"yogaga/configs"

	"github.com/minio/minio-go/v7"
)

// Storage 文件存储接口，提供统一的文件操作抽象
type Storage interface {
	FileOperator
	PresignedURLProvider
}

type PresignedURLProvider interface {
	GetPresignedUploadURL(ctx context.Context, objectName string, expires time.Duration) (*url.URL, error)
	GetPresignedDownloadURL(ctx context.Context, objectName string, expires time.Duration, reqParams url.Values) (*url.URL, error)
}

// 定义一个接口实现 delete, exists, list
type FileOperator interface {
	Delete(key string) error
	Exists(key string) (bool, error)
	List(prefix string, limit int) ([]FileInfo, error)
	Upload(key string, file io.Reader) (minio.UploadInfo, error)
	UploadToSpecificBucket(bucketName, key string, file io.Reader) (minio.UploadInfo, error)
	GetBucketName(isPublic bool) string
}

// FileInfo 文件信息
type FileInfo struct {
	Key          string    // 文件键名
	Size         int64     // 文件大小
	LastModified time.Time // 最后修改时间
	ETag         string    // 文件ETag
	ContentType  string    // 内容类型
}

// BucketManager 存储桶管理接口（可选扩展）
type BucketManager interface {
	// CreateBucket 创建存储桶
	CreateBucket(bucketName string) error

	// DeleteBucket 删除存储桶
	DeleteBucket(bucketName string) error

	// ListBuckets 列出所有存储桶
	ListBuckets() ([]string, error)
}

// MetadataProvider 元数据提供者接口（可选扩展）
type MetadataProvider interface {
	// GetMetadata 获取文件元数据
	GetMetadata(key string) (map[string]string, error)

	// SetMetadata 设置文件元数据
	SetMetadata(key string, metadata map[string]string) error
}

func NewStorage(config *configs.Storage) (Storage, error) {
	switch config.Type {
	case "minio":
		return NewMinioClient(config.Endpoint, config.AccessKeyID, config.AccessKeySecret, config.BucketName, &MinioClientOptions{
			UseSSL: config.UseSSL,
		})
	default:
		return nil, errors.New("unsupported storage type")
	}
}
