package model

import (
	"time"
	"yogaga/internal/enum"

	"github.com/lib/pq"
)

// Course 课程模型
type Course struct {
	BaseModel
	Name        string            `json:"name" gorm:"type:varchar(100);not null;comment:课程名称"`
	Description string            `json:"description" gorm:"type:text;comment:课程描述"`
	StartTime   time.Time         `json:"start_time" gorm:"not null;comment:课程开始时间"`
	EndTime     time.Time         `json:"end_time" gorm:"not null;comment:课程结束时间"`
	Duration    int               `json:"duration" gorm:"comment:课程时长(分钟)"`
	Capacity    int               `json:"capacity" gorm:"default:10;comment:课程容量"`
	EnrollCount int               `json:"enroll_count" gorm:"default:0;comment:已报名人数"`
	Price       float64           `json:"price" gorm:"type:decimal(10,2);comment:课程价格"`
	Level       int               `json:"level" gorm:"type:smallint;default:1;comment:课程级别 1:初级 2:中级 3:高级"`
	CategoryID  uint              `json:"category_id" gorm:"default:0;comment:课程分类ID"`
	Status      enum.CourseStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:正常 0:取消"`
	// 多教练支持（使用PostgreSQL数组）
	CoachIDs pq.StringArray `json:"coach_ids" gorm:"type:text[];comment:教练ID数组，支持多教练"`

	StoreID     uint            `json:"store_id" gorm:"not null;comment:门店ID"`
	Store       Store           `json:"store" gorm:"foreignKey:StoreID"`
	ClassRoomID *uint           `json:"classroom_id" gorm:"column:classroom_id;comment:教室ID，私教可为空"`
	ClassRoom   *ClassRoom      `json:"classroom,omitempty" gorm:"foreignKey:ClassRoomID"`
	CategoryRef *CourseCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
	Type        enum.CourseType `json:"type" gorm:"type:smallint;default:1;comment:课程类型 1:团课 2:私教"`
}

// GetAllCoachIDs 获取所有教练ID
func (c *Course) GetAllCoachIDs() []string {
	if c == nil {
		return []string{}
	}

	// 直接返回多教练数组
	return []string(c.CoachIDs)
}

// GetPrimaryCoachID 获取主教练ID（第一个教练）
func (c *Course) GetPrimaryCoachID() string {
	if c == nil || len(c.CoachIDs) == 0 {
		return ""
	}
	return c.CoachIDs[0]
}

// HasCoach 检查是否包含指定教练
func (c *Course) HasCoach(coachID string) bool {
	if c == nil {
		return false
	}
	// 使用简单的循环检查，避免导入slices包
	for _, id := range c.CoachIDs {
		if id == coachID {
			return true
		}
	}
	return false
}

// SetCoachIDs 设置教练ID数组
func (c *Course) SetCoachIDs(coachIDs []string) {
	if c == nil {
		return
	}

	// 设置多教练数组
	c.CoachIDs = coachIDs
}
