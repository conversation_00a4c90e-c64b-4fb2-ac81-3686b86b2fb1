package handler

import (
	"fmt"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"
	"yogaga/pkg/utils"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MenuHandler struct {
	db          *gorm.DB
	menuService *service.MenuService
}

func NewMenuHandler(db *gorm.DB, enforcer *casbin.Enforcer) *MenuHandler {
	return &MenuHandler{
		db:          db,
		menuService: service.NewMenuService(enforcer),
	}
}

// Create a new menu item
func (h *MenuHandler) Create(c *gin.Context) {
	var req dto.CreateMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定菜单创建请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到创建菜单请求", "request", req)

	// 验证菜单类型
	menuType := model.MenuType(req.Type)
	if !menuType.IsValid() {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的菜单类型"))
		return
	}

	menu := model.Menu{
		Title:         req.Title,
		Path:          req.Path,
		Component:     req.Component,
		Redirect:      req.Redirect,
		Type:          menuType,
		Icon:          req.Icon,
		SvgIcon:       req.SvgIcon,
		ParentID:      req.ParentID,
		Sort:          req.Sort,
		Hidden:        req.Hidden,
		KeepAlive:     req.KeepAlive,
		Breadcrumb:    req.Breadcrumb,
		ShowInTabs:    req.ShowInTabs,
		AlwaysShow:    req.AlwaysShow,
		Affix:         req.Affix,
		ActiveMenu:    req.ActiveMenu,
		Status:        req.Status,
		PermissionKey: req.PermissionKey,
	}

	if err := h.db.Create(&menu).Error; err != nil {
		log.Error("创建菜单失败", "title", req.Title, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建菜单失败"))
		return
	}
	code.AutoResponse(c, menu, nil)
}

// List all menus and return them as a tree structure
func (h *MenuHandler) List(c *gin.Context) {
	var menus []model.Menu
	if err := h.db.Order("sort asc").Find(&menus).Error; err != nil {
		log.Error("查询菜单列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询菜单列表失败"))
		return
	}

	// 构建树形结构
	menuTree := h.buildMenuTreeResponse(menus, 0)
	code.AutoResponse(c, menuTree, nil)
}

// buildMenuTreeResponse 构建菜单树形响应结构
func (h *MenuHandler) buildMenuTreeResponse(menus []model.Menu, parentID uint) []*dto.MenuTreeResponse {
	if len(menus) == 0 {
		return []*dto.MenuTreeResponse{}
	}

	var tree []*dto.MenuTreeResponse

	// 预分配切片容量，减少内存重新分配
	tree = make([]*dto.MenuTreeResponse, 0, len(menus)/2) // 估算容量

	for _, menu := range menus {
		if menu.ParentID == parentID {
			// 构建子菜单
			children := h.buildMenuTreeResponse(menus, menu.ID)

			response := &dto.MenuTreeResponse{
				ID:            menu.ID,
				Title:         menu.Title,
				Path:          menu.Path,
				Component:     menu.Component,
				Redirect:      menu.Redirect,
				Type:          int(menu.Type),
				Icon:          menu.Icon,
				SvgIcon:       menu.SvgIcon,
				ParentID:      menu.ParentID,
				Sort:          menu.Sort,
				Hidden:        menu.Hidden,
				KeepAlive:     menu.KeepAlive,
				Breadcrumb:    menu.Breadcrumb,
				ShowInTabs:    menu.ShowInTabs,
				AlwaysShow:    menu.AlwaysShow,
				Affix:         menu.Affix,
				ActiveMenu:    menu.ActiveMenu,
				Status:        menu.Status,
				PermissionKey: menu.PermissionKey,
				AccessType:    menu.GetAccessibleMenuType(),
				Children:      children,
			}
			tree = append(tree, response)
		}
	}

	return tree
}

// Get a single menu item by ID
func (h *MenuHandler) Get(c *gin.Context) {
	var menu model.Menu
	id := c.Param("id")
	if err := h.db.First(&menu, id).Error; err != nil {
		log.Error("查询菜单详情失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "菜单不存在"))
		return
	}
	code.AutoResponse(c, menu, nil)
}

// Update an existing menu item
func (h *MenuHandler) Update(c *gin.Context) {
	id := c.Param("id")
	var menu model.Menu
	if err := h.db.First(&menu, id).Error; err != nil {
		log.Error("查询菜单失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "菜单不存在"))
		return
	}
	if err := c.ShouldBindJSON(&menu); err != nil {
		log.Error("绑定菜单更新请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	if err := h.db.Save(&menu).Error; err != nil {
		log.Error("更新菜单失败", "id", id, "menu", menu, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "更新菜单失败"))
		return
	}
	code.AutoResponse(c, menu, nil)
}

// Delete a menu item
func (h *MenuHandler) Delete(c *gin.Context) {
	id := c.Param("id")
	if err := h.db.Delete(&model.Menu{}, id).Error; err != nil {
		log.Error("删除菜单失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除菜单失败"))
		return
	}
	response := dto.MessageResponse{Message: "Menu deleted successfully"}
	code.AutoResponse(c, response, nil)
}

// BatchDelete 批量删除菜单 - 支持查询参数和请求体两种方式
func (h *MenuHandler) BatchDelete(c *gin.Context) {
	// 使用通用函数解析批量ID
	ids, err := utils.ParseBatchIDs(c)
	if err != nil {
		log.Error("解析批量删除菜单ID失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	if len(ids) == 0 {
		log.Error("未提供要删除的菜单ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请提供要删除的菜单ID"))
		return
	}

	log.Info("收到批量删除菜单请求", "ids", ids)

	// 检查菜单是否存在
	var existingMenus []model.Menu
	if err := h.db.Where("id IN ?", ids).Find(&existingMenus).Error; err != nil {
		log.Error("查询菜单失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询菜单失败"))
		return
	}

	if len(existingMenus) != len(ids) {
		log.Warn("部分菜单不存在", "requested_ids", ids, "found_count", len(existingMenus))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "部分菜单不存在"))
		return
	}

	// 检查是否有子菜单依赖这些菜单
	var childCount int64
	if err := h.db.Model(&model.Menu{}).Where("parent_id IN ?", ids).Count(&childCount).Error; err != nil {
		log.Error("检查子菜单失败", "parent_ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查子菜单失败"))
		return
	}

	if childCount > 0 {
		log.Warn("存在子菜单，无法删除", "parent_ids", ids, "child_count", childCount)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "存在子菜单，请先删除子菜单"))
		return
	}

	// 执行批量删除
	if err := h.db.Delete(&model.Menu{}, ids).Error; err != nil {
		log.Error("批量删除菜单失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "批量删除菜单失败"))
		return
	}

	log.Info("批量删除菜单成功", "ids", ids, "count", len(ids))
	response := dto.MessageResponse{Message: "Menus deleted successfully"}
	code.AutoResponse(c, response, nil)
}

// GetUserMenus 获取当前用户可访问的菜单树
func (h *MenuHandler) GetUserMenus(c *gin.Context) {
	// 获取用户角色
	userRoles := h.extractUserRoles(c)

	// 如果用户没有角色，直接返回空菜单
	if len(userRoles) == 0 {
		log.Info("用户无有效角色，返回空菜单")
		code.AutoResponse(c, []*dto.MenuTreeResponse{}, nil)
		return
	}

	log.Debug("开始获取用户菜单", "user_roles", userRoles)

	// 查询所有启用的菜单
	var menus []model.Menu
	if err := h.db.Where("status = ?", 1).
		Order("sort ASC, id ASC"). // 添加 id 排序确保结果稳定
		Find(&menus).Error; err != nil {
		log.Error("查询菜单列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询菜单列表失败"))
		return
	}

	// 如果没有菜单数据，直接返回空数组
	if len(menus) == 0 {
		log.Warn("系统中没有启用的菜单")
		code.AutoResponse(c, []*dto.MenuTreeResponse{}, nil)
		return
	}

	// 构建完整的菜单树
	allMenuTree := h.buildMenuTreeResponse(menus, 0)

	// 根据用户权限过滤菜单
	accessibleMenus := h.menuService.GetUserAccessibleMenus(allMenuTree, userRoles)

	log.Info("用户菜单获取完成",
		"user_roles", userRoles,
		"total_menus", len(allMenuTree),
		"accessible_menus", len(accessibleMenus))

	code.AutoResponse(c, accessibleMenus, nil)
}

// extractUserRoles 提取用户角色信息
func (h *MenuHandler) extractUserRoles(c *gin.Context) []string {
	rolesInterface, exists := c.Get("roles")
	if !exists {
		log.Debug("用户未登录或无角色信息，拒绝访问")
		return []string{} // 返回空切片，不允许访问任何菜单
	}

	switch r := rolesInterface.(type) {
	case []string:
		if len(r) == 0 {
			log.Debug("用户角色列表为空，拒绝访问")
			return []string{} // 返回空切片，不允许访问任何菜单
		}
		return r
	case string:
		if r == "" {
			log.Debug("用户角色字符串为空，拒绝访问")
			return []string{} // 返回空切片，不允许访问任何菜单
		}
		return []string{r}
	default:
		log.Warn("用户角色类型异常，拒绝访问", "type", fmt.Sprintf("%T", r))
		return []string{} // 返回空切片，不允许访问任何菜单
	}
}
