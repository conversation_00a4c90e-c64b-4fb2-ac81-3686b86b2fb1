package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// DashboardHandler 仪表盘处理器
type DashboardHandler struct {
	db *gorm.DB
}

// NewDashboardHandler 创建仪表盘处理器实例
func NewDashboardHandler(db *gorm.DB) *DashboardHandler {
	return &DashboardHandler{
		db: db,
	}
}

// GetDashboardData 获取仪表盘数据
func (h *DashboardHandler) GetDashboardData(c *gin.Context) {
	// 获取今日数据
	today := time.Now()
	startOfToday := time.Date(today.Year(), today.Month(), today.Day(), 0, 0, 0, 0, today.Location())
	endOfToday := startOfToday.Add(24 * time.Hour)

	// 获取本月数据
	startOfMonth := time.Date(today.Year(), today.Month(), 1, 0, 0, 0, 0, today.Location())
	endOfMonth := startOfMonth.AddDate(0, 1, 0)

	// 统计用户数据
	userStats, err := h.getUserStats(startOfToday, endOfToday, startOfMonth, endOfMonth)
	if err != nil {
		log.Error("获取用户统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取仪表盘数据失败"))
		return
	}

	// 统计会员卡数据
	cardStats, err := h.getCardStats(startOfToday, endOfToday, startOfMonth, endOfMonth)
	if err != nil {
		log.Error("获取会员卡统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取仪表盘数据失败"))
		return
	}

	// 统计预约数据
	bookingStats, err := h.getBookingStats(startOfToday, endOfToday, startOfMonth, endOfMonth)
	if err != nil {
		log.Error("获取预约统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取仪表盘数据失败"))
		return
	}

	// 统计收入数据
	revenueStats, err := h.getRevenueStats(startOfToday, endOfToday, startOfMonth, endOfMonth)
	if err != nil {
		log.Error("获取收入统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取仪表盘数据失败"))
		return
	}

	// 获取即将到期的会员卡
	expiringCards, err := h.getExpiringCards()
	if err != nil {
		log.Error("获取即将到期会员卡失败", "error", err)
		// 不影响主要功能，设置空数组
		expiringCards = []dto.ExpiringCard{}
	}

	// 获取热门课程
	popularCourses, err := h.getPopularCourses()
	if err != nil {
		log.Error("获取热门课程失败", "error", err)
		// 不影响主要功能，设置空数组
		popularCourses = []dto.PopularCourse{}
	}

	// 构建响应
	response := dto.DashboardData{
		UserStats:      userStats,
		CardStats:      cardStats,
		BookingStats:   bookingStats,
		RevenueStats:   revenueStats,
		ExpiringCards:  expiringCards,
		PopularCourses: popularCourses,
		LastUpdateTime: time.Now(),
	}

	code.AutoResponse(c, response, nil)
}

// getUserStats 获取用户统计数据
func (h *DashboardHandler) getUserStats(todayStart, todayEnd, monthStart, monthEnd time.Time) (dto.UserStats, error) {
	var stats dto.UserStats

	// 总用户数
	err := h.db.Model(&model.User{}).Where("platform = 'app'").Count(&stats.TotalUsers).Error
	if err != nil {
		return stats, err
	}

	// 今日新增用户
	err = h.db.Model(&model.User{}).
		Where("platform = 'app' AND created_at >= ? AND created_at < ?", todayStart, todayEnd).
		Count(&stats.TodayNewUsers).Error
	if err != nil {
		return stats, err
	}

	// 本月新增用户
	err = h.db.Model(&model.User{}).
		Where("platform = 'app' AND created_at >= ? AND created_at < ?", monthStart, monthEnd).
		Count(&stats.MonthNewUsers).Error
	if err != nil {
		return stats, err
	}

	// 活跃用户数（本月有预约的用户）
	err = h.db.Model(&model.User{}).
		Joins("JOIN bookings ON users.id = bookings.user_id").
		Where("users.platform = 'app' AND bookings.created_at >= ? AND bookings.created_at < ?", monthStart, monthEnd).
		Distinct("users.id").
		Count(&stats.ActiveUsers).Error
	if err != nil {
		return stats, err
	}

	return stats, nil
}

// getCardStats 获取会员卡统计数据
func (h *DashboardHandler) getCardStats(todayStart, todayEnd, monthStart, monthEnd time.Time) (dto.CardStats, error) {
	var stats dto.CardStats

	// 总会员卡数
	err := h.db.Model(&model.MembershipCard{}).Count(&stats.TotalCards).Error
	if err != nil {
		return stats, err
	}

	// 今日开卡数
	err = h.db.Model(&model.MembershipCard{}).
		Where("created_at >= ? AND created_at < ?", todayStart, todayEnd).
		Count(&stats.TodayNewCards).Error
	if err != nil {
		return stats, err
	}

	// 本月开卡数
	err = h.db.Model(&model.MembershipCard{}).
		Where("created_at >= ? AND created_at < ?", monthStart, monthEnd).
		Count(&stats.MonthNewCards).Error
	if err != nil {
		return stats, err
	}

	// 活跃会员卡数（状态正常）
	err = h.db.Model(&model.MembershipCard{}).
		Where("status = 1").
		Count(&stats.ActiveCards).Error
	if err != nil {
		return stats, err
	}

	// 即将到期会员卡数（30天内）
	expiryDate := time.Now().AddDate(0, 0, 30)
	err = h.db.Model(&model.MembershipCard{}).
		Where("status = 1 AND end_date IS NOT NULL AND end_date <= ? AND end_date > ?", expiryDate, time.Now()).
		Count(&stats.ExpiringCards).Error
	if err != nil {
		return stats, err
	}

	return stats, nil
}

// getBookingStats 获取预约统计数据
func (h *DashboardHandler) getBookingStats(todayStart, todayEnd, monthStart, monthEnd time.Time) (dto.BookingStats, error) {
	var stats dto.BookingStats

	// 总预约数
	err := h.db.Model(&model.Booking{}).Count(&stats.TotalBookings).Error
	if err != nil {
		return stats, err
	}

	// 今日预约数
	err = h.db.Model(&model.Booking{}).
		Where("created_at >= ? AND created_at < ?", todayStart, todayEnd).
		Count(&stats.TodayBookings).Error
	if err != nil {
		return stats, err
	}

	// 本月预约数
	err = h.db.Model(&model.Booking{}).
		Where("created_at >= ? AND created_at < ?", monthStart, monthEnd).
		Count(&stats.MonthBookings).Error
	if err != nil {
		return stats, err
	}

	// 已完成预约数
	err = h.db.Model(&model.Booking{}).
		Where("status = 3").
		Count(&stats.CompletedBookings).Error
	if err != nil {
		return stats, err
	}

	return stats, nil
}

// getRevenueStats 获取收入统计数据
func (h *DashboardHandler) getRevenueStats(todayStart, todayEnd, monthStart, monthEnd time.Time) (dto.RevenueStats, error) {
	var stats dto.RevenueStats

	// 今日收入（会员卡销售）
	err := h.db.Model(&model.MembershipCard{}).
		Select("COALESCE(SUM(purchase_price), 0)").
		Where("created_at >= ? AND created_at < ?", todayStart, todayEnd).
		Scan(&stats.TodayRevenue).Error
	if err != nil {
		return stats, err
	}

	// 本月收入（会员卡销售）
	err = h.db.Model(&model.MembershipCard{}).
		Select("COALESCE(SUM(purchase_price), 0)").
		Where("created_at >= ? AND created_at < ?", monthStart, monthEnd).
		Scan(&stats.MonthRevenue).Error
	if err != nil {
		return stats, err
	}

	// 总收入（会员卡销售）
	err = h.db.Model(&model.MembershipCard{}).
		Select("COALESCE(SUM(purchase_price), 0)").
		Scan(&stats.TotalRevenue).Error
	if err != nil {
		return stats, err
	}

	return stats, nil
}

// getExpiringCards 获取即将到期的会员卡
func (h *DashboardHandler) getExpiringCards() ([]dto.ExpiringCard, error) {
	expiryDate := time.Now().AddDate(0, 0, 30)

	var cards []model.MembershipCard
	err := h.db.Preload("User").Preload("CardType").
		Where("status = 1 AND end_date IS NOT NULL AND end_date <= ? AND end_date > ?", expiryDate, time.Now()).
		Order("end_date ASC").
		Limit(10).
		Find(&cards).Error

	if err != nil {
		return nil, err
	}

	var expiringCards []dto.ExpiringCard
	for _, card := range cards {
		daysLeft := int(card.EndDate.Sub(time.Now()).Hours() / 24)
		expiringCard := dto.ExpiringCard{
			CardID:       card.ID,
			CardNumber:   card.CardNumber,
			UserName:     card.User.NickName,
			CardTypeName: card.CardType.Name,
			EndDate:      card.EndDate.Format("2006-01-02"),
			DaysLeft:     daysLeft,
		}
		expiringCards = append(expiringCards, expiringCard)
	}

	return expiringCards, nil
}

// getPopularCourses 获取热门课程
func (h *DashboardHandler) getPopularCourses() ([]dto.PopularCourse, error) {
	var courses []dto.PopularCourse

	err := h.db.Model(&model.Course{}).
		Select("courses.id, courses.name, COUNT(bookings.id) as booking_count").
		Joins("LEFT JOIN bookings ON courses.id = bookings.course_id").
		Where("courses.status = 1").
		Group("courses.id, courses.name").
		Order("booking_count DESC").
		Limit(10).
		Find(&courses).Error

	return courses, err
}
