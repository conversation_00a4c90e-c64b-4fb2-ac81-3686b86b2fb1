package bootstrap

import (
	"fmt"
	"log"
	"time"
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"github.com/casbin/casbin/v2"
	"gorm.io/gorm"
)

// FixedPermission 固定ID的权限结构
type FixedPermission struct {
	ID          uint
	Name        string
	Key         string
	Method      string
	Description string
}

// FixedRole 固定ID的角色结构
type FixedRole struct {
	ID            uint
	Name          string
	Description   string
	PermissionIDs []uint
}

// 定义固定ID的权限（从1开始）
var autoPermissions = []FixedPermission{
	// 用户管理权限 (1-10)
	{ID: 1, Name: "创建用户", Key: "user:create", Method: "POST", Description: "创建新用户"},
	{ID: 2, Name: "更新用户角色", Key: "user:update_roles", Method: "PUT", Description: "更新用户角色"},
	{ID: 3, Name: "删除用户", Key: "user:delete", Method: "DELETE", Description: "删除用户"},
	{ID: 4, Name: "查看用户列表", Key: "user:list", Method: "GET", Description: "查看用户列表"},
	{ID: 5, Name: "查看用户详情", Key: "user:detail", Method: "GET", Description: "查看用户详情"},

	// 角色管理权限 (11-20)
	{ID: 11, Name: "创建角色", Key: "role:create", Method: "POST", Description: "创建新角色"},
	{ID: 12, Name: "更新角色", Key: "role:update", Method: "PUT", Description: "更新角色信息"},
	{ID: 13, Name: "删除角色", Key: "role:delete", Method: "DELETE", Description: "删除角色"},
	{ID: 14, Name: "查看角色列表", Key: "role:list", Method: "GET", Description: "查看角色列表"},
	{ID: 15, Name: "更新角色权限", Key: "role:update_permissions", Method: "PUT", Description: "更新角色权限"},
	{ID: 16, Name: "查看角色详情", Key: "role:get", Method: "GET", Description: "查看角色详情"},
	{ID: 17, Name: "获取角色权限", Key: "role:get_permissions", Method: "GET", Description: "获取角色权限"},

	// 权限管理权限 (21-30)
	{ID: 21, Name: "创建权限", Key: "permission:create", Method: "POST", Description: "创建新权限"},
	{ID: 22, Name: "更新权限", Key: "permission:update", Method: "PUT", Description: "更新权限信息"},
	{ID: 23, Name: "删除权限", Key: "permission:delete", Method: "DELETE", Description: "删除权限"},
	{ID: 24, Name: "查看权限列表", Key: "permission:list", Method: "GET", Description: "查看权限列表"},
	{ID: 25, Name: "查看权限详情", Key: "permission:view", Method: "GET", Description: "查看权限详情"},

	// 菜单管理权限 (31-40)
	{ID: 31, Name: "创建菜单", Key: "menu:create", Method: "POST", Description: "创建新菜单"},
	{ID: 32, Name: "更新菜单", Key: "menu:update", Method: "PUT", Description: "更新菜单信息"},
	{ID: 33, Name: "删除菜单", Key: "menu:delete", Method: "DELETE", Description: "删除菜单"},
	{ID: 34, Name: "查看菜单列表", Key: "menu:list", Method: "GET", Description: "查看菜单列表"},
	{ID: 35, Name: "查看菜单详情", Key: "menu:get", Method: "GET", Description: "查看菜单详情"},

	// 门店管理权限 (41-50)
	{ID: 41, Name: "创建门店", Key: "store:create", Method: "POST", Description: "创建新门店"},
	{ID: 42, Name: "更新门店", Key: "store:update", Method: "PUT", Description: "更新门店信息"},
	{ID: 43, Name: "删除门店", Key: "store:delete", Method: "DELETE", Description: "删除门店"},
	{ID: 44, Name: "查看门店列表", Key: "store:list", Method: "GET", Description: "查看门店列表"},

	// 课程管理权限 (51-60)
	{ID: 51, Name: "创建课程", Key: "course:create", Method: "POST", Description: "创建新课程"},
	{ID: 52, Name: "更新课程", Key: "course:update", Method: "PUT", Description: "更新课程信息"},
	{ID: 53, Name: "删除课程", Key: "course:delete", Method: "DELETE", Description: "删除课程"},
	{ID: 54, Name: "查看课程列表", Key: "course:list", Method: "GET", Description: "查看课程列表"},

	// 预约管理权限 (61-70)
	{ID: 61, Name: "创建预约", Key: "booking:create", Method: "POST", Description: "创建新预约"},
	{ID: 62, Name: "更新预约", Key: "booking:update", Method: "PUT", Description: "更新预约信息"},
	{ID: 63, Name: "取消预约", Key: "booking:cancel", Method: "PUT", Description: "取消预约"},
	{ID: 64, Name: "查看预约列表", Key: "booking:list", Method: "GET", Description: "查看预约列表"},

	// 会员卡管理权限 (71-80)
	{ID: 71, Name: "创建会员卡", Key: "membership:create", Method: "POST", Description: "创建新会员卡"},
	{ID: 72, Name: "更新会员卡", Key: "membership:update", Method: "PUT", Description: "更新会员卡信息"},
	{ID: 73, Name: "删除会员卡", Key: "membership:delete", Method: "DELETE", Description: "删除会员卡"},
	{ID: 74, Name: "查看会员卡列表", Key: "membership:list", Method: "GET", Description: "查看会员卡列表"},

	// 文件管理权限 (81-90)
	{ID: 81, Name: "上传文件", Key: "file:upload", Method: "POST", Description: "上传文件"},
	{ID: 82, Name: "删除文件", Key: "file:delete", Method: "DELETE", Description: "删除文件"},
	{ID: 83, Name: "查看文件列表", Key: "file:list", Method: "GET", Description: "查看文件列表"},
	{ID: 84, Name: "获取上传链接", Key: "file:get_presigned_upload_url", Method: "GET", Description: "获取预签名上传链接"},
	{ID: 85, Name: "获取下载链接", Key: "file:get_presigned_download_url", Method: "GET", Description: "获取预签名下载链接"},
	{ID: 86, Name: "代理上传", Key: "file:proxy_upload", Method: "POST", Description: "代理上传文件"},

	// 数据统计权限 (91-100)
	{ID: 91, Name: "查看仪表盘", Key: "dashboard:view", Method: "GET", Description: "查看数据仪表盘"},
	{ID: 92, Name: "查看报表", Key: "report:view", Method: "GET", Description: "查看各类报表"},
	{ID: 93, Name: "导出数据", Key: "data:export", Method: "POST", Description: "导出系统数据"},

	// 灵活扣减系统权限 (101-115)
	{ID: 101, Name: "查看课程类型配置", Key: "flexible:course_types:list", Method: "GET", Description: "查看课程类型配置列表"},
	{ID: 102, Name: "创建课程类型配置", Key: "flexible:course_types:create", Method: "POST", Description: "创建课程类型配置"},
	{ID: 103, Name: "更新课程类型配置", Key: "flexible:course_types:update", Method: "PUT", Description: "更新课程类型配置"},
	{ID: 104, Name: "删除课程类型配置", Key: "flexible:course_types:delete", Method: "DELETE", Description: "删除课程类型配置"},
	{ID: 105, Name: "查看灵活扣减规则", Key: "flexible:deduction_rules:list", Method: "GET", Description: "查看灵活扣减规则列表"},
	{ID: 106, Name: "查看扣减规则详情", Key: "flexible:deduction_rules:detail", Method: "GET", Description: "查看扣减规则详情"},
	{ID: 107, Name: "创建灵活扣减规则", Key: "flexible:deduction_rules:create", Method: "POST", Description: "创建灵活扣减规则"},
	{ID: 108, Name: "更新灵活扣减规则", Key: "flexible:deduction_rules:update", Method: "PUT", Description: "更新灵活扣减规则"},
	{ID: 109, Name: "删除灵活扣减规则", Key: "flexible:deduction_rules:delete", Method: "DELETE", Description: "删除灵活扣减规则"},
	{ID: 110, Name: "切换扣减规则状态", Key: "flexible:deduction_rules:toggle", Method: "PUT", Description: "启用或停用扣减规则"},
	{ID: 111, Name: "测试扣减规则", Key: "flexible:test_deduction", Method: "POST", Description: "测试扣减规则匹配"},
	{ID: 112, Name: "查看卡种支持课程", Key: "flexible:card_supported_courses", Method: "GET", Description: "查看会员卡支持的课程类型"},
}

// 定义默认菜单数据
var autoMenus = []struct {
	ID            uint
	Title         string
	Path          string
	Component     string
	Redirect      string
	Type          model.MenuType
	Icon          string
	SvgIcon       string
	ParentID      uint
	Sort          int
	Hidden        bool
	KeepAlive     bool
	Breadcrumb    bool
	ShowInTabs    bool
	AlwaysShow    bool
	Affix         bool
	ActiveMenu    string
	Status        int
	PermissionKey string
}{
	// 一级菜单 - 系统管理
	{ID: 1, Title: "系统管理", Path: "/system", Component: "", Redirect: "/system/user", Type: model.MenuTypeDirectory, Icon: "system", ParentID: 0, Sort: 1, Status: 1, PermissionKey: ""},

	// 二级菜单 - 用户管理
	{ID: 2, Title: "用户管理", Path: "/system/user", Component: "system/user/index", Type: model.MenuTypePage, Icon: "user", ParentID: 1, Sort: 1, Status: 1, PermissionKey: "user:list"},
	{ID: 3, Title: "创建用户", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 2, Sort: 1, Status: 1, PermissionKey: "user:create"},
	{ID: 4, Title: "编辑用户", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 2, Sort: 2, Status: 1, PermissionKey: "user:update_roles"},
	{ID: 5, Title: "删除用户", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 2, Sort: 3, Status: 1, PermissionKey: "user:delete"},

	// 二级菜单 - 角色管理
	{ID: 6, Title: "角色管理", Path: "/system/role", Component: "system/role/index", Type: model.MenuTypePage, Icon: "role", ParentID: 1, Sort: 2, Status: 1, PermissionKey: "role:list"},
	{ID: 7, Title: "创建角色", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 6, Sort: 1, Status: 1, PermissionKey: "role:create"},
	{ID: 8, Title: "编辑角色", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 6, Sort: 2, Status: 1, PermissionKey: "role:update"},
	{ID: 9, Title: "删除角色", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 6, Sort: 3, Status: 1, PermissionKey: "role:delete"},

	// 二级菜单 - 权限管理
	{ID: 10, Title: "权限管理", Path: "/system/permission", Component: "system/permission/index", Type: model.MenuTypePage, Icon: "permission", ParentID: 1, Sort: 3, Status: 1, PermissionKey: "permission:list"},
	{ID: 11, Title: "创建权限", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 10, Sort: 1, Status: 1, PermissionKey: "permission:create"},
	{ID: 12, Title: "编辑权限", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 10, Sort: 2, Status: 1, PermissionKey: "permission:update"},
	{ID: 13, Title: "删除权限", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 10, Sort: 3, Status: 1, PermissionKey: "permission:delete"},

	// 二级菜单 - 菜单管理
	{ID: 14, Title: "菜单管理", Path: "/system/menu", Component: "system/menu/index", Type: model.MenuTypePage, Icon: "menu", ParentID: 1, Sort: 4, Status: 1, PermissionKey: "menu:list"},
	{ID: 15, Title: "创建菜单", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 14, Sort: 1, Status: 1, PermissionKey: "menu:create"},
	{ID: 16, Title: "编辑菜单", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 14, Sort: 2, Status: 1, PermissionKey: "menu:update"},
	{ID: 17, Title: "删除菜单", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 14, Sort: 3, Status: 1, PermissionKey: "menu:delete"},
	{ID: 18, Title: "查看菜单详情", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 14, Sort: 4, Status: 1, PermissionKey: "menu:get"},

	// 一级菜单 - 门店管理
	{ID: 20, Title: "门店管理", Path: "/store", Component: "", Redirect: "/store/list", Type: model.MenuTypeDirectory, Icon: "store", ParentID: 0, Sort: 2, Status: 1, PermissionKey: ""},
	{ID: 21, Title: "门店列表", Path: "/store/list", Component: "store/index", Type: model.MenuTypePage, Icon: "list", ParentID: 20, Sort: 1, Status: 1, PermissionKey: "store:list"},
	{ID: 22, Title: "创建门店", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 21, Sort: 1, Status: 1, PermissionKey: "store:create"},
	{ID: 23, Title: "编辑门店", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 21, Sort: 2, Status: 1, PermissionKey: "store:update"},
	{ID: 24, Title: "删除门店", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 21, Sort: 3, Status: 1, PermissionKey: "store:delete"},

	// 一级菜单 - 课程管理
	{ID: 30, Title: "课程管理", Path: "/course", Component: "", Redirect: "/course/list", Type: model.MenuTypeDirectory, Icon: "course", ParentID: 0, Sort: 3, Status: 1, PermissionKey: ""},
	{ID: 31, Title: "课程列表", Path: "/course/list", Component: "course/index", Type: model.MenuTypePage, Icon: "list", ParentID: 30, Sort: 1, Status: 1, PermissionKey: "course:list"},
	{ID: 32, Title: "创建课程", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 31, Sort: 1, Status: 1, PermissionKey: "course:create"},
	{ID: 33, Title: "编辑课程", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 31, Sort: 2, Status: 1, PermissionKey: "course:update"},
	{ID: 34, Title: "删除课程", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 31, Sort: 3, Status: 1, PermissionKey: "course:delete"},

	// 一级菜单 - 预约管理
	{ID: 40, Title: "预约管理", Path: "/booking", Component: "", Redirect: "/booking/list", Type: model.MenuTypeDirectory, Icon: "booking", ParentID: 0, Sort: 4, Status: 1, PermissionKey: ""},
	{ID: 41, Title: "预约列表", Path: "/booking/list", Component: "booking/index", Type: model.MenuTypePage, Icon: "list", ParentID: 40, Sort: 1, Status: 1, PermissionKey: "booking:list"},
	{ID: 42, Title: "处理预约", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 41, Sort: 1, Status: 1, PermissionKey: "booking:update"},
	{ID: 43, Title: "取消预约", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 41, Sort: 2, Status: 1, PermissionKey: "booking:cancel"},

	// 一级菜单 - 会员管理
	{ID: 50, Title: "会员管理", Path: "/membership", Component: "", Redirect: "/membership/list", Type: model.MenuTypeDirectory, Icon: "membership", ParentID: 0, Sort: 5, Status: 1, PermissionKey: ""},
	{ID: 51, Title: "会员卡列表", Path: "/membership/list", Component: "membership/index", Type: model.MenuTypePage, Icon: "list", ParentID: 50, Sort: 1, Status: 1, PermissionKey: "membership:list"},
	{ID: 52, Title: "创建会员卡", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 51, Sort: 1, Status: 1, PermissionKey: "membership:create"},
	{ID: 53, Title: "编辑会员卡", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 51, Sort: 2, Status: 1, PermissionKey: "membership:update"},
	{ID: 54, Title: "删除会员卡", Path: "", Component: "", Type: model.MenuTypeButton, ParentID: 51, Sort: 3, Status: 1, PermissionKey: "membership:delete"},

	// 一级菜单 - 数据统计
	{ID: 60, Title: "数据统计", Path: "/dashboard", Component: "dashboard/index", Type: model.MenuTypePage, Icon: "dashboard", ParentID: 0, Sort: 6, Status: 1, PermissionKey: "dashboard:view"},
}

// 定义固定ID的角色（从1开始）
var autoRoles = []FixedRole{
	{
		ID:          1,
		Name:        "超级管理员",
		Description: "系统超级管理员，拥有所有权限",
		PermissionIDs: []uint{
			1, 2, 3, 4, 5, 11, 12, 13, 14, 15, 16, 17, 21, 22, 23, 24, 25, 31, 32, 33, 34, 35,
			41, 42, 43, 44, 51, 52, 53, 54, 61, 62, 63, 64, 71, 72, 73, 74,
			81, 82, 83, 84, 85, 86, 91, 92, 93,
			101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, // 灵活扣减系统权限
		},
	},
	{
		ID:          2,
		Name:        "总监",
		Description: "运营总监，负责整体运营管理和决策",
		PermissionIDs: []uint{
			4, 5, 14, 16, 24, 25, 34, 35, 44, 54, 64, 74, 83, 84, 85, 91, 92, 93,
			41, 42, 43, 51, 52, 53, 62, 63, 71, 72, 73,
			101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, // 灵活扣减系统权限
		},
	},
	{
		ID:          3,
		Name:        "店长",
		Description: "门店管理者，负责门店日常运营管理",
		PermissionIDs: []uint{
			4, 5, 54, 64, 74, 91, 92,
			42, 51, 52, 53, 62, 63, 71, 72, 73, 81, 82, 84, 85,
		},
	},
	{
		ID:          4,
		Name:        "销售",
		Description: "销售人员，负责会员卡销售和客户维护",
		PermissionIDs: []uint{
			4, 5, 54, 64, 74,
			71, 72, 61, 62, 63, 81,
		},
	},
	{
		ID:          5,
		Name:        "教练",
		Description: "瑜伽教练，负责课程教学和指导",
		PermissionIDs: []uint{
			54, 64, 4, 5,
			52, 62, 81,
		},
	},
	{
		ID:          6,
		Name:        "前台",
		Description: "前台接待，负责客户接待和基础服务",
		PermissionIDs: []uint{
			4, 5, 54, 64, 74,
			61, 62, 63, 72,
		},
	},
}

// MigrationConfig 迁移配置
type MigrationConfig struct {
	Name        string
	Version     string
	Description string
	Handler     func(*gorm.DB, *casbin.Enforcer) error
}

// 包装函数，适配不同的函数签名
func migrateDefaultUsers(db *gorm.DB, enforcer *casbin.Enforcer) error {
	return createDefaultUsers(db)
}

func migrateDefaultMenus(db *gorm.DB, enforcer *casbin.Enforcer) error {
	return initializeMenus(db)
}

// 包装函数，适配课程分类初始化
func migrateCourseCategories(db *gorm.DB, enforcer *casbin.Enforcer) error {
	return initializeCourseCategories(db)
}

// 包装函数，适配会员卡类型初始化
func migrateMembershipCardTypes(db *gorm.DB, enforcer *casbin.Enforcer) error {
	return initializeMembershipCardTypes(db)
}

// 包装函数，适配灵活扣减系统初始化
func migrateFlexibleSystem(db *gorm.DB, enforcer *casbin.Enforcer) error {
	return MigrateFlexibleSystem(db)
}

// 定义所有迁移任务
var migrations = []MigrationConfig{
	{
		Name:        "role_permission_system",
		Version:     "1.0.0",
		Description: "初始化角色权限系统",
		Handler:     autoInitializeRolePermissions,
	},
	{
		Name:        "default_users",
		Version:     "1.0.0",
		Description: "创建默认用户",
		Handler:     migrateDefaultUsers,
	},
	{
		Name:        "default_menus",
		Version:     "1.0.0",
		Description: "初始化默认菜单",
		Handler:     migrateDefaultMenus,
	},
	{
		Name:        "course_categories",
		Version:     "1.0.0",
		Description: "初始化课程分类",
		Handler:     migrateCourseCategories,
	},
	{
		Name:        "membership_card_types",
		Version:     "1.0.0",
		Description: "初始化会员卡类型",
		Handler:     migrateMembershipCardTypes,
	},
	{
		Name:        "flexible_deduction_system",
		Version:     "1.0.0",
		Description: "初始化灵活扣减系统",
		Handler:     migrateFlexibleSystem,
	},
	{
		Name:        "banner_table_cleanup",
		Version:     "1.0.0",
		Description: "清理banners表中已废弃的link_type字段",
		Handler:     migrateBannerTableCleanup,
	},
}

// checkMigrationStatus 检查迁移状态
func checkMigrationStatus(db *gorm.DB, migrationName string) (bool, error) {
	var migration model.MigrationStatus
	err := db.Where("name = ? AND status = ?", migrationName, "completed").First(&migration).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return false, nil // 未完成
		}
		return false, err
	}
	return true, nil // 已完成
}

// startMigration 开始迁移
func startMigration(db *gorm.DB, config MigrationConfig) error {
	now := time.Now()
	migration := model.MigrationStatus{
		Name:        config.Name,
		Version:     config.Version,
		Status:      "running",
		Description: config.Description,
		StartedAt:   &now,
	}

	// 先删除可能存在的失败记录
	db.Where("name = ? AND status IN (?)", config.Name, []string{"pending", "failed"}).Delete(&model.MigrationStatus{})

	return db.Create(&migration).Error
}

// completeMigration 完成迁移
func completeMigration(db *gorm.DB, migrationName string, err error) {
	now := time.Now()
	updates := map[string]interface{}{
		"completed_at": &now,
	}

	if err != nil {
		updates["status"] = "failed"
		updates["error_msg"] = err.Error()
	} else {
		updates["status"] = "completed"
		updates["error_msg"] = ""
	}

	db.Model(&model.MigrationStatus{}).Where("name = ?", migrationName).Updates(updates)
}

// hasCompleteRolePermissionData 检查是否有完整的角色权限数据
func hasCompleteRolePermissionData(db *gorm.DB) bool {
	var roleCount, permissionCount int64
	db.Model(&model.Role{}).Count(&roleCount)
	db.Model(&model.Permission{}).Count(&permissionCount)

	// 检查是否有预期数量的角色和权限
	expectedRoles := len(autoRoles)
	expectedPermissions := len(autoPermissions)

	if roleCount >= int64(expectedRoles) && permissionCount >= int64(expectedPermissions) {
		log.Printf("✅ 发现完整角色权限数据: %d 个角色 (预期 %d), %d 个权限 (预期 %d)",
			roleCount, expectedRoles, permissionCount, expectedPermissions)
		return true
	}

	log.Printf("📝 角色权限数据不完整: %d 个角色 (预期 %d), %d 个权限 (预期 %d)",
		roleCount, expectedRoles, permissionCount, expectedPermissions)
	return false
}

// autoInitializeRolePermissions 自动初始化角色权限系统
func autoInitializeRolePermissions(db *gorm.DB, enforcer *casbin.Enforcer) error {
	log.Println("🚀 开始自动初始化角色权限系统...")

	// 检查是否已有完整数据，避免重复初始化
	if hasCompleteRolePermissionData(db) {
		log.Println("✅ 角色权限数据已完整，跳过初始化")
		return nil
	}

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 创建权限 - 使用 GORM 模型
	log.Printf("📊 创建 %d 个权限...", len(autoPermissions))
	for _, permInfo := range autoPermissions {
		permission := model.Permission{
			BaseModel: model.BaseModel{
				ID: permInfo.ID,
			},
			Name:        permInfo.Name,
			Key:         permInfo.Key,
			Description: permInfo.Description,
		}

		if err := tx.Create(&permission).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建权限 %s 失败: %v", permInfo.Name, err)
		}
	}

	// 2. 创建角色 - 使用 GORM 模型
	log.Printf("📊 创建 %d 个角色...", len(autoRoles))
	for _, roleInfo := range autoRoles {
		role := model.Role{
			BaseModel: model.BaseModel{
				ID: roleInfo.ID,
			},
			Name:        roleInfo.Name,
			Description: roleInfo.Description,
		}

		if err := tx.Create(&role).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建角色 %s 失败: %v", roleInfo.Name, err)
		}

		// 3. 创建角色权限关联 - 使用 GORM 关联
		var permissions []model.Permission
		for _, permID := range roleInfo.PermissionIDs {
			var permission model.Permission
			if err := tx.First(&permission, permID).Error; err != nil {
				tx.Rollback()
				return fmt.Errorf("查找权限 %d 失败: %v", permID, err)
			}
			permissions = append(permissions, permission)
		}

		if err := tx.Model(&role).Association("Permissions").Append(permissions); err != nil {
			tx.Rollback()
			return fmt.Errorf("关联权限到角色 %s 失败: %v", roleInfo.Name, err)
		}

		// 4. 创建 Casbin 策略
		for _, permID := range roleInfo.PermissionIDs {
			var permKey, method string
			for _, perm := range autoPermissions {
				if perm.ID == permID {
					permKey = perm.Key
					method = perm.Method
					break
				}
			}

			if _, err := enforcer.AddPolicy(roleInfo.Name, permKey, method); err != nil {
				tx.Rollback()
				return fmt.Errorf("添加 Casbin 策略失败: %v", err)
			}
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	// 保存 Casbin 策略
	if err := enforcer.SavePolicy(); err != nil {
		return fmt.Errorf("保存 Casbin 策略失败: %v", err)
	}

	// 更新PostgreSQL序列
	if err := updatePostgreSQLSequences(db); err != nil {
		log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
		// 不返回错误，因为这不是致命问题
	}

	log.Printf("✅ 自动初始化完成: %d 个角色, %d 个权限", len(autoRoles), len(autoPermissions))
	return nil
}

// createDefaultUsers 创建所有角色的默认用户
func createDefaultUsers(db *gorm.DB) error {
	log.Println("📝 开始创建默认用户...")

	// 定义默认用户信息
	defaultUsers := []struct {
		Username       string
		Email          string
		Password       string
		Phone          string
		NickName       string
		PlatformAccess enum.PlatformAccess
		IsCoach        bool
		RoleID         uint
		Description    string
	}{
		{
			Username:       "admin",
			Email:          "<EMAIL>",
			Password:       "admin123",
			Phone:          "13800000000",
			NickName:       "系统管理员",
			PlatformAccess: enum.PlatformAccessAdmin,
			IsCoach:        false,
			RoleID:         1,
			Description:    "超级管理员",
		},
		{
			Username:       "director",
			Email:          "<EMAIL>",
			Password:       "director123",
			Phone:          "13800000001",
			NickName:       "运营总监",
			PlatformAccess: enum.PlatformAccessAdmin,
			IsCoach:        false,
			RoleID:         2,
			Description:    "总监",
		},
		{
			Username:       "manager",
			Email:          "<EMAIL>",
			Password:       "manager123",
			Phone:          "13800000002",
			NickName:       "门店店长",
			PlatformAccess: enum.PlatformAccessAdmin,
			IsCoach:        false,
			RoleID:         3,
			Description:    "店长",
		},
		{
			Username:       "sales",
			Email:          "<EMAIL>",
			Password:       "sales123",
			Phone:          "13800000003",
			NickName:       "销售专员",
			PlatformAccess: enum.PlatformAccessAdmin,
			IsCoach:        false,
			RoleID:         4,
			Description:    "销售",
		},
		{
			Username:       "coach",
			Email:          "<EMAIL>",
			Password:       "coach123",
			Phone:          "13800000004",
			NickName:       "瑜伽教练",
			PlatformAccess: enum.PlatformAccessAdmin | enum.PlatformAccessWechat, // 教练可以访问两个平台
			IsCoach:        true,
			RoleID:         5,
			Description:    "教练",
		},
		{
			Username:       "receptionist",
			Email:          "<EMAIL>",
			Password:       "receptionist123",
			Phone:          "13800000005",
			NickName:       "前台接待",
			PlatformAccess: enum.PlatformAccessAdmin,
			IsCoach:        false,
			RoleID:         6,
			Description:    "前台",
		},
	}

	// 创建每个角色的默认用户
	for _, userInfo := range defaultUsers {
		// 检查用户是否已存在
		var existingUser model.User
		if err := db.Where("username = ?", userInfo.Username).First(&existingUser).Error; err == nil {
			log.Printf("✅ %s用户已存在，跳过创建", userInfo.Description)
			continue
		}

		// 创建用户
		user := model.User{
			Username:       userInfo.Username,
			Email:          userInfo.Email,
			Password:       userInfo.Password, // 会被 BeforeCreate hook 自动哈希
			Phone:          userInfo.Phone,
			NickName:       userInfo.NickName,
			PlatformAccess: userInfo.PlatformAccess,
			IsActive:       true,
			IsCoach:        userInfo.IsCoach,
		}

		// 创建用户
		if err := db.Create(&user).Error; err != nil {
			return fmt.Errorf("创建%s用户失败: %v", userInfo.Description, err)
		}

		// 分配角色 - 使用GORM的Association方法来处理UUID关联
		var role model.Role
		if err := db.First(&role, userInfo.RoleID).Error; err != nil {
			return fmt.Errorf("查找%s角色失败: %v", userInfo.Description, err)
		}

		if err := db.Model(&user).Association("Roles").Append(&role); err != nil {
			return fmt.Errorf("分配%s角色失败: %v", userInfo.Description, err)
		}

		log.Printf("✅ %s用户创建成功", userInfo.Description)
		log.Printf("   📧 用户名: %s", user.Username)
		log.Printf("   🔑 密码: %s", userInfo.Password)
		log.Printf("   📧 邮箱: %s", user.Email)
		log.Printf("   📞 电话: %s", user.Phone)
		log.Printf("   👤 昵称: %s", user.NickName)
		log.Printf("   🆔 用户ID: %s", user.ID)
	}

	log.Println("✅ 所有默认用户创建完成")
	return nil
}

// initializeMenus 初始化菜单数据
func initializeMenus(db *gorm.DB) error {
	log.Println("📋 开始检查菜单数据...")

	// 检查是否已有菜单数据
	var menuCount int64
	if err := db.Model(&model.Menu{}).Count(&menuCount).Error; err != nil {
		return fmt.Errorf("检查菜单数据失败: %v", err)
	}

	if menuCount > 0 {
		log.Printf("✅ 发现 %d 个菜单，跳过初始化", menuCount)
		return nil
	}

	log.Printf("📝 开始创建 %d 个默认菜单...", len(autoMenus))

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建菜单
	for _, menuInfo := range autoMenus {
		menu := model.Menu{
			BaseModel: model.BaseModel{
				ID: menuInfo.ID,
			},
			Title:         menuInfo.Title,
			Path:          menuInfo.Path,
			Component:     menuInfo.Component,
			Redirect:      menuInfo.Redirect,
			Type:          menuInfo.Type,
			Icon:          menuInfo.Icon,
			SvgIcon:       menuInfo.SvgIcon,
			ParentID:      menuInfo.ParentID,
			Sort:          menuInfo.Sort,
			Hidden:        menuInfo.Hidden,
			KeepAlive:     menuInfo.KeepAlive,
			Breadcrumb:    menuInfo.Breadcrumb,
			ShowInTabs:    menuInfo.ShowInTabs,
			AlwaysShow:    menuInfo.AlwaysShow,
			Affix:         menuInfo.Affix,
			ActiveMenu:    menuInfo.ActiveMenu,
			Status:        menuInfo.Status,
			PermissionKey: menuInfo.PermissionKey,
		}

		if err := tx.Create(&menu).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建菜单 %s 失败: %v", menuInfo.Title, err)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %v", err)
	}

	// 更新PostgreSQL序列
	if err := updatePostgreSQLSequences(db); err != nil {
		log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
		// 不返回错误，因为这不是致命问题
	}

	log.Printf("✅ 菜单初始化完成: %d 个菜单", len(autoMenus))
	return nil
}

// RunMigrations 运行所有迁移任务
func RunMigrations(db *gorm.DB, enforcer *casbin.Enforcer) error {
	log.Println("🚀 开始执行数据库迁移任务...")

	for _, migration := range migrations {
		// 检查迁移是否已完成
		completed, err := checkMigrationStatus(db, migration.Name)
		if err != nil {
			log.Printf("❌ 检查迁移状态失败 [%s]: %v", migration.Name, err)
			return fmt.Errorf("检查迁移状态失败 [%s]: %v", migration.Name, err)
		}

		if completed {
			log.Printf("✅ 迁移已完成，跳过 [%s]", migration.Name)
			continue
		}

		log.Printf("🔄 开始执行迁移 [%s]: %s", migration.Name, migration.Description)

		// 记录迁移开始
		if err := startMigration(db, migration); err != nil {
			log.Printf("❌ 记录迁移开始失败 [%s]: %v", migration.Name, err)
			return fmt.Errorf("记录迁移开始失败 [%s]: %v", migration.Name, err)
		}

		// 执行迁移
		migrationErr := migration.Handler(db, enforcer)

		// 记录迁移结果
		completeMigration(db, migration.Name, migrationErr)

		if migrationErr != nil {
			log.Printf("❌ 迁移执行失败 [%s]: %v", migration.Name, migrationErr)
			return fmt.Errorf("迁移执行失败 [%s]: %v", migration.Name, migrationErr)
		}

		log.Printf("✅ 迁移执行成功 [%s]", migration.Name)
	}

	log.Println("🎉 所有迁移任务执行完成！")
	return nil
}

// GetAutoPermissions 导出权限数据供外部使用
func GetAutoPermissions() []FixedPermission {
	return autoPermissions
}

// GetAutoRoles 导出角色数据供外部使用
func GetAutoRoles() []FixedRole {
	return autoRoles
}

// updatePostgreSQLSequences 更新PostgreSQL序列到正确的值
func updatePostgreSQLSequences(db *gorm.DB) error {
	// 检查是否是PostgreSQL数据库
	if db.Dialector.Name() != "postgres" {
		return nil // 非PostgreSQL数据库，跳过
	}

	log.Println("🔄 更新PostgreSQL序列...")

	// 定义需要更新序列的表
	tables := []struct {
		tableName    string
		sequenceName string
	}{
		// 基础表
		{"permissions", "permissions_id_seq"},
		{"roles", "roles_id_seq"},
		{"menus", "menus_id_seq"},
		{"users", "users_id_seq"},
		{"stores", "stores_id_seq"},
		{"files", "files_id_seq"},

		// 业务表
		{"course_categories", "course_categories_id_seq"},
		{"membership_card_types", "membership_card_types_id_seq"},
		{"course_type_configs", "course_type_configs_id_seq"},
		{"flexible_deduction_rules", "flexible_deduction_rules_id_seq"},

		// 注意：多对多关联表(user_roles, role_permissions)通常没有自增ID，只有外键字段

		// 其他可能的表
		{"courses", "courses_id_seq"},
		{"class_rooms", "class_rooms_id_seq"},
		{"class_schedules", "class_schedules_id_seq"},
		{"membership_cards", "membership_cards_id_seq"},
		{"membership_card_transactions", "membership_card_transactions_id_seq"},
		{"membership_card_leaves", "membership_card_leaves_id_seq"},
		{"bookings", "bookings_id_seq"},
		{"booking_queues", "booking_queues_id_seq"},
		{"booking_applications", "booking_applications_id_seq"},
		{"admin_notifications", "admin_notifications_id_seq"},
		{"banners", "banners_id_seq"},
		{"coach_banners", "coach_banners_id_seq"},
		{"favorites", "favorites_id_seq"},
		{"operation_logs", "operation_logs_id_seq"},
		{"user_store_assignments", "user_store_assignments_id_seq"},
		{"member_sales_assignments", "member_sales_assignments_id_seq"},
		{"resource_assignments", "resource_assignments_id_seq"},
		{"migration_status", "migration_status_id_seq"},
	}

	for _, table := range tables {
		// 查询表中的最大ID
		var maxID uint
		if err := db.Table(table.tableName).Select("COALESCE(MAX(id), 0)").Scan(&maxID).Error; err != nil {
			log.Printf("⚠️ 查询表 %s 最大ID失败: %v", table.tableName, err)
			continue
		}

		if maxID == 0 {
			log.Printf("📝 表 %s 为空，跳过序列更新", table.tableName)
			continue
		}

		// 更新序列到下一个可用值
		nextVal := maxID + 1
		sql := fmt.Sprintf("SELECT setval('%s', %d, true)", table.sequenceName, nextVal)

		if err := db.Exec(sql).Error; err != nil {
			log.Printf("⚠️ 更新序列 %s 失败: %v", table.sequenceName, err)
			// 尝试使用表名_id_seq格式
			altSeqName := fmt.Sprintf("%s_id_seq", table.tableName)
			altSQL := fmt.Sprintf("SELECT setval('%s', %d, true)", altSeqName, nextVal)
			if err2 := db.Exec(altSQL).Error; err2 != nil {
				log.Printf("⚠️ 使用备用序列名 %s 也失败: %v", altSeqName, err2)
				continue
			}
			log.Printf("✅ 序列 %s 已更新到 %d", altSeqName, nextVal)
		} else {
			log.Printf("✅ 序列 %s 已更新到 %d", table.sequenceName, nextVal)
		}
	}

	return nil
}

// migrateBannerTableCleanup 清理banners表中已废弃的link_type字段
func migrateBannerTableCleanup(db *gorm.DB, enforcer *casbin.Enforcer) error {
	log.Println("🔄 开始清理banners表中已废弃的link_type字段...")

	// 检查是否存在link_type字段
	if !db.Migrator().HasColumn(&model.Banner{}, "link_type") {
		log.Println("✅ banners表中不存在link_type字段，无需清理")
		return nil
	}

	// 删除link_type字段
	if err := db.Migrator().DropColumn(&model.Banner{}, "link_type"); err != nil {
		log.Printf("❌ 删除banners表link_type字段失败: %v", err)
		return fmt.Errorf("删除banners表link_type字段失败: %w", err)
	}

	log.Println("✅ 成功删除banners表中的link_type字段")
	return nil
}
