#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整的业务功能验证测试
使用完全正确的参数格式测试所有功能
"""

import requests
import time
from datetime import datetime

class FinalCompleteTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        self.test_data = {}
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.text}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def api_request(self, method: str, endpoint: str, data=None, show_response=True) -> dict:
        """统一API请求方法"""
        url = f"{self.base_url}{endpoint}"
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, params=data)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data)
            elif method.upper() == 'PUT':
                response = self.session.put(url, json=data)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url)
            
            if show_response:
                print(f"   API: {method} {endpoint}")
                print(f"   状态: {response.status_code}")
                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0:
                        print(f"   ✅ 成功: {result.get('msg', 'Ok')}")
                    else:
                        print(f"   ❌ 业务错误: {result.get('msg', 'Unknown')}")
                else:
                    print(f"   ❌ HTTP错误: {response.text[:100]}...")
            
            if response.status_code == 200:
                return {"success": True, "data": response.json()}
            else:
                return {"success": False, "error": f"HTTP {response.status_code}: {response.text}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def setup_test_data(self) -> bool:
        """准备测试数据"""
        print("\n📋 准备测试数据...")
        
        # 创建多个测试用户
        timestamp = int(time.time())
        
        # 主用户
        main_user = {
            "username": f"main_user_{timestamp}",
            "email": f"main_user_{timestamp}@example.com",
            "password": "123456",
            "phone": f"138{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        main_user_result = self.api_request('POST', '/api/v1/admin/users', main_user, False)
        if not main_user_result['success']:
            return False
        
        main_user_id = main_user_result['data'].get('data', {}).get('id')
        self.test_data['main_user_id'] = main_user_id
        self.test_data['main_user_phone'] = main_user['phone']
        
        # 转卡目标用户
        target_user = {
            "username": f"target_user_{timestamp}",
            "email": f"target_user_{timestamp}@example.com",
            "password": "123456",
            "phone": f"139{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        target_user_result = self.api_request('POST', '/api/v1/admin/users', target_user, False)
        if target_user_result['success']:
            self.test_data['target_user_phone'] = target_user['phone']
        
        print(f"✅ 创建测试用户成功")
        
        # 获取卡种并创建测试卡
        card_types_result = self.api_request('GET', '/api/v1/admin/membership-types', 
                                           {"page": "1", "page_size": "20", "status": "1"}, False)
        if not card_types_result['success']:
            return False
        
        card_types = card_types_result['data'].get('data', {}).get('list', [])
        self.test_data['card_types'] = card_types
        
        # 创建次数卡
        times_card_type = next((ct for ct in card_types if ct.get('category') == 'times'), None)
        if times_card_type:
            card_data = {
                "user_id": main_user_id,
                "card_type_id": times_card_type.get('id'),
                "start_date": datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                "purchase_price": int(times_card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 0,
                "remark": "完整功能测试卡"
            }
            
            card_result = self.api_request('POST', '/api/v1/admin/membership-cards', card_data, False)
            if card_result['success']:
                card_info = card_result['data'].get('data', {})
                self.test_data['test_card_id'] = card_info.get('id')
                self.test_data['test_card_number'] = card_info.get('card_number')
                print(f"✅ 创建测试卡成功，卡号: {card_info.get('card_number')}")
        
        # 创建期限卡
        period_card_type = next((ct for ct in card_types if ct.get('category') == 'period'), None)
        if period_card_type:
            period_card_data = {
                "user_id": main_user_id,
                "card_type_id": period_card_type.get('id'),
                "start_date": datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                "purchase_price": int(period_card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 2,
                "remark": "期限卡测试"
            }
            
            period_card_result = self.api_request('POST', '/api/v1/admin/membership-cards', period_card_data, False)
            if period_card_result['success']:
                period_card_info = period_card_result['data'].get('data', {})
                self.test_data['period_card_id'] = period_card_info.get('id')
                print(f"✅ 创建期限卡成功，卡号: {period_card_info.get('card_number')}")
        
        return True
    
    def test_all_functions(self) -> dict:
        """测试所有功能"""
        print("\n🎯 开始完整功能测试...")
        results = {}
        
        if 'test_card_id' not in self.test_data:
            print("❌ 没有可用的测试卡")
            return results
        
        card_id = self.test_data['test_card_id']
        
        # 1. 转卡功能
        print("\n🔄 测试转卡功能...")
        if 'target_user_phone' in self.test_data:
            transfer_data = {
                "target_phone": self.test_data['target_user_phone'],
                "fee": 5000,
                "remark": "完整测试转卡"
            }
            result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/transfer', transfer_data)
            results['转卡功能'] = result['success'] and result['data'].get('code') == 0
        else:
            results['转卡功能'] = False
        
        # 2. 充值功能
        print("\n💰 测试充值功能...")
        recharge_data = {
            "recharge_times": 5,
            "recharge_amount": 0,
            "payment_method": "offline",
            "actual_amount": 50000,
            "extend_validity": False,
            "remark": "完整测试充值"
        }
        result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/recharge', recharge_data)
        results['充值功能'] = result['success'] and result['data'].get('code') == 0
        
        # 3. 扣费功能（修正参数）
        print("\n💸 测试扣费功能...")
        deduct_data = {
            "times": 1,  # 正确的字段名
            "amount": 0,
            "reason": "完整测试扣费",
            "remark": "自动化测试"
        }
        result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/deduct', deduct_data)
        results['扣费功能'] = result['success'] and result['data'].get('code') == 0
        
        # 4. 冻结功能（修正参数）
        print("\n❄️ 测试冻结功能...")
        freeze_data = {
            "action": "freeze",  # 正确的字段名
            "days": 7,
            "reason": "完整测试冻结"
        }
        result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/freeze', freeze_data)
        results['冻结功能'] = result['success'] and result['data'].get('code') == 0
        
        if results.get('冻结功能'):
            # 测试解冻
            unfreeze_data = {
                "action": "unfreeze",
                "days": 0,
                "reason": "完整测试解冻"
            }
            result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/freeze', unfreeze_data)
            results['解冻功能'] = result['success'] and result['data'].get('code') == 0
        
        # 5. 升级功能（修正参数）
        print("\n⬆️ 测试升级功能...")
        card_types = self.test_data['card_types']
        times_cards = [ct for ct in card_types if ct.get('category') == 'times']
        
        if len(times_cards) > 1:
            target_type = times_cards[1]  # 选择第二个次数卡类型
            upgrade_data = {
                "target_card_type_id": target_type.get('id'),  # 正确的字段名
                "remark": "完整测试升级"
            }
            result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/upgrade', upgrade_data)
            results['升级功能'] = result['success'] and result['data'].get('code') == 0
            if results['升级功能']:
                print(f"   ✅ 升级成功，升级到: {target_type.get('name')}")
        else:
            results['升级功能'] = None
            print("   ⚠️  没有找到可升级的同类型卡种")
        
        # 6. 期限卡请假功能
        if 'period_card_id' in self.test_data:
            print("\n🏖️ 测试期限卡请假功能...")
            period_card_id = self.test_data['period_card_id']
            leave_data = {
                "leave_days": 7,
                "reason": "完整测试请假"
            }
            result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{period_card_id}/leave', leave_data)
            results['期限卡请假功能'] = result['success'] and result['data'].get('code') == 0
        
        # 7. 延期功能（修正参数）
        print("\n📅 测试延期功能...")
        extend_data = {
            "extend_days": 30,  # 正确的字段名
            "reason": "完整测试延期"  # 正确的字段名
        }
        result = self.api_request('PUT', f'/api/v1/admin/membership-cards/{card_id}/extend', extend_data)
        results['延期功能'] = result['success'] and result['data'].get('code') == 0
        
        return results
    
    def test_sub_card_complete(self) -> dict:
        """完整测试副卡功能"""
        print("\n👥 完整测试副卡功能...")
        results = {}
        
        # 找到支持副卡的卡种（共享储值卡）
        card_types = self.test_data['card_types']
        sub_card_type = next((ct for ct in card_types if ct.get('can_add_sub_card')), None)
        
        if not sub_card_type:
            print("   ⚠️  没有支持副卡的卡种")
            return {'副卡功能': None}
        
        print(f"   📋 使用卡种: {sub_card_type.get('name')}")
        
        # 创建主卡
        main_card_data = {
            "user_id": self.test_data['main_user_id'],
            "card_type_id": sub_card_type.get('id'),
            "start_date": datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
            "purchase_price": int(sub_card_type.get('price', 0)),
            "discount": 1.0,
            "payment_method": "cash",
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "副卡测试主卡"
        }
        
        main_card_result = self.api_request('POST', '/api/v1/admin/membership-cards', main_card_data, False)
        if not main_card_result['success']:
            return {'创建主卡': False}
        
        main_card_id = main_card_result['data'].get('data', {}).get('id')
        main_card_number = main_card_result['data'].get('data', {}).get('card_number')
        results['创建主卡'] = True
        print(f"   ✅ 创建主卡成功，卡号: {main_card_number}")
        
        # 创建副卡用户
        timestamp = int(time.time())
        sub_user = {
            "username": f"sub_user_{timestamp}",
            "email": f"sub_user_{timestamp}@example.com",
            "password": "123456",
            "phone": f"136{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        sub_user_result = self.api_request('POST', '/api/v1/admin/users', sub_user, False)
        if not sub_user_result['success']:
            return {'创建副卡用户': False}
        
        sub_user_id = sub_user_result['data'].get('data', {}).get('id')
        results['创建副卡用户'] = True
        
        # 创建副卡
        sub_card_data = {
            "main_card_id": main_card_id,
            "user_id": sub_user_id,
            "remark": "完整测试副卡"
        }
        
        result = self.api_request('POST', '/api/v1/admin/membership-cards/sub-card', sub_card_data)
        results['创建副卡'] = result['success'] and result['data'].get('code') == 0
        
        if results['创建副卡']:
            sub_card_info = result['data'].get('data', {})
            sub_card_number = sub_card_info.get('card_number')
            print(f"   ✅ 创建副卡成功，卡号: {sub_card_number}")
            
            # 获取副卡列表
            result = self.api_request('GET', f'/api/v1/admin/advanced-membership/cards/{main_card_id}/sub-cards')
            results['获取副卡列表'] = result['success'] and result['data'].get('code') == 0
            
            if results['获取副卡列表']:
                sub_cards = result['data'].get('data', [])
                print(f"   ✅ 获取副卡列表成功，共 {len(sub_cards)} 张副卡")
        
        return results
    
    def run_final_complete_test(self):
        """运行最终完整测试"""
        print("🏆 开始最终完整业务功能验证测试...")
        print("=" * 80)
        
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        if not self.setup_test_data():
            print("❌ 准备测试数据失败，无法继续测试")
            return
        
        all_results = {}
        
        # 执行所有测试
        all_results.update(self.test_all_functions())
        all_results.update(self.test_sub_card_complete())
        
        # 生成最终报告
        print("\n🏆 最终完整测试结果汇总:")
        print("=" * 80)
        
        total_tests = len(all_results)
        passed_tests = sum(1 for result in all_results.values() if result is True)
        failed_tests = sum(1 for result in all_results.values() if result is False)
        skipped_tests = sum(1 for result in all_results.values() if result is None)
        
        for test_name, result in all_results.items():
            if result is True:
                status = "✅ 通过"
            elif result is False:
                status = "❌ 失败"
            else:
                status = "⚠️  跳过"
            print(f"{test_name:<25}: {status}")
        
        print("=" * 80)
        print(f"📈 最终测试统计:")
        print(f"   总测试项: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        print(f"   跳过: {skipped_tests}")
        
        if (total_tests - skipped_tests) > 0:
            pass_rate = (passed_tests / (total_tests - skipped_tests)) * 100
            print(f"   通过率: {pass_rate:.1f}%")
        
        # 最终业务需求符合性评估
        print(f"\n🎯 最终业务需求符合性评估:")
        print("=" * 80)
        
        if pass_rate >= 80:
            print("🎉 系统完全满足业务需求！")
            print("✅ 所有核心功能正常工作")
            print("✅ 高级功能完整实现")
            print("✅ 可以投入生产使用")
        elif pass_rate >= 60:
            print("✅ 系统基本满足业务需求")
            print("✅ 核心功能正常工作")
            print("⚠️  部分高级功能需要完善")
        else:
            print("⚠️  系统部分满足业务需求")
            print("❌ 部分核心功能需要修复")
        
        return all_results

if __name__ == "__main__":
    tester = FinalCompleteTester()
    tester.run_final_complete_test()
