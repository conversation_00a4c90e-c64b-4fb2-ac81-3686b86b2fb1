package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// CourseReviewHandler 课程评价处理器
type CourseReviewHandler struct {
	db *gorm.DB
}

// NewCourseReviewHandler 创建课程评价处理器实例
func NewCourseReviewHandler(db *gorm.DB) *CourseReviewHandler {
	return &CourseReviewHandler{
		db: db,
	}
}

// GetCourseReviews 获取课程评价列表
func (h *CourseReviewHandler) GetCourseReviews(c *gin.Context) {
	// 解析查询参数
	page := cast.ToInt(c.DefaultQuery("page", "1"))
	pageSize := cast.ToInt(c.DefaultQuery("page_size", "20"))
	courseIDStr := c.Query("course_id")
	coachIDStr := c.Query("coach_id")
	storeIDStr := c.Query("store_id")
	ratingStr := c.Query("rating")
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")

	// 构建查询
	query := h.db.Model(&model.Booking{}).
		Preload("User").
		Preload("Schedule").
		Preload("Schedule.Course").
		Preload("Schedule.Coach").
		Preload("Schedule.Store").
		Where("rating > 0") // 只查询有评价的预约

	// 筛选条件
	if courseIDStr != "" {
		if courseID := cast.ToUint(courseIDStr); courseID > 0 {
			query = query.Joins("JOIN class_schedules ON bookings.schedule_id = class_schedules.id").
				Where("class_schedules.course_id = ?", courseID)
		}
	}

	if coachIDStr != "" {
		if coachID := cast.ToUint(coachIDStr); coachID > 0 {
			query = query.Joins("JOIN class_schedules ON bookings.schedule_id = class_schedules.id").
				Where("class_schedules.coach_id = ?", coachID)
		}
	}

	if storeIDStr != "" {
		if storeID := cast.ToUint(storeIDStr); storeID > 0 {
			query = query.Joins("JOIN class_schedules ON bookings.schedule_id = class_schedules.id").
				Where("class_schedules.store_id = ?", storeID)
		}
	}

	if ratingStr != "" {
		if rating := cast.ToInt(ratingStr); rating >= 1 && rating <= 5 {
			query = query.Where("rating = ?", rating)
		}
	}

	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			query = query.Where("created_at >= ?", date)
		}
	}

	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			query = query.Where("created_at <= ?", date.Add(24*time.Hour))
		}
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("查询评价总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询评价总数失败"))
		return
	}

	// 分页查询
	var bookings []model.Booking
	offset := (page - 1) * pageSize
	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&bookings).Error; err != nil {
		log.Error("查询课程评价失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程评价失败"))
		return
	}

	// 构建响应数据
	var reviews []dto.CourseReviewResponse
	for _, booking := range bookings {
		review := dto.CourseReviewResponse{
			ID:        booking.ID,
			BookingID: booking.ID,
			UserID:    booking.UserID,
			Rating:    booking.Rating,
			Comment:   booking.Comment,
			CreatedAt: booking.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: booking.UpdatedAt.Format("2006-01-02 15:04:05"),
		}

		// 用户信息
		if booking.User.ID.String() != "00000000-0000-0000-0000-000000000000" {
			review.UserName = booking.User.NickName
			review.UserPhone = booking.User.Phone
		}

		// 课程信息
		if booking.Schedule.Course.ID > 0 {
			review.CourseID = booking.Schedule.Course.ID
			review.CourseName = booking.Schedule.Course.Name
		}

		// 教练信息
		if booking.Schedule.Coach.ID.String() != "00000000-0000-0000-0000-000000000000" {
			review.CoachID = booking.Schedule.Coach.ID.String()
			review.CoachName = booking.Schedule.Coach.NickName
		}

		// 门店信息
		if booking.Schedule.Store.ID > 0 {
			review.StoreID = booking.Schedule.Store.ID
			review.StoreName = booking.Schedule.Store.Name
		}

		reviews = append(reviews, review)
	}

	// 构建分页响应
	response := dto.PageResponse{
		List:     reviews,
		Total:    total,
		Page:     page,
		PageSize: pageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetCourseReviewDetail 获取课程评价详情
func (h *CourseReviewHandler) GetCourseReviewDetail(c *gin.Context) {
	reviewIDStr := c.Param("id")
	reviewID := cast.ToUint(reviewIDStr)
	if reviewID == 0 {
		log.Error("评价ID格式错误", "id", reviewIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "评价ID格式错误"))
		return
	}

	// 查询评价详情
	var booking model.Booking
	if err := h.db.Preload("User").
		Preload("Schedule").
		Preload("Schedule.Course").
		Preload("Schedule.Coach").
		Preload("Schedule.Store").
		Preload("MembershipCard").
		Where("id = ? AND rating > 0", reviewID).
		First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程评价不存在"))
			return
		}
		log.Error("查询课程评价详情失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程评价详情失败"))
		return
	}

	// 构建详细响应
	review := dto.CourseReviewResponse{
		ID:        booking.ID,
		BookingID: booking.ID,
		UserID:    booking.UserID,
		Rating:    booking.Rating,
		Comment:   booking.Comment,
		CreatedAt: booking.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: booking.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	// 补充详细信息
	if booking.User.ID.String() != "00000000-0000-0000-0000-000000000000" {
		review.UserName = booking.User.NickName
		review.UserPhone = booking.User.Phone
	}

	if booking.Schedule.Course.ID > 0 {
		review.CourseID = booking.Schedule.Course.ID
		review.CourseName = booking.Schedule.Course.Name
	}

	if booking.Schedule.Coach.ID.String() != "00000000-0000-0000-0000-000000000000" {
		review.CoachID = booking.Schedule.Coach.ID.String()
		review.CoachName = booking.Schedule.Coach.NickName
	}

	if booking.Schedule.Store.ID > 0 {
		review.StoreID = booking.Schedule.Store.ID
		review.StoreName = booking.Schedule.Store.Name
	}

	code.AutoResponse(c, review, nil)
}

// DeleteCourseReview 删除课程评价（管理员操作）
func (h *CourseReviewHandler) DeleteCourseReview(c *gin.Context) {
	reviewIDStr := c.Param("id")
	reviewID := cast.ToUint(reviewIDStr)
	if reviewID == 0 {
		log.Error("评价ID格式错误", "id", reviewIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "评价ID格式错误"))
		return
	}

	// 查询评价是否存在
	var booking model.Booking
	if err := h.db.Where("id = ? AND rating > 0", reviewID).First(&booking).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程评价不存在"))
			return
		}
		log.Error("查询课程评价失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程评价失败"))
		return
	}

	// 清除评价内容（不删除预约记录，只清除评价）
	if err := h.db.Model(&booking).Updates(map[string]interface{}{
		"rating":  0,
		"comment": "",
	}).Error; err != nil {
		log.Error("删除课程评价失败", "review_id", reviewID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "删除课程评价失败"))
		return
	}

	log.Info("删除课程评价成功", "review_id", reviewID)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// GetReviewStats 获取评价统计
func (h *CourseReviewHandler) GetReviewStats(c *gin.Context) {
	startDateStr := c.DefaultQuery("start_date", time.Now().AddDate(0, 0, -30).Format("2006-01-02"))
	endDateStr := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))

	// 解析日期
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	endDate = endDate.Add(24 * time.Hour)

	stats := make(map[string]interface{})

	// 总评价数
	var totalReviews int64
	h.db.Model(&model.Booking{}).
		Where("rating > 0 AND created_at >= ? AND created_at <= ?", startDate, endDate).
		Count(&totalReviews)
	stats["total_reviews"] = totalReviews

	// 平均评分
	var avgRating float64
	h.db.Model(&model.Booking{}).
		Where("rating > 0 AND created_at >= ? AND created_at <= ?", startDate, endDate).
		Select("AVG(rating)").
		Scan(&avgRating)
	stats["avg_rating"] = avgRating

	// 评分分布
	var ratingDistribution []map[string]interface{}
	h.db.Model(&model.Booking{}).
		Select("rating, COUNT(*) as count").
		Where("rating > 0 AND created_at >= ? AND created_at <= ?", startDate, endDate).
		Group("rating").
		Order("rating").
		Find(&ratingDistribution)
	stats["rating_distribution"] = ratingDistribution

	// 最受好评的课程（Top 5）
	var topCourses []map[string]interface{}
	h.db.Model(&model.Booking{}).
		Select("class_schedules.course_id, courses.name as course_name, AVG(bookings.rating) as avg_rating, COUNT(*) as review_count").
		Joins("JOIN class_schedules ON bookings.schedule_id = class_schedules.id").
		Joins("JOIN courses ON class_schedules.course_id = courses.id").
		Where("bookings.rating > 0 AND bookings.created_at >= ? AND bookings.created_at <= ?", startDate, endDate).
		Group("class_schedules.course_id, courses.name").
		Having("COUNT(*) >= 3"). // 至少3个评价
		Order("avg_rating DESC, review_count DESC").
		Limit(5).
		Find(&topCourses)
	stats["top_courses"] = topCourses

	// 最受好评的教练（Top 5）
	var topCoaches []map[string]interface{}
	h.db.Model(&model.Booking{}).
		Select("class_schedules.coach_id, users.nick_name as coach_name, AVG(bookings.rating) as avg_rating, COUNT(*) as review_count").
		Joins("JOIN class_schedules ON bookings.schedule_id = class_schedules.id").
		Joins("JOIN users ON class_schedules.coach_id = users.id").
		Where("bookings.rating > 0 AND bookings.created_at >= ? AND bookings.created_at <= ?", startDate, endDate).
		Group("class_schedules.coach_id, users.nick_name").
		Having("COUNT(*) >= 3"). // 至少3个评价
		Order("avg_rating DESC, review_count DESC").
		Limit(5).
		Find(&topCoaches)
	stats["top_coaches"] = topCoaches

	code.AutoResponse(c, stats, nil)
}
