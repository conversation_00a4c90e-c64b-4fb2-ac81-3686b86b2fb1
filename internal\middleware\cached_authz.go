package middleware

import (
	"context"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// CheckPermissionByKeyWithCache 带缓存的权限检查中间件
func CheckPermissionByKeyWithCache(key string, enforcer *casbin.Enforcer, db *gorm.DB, redis *redis.Client) gin.HandlerFunc {
	cacheService := service.NewPermissionCacheService(db, redis)

	return func(c *gin.Context) {
		ctx := context.Background()

		// 1. 获取用户ID
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			log.Warn("用户未登录", "key", key)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
			c.Abort()
			return
		}

		userID := userIDInterface.(string)

		// 2. 从缓存获取用户角色
		roles, err := cacheService.GetUserRoles(ctx, userID)
		if err != nil {
			log.Error("获取用户角色失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "权限验证失败"))
			c.Abort()
			return
		}

		if len(roles) == 0 {
			log.Warn("用户无角色", "user_id", userID)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
			c.Abort()
			return
		}

		// 3. 检查权限
		action := c.Request.Method
		var allowed bool
		var lastErr error

		for _, role := range roles {
			// 3.1 先检查缓存
			if cachedResult, hit := cacheService.GetCachedPermissionResult(ctx, role, key, action); hit {
				if cachedResult {
					allowed = true
					log.Debug("权限检查通过(缓存)", "role", role, "key", key, "action", action)
					break
				}
				continue
			}

			// 3.2 缓存未命中，使用Casbin检查
			result, err := enforcer.Enforce(role, key, action)
			if err != nil {
				lastErr = err
				log.Error("权限检查失败", "role", role, "key", key, "action", action, "error", err)
				continue
			}

			// 3.3 缓存权限检查结果
			cacheService.CachePermissionResult(ctx, role, key, action, result)

			if result {
				allowed = true
				log.Debug("权限检查通过(Casbin)", "role", role, "key", key, "action", action)
				break
			}
		}

		if lastErr != nil && !allowed {
			log.Error("权限验证过程中发生错误", "user_id", userID, "key", key, "error", lastErr)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "权限验证失败"))
			c.Abort()
			return
		}

		if !allowed {
			log.Warn("权限不足", "user_id", userID, "roles", roles, "key", key, "action", action)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
			c.Abort()
			return
		}

		// 将角色信息存储到上下文，供后续中间件使用
		c.Set("roles", roles)
		c.Next()
	}
}

// CachedAuthMiddleware 带缓存的认证中间件（替换原有的auth中间件）
func CachedAuthMiddleware(jwtSvc interface{}, db *gorm.DB, redis *redis.Client) gin.HandlerFunc {
	cacheService := service.NewPermissionCacheService(db, redis)

	return func(c *gin.Context) {
		ctx := context.Background()

		// 1. JWT认证逻辑（这里需要根据你们的JWT服务调整）
		// 假设JWT认证成功后，用户ID已经设置到context中
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
			c.Abort()
			return
		}

		userID := userIDInterface.(string)

		// 2. 从缓存获取用户角色
		roles, err := cacheService.GetUserRoles(ctx, userID)
		if err != nil {
			log.Error("获取用户角色失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "权限验证失败"))
			c.Abort()
			return
		}

		// 3. 设置角色信息到上下文
		c.Set("roles", roles)

		log.Debug("用户认证成功", "user_id", userID, "roles", roles)
		c.Next()
	}
}

// CheckPermissionByKeyWithCacheAdvanced 带缓存的高级权限检查中间件（支持资源级权限的业务逻辑检查）
func CheckPermissionByKeyWithCacheAdvanced(key string, enforcer *casbin.Enforcer, db *gorm.DB, redis *redis.Client, resourceChecker func(*gin.Context, string) bool) gin.HandlerFunc {
	cacheService := service.NewPermissionCacheService(db, redis)

	return func(c *gin.Context) {
		ctx := context.Background()

		// 1. 获取用户信息
		userIDInterface, exists := c.Get("user_id")
		if !exists {
			log.Warn("用户未登录", "key", key)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
			c.Abort()
			return
		}

		userID := userIDInterface.(string)

		// 2. 从缓存获取用户角色
		roles, err := cacheService.GetUserRoles(ctx, userID)
		if err != nil {
			log.Error("获取用户角色失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "权限验证失败"))
			c.Abort()
			return
		}

		if len(roles) == 0 {
			log.Warn("用户无角色", "user_id", userID)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
			c.Abort()
			return
		}

		// 3. 检查基础权限（3参数模式）
		action := c.Request.Method
		var allowed bool
		var lastErr error

		for _, role := range roles {
			// 3.1 先检查缓存
			if cachedResult, hit := cacheService.GetCachedPermissionResult(ctx, role, key, action); hit {
				if cachedResult {
					allowed = true
					log.Debug("权限检查通过(缓存)", "role", role, "key", key, "action", action)
					break
				}
				continue
			}

			// 3.2 缓存未命中，使用Casbin检查（3参数）
			result, err := enforcer.Enforce(role, key, action)
			if err != nil {
				lastErr = err
				log.Error("权限检查失败", "role", role, "key", key, "action", action, "error", err)
				continue
			}

			// 3.3 缓存权限检查结果
			cacheService.CachePermissionResult(ctx, role, key, action, result)

			if result {
				allowed = true
				log.Debug("权限检查通过(Casbin)", "role", role, "key", key, "action", action)
				break
			}
		}

		if lastErr != nil && !allowed {
			log.Error("权限验证过程中发生错误", "user_id", userID, "key", key, "error", lastErr)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "权限验证失败"))
			c.Abort()
			return
		}

		if !allowed {
			log.Warn("权限不足", "user_id", userID, "roles", roles, "key", key, "action", action)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
			c.Abort()
			return
		}

		// 4. 可选的资源级权限检查（业务逻辑）
		if resourceChecker != nil {
			if !resourceChecker(c, userID) {
				log.Warn("资源级权限检查失败", "user_id", userID, "key", key)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "无权访问该资源"))
				c.Abort()
				return
			}
		}

		// 将角色信息存储到上下文，供后续中间件使用
		c.Set("roles", roles)
		c.Next()
	}
}
