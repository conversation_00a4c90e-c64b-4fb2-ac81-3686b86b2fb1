package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// ClassRoomHandler 教室处理器
type ClassRoomHandler struct {
	db *gorm.DB
}

// NewClassRoomHandler 创建教室处理器实例
func NewClassRoomHandler(db *gorm.DB) *ClassRoomHandler {
	return &ClassRoomHandler{
		db: db,
	}
}

// GetClassRooms 获取教室列表
func (h *ClassRoomHandler) GetClassRooms(c *gin.Context) {
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.ClassRoom{}).Preload("Store")

	// 门店筛选
	if req.StoreID != "" {
		if storeIDInt := cast.ToUint(req.StoreID); storeIDInt > 0 {
			query = query.Where("store_id = ?", storeIDInt)
		}
	}

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+req.Keyword+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var classrooms []model.ClassRoom
	err := query.Order("created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&classrooms).Error

	if err != nil {
		log.Error("查询教室失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教室失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     classrooms,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetStoreClassRooms 获取门店的教室列表
func (h *ClassRoomHandler) GetStoreClassRooms(c *gin.Context) {
	storeIDStr := c.Param("id") // 路由参数是 :id，不是 :store_id
	storeID := cast.ToUint(storeIDStr)
	if storeID == 0 {
		log.Error("门店ID格式错误", "store_id", storeIDStr, "converted", storeID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "门店ID格式错误"))
		return
	}

	// 验证门店是否存在
	var store model.Store
	if err := h.db.First(&store, uint(storeID)).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		return
	}

	// 查询教室列表
	var classrooms []model.ClassRoom
	if err := h.db.Where("store_id = ? AND status = 1", uint(storeID)).
		Order("name ASC").
		Find(&classrooms).Error; err != nil {
		log.Error("查询门店教室失败", "store_id", storeID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教室失败"))
		return
	}

	code.AutoResponse(c, classrooms, nil)
}

// GetClassRoom 获取教室详情
func (h *ClassRoomHandler) GetClassRoom(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教室ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var classroom model.ClassRoom
	if err := h.db.Preload("Store").First(&classroom, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教室不存在"))
		} else {
			log.Error("查询教室失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教室失败"))
		}
		return
	}

	code.AutoResponse(c, classroom, nil)
}

// CreateClassRoom 创建教室
func (h *ClassRoomHandler) CreateClassRoom(c *gin.Context) {
	var req dto.CreateClassRoomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建教室参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证门店是否存在
	var store model.Store
	if err := h.db.First(&store, req.StoreID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		return
	}

	// 检查同一门店下教室名称是否重复
	var count int64
	h.db.Model(&model.ClassRoom{}).
		Where("store_id = ? AND name = ?", req.StoreID, req.Name).
		Count(&count)
	if count > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该门店下已存在同名教室"))
		return
	}

	// 创建教室
	classroom := model.ClassRoom{
		StoreID:     req.StoreID,
		Name:        req.Name,
		Capacity:    req.Capacity,
		Equipment:   req.Equipment,
		Description: req.Description,
		Status:      1, // 默认可用
	}

	if err := h.db.Create(&classroom).Error; err != nil {
		log.Error("创建教室失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建教室失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Store").First(&classroom, classroom.ID)

	log.Info("创建教室成功", "id", classroom.ID, "name", classroom.Name, "store_id", req.StoreID)
	code.AutoResponse(c, classroom, nil)
}

// UpdateClassRoom 更新教室
func (h *ClassRoomHandler) UpdateClassRoom(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教室ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateClassRoomRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新教室参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询教室是否存在
	var classroom model.ClassRoom
	if err := h.db.First(&classroom, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教室不存在"))
		} else {
			log.Error("查询教室失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教室失败"))
		}
		return
	}

	// 检查教室名称是否重复（排除自己）
	if req.Name != "" {
		var count int64
		h.db.Model(&model.ClassRoom{}).
			Where("store_id = ? AND name = ? AND id != ?", classroom.StoreID, req.Name, id).
			Count(&count)
		if count > 0 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该门店下已存在同名教室"))
			return
		}
	}

	// 更新字段
	updates := dto.NewUpdateFields().
		SetIfNotEmpty("name", req.Name).
		SetIfNotNil("capacity", req.Capacity).
		SetIfNotEmpty("equipment", req.Equipment).
		SetIfNotEmpty("description", req.Description).
		SetIfNotNil("status", req.Status)

	if !updates.HasFields() {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "没有需要更新的字段"))
		return
	}

	if err := h.db.Model(&classroom).Updates(updates.GetFields()).Error; err != nil {
		log.Error("更新教室失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新教室失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("Store").First(&classroom, id)

	log.Info("更新教室成功", "id", id)
	code.AutoResponse(c, classroom, nil)
}

// DeleteClassRoom 删除教室
func (h *ClassRoomHandler) DeleteClassRoom(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教室ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 检查是否有关联的排课
	var scheduleCount int64
	h.db.Model(&model.ClassSchedule{}).Where("class_room_id = ?", id).Count(&scheduleCount)
	if scheduleCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该教室下还有排课记录，无法删除"))
		return
	}

	// 删除教室
	if err := h.db.Delete(&model.ClassRoom{}, id).Error; err != nil {
		log.Error("删除教室失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除教室失败"))
		return
	}

	log.Info("删除教室成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}
