package handler

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// CachedMenuHandler 带缓存的菜单处理器
type CachedMenuHandler struct {
	db           *gorm.DB
	redis        *redis.Client
	cacheService *service.PermissionCacheService
	menuService  *service.MenuService
}

// NewCachedMenuHandler 创建带缓存的菜单处理器
func NewCachedMenuHandler(db *gorm.DB, redis *redis.Client, menuService *service.MenuService) *CachedMenuHandler {
	return &CachedMenuHandler{
		db:           db,
		redis:        redis,
		cacheService: service.NewPermissionCacheService(db, redis),
		menuService:  menuService,
	}
}

// GetUserMenusWithCache 获取用户菜单（带缓存）
func (h *CachedMenuHandler) GetUserMenusWithCache(c *gin.Context) {
	ctx := context.Background()

	// 1. 获取用户ID
	userIDInterface, exists := c.Get("user_id")
	if !exists {
		log.Warn("用户未登录")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	userID := userIDInterface.(string)

	// 2. 从缓存获取用户角色
	userRoles, err := h.cacheService.GetUserRoles(ctx, userID)
	if err != nil {
		log.Error("获取用户角色失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取用户角色失败"))
		return
	}

	// 3. 如果用户没有角色，直接返回空菜单
	if len(userRoles) == 0 {
		log.Info("用户无有效角色，返回空菜单", "user_id", userID)
		code.AutoResponse(c, []*dto.MenuTreeResponse{}, nil)
		return
	}

	log.Debug("开始获取用户菜单", "user_id", userID, "user_roles", userRoles)

	// 4. 尝试从缓存获取用户菜单
	accessibleMenus, err := h.getUserMenusFromCacheOrDB(ctx, userID, userRoles)
	if err != nil {
		log.Error("获取用户菜单失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取菜单失败"))
		return
	}

	log.Info("用户菜单获取完成",
		"user_id", userID,
		"user_roles", userRoles,
		"accessible_menus", len(accessibleMenus))

	code.AutoResponse(c, accessibleMenus, nil)
}

// getUserMenusFromCacheOrDB 从缓存或数据库获取用户菜单
func (h *CachedMenuHandler) getUserMenusFromCacheOrDB(ctx context.Context, userID string, userRoles []string) ([]*dto.MenuTreeResponse, error) {
	// 1. 尝试从缓存获取
	cacheKey := fmt.Sprintf("auth:user:menus:%s", userID)
	cached, err := h.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		var menus []*dto.MenuTreeResponse
		if err := json.Unmarshal([]byte(cached), &menus); err == nil {
			log.Debug("用户菜单缓存命中", "user_id", userID, "menu_count", len(menus))
			return menus, nil
		}
	}

	// 2. 缓存未命中，从数据库构建菜单
	log.Debug("用户菜单缓存未命中，从数据库构建", "user_id", userID)

	// 2.1 获取所有启用的菜单
	allMenus, err := h.cacheService.GetAllMenus(ctx)
	if err != nil {
		return nil, err
	}

	if len(allMenus) == 0 {
		log.Warn("系统中没有启用的菜单")
		return []*dto.MenuTreeResponse{}, nil
	}

	// 2.2 构建完整的菜单树
	allMenuTree := h.buildMenuTreeResponse(allMenus, 0)

	// 2.3 根据用户权限过滤菜单
	accessibleMenus := h.menuService.GetUserAccessibleMenus(allMenuTree, userRoles)

	// 2.4 缓存结果
	if data, err := json.Marshal(accessibleMenus); err == nil {
		h.redis.Set(ctx, cacheKey, data, 15*time.Minute) // 缓存15分钟
		log.Debug("用户菜单已缓存", "user_id", userID, "menu_count", len(accessibleMenus))
	}

	return accessibleMenus, nil
}

// buildMenuTreeResponse 构建菜单树响应（复用原有逻辑）
func (h *CachedMenuHandler) buildMenuTreeResponse(menus []model.Menu, parentID uint) []*dto.MenuTreeResponse {
	var result []*dto.MenuTreeResponse

	for _, menu := range menus {
		if menu.ParentID == parentID {
			menuResponse := &dto.MenuTreeResponse{
				ID:            menu.ID,
				Title:         menu.Title,
				Path:          menu.Path,
				Component:     menu.Component,
				Redirect:      menu.Redirect,
				Type:          int(menu.Type),
				Icon:          menu.Icon,
				SvgIcon:       menu.SvgIcon,
				ParentID:      menu.ParentID,
				Sort:          menu.Sort,
				Hidden:        menu.Hidden,
				KeepAlive:     menu.KeepAlive,
				Breadcrumb:    menu.Breadcrumb,
				ShowInTabs:    menu.ShowInTabs,
				AlwaysShow:    menu.AlwaysShow,
				Affix:         menu.Affix,
				ActiveMenu:    menu.ActiveMenu,
				Status:        menu.Status,
				PermissionKey: menu.PermissionKey,
				AccessType:    menu.GetAccessibleMenuType(),
				Children:      h.buildMenuTreeResponse(menus, menu.ID),
			}
			result = append(result, menuResponse)
		}
	}

	return result
}

// InvalidateUserMenuCache 清除用户菜单缓存
func (h *CachedMenuHandler) InvalidateUserMenuCache(c *gin.Context) {
	ctx := context.Background()

	// 获取用户ID参数
	userID := c.Param("user_id")
	if userID == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "用户ID不能为空"))
		return
	}

	// 清除用户缓存
	err := h.cacheService.InvalidateUserCache(ctx, userID)
	if err != nil {
		log.Error("清除用户缓存失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "清除缓存失败"))
		return
	}

	log.Info("用户菜单缓存已清除", "user_id", userID)
	code.AutoResponse(c, gin.H{"message": "缓存清除成功"}, nil)
}

// InvalidateAllMenuCache 清除所有菜单缓存
func (h *CachedMenuHandler) InvalidateAllMenuCache(c *gin.Context) {
	ctx := context.Background()

	// 清除所有菜单缓存
	err := h.cacheService.InvalidateAllMenusCache(ctx)
	if err != nil {
		log.Error("清除所有菜单缓存失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "清除缓存失败"))
		return
	}

	log.Info("所有菜单缓存已清除")
	code.AutoResponse(c, gin.H{"message": "所有菜单缓存清除成功"}, nil)
}

// WarmUpMenuCache 预热菜单缓存
func (h *CachedMenuHandler) WarmUpMenuCache(c *gin.Context) {
	ctx := context.Background()

	// 预热缓存
	err := h.cacheService.WarmUpCache(ctx)
	if err != nil {
		log.Error("预热缓存失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "预热缓存失败"))
		return
	}

	log.Info("菜单缓存预热完成")
	code.AutoResponse(c, gin.H{"message": "缓存预热成功"}, nil)
}

// GetCacheStats 获取缓存统计信息
func (h *CachedMenuHandler) GetCacheStats(c *gin.Context) {
	ctx := context.Background()

	// 获取Redis统计信息
	info, err := h.redis.Info(ctx, "memory").Result()
	if err != nil {
		log.Error("获取Redis信息失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "获取缓存统计失败"))
		return
	}

	// 获取权限相关缓存键的数量
	userRoleKeys, _ := h.redis.Keys(ctx, "auth:user:roles:*").Result()
	userMenuKeys, _ := h.redis.Keys(ctx, "auth:user:menus:*").Result()
	permissionKeys, _ := h.redis.Keys(ctx, "auth:permission:*").Result()

	stats := gin.H{
		"redis_info": info,
		"cache_keys": gin.H{
			"user_roles_count": len(userRoleKeys),
			"user_menus_count": len(userMenuKeys),
			"permission_count": len(permissionKeys),
		},
	}

	code.AutoResponse(c, stats, nil)
}
