package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// UserStoreAssignmentHandler 用户门店分配处理器
type UserStoreAssignmentHandler struct {
	db *gorm.DB
}

// NewUserStoreAssignmentHandler 创建用户门店分配处理器实例
func NewUserStoreAssignmentHandler(db *gorm.DB) *UserStoreAssignmentHandler {
	return &UserStoreAssignmentHandler{
		db: db,
	}
}

// AssignUserToStore 分配用户到门店
func (h *UserStoreAssignmentHandler) AssignUserToStore(c *gin.Context) {
	var req dto.AssignUserToStoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定分配用户到门店参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 获取当前用户ID
	currentUserID, exists := c.Get("user_id")
	if !exists {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 验证用户是否存在且为管理端用户
	var user model.User
	err := h.db.Where("id = ? AND platform = ?", req.UserID, enum.PlatformAdmin).First(&user).Error
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在或不是管理端用户"))
		return
	}

	// 验证门店是否存在
	var store model.Store
	if err := h.db.First(&store, req.StoreID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		return
	}

	// 检查是否已经分配
	var existingAssignment model.UserStoreAssignment
	err = h.db.Where("user_id = ? AND store_id = ? AND status = 1", req.UserID, req.StoreID).First(&existingAssignment).Error
	if err == nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "用户已经分配到该门店"))
		return
	}

	// 创建分配记录
	assignment := model.UserStoreAssignment{
		UserID:     req.UserID,
		StoreID:    req.StoreID,
		AssignedBy: currentUserID.(string),
		AssignedAt: time.Now(),
		Remark:     req.Remark,
		Status:     1,
	}

	if err := h.db.Create(&assignment).Error; err != nil {
		log.Error("分配用户到门店失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "分配失败"))
		return
	}

	log.Info("分配用户到门店成功", "user_id", req.UserID, "store_id", req.StoreID)
	response := dto.MessageResponse{Message: "分配成功"}
	code.AutoResponse(c, response, nil)
}

// GetUserStoreAssignments 获取用户门店分配列表
func (h *UserStoreAssignmentHandler) GetUserStoreAssignments(c *gin.Context) {
	userIDStr := c.Param("user_id")
	// 现在userID是UUID字符串，不需要转换
	userID := userIDStr

	var assignments []model.UserStoreAssignment
	err := h.db.Preload("Store").Preload("AssignedByUser").
		Where("user_id = ? AND status = 1", userID).
		Find(&assignments).Error

	if err != nil {
		log.Error("查询用户门店分配失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	code.AutoResponse(c, assignments, nil)
}

// GetStoreUsers 获取门店的用户列表
func (h *UserStoreAssignmentHandler) GetStoreUsers(c *gin.Context) {
	storeIDStr := c.Param("store_id")
	storeID := cast.ToUint(storeIDStr)
	if storeID == 0 {
		log.Error("门店ID格式错误", "id", storeIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var assignments []model.UserStoreAssignment
	err := h.db.Preload("User").Preload("User.Roles").
		Where("store_id = ? AND status = 1", storeID).
		Find(&assignments).Error

	if err != nil {
		log.Error("查询门店用户列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	code.AutoResponse(c, assignments, nil)
}

// RemoveUserStoreAssignment 移除用户门店分配
func (h *UserStoreAssignmentHandler) RemoveUserStoreAssignment(c *gin.Context) {
	var req dto.RemoveUserStoreAssignmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定移除分配参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 将分配设为无效
	err := h.db.Model(&model.UserStoreAssignment{}).
		Where("user_id = ? AND store_id = ? AND status = 1", req.UserID, req.StoreID).
		Update("status", 0).Error

	if err != nil {
		log.Error("移除用户门店分配失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "移除分配失败"))
		return
	}

	log.Info("移除用户门店分配成功", "user_id", req.UserID, "store_id", req.StoreID)
	response := dto.MessageResponse{Message: "移除成功"}
	code.AutoResponse(c, response, nil)
}

// UpdateUserPlatformAccess 更新用户平台访问权限
func (h *UserStoreAssignmentHandler) UpdateUserPlatformAccess(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID := cast.ToUint(userIDStr)
	if userID == 0 {
		log.Error("用户ID格式错误", "id", userIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateUserPlatformAccessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新平台访问权限参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询用户
	var user model.User
	if err := h.db.First(&user, userID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		return
	}

	// 更新平台访问权限
	updates := map[string]interface{}{
		"platform_access": req.PlatformAccess,
	}

	if err := h.db.Model(&user).Updates(updates).Error; err != nil {
		log.Error("更新用户平台访问权限失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新失败"))
		return
	}

	// 重新查询用户信息
	h.db.Preload("Roles").First(&user, userID)

	log.Info("更新用户平台访问权限成功", "user_id", userID, "platform_access", req.PlatformAccess)
	code.AutoResponse(c, user, nil)
}

// GetUsersByPlatform 根据平台获取用户列表
func (h *UserStoreAssignmentHandler) GetUsersByPlatform(c *gin.Context) {
	platform := c.Query("platform")

	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()
	search := c.Query("search")

	// 构建查询
	query := h.db.Model(&model.User{}).Preload("Roles")

	// 平台筛选
	if platform != "" {
		// 支持查询可以访问指定平台的用户
		query = query.Where("platform_access LIKE ?", "%"+platform+"%")
	}

	// 搜索条件
	if search != "" {
		query = query.Where("nick_name LIKE ? OR username LIKE ? OR phone LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 查询数据
	var users []model.User
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).
		Order("created_at DESC").
		Find(&users).Error

	if err != nil {
		log.Error("查询用户列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询用户列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     users,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}
