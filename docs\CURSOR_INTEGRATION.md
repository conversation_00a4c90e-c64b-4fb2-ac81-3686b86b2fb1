# 🤖 Cursor AI 集成指南

## 📋 概述

本文档说明了如何在 Yogaga 项目中使用 Cursor AI 工具，以及项目中配置的 AI 规则文件。

## 🎯 Cursor 规则文件配置

为了确保 Cursor AI 能够正确理解和遵循项目的开发规范，我们配置了两个规则文件：

### 1. `.cursorrules` (传统格式)
- **位置**: 项目根目录
- **用途**: 兼容旧版本的 Cursor
- **格式**: Markdown 格式的规则文档

### 2. `.cursor/rules` (新版本格式)
- **位置**: `.cursor/rules`
- **用途**: 新版本 Cursor 的标准位置
- **格式**: 与 `.cursorrules` 相同的内容

## 🔧 规则文件内容

两个文件包含相同的规则内容，涵盖以下方面：

### 核心架构说明
- Go + Gin + GORM + Casbin 技术栈
- RBAC 权限管理系统
- RESTful API 设计原则

### 强制性规则
1. **数据模型规范**
   - 必须使用 `BaseModel` 而不是 `gorm.Model`
   - 所有字段必须有 snake_case 的 JSON 标签
   - 敏感字段使用 `json:"-"` 隐藏

2. **API 响应格式**
   - 使用 `code.AutoResponse()` 统一响应
   - 禁止使用 `gin.H`（健康检查除外）
   - 错误处理使用标准格式

3. **包管理规范**
   - 禁止手动编辑 go.mod 等配置文件
   - 必须使用包管理器命令

### 代码模板
- 标准模型创建模板
- Handler 函数模板
- 错误处理模式

## 🎯 AI 工具使用效果

配置这些规则文件后，Cursor AI 将能够：

### ✅ 自动遵循的规范
- 创建新模型时自动使用 `BaseModel`
- 为所有字段添加正确的 JSON 标签
- 使用统一的 API 响应格式
- 遵循项目的错误处理模式
- 使用正确的文件组织结构

### ✅ 避免的常见错误
- 不会使用 `gorm.Model`
- 不会忘记添加 JSON 标签
- 不会使用 PascalCase 的 JSON 字段名
- 不会暴露敏感字段
- 不会使用 `gin.H` 进行 API 响应

## 📊 验证方法

### 自动检查
运行项目的规范检查脚本：
```bash
bash scripts/check_rules.sh
```

### 手动验证
检查 AI 生成的代码是否符合以下标准：

#### 模型检查清单
- [ ] 继承 `BaseModel`
- [ ] 所有字段有 `json` 标签
- [ ] JSON 标签使用 snake_case
- [ ] 敏感字段使用 `json:"-"`
- [ ] GORM 标签包含约束和注释

#### Handler 检查清单
- [ ] 使用 `code.AutoResponse()`
- [ ] 错误处理使用 `code.NewErrCodeMsg()`
- [ ] 包含适当的参数验证
- [ ] 使用结构化日志记录

## 🔄 规则文件维护

### 更新规则
当项目规范发生变化时，需要同时更新两个文件：
1. `.cursorrules`
2. `.cursor/rules`

### 版本控制
- 两个规则文件都应该纳入版本控制
- 确保团队成员都使用相同的规则
- 在 PR 中检查规则文件的一致性

## 🎯 最佳实践

### 开发者使用指南
1. **安装 Cursor**: 确保使用最新版本的 Cursor
2. **打开项目**: Cursor 会自动读取规则文件
3. **验证配置**: 使用 AI 生成代码时检查是否遵循规范
4. **运行检查**: 定期运行 `bash scripts/check_rules.sh`

### 团队协作
1. **统一工具**: 团队成员都使用 Cursor 或兼容的 AI 工具
2. **规范培训**: 确保团队了解项目规范
3. **代码审查**: 在 PR 中检查 AI 生成代码的质量
4. **持续改进**: 根据使用情况优化规则文件

## 📈 效果评估

### 预期收益
- **代码一致性**: 所有 AI 生成的代码都符合项目规范
- **开发效率**: 减少手动修正 AI 代码的时间
- **质量保证**: 自动避免常见的编码错误
- **团队协作**: 统一的代码风格和结构

### 成功指标
- 规范检查脚本通过率: 100%
- AI 生成代码的直接可用率: >90%
- 代码审查中规范问题数量: 显著减少
- 新功能开发速度: 提升

## 🔧 故障排除

### 常见问题

#### Cursor 没有读取规则文件
**解决方案**:
1. 检查文件是否存在于正确位置
2. 重启 Cursor 应用
3. 检查文件权限和格式

#### AI 生成的代码不符合规范
**解决方案**:
1. 检查规则文件内容是否完整
2. 在提示中明确引用规则
3. 使用更具体的指令

#### 规则文件冲突
**解决方案**:
1. 确保两个规则文件内容一致
2. 删除过时的规则文件
3. 重新生成规则文件

## 📝 总结

通过配置 Cursor AI 规则文件，Yogaga 项目实现了：

- ✅ **完全自动化的规范遵循**
- ✅ **一致的代码质量**
- ✅ **高效的开发体验**
- ✅ **团队协作标准化**

这些规则文件是项目开发规范的重要组成部分，确保了无论是人工编写还是 AI 生成的代码都能保持高质量和一致性。

---

**维护者**: Augment Agent  
**最后更新**: 2025-07-10  
**版本**: 1.0
