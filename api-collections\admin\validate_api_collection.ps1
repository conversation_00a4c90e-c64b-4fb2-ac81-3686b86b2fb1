# API集合验证脚本
# 验证admin_api.json中的API接口是否与路由器定义一致

param(
    [string]$ApiCollectionPath = ".\admin_api.json",
    [string]$RouterPath = "..\..\routers\router.go"
)

Write-Host "🔍 开始验证API集合..." -ForegroundColor Green

# 读取API集合文件
if (-not (Test-Path $ApiCollectionPath)) {
    Write-Host "❌ API集合文件不存在: $ApiCollectionPath" -ForegroundColor Red
    exit 1
}

$apiCollection = Get-Content $ApiCollectionPath -Raw | ConvertFrom-Json

# 读取路由器文件
if (-not (Test-Path $RouterPath)) {
    Write-Host "❌ 路由器文件不存在: $RouterPath" -ForegroundColor Red
    exit 1
}

$routerContent = Get-Content $RouterPath -Raw

# 提取API集合中的所有端点
$endpoints = @()
function Extract-Endpoints($folder) {
    foreach ($request in $folder.requests) {
        $endpoint = $request.endpoint -replace "<<baseURL>>", ""
        $method = $request.method
        $endpoints += @{
            Method = $method
            Path = $endpoint
            Name = $request.name
        }
    }

    foreach ($subfolder in $folder.folders) {
        Extract-Endpoints $subfolder
    }
}

foreach ($folder in $apiCollection.folders) {
    Extract-Endpoints $folder
}

Write-Host "📊 API集合统计:" -ForegroundColor Cyan
Write-Host "  总端点数: $($endpoints.Count)" -ForegroundColor White

# 按方法分组统计
$methodStats = $endpoints | Group-Object Method | Sort-Object Name
foreach ($group in $methodStats) {
    Write-Host "  $($group.Name): $($group.Count)" -ForegroundColor White
}

# 验证新增的课程管理端点
Write-Host "`n🎯 验证新增的课程管理端点:" -ForegroundColor Green

$expectedEndpoints = @(
    @{ Method = "POST"; Path = "/api/v1/admin/courses"; Name = "创建课程模板" },
    @{ Method = "POST"; Path = "/api/v1/admin/courses/schedule"; Name = "课程排课" },
    @{ Method = "POST"; Path = "/api/v1/admin/courses/batch"; Name = "批量创建课程" }
)

$missingEndpoints = @()
$foundEndpoints = @()

foreach ($expected in $expectedEndpoints) {
    $found = $endpoints | Where-Object {
        $_.Method -eq $expected.Method -and $_.Path -eq $expected.Path
    }

    if ($found) {
        $foundEndpoints += $expected
        Write-Host "  ✅ $($expected.Method) $($expected.Path) - $($found.Name)" -ForegroundColor Green
    } else {
        $missingEndpoints += $expected
        Write-Host "  ❌ $($expected.Method) $($expected.Path) - 缺失" -ForegroundColor Red
    }
}

# 验证路由器中是否定义了对应的路由
Write-Host "`n🔍 验证路由器定义:" -ForegroundColor Green

$routePatterns = @{
    "POST.*courses.*schedule" = "课程排课路由"
    "POST.*courses.*batch" = "批量创建课程路由"
    "POST.*courses[^/]*$" = "创建课程路由"
}

foreach ($pattern in $routePatterns.Keys) {
    if ($routerContent -match $pattern) {
        Write-Host "  ✅ $($routePatterns[$pattern]) - 已定义" -ForegroundColor Green
    } else {
        Write-Host "  ❌ $($routePatterns[$pattern]) - 未找到" -ForegroundColor Red
    }
}

# 验证请求体格式
Write-Host "`n📝 验证请求体格式:" -ForegroundColor Green

$courseScheduleRequests = $endpoints | Where-Object {
    $_.Path -eq "/api/v1/admin/courses/schedule" -and $_.Method -eq "POST"
}

if ($courseScheduleRequests) {
    Write-Host "  ✅ 课程排课请求已定义" -ForegroundColor Green

    # 查找对应的请求体
    $scheduleRequest = $null
    foreach ($folder in $apiCollection.folders) {
        foreach ($request in $folder.requests) {
            $cleanEndpoint = $request.endpoint -replace "<<baseURL>>", ""
            if ($cleanEndpoint -eq "/api/v1/admin/courses/schedule" -and $request.method -eq "POST") {
                $scheduleRequest = $request
                break
            }
        }
        if ($scheduleRequest) { break }
    }

    if ($scheduleRequest -and $scheduleRequest.body -and $scheduleRequest.body.body) {
        try {
            $requestBody = $scheduleRequest.body.body | ConvertFrom-Json

            # 验证必需字段
            $requiredFields = @("start_date", "end_date", "start_time", "store_id", "weekly_schedules")
            $missingFields = @()

            foreach ($field in $requiredFields) {
                if (-not $requestBody.PSObject.Properties[$field]) {
                    $missingFields += $field
                }
            }

            if ($missingFields.Count -eq 0) {
                Write-Host "  ✅ 请求体包含所有必需字段" -ForegroundColor Green
            } else {
                Write-Host "  ❌ 请求体缺少字段: $($missingFields -join ', ')" -ForegroundColor Red
            }

            # 验证weekly_schedules结构
            if ($requestBody.weekly_schedules -and $requestBody.weekly_schedules.Count -gt 0) {
                $schedule = $requestBody.weekly_schedules[0]
                $scheduleFields = @("weekday", "course_id", "coach_ids")
                $missingScheduleFields = @()

                foreach ($field in $scheduleFields) {
                    if (-not $schedule.PSObject.Properties[$field]) {
                        $missingScheduleFields += $field
                    }
                }

                if ($missingScheduleFields.Count -eq 0) {
                    Write-Host "  ✅ weekly_schedules结构正确" -ForegroundColor Green
                } else {
                    Write-Host "  ❌ weekly_schedules缺少字段: $($missingScheduleFields -join ', ')" -ForegroundColor Red
                }
            }

        } catch {
            Write-Host "  ❌ 请求体JSON格式错误: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 验证环境变量使用
Write-Host "`n🔧 验证环境变量使用:" -ForegroundColor Green

$envVarPattern = "<<(\w+)>>"
$usedVars = @()

foreach ($folder in $apiCollection.folders) {
    foreach ($request in $folder.requests) {
        if ($request.body -and $request.body.body) {
            $matches = [regex]::Matches($request.body.body, $envVarPattern)
            foreach ($match in $matches) {
                $varName = $match.Groups[1].Value
                if ($usedVars -notcontains $varName) {
                    $usedVars += $varName
                }
            }
        }
    }
}

$expectedVars = @("baseURL", "access_token", "course_template_id", "yoga_basic_template_id", "flow_yoga_template_id", "yin_yoga_template_id")
foreach ($var in $expectedVars) {
    if ($usedVars -contains $var) {
        Write-Host "  ✅ 环境变量 $var 已使用" -ForegroundColor Green
    } else {
        Write-Host "  ⚠️  环境变量 $var 未使用" -ForegroundColor Yellow
    }
}

# 生成验证报告
Write-Host "`n📋 验证报告:" -ForegroundColor Cyan
Write-Host "  找到的端点: $($foundEndpoints.Count)/$($expectedEndpoints.Count)" -ForegroundColor White
Write-Host "  缺失的端点: $($missingEndpoints.Count)" -ForegroundColor White

if ($missingEndpoints.Count -eq 0) {
    Write-Host "`n🎉 验证通过！所有API端点都已正确定义。" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ 验证失败！存在缺失的API端点。" -ForegroundColor Red
    exit 1
}
