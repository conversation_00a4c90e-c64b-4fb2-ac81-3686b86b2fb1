# 会员卡管理系统数据库字段说明文档

## 📋 **概览**

会员卡管理系统包含三个核心数据表：
- `membership_card_types` - 会员卡类型定义表
- `membership_cards` - 会员卡实例表  
- `membership_card_stores` - 会员卡门店关联表

---

## 🎯 **1. 会员卡类型表 (membership_card_types)**

### 基础字段

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `id` | uint | 主键ID | 1 | 自增主键 |
| `created_at` | timestamp | 创建时间 | 2024-01-01 10:00:00 | 自动生成 |
| `updated_at` | timestamp | 更新时间 | 2024-01-01 10:00:00 | 自动更新 |
| `deleted_at` | timestamp | 删除时间 | null | 软删除标记 |

### 卡种基本信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `name` | varchar(100) | 卡种名称 | "Yogaga瑜伽团课卡" | 必填，显示名称 |
| `category` | varchar(20) | 卡种类别 | "times" | times/period/balance |
| `description` | text | 卡种描述 | "瑜伽团课专用次数卡" | 详细说明 |

**卡种类别说明**：
- `times` - 次数卡：按使用次数扣减
- `period` - 期限卡：在有效期内无限次使用
- `balance` - 储值卡：按金额扣减

### 价格相关

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `price` | int | 售价(分) | 150000 | 1500元，存储为分 |
| `original_price` | int | 原价(分) | 180000 | 1800元，用于显示优惠 |
| `discount` | decimal(3,2) | 折扣率 | 0.85 | 0.85表示85折 |

### 卡种规格

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `validity_days` | int | 有效天数 | 365 | 0表示永久有效 |
| `times` | int | 次数 | 10 | 次数卡专用，0表示无限次 |
| `amount` | int | 储值金额(分) | 100000 | 储值卡专用，1000元 |

### 业务规则

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `transfer_limit` | int | 转让次数限制 | 1 | 0表示不可转让 |
| `freeze_limit` | int | 冻结次数限制 | 2 | 0表示不可冻结 |
| `refundable` | boolean | 是否可退款 | false | true/false |
| `shareable` | boolean | 是否可共享 | true | 共享储值卡功能 |

### 功能开关

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `can_set_expiry` | boolean | 可设置到期时间 | true | 开卡时是否可自定义到期时间 |
| `can_select_stores` | boolean | 可选择使用门店 | true | 是否支持指定门店使用 |
| `can_add_sub_card` | boolean | 可添加副卡 | false | 是否支持主副卡功能 |
| `can_set_daily_limit` | boolean | 可设置单日限制 | false | 是否支持单日预约限制 |

### 预约限制

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `daily_booking_limit` | int | 单日预约限制 | 2 | 0表示无限制 |

### 课程规则

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `course_deduct_rules` | text | 课程扣减规则 | JSON格式 | 不同课程的扣减规则 |
| `applicable_courses` | text | 适用课程 | JSON格式 | 可使用的课程列表 |

### 显示控制

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `sort_order` | int | 排序序号 | 1 | 显示顺序，数字越小越靠前 |
| `status` | int | 状态 | 1 | 1:启用 0:停用 |

---

## 💳 **2. 会员卡实例表 (membership_cards)**

### 基础字段

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `id` | uint | 主键ID | 1001 | 自增主键 |
| `created_at` | timestamp | 创建时间 | 2024-01-01 10:00:00 | 开卡时间 |
| `updated_at` | timestamp | 更新时间 | 2024-01-01 10:00:00 | 最后操作时间 |
| `deleted_at` | timestamp | 删除时间 | null | 软删除标记 |

### 关联信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `user_id` | char(36) | 用户ID | "uuid-string" | 卡片持有者 |
| `card_type_id` | uint | 卡种ID | 5 | 关联卡种定义 |
| `card_number` | varchar(50) | 卡号 | "YG20240101001" | 唯一卡号 |

### 主副卡关系

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `main_card_id` | uint | 主卡ID | 1001 | 副卡专用，指向主卡 |
| `is_main_card` | boolean | 是否为主卡 | true | true:主卡 false:副卡 |

### 余额信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `remaining_times` | int | 剩余次数 | 8 | 次数卡当前剩余次数 |
| `remaining_amount` | int | 剩余金额(分) | 50000 | 储值卡剩余金额，500元 |
| `total_times` | int | 总次数 | 10 | 次数卡原始总次数 |
| `total_amount` | int | 总金额(分) | 100000 | 储值卡原始总金额，1000元 |

### 时间信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `start_date` | date | 开始日期 | 2024-01-01 | 卡片生效日期 |
| `end_date` | date | 结束日期 | 2024-12-31 | 卡片到期日期 |
| `activated_at` | timestamp | 激活时间 | 2024-01-01 10:00:00 | 首次使用时间 |

### 购买信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `purchase_price` | int | 购买价格(分) | 150000 | 实际支付金额，1500元 |
| `discount` | decimal(3,2) | 购买折扣 | 0.85 | 开卡时的折扣率 |

### 使用限制

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `available_stores` | text | 可用门店 | JSON格式 | 可使用的门店ID列表 |
| `daily_booking_limit` | int | 单日预约限制 | 2 | 每日最多预约次数 |

### 状态信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `status` | smallint | 卡片状态 | 1 | 1:正常 2:冻结 3:过期 4:用完 |
| `remark` | text | 备注 | "生日礼品卡" | 管理员备注信息 |

**卡片状态说明**：
- `1` - 正常：可正常使用
- `2` - 冻结：暂停使用，可解冻
- `3` - 过期：超过有效期
- `4` - 用完：次数或金额已用完

---

## 🏪 **3. 会员卡门店关联表 (membership_card_stores)**

### 基础字段

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `id` | uint | 主键ID | 1 | 自增主键 |
| `created_at` | timestamp | 创建时间 | 2024-01-01 10:00:00 | 关联创建时间 |
| `updated_at` | timestamp | 更新时间 | 2024-01-01 10:00:00 | 关联更新时间 |
| `deleted_at` | timestamp | 删除时间 | null | 软删除标记 |

### 关联信息

| 字段名 | 类型 | 说明 | 示例值 | 备注 |
|--------|------|------|--------|------|
| `card_id` | uint | 会员卡ID | 1001 | 关联具体会员卡 |
| `store_id` | uint | 门店ID | 5 | 关联具体门店 |

---

## 📊 **4. 业务逻辑说明**

### 卡种类别业务逻辑

#### 次数卡 (times)
- 主要字段：`times`（总次数）
- 扣减方式：每次使用扣减 `remaining_times`
- 到期条件：`remaining_times = 0` 或超过 `end_date`

#### 期限卡 (period)  
- 主要字段：`validity_days`（有效天数）
- 使用方式：有效期内无限次使用
- 到期条件：超过 `end_date`

#### 储值卡 (balance)
- 主要字段：`amount`（储值金额）
- 扣减方式：按课程价格扣减 `remaining_amount`
- 到期条件：`remaining_amount = 0` 或超过 `end_date`

### 主副卡业务逻辑

#### 主卡特权
- `is_main_card = true`
- 可查看所有副卡使用记录
- 可为副卡代约课程
- 余额/次数统一管理

#### 副卡限制
- `is_main_card = false`
- `main_card_id` 指向主卡
- 只能查看自己的使用记录
- 共享主卡的余额/次数

### 门店限制逻辑

#### 全市通用
- `available_stores` 为空或包含所有门店ID
- 可在任意门店使用

#### 指定门店
- `available_stores` 包含特定门店ID列表
- 只能在指定门店使用

---

## 🔧 **5. 常用查询示例**

### 获取用户的有效会员卡
```sql
SELECT mc.*, mct.name as card_type_name, mct.category
FROM membership_cards mc
JOIN membership_card_types mct ON mc.card_type_id = mct.id
WHERE mc.user_id = 'user-uuid' 
  AND mc.status = 1 
  AND (mc.end_date IS NULL OR mc.end_date > NOW())
  AND (mc.remaining_times > 0 OR mc.remaining_amount > 0 OR mct.category = 'period');
```

### 获取启用的卡种列表
```sql
SELECT * FROM membership_card_types 
WHERE status = 1 
ORDER BY sort_order, id;
```

### 获取主卡及其所有副卡
```sql
SELECT * FROM membership_cards 
WHERE id = :main_card_id OR main_card_id = :main_card_id
ORDER BY is_main_card DESC, created_at;
```

---

## 💡 **6. 最佳实践建议**

### 数据完整性
1. 确保 `card_number` 的唯一性
2. 副卡的 `main_card_id` 必须指向有效的主卡
3. 金额字段统一使用分为单位，避免浮点数精度问题

### 性能优化
1. 在 `user_id`, `card_type_id`, `status` 字段上建立索引
2. 在 `card_number` 上建立唯一索引
3. 定期清理软删除的数据

### 业务规则
1. 停用的卡种不应出现在前端选择列表中
2. 过期或用完的卡片应自动更新状态
3. 主副卡的余额操作需要事务保证一致性

---

## 📝 **7. 更新日志**

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| v1.0 | 2024-01-01 | 初始版本，基础会员卡功能 |
| v2.0 | 2024-07-18 | 增加主副卡功能、门店选择、单日限制等高级功能 |
