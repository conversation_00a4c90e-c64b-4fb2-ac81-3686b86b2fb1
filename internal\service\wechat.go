package service

import (
	"fmt"
	"yogaga/configs"

	"github.com/charmbracelet/log"
	"github.com/silenceper/wechat/v2"
	"github.com/silenceper/wechat/v2/cache"
	"github.com/silenceper/wechat/v2/miniprogram"
	miniConfig "github.com/silenceper/wechat/v2/miniprogram/config"
	"github.com/silenceper/wechat/v2/miniprogram/subscribe"
)

// WeChatService 微信小程序服务
type WeChatService struct {
	miniProgram *miniprogram.MiniProgram
	config      configs.WeChat
}

// NewWeChatService 创建微信小程序服务实例
func NewWeChatService(config configs.WeChat) *WeChatService {
	// 使用内存缓存
	memory := cache.NewMemory()

	// 创建微信配置
	cfg := &miniConfig.Config{
		AppID:     config.AppID,
		AppSecret: config.AppSecret,
		Cache:     memory,
	}

	// 创建微信实例
	wc := wechat.NewWechat()
	miniProgram := wc.GetMiniProgram(cfg)

	return &WeChatService{
		miniProgram: miniProgram,
		config:      config,
	}
}

// Code2SessionResponse 微信 code2session 接口响应
type Code2SessionResponse struct {
	OpenID     string `json:"openid"`      // 用户唯一标识
	SessionKey string `json:"session_key"` // 会话密钥
	UnionID    string `json:"unionid"`     // 用户在开放平台的唯一标识符，若当前小程序已绑定到微信开放平台帐号下会返回
}

// Code2Session 通过 code 获取 session_key 和 openid
func (s *WeChatService) Code2Session(code string) (*Code2SessionResponse, error) {
	log.Debug("调用微信 code2session 接口", "code", code)

	// 使用 SDK 调用 code2session 接口
	result, err := s.miniProgram.GetAuth().Code2Session(code)
	if err != nil {
		log.Error("调用微信 code2session 接口失败", "error", err)
		return nil, fmt.Errorf("调用微信接口失败: %w", err)
	}

	// 检查必要字段
	if result.OpenID == "" {
		log.Error("微信接口未返回 openid", "response", result)
		return nil, fmt.Errorf("微信接口未返回有效的 openid")
	}

	log.Info("微信 code2session 成功", "openid", result.OpenID)

	// 转换为我们的响应结构
	response := &Code2SessionResponse{
		OpenID:     result.OpenID,
		SessionKey: result.SessionKey,
		UnionID:    result.UnionID,
	}

	return response, nil
}

// GetAccessToken 获取小程序全局唯一后台接口调用凭据
func (s *WeChatService) GetAccessToken() (string, error) {
	token, err := s.miniProgram.GetAuth().GetAccessToken()
	if err != nil {
		log.Error("获取 access_token 失败", "error", err)
		return "", fmt.Errorf("获取 access_token 失败: %w", err)
	}

	return token, nil
}

// SendTemplateMessage 发送模板消息
func (s *WeChatService) SendTemplateMessage(openID, templateID string, data map[string]interface{}, page string) error {
	// 获取订阅消息实例
	subscribeMsg := s.miniProgram.GetSubscribe()

	// 构建模板消息数据 - 使用正确的 SDK 结构
	templateData := make(map[string]*subscribe.DataItem)
	for key, value := range data {
		templateData[key] = &subscribe.DataItem{
			Value: fmt.Sprintf("%v", value),
		}
	}

	// 构建消息
	message := &subscribe.Message{
		ToUser:           openID,
		TemplateID:       templateID,
		Page:             page,
		Data:             templateData,
		MiniprogramState: "formal", // 正式版
		Lang:             "zh_CN",  // 简体中文
	}

	// 发送订阅消息
	err := subscribeMsg.Send(message)
	if err != nil {
		log.Error("发送订阅消息失败", "openid", openID, "template_id", templateID, "error", err)
		return fmt.Errorf("发送订阅消息失败: %w", err)
	}

	log.Info("订阅消息发送成功", "openid", openID, "template_id", templateID)
	return nil
}

// SendCourseReminder 发送开课提醒
func (s *WeChatService) SendCourseReminder(openID, courseName, startTime, storeName string) error {
	data := map[string]interface{}{
		"thing1":  courseName, // 课程名称
		"time2":   startTime,  // 开课时间
		"thing3":  storeName,  // 门店名称
		"phrase4": "即将开始",     // 状态
	}

	page := "pages/booking/detail"
	return s.SendTemplateMessage(openID, s.config.CourseReminderTemplateID, data, page)
}

// SendBookingCancelNotice 发送预约取消通知
func (s *WeChatService) SendBookingCancelNotice(openID, courseName, cancelTime, reason string) error {
	data := map[string]interface{}{
		"thing1": courseName, // 课程名称
		"time2":  cancelTime, // 取消时间
		"thing3": reason,     // 取消原因
	}

	page := "pages/booking/list"
	return s.SendTemplateMessage(openID, s.config.CancelNoticeTemplateID, data, page)
}

// SendQueueNotification 发送排队转预约通知
func (s *WeChatService) SendQueueNotification(openID, courseName, availableTime string) error {
	data := map[string]interface{}{
		"thing1":  courseName,    // 课程名称
		"time2":   availableTime, // 可预约时间
		"phrase3": "有名额了",        // 状态
		"thing4":  "请尽快预约",       // 提醒内容
	}

	page := "pages/course/detail"
	return s.SendTemplateMessage(openID, s.config.QueueNotificationTemplateID, data, page)
}

// SendBookingConfirmation 发送预约成功通知
func (s *WeChatService) SendBookingConfirmation(openID, courseName, startTime, storeName, bookingNumber string) error {
	data := map[string]interface{}{
		"character_string1": bookingNumber, // 预约单号
		"thing2":            courseName,    // 课程名称
		"time3":             startTime,     // 课程时间
		"thing4":            storeName,     // 门店名称
		"phrase5":           "预约成功",        // 状态
	}

	page := "pages/booking/detail?id=" + bookingNumber
	return s.SendTemplateMessage(openID, s.config.BookingConfirmationTemplateID, data, page)
}

// SendCourseCancelNotice 发送课程取消通知（因人数不足）
func (s *WeChatService) SendCourseCancelNotice(openID, courseName, startTime, storeName, reason string) error {
	data := map[string]interface{}{
		"thing1":  courseName, // 课程名称
		"time2":   startTime,  // 课程时间
		"thing3":  storeName,  // 门店名称
		"thing4":  reason,     // 取消原因
		"phrase5": "已退款",      // 状态
	}

	page := "pages/booking/list"
	return s.SendTemplateMessage(openID, s.config.CourseCancelTemplateID, data, page)
}

// SendCardExpiryReminder 发送会员卡到期提醒
func (s *WeChatService) SendCardExpiryReminder(openID, cardTypeName, expiryDate, daysLeft string) error {
	data := map[string]interface{}{
		"thing1":  cardTypeName, // 会员卡类型
		"date2":   expiryDate,   // 到期日期
		"thing3":  daysLeft,     // 剩余天数
		"phrase4": "即将到期",       // 状态
		"thing5":  "请及时续费",      // 提醒内容
	}

	page := "pages/membership/list"
	return s.SendTemplateMessage(openID, s.config.CardExpiryReminderTemplateID, data, page)
}

// SendValidityDeductReminder 发送有效期扣除提醒
func (s *WeChatService) SendValidityDeductReminder(openID, cardTypeName, noShowCount, deductDays string) error {
	data := map[string]interface{}{
		"thing1":  cardTypeName, // 会员卡类型
		"thing2":  noShowCount,  // 未签到次数
		"thing3":  deductDays,   // 扣除天数
		"phrase4": "有效期扣除",      // 状态
		"thing5":  "请按时上课",      // 提醒内容
	}

	page := "pages/membership/detail"
	return s.SendTemplateMessage(openID, s.config.ValidityDeductReminderTemplateID, data, page)
}
