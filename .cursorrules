# Yogaga Project - Cursor AI Rules

You are an expert Go developer working on the Yogaga yoga studio management system. This project uses Go + Gin + GORM + <PERSON><PERSON>bin for a complete RBAC permission management system.

## Core Architecture

This is a Go web application with the following structure:

- **Framework**: Gin for HTTP routing
- **ORM**: GORM for database operations
- **Auth**: J<PERSON><PERSON> + <PERSON><PERSON>bin for RBAC permission control
- **Database**: MySQL with structured logging
- **API Style**: RESTful with consistent snake_case JSON responses

## Critical Rules - ALWAYS Follow

### 1. Data Models - MANDATORY BaseModel Usage

```go
// ✅ CORRECT - Always use BaseModel
type User struct {
    BaseModel
    Username string `json:"username" gorm:"type:varchar(50);not null"`
    Email    string `json:"email" gorm:"type:varchar(100)"`
    Password string `json:"-" gorm:"type:varchar(255);not null"` // Hide sensitive fields
}

// ❌ NEVER use gorm.Model directly
type User struct {
    gorm.Model // DON'T DO THIS
    Username string
}
```

**BaseModel Definition** (already exists in internal/model/vars.go):

```go
type BaseModel struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
```

### 2. JSON Tags - MANDATORY snake_case

- ALL model fields MUST have `json` tags in snake_case format
- Sensitive fields (passwords) MUST use `json:"-"`
- Association fields MUST have snake_case tags: `json:"user_roles"`

### 3. API Response Format - MANDATORY

```go
// ✅ CORRECT - Use code.AutoResponse
code.AutoResponse(c, data, nil)
code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorCode, "message"))

// ❌ NEVER use gin.H (except health checks)
c.JSON(200, gin.H{"data": data}) // DON'T DO THIS
```

### 4. Package Management - MANDATORY

- NEVER manually edit go.mod, package.json, requirements.txt
- ALWAYS use package managers: `go get`, `npm install`, `pip install`
- Let package managers handle version resolution and conflicts

## Model Creation Checklist

When creating ANY new model, verify:

- [ ] Inherits `BaseModel` (not `gorm.Model`)
- [ ] ALL fields have `json` tags in snake_case
- [ ] Sensitive fields use `json:"-"`
- [ ] GORM tags include proper constraints and comments
- [ ] Association fields have correct JSON tags
