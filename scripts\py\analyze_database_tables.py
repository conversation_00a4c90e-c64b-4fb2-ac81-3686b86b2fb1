#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析数据库表使用情况（基于代码分析，不需要数据库连接）
"""

import os
import re
from typing import Set, Dict, List

class DatabaseTableAnalyzer:
    def __init__(self, project_root: str = "."):
        self.project_root = project_root

        # 当前系统中使用的表（根据模型定义）
        self.used_tables = {
            # 核心业务表
            'users',
            'roles',
            'permissions',
            'menus',
            'stores',
            'files',

            # 课程相关
            'course_categories',
            'courses',
            # 'course_deduction_rules', # 已废弃，使用灵活扣减系统
            'course_type_configs',
            'flexible_deduction_rules',
            'class_rooms',
            'class_schedules',

            # 会员卡相关
            'membership_card_types',
            'membership_cards',
            'membership_card_transactions',
            'membership_card_leaves',

            # 预约相关
            'bookings',
            'booking_queues',
            'booking_applications',

            # 其他功能
            'admin_notifications',
            'banners',
            'coach_banners',
            'favorites',
            'operation_logs',

            # 分配关系表
            'user_store_assignments',
            'member_sales_assignments',
            'resource_assignments',

            # 系统表
            'migration_status',
        }

        # 已知的废弃表（可以安全删除）
        self.deprecated_tables = {
            # 旧的副卡相关表（已确认废弃）
            'membership_sub_cards',
            'membership_card_extensions',

            # 旧的测试表
            'test_tables',
            'temp_tables',

            # 其他已废弃的表
            'old_booking_system',
            'legacy_user_data',
        }

        # 可能废弃的表（需要确认）
        self.potentially_deprecated = {
            # Legacy表前缀
            'tbl_',

            # 临时表前缀
            'temp_',
            'tmp_',

            # 测试表前缀
            'test_',
        }

    def scan_model_files(self) -> Set[str]:
        """扫描模型文件，提取表名"""
        print("🔍 扫描模型文件...")

        found_tables = set()
        model_dir = os.path.join(self.project_root, "internal", "model")

        if not os.path.exists(model_dir):
            print(f"❌ 模型目录不存在: {model_dir}")
            return found_tables

        for filename in os.listdir(model_dir):
            if filename.endswith('.go') and not filename.startswith('legacy'):
                filepath = os.path.join(model_dir, filename)
                tables = self.extract_tables_from_file(filepath)
                found_tables.update(tables)
                if tables:
                    print(f"   📄 {filename}: {', '.join(tables)}")

        print(f"✅ 从模型文件中发现 {len(found_tables)} 个表")
        return found_tables

    def extract_tables_from_file(self, filepath: str) -> Set[str]:
        """从Go文件中提取表名"""
        tables = set()

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找 TableName() 方法
            table_name_pattern = r'func\s*\([^)]*\)\s*TableName\(\)\s*string\s*{\s*return\s*"([^"]+)"'
            matches = re.findall(table_name_pattern, content)
            tables.update(matches)

            # 查找 gorm 标签中的表名
            gorm_table_pattern = r'gorm:"[^"]*table:([^;"]+)[^"]*"'
            matches = re.findall(gorm_table_pattern, content)
            tables.update(matches)

        except Exception as e:
            print(f"❌ 读取文件失败 {filepath}: {e}")

        return tables

    def scan_migration_files(self) -> Set[str]:
        """扫描迁移文件，提取表名"""
        print("\n🔍 扫描迁移文件...")

        found_tables = set()

        # 扫描可能的迁移目录
        migration_dirs = [
            "internal/bootstrap",
            "migrations",
            "scripts",
        ]

        for migration_dir in migration_dirs:
            full_path = os.path.join(self.project_root, migration_dir)
            if os.path.exists(full_path):
                tables = self.scan_directory_for_tables(full_path)
                found_tables.update(tables)

        print(f"✅ 从迁移文件中发现 {len(found_tables)} 个表")
        return found_tables

    def scan_directory_for_tables(self, directory: str) -> Set[str]:
        """扫描目录中的文件，提取表名"""
        tables = set()

        for root, dirs, files in os.walk(directory):
            for filename in files:
                if filename.endswith(('.go', '.sql')):
                    filepath = os.path.join(root, filename)
                    file_tables = self.extract_tables_from_migration_file(filepath)
                    tables.update(file_tables)

        return tables

    def extract_tables_from_migration_file(self, filepath: str) -> Set[str]:
        """从迁移文件中提取表名"""
        tables = set()

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 查找 AutoMigrate 调用
            automigrate_pattern = r'AutoMigrate\([^)]*model\.(\w+)[^)]*\)'
            matches = re.findall(automigrate_pattern, content)

            # 转换为表名（驼峰转下划线）
            for match in matches:
                table_name = self.camel_to_snake(match)
                tables.add(table_name)

            # 查找直接的表名引用
            table_patterns = [
                r'CREATE TABLE[^(]*\s+(\w+)',
                r'DROP TABLE[^(]*\s+(\w+)',
                r'ALTER TABLE[^(]*\s+(\w+)',
                r'"([a-z_]+)".*table',
            ]

            for pattern in table_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                tables.update(matches)

        except Exception as e:
            print(f"❌ 读取迁移文件失败 {filepath}: {e}")

        return tables

    def camel_to_snake(self, name: str) -> str:
        """驼峰命名转下划线命名"""
        # 处理连续大写字母
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        # 处理其他大写字母
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def analyze_table_usage(self) -> Dict:
        """分析表的使用情况"""
        print("\n📊 分析表的使用情况...")

        # 从代码中发现的表
        model_tables = self.scan_model_files()
        migration_tables = self.scan_migration_files()

        # 合并所有发现的表
        all_discovered_tables = model_tables.union(migration_tables)

        analysis = {
            'used': [],           # 正在使用的表
            'deprecated': [],     # 已废弃的表
            'potentially_deprecated': [],  # 可能废弃的表
            'discovered': list(all_discovered_tables),  # 从代码中发现的表
        }

        # 分类表
        for table in all_discovered_tables:
            if table in self.used_tables:
                analysis['used'].append(table)
            elif table in self.deprecated_tables:
                analysis['deprecated'].append(table)
            elif any(table.startswith(prefix) for prefix in self.potentially_deprecated):
                analysis['potentially_deprecated'].append(table)
            else:
                # 新发现的表，添加到使用中的表
                analysis['used'].append(table)

        return analysis

    def generate_cleanup_recommendations(self, analysis: Dict) -> str:
        """生成清理建议"""
        print("\n📝 生成清理建议...")

        recommendations = []
        recommendations.append("# 数据库表清理建议")
        recommendations.append("# 基于代码分析生成")
        recommendations.append("")

        # 正在使用的表
        recommendations.append("## ✅ 正在使用的表")
        recommendations.append(f"共 {len(analysis['used'])} 个表，建议保留：")
        for table in sorted(analysis['used']):
            recommendations.append(f"- {table}")
        recommendations.append("")

        # 已废弃的表
        if analysis['deprecated']:
            recommendations.append("## 🗑️ 已确认废弃的表")
            recommendations.append(f"共 {len(analysis['deprecated'])} 个表，可以安全删除：")
            for table in sorted(analysis['deprecated']):
                recommendations.append(f"- {table}")
            recommendations.append("")

            recommendations.append("### 删除SQL（已废弃的表）：")
            for table in sorted(analysis['deprecated']):
                recommendations.append(f'DROP TABLE IF EXISTS "{table}";')
            recommendations.append("")

        # 可能废弃的表
        if analysis['potentially_deprecated']:
            recommendations.append("## ⚠️ 可能废弃的表")
            recommendations.append(f"共 {len(analysis['potentially_deprecated'])} 个表，需要手动确认：")
            for table in sorted(analysis['potentially_deprecated']):
                recommendations.append(f"- {table}")
            recommendations.append("")

            recommendations.append("### 删除SQL（需确认后执行）：")
            for table in sorted(analysis['potentially_deprecated']):
                recommendations.append(f'-- DROP TABLE IF EXISTS "{table}";  -- 请确认后取消注释')
            recommendations.append("")

        # 发现的新表
        new_tables = set(analysis['discovered']) - self.used_tables - self.deprecated_tables
        if new_tables:
            recommendations.append("## 🆕 新发现的表")
            recommendations.append(f"共 {len(new_tables)} 个表，请确认用途：")
            for table in sorted(new_tables):
                recommendations.append(f"- {table}")
            recommendations.append("")

        return "\n".join(recommendations)

    def run_analysis(self):
        """运行完整分析"""
        print("🧹 开始数据库表使用情况分析...")
        print("=" * 80)

        # 分析表使用情况
        analysis = self.analyze_table_usage()

        # 生成清理建议
        recommendations = self.generate_cleanup_recommendations(analysis)

        # 保存建议到文件
        output_file = "scripts/database_cleanup_recommendations.md"
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(recommendations)
            print(f"\n📄 清理建议已保存到: {output_file}")
        except Exception as e:
            print(f"❌ 保存建议文件失败: {e}")

        # 打印摘要
        print("\n" + "=" * 80)
        print("📊 分析摘要")
        print("=" * 80)
        print(f"✅ 正在使用的表: {len(analysis['used'])} 个")
        print(f"🗑️ 已废弃的表: {len(analysis['deprecated'])} 个")
        print(f"⚠️ 可能废弃的表: {len(analysis['potentially_deprecated'])} 个")
        print(f"🔍 从代码发现的表: {len(analysis['discovered'])} 个")

        print(f"\n💡 建议:")
        print(f"1. 检查生成的建议文件: {output_file}")
        print(f"2. 确认可能废弃的表的用途")
        print(f"3. 备份数据库后执行清理SQL")

        return True

def main():
    """主函数"""
    analyzer = DatabaseTableAnalyzer()
    success = analyzer.run_analysis()

    if success:
        print("\n🏆 数据库表分析完成！")
        exit(0)
    else:
        print("\n💥 数据库表分析失败！")
        exit(1)

if __name__ == "__main__":
    main()
