#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建缺失的数据库表
手动创建 membership_card_transactions 表
"""

import psycopg2
from psycopg2 import sql

def create_missing_tables():
    """创建缺失的数据库表"""

    # 数据库连接配置
    db_config = {
        'host': '************',
        'port': 5432,
        'database': 'yogaga',
        'user': 'yogaga',
        'password': 'QjZ2xyD42bPYKTyx'
    }

    try:
        # 连接数据库
        print("🔗 连接数据库...")
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()

        # 检查表是否存在
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'membership_card_transactions'
            );
        """)

        table_exists = cursor.fetchone()[0]

        if table_exists:
            print("⚠️  membership_card_transactions 表已存在，删除重建...")
            cursor.execute("DROP TABLE membership_card_transactions CASCADE;")
            print("✅ 旧表已删除")

        print("📋 创建 membership_card_transactions 表...")

        # 创建交易表
        create_table_sql = """
            CREATE TABLE membership_card_transactions (
                id SERIAL PRIMARY KEY,
                card_id INTEGER NOT NULL,
                transaction_type VARCHAR(20) NOT NULL,

                -- 余额变化
                times_change INTEGER DEFAULT 0,
                amount_change INTEGER DEFAULT 0,

                -- 变化前后状态
                before_times INTEGER DEFAULT 0,
                after_times INTEGER DEFAULT 0,
                before_amount INTEGER DEFAULT 0,
                after_amount INTEGER DEFAULT 0,
                before_status SMALLINT,
                after_status SMALLINT,

                -- 业务关联
                related_booking_id INTEGER,
                related_user_id CHAR(36),

                -- 操作信息
                operator_id CHAR(36) NOT NULL,
                operator_name VARCHAR(50),
                reason VARCHAR(200),
                remarks TEXT,

                -- 财务信息
                real_cost INTEGER DEFAULT 0,

                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                deleted_at TIMESTAMP WITH TIME ZONE
            );
            """

        cursor.execute(create_table_sql)

        # 创建索引
        cursor.execute("""
                CREATE INDEX idx_membership_card_transactions_card_id
                ON membership_card_transactions(card_id);
            """)

        cursor.execute("""
            CREATE INDEX idx_membership_card_transactions_type
            ON membership_card_transactions(transaction_type);
        """)

        cursor.execute("""
            CREATE INDEX idx_membership_card_transactions_created_at
            ON membership_card_transactions(created_at);
        """)

        # 提交事务
        conn.commit()
        print("✅ membership_card_transactions 表创建成功")

        # 检查其他可能缺失的表
        tables_to_check = [
            'membership_cards',
            'membership_card_types',
            'users',
            'sub_cards'
        ]

        print("\n📋 检查其他表...")
        for table_name in tables_to_check:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_name = %s
                );
            """, (table_name,))

            exists = cursor.fetchone()[0]
            status = "✅ 存在" if exists else "❌ 不存在"
            print(f"   {table_name}: {status}")

        cursor.close()
        conn.close()

        print("\n🎉 数据库表检查完成！")
        return True

    except Exception as e:
        print(f"❌ 数据库操作失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始创建缺失的数据库表...")
    print("=" * 50)

    success = create_missing_tables()

    print("=" * 50)
    if success:
        print("🎉 数据库表创建成功！现在可以重新测试会员卡功能了。")
    else:
        print("❌ 数据库表创建失败")
