# 会员卡系统快速参考手册

## 🚀 **快速导航**

| 文档 | 用途 | 链接 |
|------|------|------|
| 数据库字段说明 | 了解每个字段的含义和用法 | [MEMBERSHIP_CARD_DATABASE_SCHEMA.md](./MEMBERSHIP_CARD_DATABASE_SCHEMA.md) |
| 业务流程指南 | 了解业务场景和操作流程 | [MEMBERSHIP_CARD_BUSINESS_GUIDE.md](./MEMBERSHIP_CARD_BUSINESS_GUIDE.md) |
| 系统升级报告 | 了解功能特性和升级内容 | [MEMBERSHIP_CARD_UPGRADE.md](./MEMBERSHIP_CARD_UPGRADE.md) |

---

## 📋 **核心概念速查**

### 会员卡三大类型
| 类型 | 英文标识 | 主要字段 | 扣减方式 | 使用场景 |
|------|----------|----------|----------|----------|
| 次数卡 | `times` | `times`, `remaining_times` | 按次扣减 | 团课、私教课 |
| 期限卡 | `period` | `validity_days`, `end_date` | 期限内无限用 | 月卡、季卡 |
| 储值卡 | `balance` | `amount`, `remaining_amount` | 按金额扣减 | 灵活消费 |

### 卡片状态说明
| 状态码 | 状态名称 | 说明 | 可操作 |
|--------|----------|------|--------|
| 1 | 正常 | 可正常使用 | 预约、扣费 |
| 2 | 冻结 | 暂停使用 | 解冻 |
| 3 | 过期 | 超过有效期 | 续期 |
| 4 | 用完 | 次数/金额用完 | 充值 |

---

## 💰 **价格字段速查**

| 字段名 | 单位 | 说明 | 示例 |
|--------|------|------|------|
| `price` | 分 | 售价 | 150000 = 1500元 |
| `original_price` | 分 | 原价 | 180000 = 1800元 |
| `purchase_price` | 分 | 实付价格 | 150000 = 1500元 |
| `amount` | 分 | 储值金额 | 100000 = 1000元 |
| `remaining_amount` | 分 | 剩余金额 | 50000 = 500元 |

**💡 提示**：所有金额字段统一使用分为单位，避免浮点数精度问题

---

## 🔧 **功能开关速查**

| 字段名 | 类型 | 说明 | 业务影响 |
|--------|------|------|----------|
| `can_set_expiry` | boolean | 可设置到期时间 | 开卡时是否可自定义到期日期 |
| `can_select_stores` | boolean | 可选择门店 | 是否支持指定门店使用 |
| `can_add_sub_card` | boolean | 可添加副卡 | 是否支持主副卡功能 |
| `can_set_daily_limit` | boolean | 可设置单日限制 | 开卡时是否可调整每日预约限制 |
| `shareable` | boolean | 可共享使用 | 是否支持多人共用一张卡 |
| `refundable` | boolean | 可退款 | 是否支持退款操作 |

---

## 📊 **常用SQL速查**

### 查询用户有效会员卡
```sql
SELECT mc.*, mct.name, mct.category
FROM membership_cards mc
JOIN membership_card_types mct ON mc.card_type_id = mct.id
WHERE mc.user_id = ? AND mc.status = 1 
  AND (mc.end_date IS NULL OR mc.end_date > NOW());
```

### 查询启用的卡种
```sql
SELECT * FROM membership_card_types 
WHERE status = 1 
ORDER BY sort_order, id;
```

### 扣减次数卡
```sql
UPDATE membership_cards 
SET remaining_times = remaining_times - 1
WHERE id = ? AND remaining_times > 0 AND status = 1;
```

### 扣减储值卡
```sql
UPDATE membership_cards 
SET remaining_amount = remaining_amount - ?
WHERE id = ? AND remaining_amount >= ? AND status = 1;
```

---

## 🎯 **业务场景速查**

### 开卡场景
| 场景 | 卡种类型 | 关键配置 | 注意事项 |
|------|----------|----------|----------|
| 新用户体验 | 次数卡 | `times=1`, `validity_days=30` | 短期有效，引导转化 |
| 常规团课 | 次数卡 | `times=10`, `validity_days=365` | 平衡使用频率和有效期 |
| VIP会员 | 期限卡 | `validity_days=90`, 无次数限制 | 高端服务，无使用限制 |
| 灵活消费 | 储值卡 | `amount=100000`, `shareable=true` | 支持多人使用 |

### 门店限制场景
| 场景 | 配置 | 说明 |
|------|------|------|
| 全市通用 | `available_stores=[]` | 任意门店可用 |
| 区域限制 | `available_stores=[1,2,3]` | 指定门店可用 |
| 单店专属 | `available_stores=[1]` | 仅限单店使用 |

---

## 🔄 **状态流转速查**

```mermaid
graph TD
    A[新开卡] --> B[正常状态 1]
    B --> C[冻结状态 2]
    C --> B
    B --> D[过期状态 3]
    B --> E[用完状态 4]
    D --> F[续期] --> B
    E --> G[充值] --> B
```

### 状态变更操作
| 当前状态 | 目标状态 | 操作 | SQL示例 |
|----------|----------|------|---------|
| 正常(1) | 冻结(2) | 手动冻结 | `UPDATE ... SET status=2` |
| 冻结(2) | 正常(1) | 解冻 | `UPDATE ... SET status=1` |
| 正常(1) | 过期(3) | 系统自动 | 定时任务检查 `end_date` |
| 正常(1) | 用完(4) | 系统自动 | 余额/次数为0时 |

---

## 📱 **API接口速查**

### 管理端接口
| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 卡种列表 | GET | `/api/v1/admin/membership-types` | 获取所有卡种 |
| 创建卡种 | POST | `/api/v1/admin/membership-types` | 创建新卡种 |
| 更新卡种 | PUT | `/api/v1/admin/membership-types/:id` | 更新卡种信息 |
| 会员卡列表 | GET | `/api/v1/admin/membership-cards` | 获取会员卡列表 |
| 开卡 | POST | `/api/v1/admin/membership-cards` | 创建新会员卡 |
| 创建副卡 | POST | `/api/v1/admin/membership-cards/sub-card` | 添加副卡 |

### 小程序端接口
| 功能 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 我的会员卡 | GET | `/api/v1/app/membership-cards` | 用户会员卡列表 |
| 卡片详情 | GET | `/api/v1/app/membership-cards/:id` | 会员卡详细信息 |

---

## 🛠️ **故障排除速查**

### 常见问题
| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 开卡失败 | 卡种已停用 | 检查 `status` 字段 |
| 预约失败 | 卡片余额不足 | 检查 `remaining_times/amount` |
| 无法使用 | 卡片已过期 | 检查 `end_date` 和 `status` |
| 门店限制 | 不在可用门店列表 | 检查 `available_stores` |

### 数据修复
| 场景 | SQL示例 |
|------|---------|
| 重置卡片状态 | `UPDATE membership_cards SET status=1 WHERE id=?` |
| 延长有效期 | `UPDATE membership_cards SET end_date=? WHERE id=?` |
| 充值次数 | `UPDATE membership_cards SET remaining_times=remaining_times+? WHERE id=?` |
| 充值金额 | `UPDATE membership_cards SET remaining_amount=remaining_amount+? WHERE id=?` |

---

## 📞 **技术支持**

### 相关文档
- 📖 [完整数据库字段说明](./MEMBERSHIP_CARD_DATABASE_SCHEMA.md)
- 🔄 [业务流程详细指南](./MEMBERSHIP_CARD_BUSINESS_GUIDE.md)
- 🚀 [系统功能升级报告](./MEMBERSHIP_CARD_UPGRADE.md)

### 测试工具
- 🧪 [会员卡类型测试脚本](../scripts/js/test_membership_types.js)
- 📊 [批量添加卡种SQL](../scripts/sql/add_membership_card_types.sql)

### 联系方式
如有技术问题，请查阅相关文档或联系开发团队。
