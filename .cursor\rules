# Yogaga Project - Cursor AI Rules

You are an expert Go developer working on the Yogaga yoga studio management system. This project uses Go + Gin + GORM + <PERSON><PERSON>bin for a complete RBAC permission management system.

## Core Architecture

This is a Go web application with the following structure:

- **Framework**: Gin for HTTP routing
- **ORM**: GORM for database operations
- **Auth**: J<PERSON><PERSON> + <PERSON><PERSON>bin for RBAC permission control
- **Database**: MySQL with structured logging
- **API Style**: RESTful with consistent snake_case JSON responses

## Critical Rules - ALWAYS Follow

### 1. Data Models - MANDATORY BaseModel Usage

```go
// ✅ CORRECT - Always use BaseModel
type User struct {
    BaseModel
    Username string `json:"username" gorm:"type:varchar(50);not null"`
    Email    string `json:"email" gorm:"type:varchar(100)"`
    Password string `json:"-" gorm:"type:varchar(255);not null"` // Hide sensitive fields
}

// ❌ NEVER use gorm.Model directly
type User struct {
    gorm.Model // DON'T DO THIS
    Username string
}
```

**BaseModel Definition** (already exists in internal/model/vars.go):

```go
type BaseModel struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
```

### 2. JSON Tags - MANDATORY snake_case

- ALL model fields MUST have `json` tags in snake_case format
- Sensitive fields (passwords) MUST use `json:"-"`
- Association fields MUST have snake_case tags: `json:"user_roles"`

### 3. API Response Format - MANDATORY

```go
// ✅ CORRECT - Use code.AutoResponse
code.AutoResponse(c, data, nil)
code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorCode, "message"))

// ❌ NEVER use gin.H (except health checks)
c.JSON(200, gin.H{"data": data}) // DON'T DO THIS
```

### 4. Package Management - MANDATORY

- NEVER manually edit go.mod, package.json, requirements.txt
- ALWAYS use package managers: `go get`, `npm install`, `pip install`
- Let package managers handle version resolution and conflicts

### 5. Type Conversion - MANDATORY cast Usage

```go
// ✅ CORRECT - Always use cast library for type conversion
import "github.com/spf13/cast"

// String to integer conversions
id := cast.ToUint(idStr)
page := cast.ToInt(pageStr)
amount := cast.ToInt64(amountStr)

// Safe conversion with validation
if id := cast.ToUint(idStr); id > 0 {
    // Use id safely
}

// ❌ NEVER use strconv directly
id, err := strconv.ParseUint(idStr, 10, 32)  // DON'T DO THIS
page, _ := strconv.Atoi(pageStr)             // DON'T DO THIS
```

**Why cast over strconv:**

- cast provides safer type conversion with better error handling
- cast handles edge cases and nil values gracefully
- cast is more consistent across different data types
- cast reduces boilerplate code and potential runtime errors

## Model Creation Checklist

When creating ANY new model, verify:

- [ ] Inherits `BaseModel` (not `gorm.Model`)
- [ ] ALL fields have `json` tags in snake_case
- [ ] Sensitive fields use `json:"-"`
- [ ] GORM tags include proper constraints and comments
- [ ] Association fields have correct JSON tags
- [ ] Uses `cast` library for type conversions (not `strconv`)

## Handler Pattern

```go
func (h *ExampleHandler) Create(c *gin.Context) {
    var req dto.CreateExampleRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
        return
    }

    example := model.Example{
        Name:   req.Name,
        Status: req.Status,
    }

    if err := h.db.Create(&example).Error; err != nil {
        log.Error("创建失败", "error", err)
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建失败"))
        return
    }

    code.AutoResponse(c, example, nil)
}
```

## RBAC System Rules

- Menus associate with permissions, not roles directly
- Menu `PermissionKey` can be empty (no permission required)
- Use enum types for menu types (MenuTypeDirectory, MenuTypePage, MenuTypeButton)
- JWT integrates with Casbin role-permission-menu system

## Error Handling

- Simple error messages for frontend users
- Detailed errors only in server logs
- Use consistent error codes: `code.NewErrCodeMsg(code.ErrorCode, "message")`

## File Organization

- Models: `internal/model/`
- Handlers: `internal/handler/`
- DTOs: `internal/dto/`
- Services: `internal/service/`
- Middleware: `internal/middleware/`

## Expected JSON Response Format

```json
{
  "code": 0,
  "msg": "Ok",
  "data": {
    "id": 5,
    "created_at": "2025-07-10T19:30:44.204+08:00",
    "updated_at": "2025-07-10T19:30:44.204+08:00",
    "deleted_at": null,
    "username": "admin",
    "email": "<EMAIL>",
    "is_active": true
  }
}
```

## Code Quality Standards

- Use structured logging with context
- Include proper GORM comments in Chinese
- Follow Go naming conventions
- Add proper error handling for all database operations
- Validate input parameters thoroughly

## Testing

- Run `bash scripts/check_rules.sh` to verify compliance
- All models must pass BaseModel usage checks
- JSON tag format must be consistent
- API responses must use snake_case format

## Common Mistakes to Avoid

1. Using `gorm.Model` instead of `BaseModel`
2. Missing JSON tags on model fields
3. Using PascalCase in JSON tags
4. Exposing sensitive fields in JSON
5. Using `gin.H` for API responses
6. Manually editing dependency files

Remember: These rules ensure API response consistency and frontend compatibility. The project has 100% test coverage and follows these standards strictly.
