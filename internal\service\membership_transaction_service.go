package service

import (
	"fmt"
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// MembershipTransactionService 会员卡交易记录服务
type MembershipTransactionService struct {
	db *gorm.DB
}

// NewMembershipTransactionService 创建会员卡交易记录服务
func NewMembershipTransactionService(db *gorm.DB) *MembershipTransactionService {
	return &MembershipTransactionService{
		db: db,
	}
}

// CreateTransactionRequest 创建交易记录请求
type CreateTransactionRequest struct {
	CardID           uint                     `json:"card_id"`
	TransactionType  enum.CardTransactionType `json:"transaction_type"`
	TimesChange      int                      `json:"times_change"`
	AmountChange     int                      `json:"amount_change"`
	RelatedBookingID *uint                    `json:"related_booking_id,omitempty"`
	RelatedUserID    string                   `json:"related_user_id,omitempty"`
	OperatorID       string                   `json:"operator_id"`
	OperatorName     string                   `json:"operator_name"`
	Reason           string                   `json:"reason"`
	Remarks          string                   `json:"remarks,omitempty"`
	RealCost         int                      `json:"real_cost,omitempty"`
}

// RecordTransaction 记录会员卡交易
func (s *MembershipTransactionService) RecordTransaction(tx *gorm.DB, req CreateTransactionRequest) error {
	// 获取当前会员卡状态
	var card model.MembershipCard
	if err := tx.First(&card, req.CardID).Error; err != nil {
		return fmt.Errorf("查询会员卡失败: %w", err)
	}

	// 记录操作前状态
	beforeTimes := card.RemainingTimes
	beforeAmount := card.RemainingAmount
	beforeStatus := card.Status

	// 计算操作后状态
	afterTimes := beforeTimes + req.TimesChange
	afterAmount := beforeAmount + req.AmountChange
	afterStatus := beforeStatus

	// 根据交易类型调整状态
	switch req.TransactionType {
	case enum.CardTransactionTypeFreeze:
		afterStatus = enum.MembershipCardStatusFrozen
	case enum.CardTransactionTypeUnfreeze:
		afterStatus = enum.MembershipCardStatusNormal
	case enum.CardTransactionTypeDeduct:
		// 扣费后检查是否用完
		if afterTimes <= 0 && afterAmount <= 0 {
			afterStatus = enum.MembershipCardStatusUsedUp
		}
	}

	// 创建交易记录
	transaction := model.MembershipCardTransaction{
		CardID:           req.CardID,
		TransactionType:  req.TransactionType,
		TimesChange:      req.TimesChange,
		AmountChange:     req.AmountChange,
		BeforeTimes:      beforeTimes,
		AfterTimes:       afterTimes,
		BeforeAmount:     beforeAmount,
		AfterAmount:      afterAmount,
		BeforeStatus:     beforeStatus,
		AfterStatus:      afterStatus,
		RelatedBookingID: req.RelatedBookingID,
		RelatedUserID:    req.RelatedUserID,
		OperatorID:       req.OperatorID,
		OperatorName:     req.OperatorName,
		Reason:           req.Reason,
		Remarks:          req.Remarks,
		RealCost:         req.RealCost,
	}

	if err := tx.Create(&transaction).Error; err != nil {
		return fmt.Errorf("创建交易记录失败: %w", err)
	}

	return nil
}

// GetCardTransactions 获取会员卡交易记录
func (s *MembershipTransactionService) GetCardTransactions(cardID uint, page, pageSize int) ([]model.MembershipCardTransaction, int64, error) {
	var transactions []model.MembershipCardTransaction
	var total int64

	// 查询总数
	if err := s.db.Model(&model.MembershipCardTransaction{}).Where("card_id = ?", cardID).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询交易记录总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := s.db.Preload("Operator").Preload("RelatedUser").Preload("RelatedBooking").
		Where("card_id = ?", cardID).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&transactions).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询交易记录失败: %w", err)
	}

	return transactions, total, nil
}

// GetTransactionsByType 按类型获取交易记录
func (s *MembershipTransactionService) GetTransactionsByType(cardID uint, transactionType enum.CardTransactionType) ([]model.MembershipCardTransaction, error) {
	var transactions []model.MembershipCardTransaction

	err := s.db.Preload("Operator").Preload("RelatedUser").Preload("RelatedBooking").
		Where("card_id = ? AND transaction_type = ?", cardID, transactionType).
		Order("created_at DESC").
		Find(&transactions).Error

	if err != nil {
		return nil, fmt.Errorf("查询交易记录失败: %w", err)
	}

	return transactions, nil
}

// GetTransactionStats 获取交易统计
func (s *MembershipTransactionService) GetTransactionStats(cardID uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 统计各类型交易次数
	var typeStats []struct {
		TransactionType string `json:"transaction_type"`
		Count           int64  `json:"count"`
		TotalTimes      int64  `json:"total_times"`
		TotalAmount     int64  `json:"total_amount"`
	}

	err := s.db.Model(&model.MembershipCardTransaction{}).
		Select("transaction_type, COUNT(*) as count, SUM(times_change) as total_times, SUM(amount_change) as total_amount").
		Where("card_id = ?", cardID).
		Group("transaction_type").
		Find(&typeStats).Error

	if err != nil {
		return nil, fmt.Errorf("查询交易统计失败: %w", err)
	}

	stats["type_stats"] = typeStats

	// 获取最近一次交易
	var lastTransaction model.MembershipCardTransaction
	err = s.db.Where("card_id = ?", cardID).Order("created_at DESC").First(&lastTransaction).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("查询最近交易失败: %w", err)
	}
	if err != gorm.ErrRecordNotFound {
		stats["last_transaction"] = lastTransaction
	}

	return stats, nil
}

// GetUserTransactionHistory 获取用户所有会员卡的交易历史
func (s *MembershipTransactionService) GetUserTransactionHistory(userID string, page, pageSize int) ([]model.MembershipCardTransaction, int64, error) {
	var transactions []model.MembershipCardTransaction
	var total int64

	// 子查询获取用户的所有会员卡ID
	subQuery := s.db.Model(&model.MembershipCard{}).Select("id").Where("user_id = ?", userID)

	// 查询总数
	if err := s.db.Model(&model.MembershipCardTransaction{}).Where("card_id IN (?)", subQuery).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("查询用户交易记录总数失败: %w", err)
	}

	// 分页查询
	offset := (page - 1) * pageSize
	err := s.db.Preload("Card").Preload("Card.CardType").Preload("Operator").Preload("RelatedUser").Preload("RelatedBooking").
		Where("card_id IN (?)", subQuery).
		Order("created_at DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&transactions).Error

	if err != nil {
		return nil, 0, fmt.Errorf("查询用户交易记录失败: %w", err)
	}

	return transactions, total, nil
}

// ValidateTransaction 验证交易是否合法
func (s *MembershipTransactionService) ValidateTransaction(req CreateTransactionRequest) error {
	// 验证交易类型
	if !req.TransactionType.IsValid() {
		return fmt.Errorf("无效的交易类型: %s", req.TransactionType)
	}

	// 验证会员卡是否存在
	var card model.MembershipCard
	if err := s.db.First(&card, req.CardID).Error; err != nil {
		return fmt.Errorf("会员卡不存在: %w", err)
	}

	// 验证扣费操作
	if req.TransactionType == enum.CardTransactionTypeDeduct {
		if req.TimesChange > 0 || req.AmountChange > 0 {
			return fmt.Errorf("扣费操作的变化值应为负数")
		}

		// 检查余额是否足够
		if card.RemainingTimes+req.TimesChange < 0 {
			return fmt.Errorf("次数余额不足")
		}
		if card.RemainingAmount+req.AmountChange < 0 {
			return fmt.Errorf("金额余额不足")
		}
	}

	// 验证充值操作
	if req.TransactionType == enum.CardTransactionTypeRecharge {
		if req.TimesChange < 0 || req.AmountChange < 0 {
			return fmt.Errorf("充值操作的变化值应为正数")
		}
	}

	return nil
}
