#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
门店图片URL测试脚本
测试门店列表接口返回的图片URL格式是否统一为预签名URL
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

class YogagaAPITester:
    def __init__(self, base_url: str = "http://localhost:8080"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """管理员登录获取token"""
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {
            "username": username,
            "password": password
        }
        
        try:
            print(f"🔐 正在登录: {username}")
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0 and result.get("data"):
                self.token = result["data"].get("access_token")
                if self.token:
                    # 设置认证头
                    self.session.headers.update({
                        "Authorization": f"Bearer {self.token}"
                    })
                    print(f"✅ 登录成功，获取到token: {self.token[:20]}...")
                    return True
            
            print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
            return False
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 登录请求失败: {e}")
            return False
    
    def get_stores(self, page: int = 1, page_size: int = 10) -> Optional[Dict[str, Any]]:
        """获取门店列表"""
        stores_url = f"{self.base_url}/api/v1/admin/stores"
        params = {
            "page": page,
            "page_size": page_size
        }
        
        try:
            print(f"🏪 正在获取门店列表...")
            response = self.session.get(stores_url, params=params)
            response.raise_for_status()
            
            result = response.json()
            if result.get("code") == 0:
                print(f"✅ 成功获取门店列表")
                return result.get("data")
            else:
                print(f"❌ 获取门店列表失败: {result.get('msg', '未知错误')}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 获取门店列表请求失败: {e}")
            return None
    
    def analyze_store_images(self, stores_data: Dict[str, Any]) -> None:
        """分析门店图片URL格式"""
        if not stores_data or "list" not in stores_data:
            print("❌ 门店数据为空")
            return
        
        stores = stores_data["list"]
        total = stores_data.get("total", 0)
        
        print(f"\n📊 门店图片URL分析报告")
        print(f"=" * 60)
        print(f"总门店数量: {total}")
        print(f"当前页门店数量: {len(stores)}")
        print(f"=" * 60)
        
        presigned_count = 0
        legacy_count = 0
        empty_count = 0
        
        for i, store in enumerate(stores, 1):
            store_id = store.get("id")
            store_name = store.get("name", "未知门店")
            images = store.get("images", [])
            
            print(f"\n🏪 门店 {i}: {store_name} (ID: {store_id})")
            
            if not images:
                print(f"   📷 图片: 无图片")
                empty_count += 1
                continue
            
            print(f"   📷 图片数量: {len(images)}")
            
            for j, image_url in enumerate(images, 1):
                if not image_url:
                    print(f"   📷 图片 {j}: 空URL")
                    continue
                
                # 分析URL格式
                if "X-Amz-Algorithm" in image_url and "X-Amz-Signature" in image_url:
                    url_type = "✅ 预签名URL"
                    presigned_count += 1
                elif image_url.startswith("/api/v1/file/download/"):
                    url_type = "❌ 传统下载URL"
                    legacy_count += 1
                elif image_url.startswith("http"):
                    url_type = "🔗 外部URL"
                else:
                    url_type = "❓ 未知格式"
                
                # 显示URL（截断显示）
                display_url = image_url[:80] + "..." if len(image_url) > 80 else image_url
                print(f"   📷 图片 {j}: {url_type}")
                print(f"       URL: {display_url}")
        
        # 统计总结
        print(f"\n📈 URL格式统计")
        print(f"=" * 60)
        print(f"✅ 预签名URL: {presigned_count}")
        print(f"❌ 传统下载URL: {legacy_count}")
        print(f"📷 无图片门店: {empty_count}")
        
        # 检查结果
        if legacy_count > 0:
            print(f"\n⚠️  发现 {legacy_count} 个传统下载URL，需要修复！")
        else:
            print(f"\n🎉 所有图片URL都已统一为预签名URL格式！")
    
    def test_image_access(self, image_url: str) -> bool:
        """测试图片URL是否可访问"""
        if not image_url or not image_url.startswith("http"):
            return False
        
        try:
            # 不使用session，直接访问预签名URL
            response = requests.head(image_url, timeout=10)
            return response.status_code == 200
        except:
            return False

def main():
    """主函数"""
    print("🧘‍♀️ Yogaga 门店图片URL测试脚本")
    print("=" * 60)
    
    # 创建测试实例
    tester = YogagaAPITester()
    
    # 登录
    if not tester.login():
        print("❌ 登录失败，退出测试")
        sys.exit(1)
    
    # 获取门店列表
    stores_data = tester.get_stores()
    if not stores_data:
        print("❌ 获取门店列表失败，退出测试")
        sys.exit(1)
    
    # 分析门店图片URL
    tester.analyze_store_images(stores_data)
    
    # 测试第一个图片的可访问性
    stores = stores_data.get("list", [])
    if stores:
        for store in stores:
            images = store.get("images", [])
            if images and images[0]:
                first_image = images[0]
                print(f"\n🔍 测试第一张图片的可访问性...")
                print(f"URL: {first_image[:100]}...")
                
                if tester.test_image_access(first_image):
                    print("✅ 图片可正常访问")
                else:
                    print("❌ 图片无法访问")
                break
    
    print(f"\n✨ 测试完成！")

if __name__ == "__main__":
    main()
