#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
门店图片URL测试脚本
测试门店列表接口返回的图片URL格式是否统一为预签名URL
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

class YogagaAPITester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None

    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """管理员登录获取token"""
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {
            "username": username,
            "password": password
        }

        try:
            print(f"🔐 正在登录: {username}")
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0 and result.get("data"):
                self.token = result["data"].get("access_token")
                if self.token:
                    # 设置认证头
                    self.session.headers.update({
                        "Authorization": f"Bearer {self.token}"
                    })
                    print(f"✅ 登录成功，获取到token: {self.token[:20]}...")
                    return True

            print(f"❌ 登录失败: {result.get('msg', '未知错误')}")
            return False

        except requests.exceptions.RequestException as e:
            print(f"❌ 登录请求失败: {e}")
            return False

    def get_banners(self) -> Optional[Dict[str, Any]]:
        """获取轮播图列表"""
        banners_url = f"{self.base_url}/api/v1/admin/banners"
        params = {
            "page": 1,
            "page_size": 10
        }

        try:
            print(f"🎨 正在获取轮播图列表...")
            response = self.session.get(banners_url, params=params)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                print(f"✅ 成功获取轮播图列表")
                return result.get("data")
            else:
                print(f"❌ 获取轮播图列表失败: {result.get('msg', '未知错误')}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 获取轮播图列表请求失败: {e}")
            return None

    def get_store_detail(self, store_id: int) -> Optional[Dict[str, Any]]:
        """获取单个门店详情"""
        store_url = f"{self.base_url}/api/v1/admin/stores/{store_id}"

        try:
            print(f"🏪 正在获取门店详情 (ID: {store_id})...")
            response = self.session.get(store_url)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                print(f"✅ 成功获取门店详情")
                return result.get("data")
            else:
                print(f"❌ 获取门店详情失败: {result.get('msg', '未知错误')}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 获取门店详情请求失败: {e}")
            return None

    def get_stores(self, page: int = 1, page_size: int = 10) -> Optional[Dict[str, Any]]:
        """获取门店列表"""
        stores_url = f"{self.base_url}/api/v1/admin/stores"
        params = {
            "page": page,
            "page_size": page_size
        }

        try:
            print(f"🏪 正在获取门店列表...")
            response = self.session.get(stores_url, params=params)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                print(f"✅ 成功获取门店列表")
                return result.get("data")
            else:
                print(f"❌ 获取门店列表失败: {result.get('msg', '未知错误')}")
                return None

        except requests.exceptions.RequestException as e:
            print(f"❌ 获取门店列表请求失败: {e}")
            return None

    def analyze_banner_images(self, banners_data: Dict[str, Any]) -> None:
        """分析轮播图图片URL格式"""
        if not banners_data or "list" not in banners_data:
            print("❌ 轮播图数据为空")
            return

        banners = banners_data["list"]
        total = banners_data.get("total", 0)

        print(f"\n🎨 轮播图图片URL分析报告")
        print(f"=" * 60)
        print(f"总轮播图数量: {total}")
        print(f"当前页轮播图数量: {len(banners)}")
        print(f"=" * 60)

        presigned_count = 0
        legacy_count = 0
        empty_count = 0

        for i, banner in enumerate(banners, 1):
            banner_id = banner.get("id")
            banner_title = banner.get("title", "未知轮播图")
            image_url = banner.get("image", "")

            print(f"\n🎨 轮播图 {i}: {banner_title} (ID: {banner_id})")

            if not image_url:
                print(f"   📷 图片: 无图片")
                empty_count += 1
                continue

            # 分析URL格式
            if "X-Amz-Algorithm" in image_url and "X-Amz-Signature" in image_url:
                url_type = "✅ 预签名URL"
                presigned_count += 1
            elif image_url.startswith("/api/v1/file/download/"):
                url_type = "❌ 传统下载URL"
                legacy_count += 1
            elif image_url.startswith("http"):
                url_type = "🔗 外部URL"
            else:
                url_type = "❓ 未知格式"

            # 显示URL（截断显示）
            display_url = image_url[:80] + "..." if len(image_url) > 80 else image_url
            print(f"   📷 图片: {url_type}")
            print(f"       URL: {display_url}")

        # 统计总结
        print(f"\n📈 轮播图URL格式统计")
        print(f"=" * 60)
        print(f"✅ 预签名URL: {presigned_count}")
        print(f"❌ 传统下载URL: {legacy_count}")
        print(f"📷 无图片轮播图: {empty_count}")

        # 检查结果
        if legacy_count > 0:
            print(f"\n⚠️  发现 {legacy_count} 个传统下载URL，需要修复！")
        else:
            print(f"\n🎉 所有轮播图URL都已统一为预签名URL格式！")

        return presigned_count, legacy_count, empty_count

    def analyze_single_store(self, store_data: Dict[str, Any]) -> None:
        """分析单个门店的图片URL格式"""
        if not store_data:
            print("❌ 门店数据为空")
            return

        store_id = store_data.get("id")
        store_name = store_data.get("name", "未知门店")
        images = store_data.get("images", [])
        image_ids = store_data.get("image_ids", [])  # 原始图片ID数组

        print(f"\n🏪 门店详情分析: {store_name} (ID: {store_id})")
        print(f"=" * 60)
        print(f"原始图片ID数组: {image_ids}")
        print(f"转换后图片URL数组: {len(images)} 个")
        print(f"=" * 60)

        if not images and not image_ids:
            print("📷 该门店确实没有图片数据")
            return

        if image_ids and not images:
            print("⚠️  发现问题：数据库中有图片ID，但转换后的URL数组为空！")
            print(f"   原始图片ID: {image_ids}")
            print("   这可能是URL转换过程中的问题")
            return

        presigned_count = 0
        legacy_count = 0

        for i, image_url in enumerate(images, 1):
            if not image_url:
                print(f"📷 图片 {i}: 空URL")
                continue

            # 分析URL格式
            if "X-Amz-Algorithm" in image_url and "X-Amz-Signature" in image_url:
                url_type = "✅ 预签名URL"
                presigned_count += 1
            elif image_url.startswith("/api/v1/file/download/"):
                url_type = "❌ 传统下载URL"
                legacy_count += 1
            elif image_url.startswith("http"):
                url_type = "🔗 外部URL"
            else:
                url_type = "❓ 未知格式"

            # 显示完整URL
            print(f"📷 图片 {i}: {url_type}")
            print(f"   URL: {image_url}")

            # 测试可访问性
            if self.test_image_access(image_url):
                print(f"   ✅ 可访问")
            else:
                print(f"   ❌ 无法访问")

        # 统计总结
        print(f"\n📈 该门店URL格式统计")
        print(f"✅ 预签名URL: {presigned_count}")
        print(f"❌ 传统下载URL: {legacy_count}")

    def analyze_store_images(self, stores_data: Dict[str, Any]) -> None:
        """分析门店图片URL格式"""
        if not stores_data or "list" not in stores_data:
            print("❌ 门店数据为空")
            return

        stores = stores_data["list"]
        total = stores_data.get("total", 0)

        print(f"\n📊 门店图片URL分析报告")
        print(f"=" * 60)
        print(f"总门店数量: {total}")
        print(f"当前页门店数量: {len(stores)}")
        print(f"=" * 60)

        presigned_count = 0
        legacy_count = 0
        empty_count = 0

        for i, store in enumerate(stores, 1):
            store_id = store.get("id")
            store_name = store.get("name", "未知门店")
            images = store.get("images", [])

            print(f"\n🏪 门店 {i}: {store_name} (ID: {store_id})")

            if not images:
                print(f"   📷 图片: 无图片")
                empty_count += 1
                continue

            print(f"   📷 图片数量: {len(images)}")

            for j, image_url in enumerate(images, 1):
                if not image_url:
                    print(f"   📷 图片 {j}: 空URL")
                    continue

                # 分析URL格式
                if "X-Amz-Algorithm" in image_url and "X-Amz-Signature" in image_url:
                    url_type = "✅ 预签名URL"
                    presigned_count += 1
                elif image_url.startswith("/api/v1/file/download/"):
                    url_type = "❌ 传统下载URL"
                    legacy_count += 1
                elif image_url.startswith("http"):
                    url_type = "🔗 外部URL"
                else:
                    url_type = "❓ 未知格式"

                # 显示URL（截断显示）
                display_url = image_url[:80] + "..." if len(image_url) > 80 else image_url
                print(f"   📷 图片 {j}: {url_type}")
                print(f"       URL: {display_url}")

        # 统计总结
        print(f"\n📈 URL格式统计")
        print(f"=" * 60)
        print(f"✅ 预签名URL: {presigned_count}")
        print(f"❌ 传统下载URL: {legacy_count}")
        print(f"📷 无图片门店: {empty_count}")

        # 检查结果
        if legacy_count > 0:
            print(f"\n⚠️  发现 {legacy_count} 个传统下载URL，需要修复！")
        else:
            print(f"\n🎉 所有图片URL都已统一为预签名URL格式！")

    def test_image_access(self, image_url: str) -> bool:
        """测试图片URL是否可访问"""
        if not image_url or not image_url.startswith("http"):
            return False

        try:
            # 不使用session，直接访问预签名URL
            response = requests.head(image_url, timeout=10)
            return response.status_code == 200
        except:
            return False

def main():
    """主函数"""
    print("🧘‍♀️ Yogaga 图片URL测试脚本")
    print("=" * 60)

    # 创建测试实例
    tester = YogagaAPITester()

    # 登录
    if not tester.login():
        print("❌ 登录失败，退出测试")
        sys.exit(1)

    # 测试轮播图
    print("\n" + "="*60)
    print("🎨 测试轮播图URL格式")
    print("="*60)

    banners_data = tester.get_banners()
    if banners_data:
        tester.analyze_banner_images(banners_data)

        # 测试第一个轮播图的可访问性
        banners = banners_data.get("list", [])
        if banners:
            for banner in banners:
                image_url = banner.get("image", "")
                if image_url:
                    print(f"\n🔍 测试第一张轮播图的可访问性...")
                    print(f"URL: {image_url[:100]}...")

                    if tester.test_image_access(image_url):
                        print("✅ 轮播图可正常访问")
                    else:
                        print("❌ 轮播图无法访问")
                    break

    # 测试特定门店图片 (ID: 19)
    print("\n" + "="*60)
    print("🏪 测试特定门店图片URL格式 (ID: 19)")
    print("="*60)

    store_19_data = tester.get_store_detail(19)
    if store_19_data:
        tester.analyze_single_store(store_19_data)

    # 测试门店列表 - 查找ID为19的门店
    print("\n" + "="*60)
    print("🏪 测试门店列表URL格式 - 查找ID为19的门店")
    print("="*60)

    # 尝试多页查找ID为19的门店
    found_store_19 = False
    for page in range(1, 5):  # 查找前4页
        print(f"\n📄 查看第{page}页...")
        stores_data = tester.get_stores(page=page, page_size=10)
        if stores_data:
            stores = stores_data.get("list", [])
            for store in stores:
                if store.get("id") == 19:
                    print(f"🎯 在第{page}页找到ID为19的门店！")
                    print(f"   门店名称: {store.get('name')}")
                    print(f"   图片数量: {len(store.get('images', []))}")
                    if store.get('images'):
                        print(f"   第一张图片: {store['images'][0][:80]}...")
                    found_store_19 = True
                    break
            if found_store_19:
                break
        else:
            break

    if not found_store_19:
        print("⚠️  在门店列表中未找到ID为19的门店")

    # 分析第一页的门店
    stores_data = tester.get_stores()
    if stores_data:
        tester.analyze_store_images(stores_data)

        # 测试第一个门店图片的可访问性
        stores = stores_data.get("list", [])
        if stores:
            for store in stores:
                images = store.get("images", [])
                if images and images[0]:
                    first_image = images[0]
                    print(f"\n🔍 测试第一张门店图片的可访问性...")
                    print(f"URL: {first_image[:100]}...")

                    if tester.test_image_access(first_image):
                        print("✅ 门店图片可正常访问")
                    else:
                        print("❌ 门店图片无法访问")
                    break

    print(f"\n✨ 测试完成！")

if __name__ == "__main__":
    main()
