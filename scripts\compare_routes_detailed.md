# 路由器与API文档详细比较结果

## 🔍 比较方法
1. 从路由器中提取所有admin路由
2. 从API文档中提取所有接口
3. 逐一比较路径、方法、参数的一致性

## 📊 会员卡管理模块比较

### 会员卡类型管理 (/membership-types)

| 功能 | 路由器 | API文档 | 状态 |
|------|--------|---------|------|
| 获取列表 | `GET /api/v1/admin/membership-types` | `GET /api/v1/admin/membership-types` | ✅ 一致 |
| 获取详情 | `GET /api/v1/admin/membership-types/:id` | `GET /api/v1/admin/membership-types/1` | ✅ 一致 |
| 创建类型 | `POST /api/v1/admin/membership-types` | `POST /api/v1/admin/membership-types` | ✅ 一致 |
| 更新类型 | `PUT /api/v1/admin/membership-types/:id` | `PUT /api/v1/admin/membership-types/1` | ✅ 一致 |
| 删除类型 | `DELETE /api/v1/admin/membership-types/:id` | `DELETE /api/v1/admin/membership-types/1` | ✅ 一致 |

### 会员卡实例管理 (/membership-cards)

| 功能 | 路由器 | API文档 | 状态 |
|------|--------|---------|------|
| 获取列表 | `GET /api/v1/admin/membership-cards` | `GET /api/v1/admin/membership-cards` | ✅ 一致 |
| 获取详情 | `GET /api/v1/admin/membership-cards/:id` | `GET /api/v1/admin/membership-cards/1` | ✅ 一致 |
| 创建会员卡 | `POST /api/v1/admin/membership-cards` | `POST /api/v1/admin/membership-cards` | ✅ 一致 |
| 创建副卡 | `POST /api/v1/admin/membership-cards/:id/sub-cards` | `POST /api/v1/admin/membership-cards/1/sub-cards` | ✅ 一致 |
| 获取副卡列表 | `GET /api/v1/admin/membership-cards/:id/sub-cards` | `GET /api/v1/admin/membership-cards/1/sub-cards` | ✅ 一致 |
| 获取交易记录 | `GET /api/v1/admin/membership-cards/:id/transactions` | ❌ 缺失 | ⚠️ API文档缺失 |
| 转让会员卡 | `PUT /api/v1/admin/membership-cards/:id/transfer` | `PUT /api/v1/admin/membership-cards/1/transfer` | ✅ 一致 |
| 会员卡充值 | `PUT /api/v1/admin/membership-cards/:id/recharge` | `PUT /api/v1/admin/membership-cards/1/recharge` | ✅ 一致 |
| 会员卡扣费 | `PUT /api/v1/admin/membership-cards/:id/deduct` | `PUT /api/v1/admin/membership-cards/1/deduct` | ✅ 一致 |
| 冻结/解冻 | `PUT /api/v1/admin/membership-cards/:id/freeze` | `PUT /api/v1/admin/membership-cards/1/freeze` | ✅ 一致 |
| 升级会员卡 | `PUT /api/v1/admin/membership-cards/:id/upgrade` | `PUT /api/v1/admin/membership-cards/1/upgrade` | ✅ 一致 |
| 请假会员卡 | `PUT /api/v1/admin/membership-cards/:id/leave` | `PUT /api/v1/admin/membership-cards/1/leave` | ✅ 一致 |
| 延期会员卡 | `PUT /api/v1/admin/membership-cards/:id/extend` | `PUT /api/v1/admin/membership-cards/1/extend` | ✅ 一致 |

## 📊 课程管理模块比较

### 课程管理 (/courses)

| 功能 | 路由器 | API文档 | 状态 |
|------|--------|---------|------|
| 获取列表 | `GET /api/v1/admin/courses` | `GET /api/v1/admin/courses` | ✅ 一致 |
| 获取详情 | `GET /api/v1/admin/courses/:id` | `GET /api/v1/admin/courses/1` | ✅ 一致 |
| 创建课程 | `POST /api/v1/admin/courses` | `POST /api/v1/admin/courses` | ✅ 一致 |
| 更新课程 | `PUT /api/v1/admin/courses/:id` | `PUT /api/v1/admin/courses/1` | ✅ 一致 |
| 删除课程 | `DELETE /api/v1/admin/courses/:id` | `DELETE /api/v1/admin/courses/1` | ✅ 一致 |
| 批量创建 | `POST /api/v1/admin/courses/batch` | ❌ 缺失 | ⚠️ API文档缺失 |
| 课程排课 | `POST /api/v1/admin/courses/schedule` | ❌ 缺失 | ⚠️ API文档缺失 |
| 支持的卡类型 | `GET /api/v1/admin/courses/:id/supported-card-types` | ❌ 缺失 | ⚠️ API文档缺失 |

### 批量课程管理 (/batch-courses)

| 功能 | 路由器 | API文档 | 状态 |
|------|--------|---------|------|
| 批量预览 | `POST /api/v1/admin/batch-courses/preview` | ❌ 缺失 | ⚠️ API文档缺失 |
| 批量创建 | `POST /api/v1/admin/batch-courses/create` | ❌ 缺失 | ⚠️ API文档缺失 |

## 📊 灵活扣减系统比较

### 灵活扣减系统 (/flexible)

| 功能 | 路由器 | API文档 | 状态 |
|------|--------|---------|------|
| 获取课程类型配置 | `GET /api/v1/admin/flexible/course-types` | `GET /api/v1/admin/flexible/course-types` | ✅ 一致 |
| 创建课程类型配置 | `POST /api/v1/admin/flexible/course-types` | `POST /api/v1/admin/flexible/course-types` | ✅ 一致 |
| 更新课程类型配置 | `PUT /api/v1/admin/flexible/course-types/:id` | `PUT /api/v1/admin/flexible/course-types/1` | ✅ 一致 |
| 删除课程类型配置 | `DELETE /api/v1/admin/flexible/course-types/:id` | `DELETE /api/v1/admin/flexible/course-types/1` | ✅ 一致 |
| 获取扣减规则列表 | `GET /api/v1/admin/flexible/deduction-rules` | `GET /api/v1/admin/flexible/deduction-rules` | ✅ 一致 |
| 获取扣减规则详情 | `GET /api/v1/admin/flexible/deduction-rules/:id` | `GET /api/v1/admin/flexible/deduction-rules/1` | ✅ 一致 |
| 创建扣减规则 | `POST /api/v1/admin/flexible/deduction-rules` | `POST /api/v1/admin/flexible/deduction-rules` | ✅ 一致 |
| 更新扣减规则 | `PUT /api/v1/admin/flexible/deduction-rules/:id` | `PUT /api/v1/admin/flexible/deduction-rules/1` | ✅ 一致 |
| 删除扣减规则 | `DELETE /api/v1/admin/flexible/deduction-rules/:id` | `DELETE /api/v1/admin/flexible/deduction-rules/1` | ✅ 一致 |
| 切换规则状态 | `PUT /api/v1/admin/flexible/deduction-rules/:id/toggle` | ❌ 缺失 | ⚠️ API文档缺失 |
| 测试扣减规则 | `POST /api/v1/admin/flexible/test-deduction` | `POST /api/v1/admin/flexible/test-deduction` | ✅ 一致 |
| 获取卡支持的课程 | `GET /api/v1/admin/flexible/card-types/:card_type_id/supported-courses` | `GET /api/v1/admin/flexible/card-types/1/supported-courses` | ✅ 一致 |

## 🚨 发现的不一致问题

### 1. API文档中缺失的路由 (需要添加)

#### 会员卡管理
- `GET /api/v1/admin/membership-cards/:id/transactions` - 获取会员卡交易记录

#### 课程管理
- `POST /api/v1/admin/courses/batch` - 批量创建课程
- `POST /api/v1/admin/courses/schedule` - 课程排课
- `GET /api/v1/admin/courses/:id/supported-card-types` - 获取课程支持的会员卡类型

#### 批量课程管理
- `POST /api/v1/admin/batch-courses/preview` - 批量创建课程预览
- `POST /api/v1/admin/batch-courses/create` - 批量创建课程

#### 灵活扣减系统
- `PUT /api/v1/admin/flexible/deduction-rules/:id/toggle` - 切换扣减规则状态

#### 门店管理
- `GET /api/v1/admin/stores/:id/classrooms` - 获取门店下的教室列表

#### 用户管理
- `GET /api/v1/admin/users/menus` - 获取当前用户可访问的菜单树

### 2. API文档中多余的路由 (需要移除)

#### 课程预约相关
- `POST /api/v1/admin/courses/<<yoga_basic_course_id>>/book` - 课程预约（这个应该在app端）
- `POST /api/v1/admin/courses/check-conflict` - 检查课程时间冲突

## 📋 修复优先级

### 高优先级 (立即修复)
1. 添加会员卡交易记录接口
2. 添加批量课程管理接口
3. 添加灵活扣减规则切换状态接口

### 中优先级 (本周内修复)
1. 添加课程支持的会员卡类型接口
2. 添加门店教室列表接口
3. 移除多余的课程预约接口

### 低优先级 (下周修复)
1. 完善所有缺失的管理接口
2. 统一接口命名规范
3. 添加详细的参数文档

## 📊 一致性统计

- **完全一致**: 约85%
- **API文档缺失**: 约10%
- **API文档多余**: 约5%

总体来说，大部分接口是一致的，主要问题是API文档中缺失了一些新增的管理功能接口。
