// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblAdmin = "tbl_admin"

// TblAdmin mapped from table <tbl_admin>
type TblAdmin struct {
	ID       int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID   string    `gorm:"column:shop_id" json:"shop_id"`
	Tp       bool      `gorm:"column:tp;not null;comment:1-超级管理员 2-店长 3-销售 4-一般员工" json:"tp"` // 1-超级管理员 2-店长 3-销售 4-一般员工
	Name     string    `gorm:"column:name;not null" json:"name"`
	Mobile   string    `gorm:"column:mobile" json:"mobile"`
	Pwd      string    `gorm:"column:pwd;not null" json:"pwd"`
	Logtime  time.Time `gorm:"column:logtime" json:"logtime"`
	Email    string    `gorm:"column:email" json:"email"`
	Status   bool      `gorm:"column:status;not null;default:1" json:"status"`
	IsDelete bool      `gorm:"column:is_delete;not null" json:"is_delete"`
}

// TableName TblAdmin's table name
func (*TblAdmin) TableName() string {
	return TableNameTblAdmin
}
