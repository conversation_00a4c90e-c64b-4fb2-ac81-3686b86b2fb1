package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type AdminBookingHandler struct {
	db             *gorm.DB
	bookingService *service.BookingService
}

func NewAdminBookingHandler(db *gorm.DB, bookingService *service.BookingService) *AdminBookingHandler {
	return &AdminBookingHandler{
		db:             db,
		bookingService: bookingService,
	}
}

// GetBookings 获取预约列表（后台管理）
func (h *AdminBookingHandler) GetBookings(c *gin.Context) {
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Booking{}).
		Preload("User").
		Preload("Course").
		Preload("Course.Coach").
		Preload("Course.Store").
		Preload("MembershipCard")

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 用户筛选
	if req.UserID != "" {
		if userIDInt := cast.ToUint(req.UserID); userIDInt > 0 {
			query = query.Where("user_id = ?", userIDInt)
		}
	}

	// 教练筛选
	if req.CoachID != "" {
		if coachIDInt := cast.ToUint(req.CoachID); coachIDInt > 0 {
			query = query.Joins("JOIN courses ON bookings.course_id = courses.id").
				Where("courses.coach_id = ?", coachIDInt)
		}
	}

	// 门店筛选
	if req.StoreID != "" {
		if storeIDInt := cast.ToUint(req.StoreID); storeIDInt > 0 {
			query = query.Joins("JOIN courses ON bookings.course_id = courses.id").
				Where("courses.store_id = ?", storeIDInt)
		}
	}

	// 搜索条件
	if req.Search != "" {
		query = query.Joins("JOIN users ON bookings.user_id = users.id").
			Where("users.nick_name LIKE ? OR users.username LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("查询预约总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约列表失败"))
		return
	}

	// 分页查询
	var bookings []model.Booking
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).Order("created_at DESC").Find(&bookings).Error
	if err != nil {
		log.Error("查询预约列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约列表失败"))
		return
	}

	// 构建响应数据
	var bookingList []dto.AdminBookingInfo
	for _, booking := range bookings {
		// 获取教练信息
		var coachName string
		if len(booking.Course.CoachIDs) > 0 {
			var coach model.User
			if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", booking.Course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
				coachName = coach.NickName
			}
		}

		bookingInfo := dto.AdminBookingInfo{
			ID:            booking.ID,
			UserName:      booking.User.NickName,
			CourseName:    booking.Course.Name,
			CoachName:     coachName,
			StoreName:     booking.Course.Store.Name,
			StartTime:     booking.Course.StartTime.Format("2006-01-02 15:04:05"),
			Status:        int(booking.Status),
			PaymentMethod: string(booking.PaymentMethod),
			Amount:        booking.Amount,
			CreatedAt:     booking.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if booking.MembershipCard != nil && booking.MembershipCard.CardType != nil {
			bookingInfo.CardType = booking.MembershipCard.CardType.Name
		}

		bookingList = append(bookingList, bookingInfo)
	}

	response := dto.PageResponse{
		List:     bookingList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetBooking 获取预约详情（后台管理）
func (h *AdminBookingHandler) GetBooking(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("预约ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var booking model.Booking
	err := h.db.Preload("User").
		Preload("Course").
		Preload("Course.Coach").
		Preload("Course.Store").
		Preload("MembershipCard").
		First(&booking, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约不存在"))
		} else {
			log.Error("查询预约详情失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约详情失败"))
		}
		return
	}

	// 获取教练信息
	var coachName string
	if len(booking.Course.CoachIDs) > 0 {
		var coach model.User
		if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", booking.Course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
			coachName = coach.NickName
		}
	}

	// 构建预约详情响应
	bookingDetail := dto.AdminBookingDetail{
		ID:            booking.ID,
		UserName:      booking.User.NickName,
		UserPhone:     booking.User.Username, // 假设用户名是手机号
		CourseName:    booking.Course.Name,
		CoachName:     coachName,
		StoreName:     booking.Course.Store.Name,
		StartTime:     booking.Course.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:       booking.Course.EndTime.Format("2006-01-02 15:04:05"),
		Status:        int(booking.Status),
		PaymentMethod: string(booking.PaymentMethod),
		Amount:        booking.Amount,
		CreatedAt:     booking.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:     booking.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	if booking.MembershipCard != nil && booking.MembershipCard.CardType != nil {
		bookingDetail.CardType = booking.MembershipCard.CardType.Name
		bookingDetail.CardBalance = float64(booking.MembershipCard.RemainingAmount + booking.MembershipCard.RemainingTimes*100) // 简化余额显示
	}

	code.AutoResponse(c, bookingDetail, nil)
}

// UpdateBookingStatus 更新预约状态（后台管理）
func (h *AdminBookingHandler) UpdateBookingStatus(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("预约ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateBookingStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新预约状态参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询预约是否存在
	var booking model.Booking
	err := h.db.First(&booking, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "预约不存在"))
		} else {
			log.Error("查询预约失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约失败"))
		}
		return
	}

	// 更新预约状态
	if err := h.db.Model(&booking).Update("status", req.Status).Error; err != nil {
		log.Error("更新预约状态失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新预约状态失败"))
		return
	}

	log.Info("更新预约状态成功", "id", id, "status", req.Status)
	response := dto.MessageResponse{Message: "状态更新成功"}
	code.AutoResponse(c, response, nil)
}

// GetBookingStats 获取预约统计（后台管理）
func (h *AdminBookingHandler) GetBookingStats(c *gin.Context) {
	// 统计各状态的预约数量
	var stats []dto.BookingStatusStat

	err := h.db.Model(&model.Booking{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&stats).Error

	if err != nil {
		log.Error("查询预约统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约统计失败"))
		return
	}

	// 统计今日预约数量
	var todayCount int64
	today := "CURDATE()" // MySQL
	err = h.db.Model(&model.Booking{}).
		Where("DATE(created_at) = " + today).
		Count(&todayCount).Error

	if err != nil {
		log.Error("查询今日预约数量失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约统计失败"))
		return
	}

	response := dto.BookingStatsResponse{
		StatusStats: stats,
		TodayCount:  todayCount,
	}

	code.AutoResponse(c, response, nil)
}

// AutoCancelCourse 自动取消课程
func (h *AdminBookingHandler) AutoCancelCourse(c *gin.Context) {
	var req dto.AutoCancelCourseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定自动取消课程请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	log.Info("收到自动取消课程请求", "schedule_id", req.ScheduleID, "reason", req.Reason)

	// 调用服务层处理
	err := h.bookingService.AutoCancelCourse(req.ScheduleID, req.Reason)
	if err != nil {
		log.Error("自动取消课程失败", "schedule_id", req.ScheduleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "取消课程失败"))
		return
	}

	log.Info("自动取消课程成功", "schedule_id", req.ScheduleID)
	response := dto.MessageResponse{Message: "课程已取消，相关预约已退款"}
	code.AutoResponse(c, response, nil)
}
