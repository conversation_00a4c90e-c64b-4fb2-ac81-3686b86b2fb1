version: '3.8'

services:
  app:
    image: yogaga:latest
    container_name: yogaga_app
    restart: always
    ports:
      - "9095:9095"
    volumes:
      # 挂载配置文件目录 - 请确保宿主机路径存在
      - ./config:/etc/yogaga:ro
      # 挂载日志目录（可选）
      - ./logs:/app/logs
    environment:
      # 设置环境变量
      - APP_ENV=prod
    networks:
      - yogaga-net
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9095/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  yogaga-net:
    driver: bridge
