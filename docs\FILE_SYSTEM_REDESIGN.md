# 文件系统重新设计总结

## 🎯 设计原则

### 核心理念
1. **只有admin端需要文件上传** - 小程序端不需要上传功能
2. **统一代理上传** - 不使用复杂的预签名URL
3. **上传返回file_id** - 关联files表的元信息
4. **前端传递用途** - purpose参数指定文件用途
5. **其他地方使用file_id** - 通过ID引用文件

## 📡 API接口设计

### 上传接口
```
POST /api/v1/admin/files/upload
```

**请求参数：**
- `file` (必需) - 上传的文件
- `purpose` (必需) - 文件用途

**支持的用途：**
- `avatar` - 用户头像
- `banner` - 轮播图
- `course` - 课程图片
- `store` - 门店图片
- `document` - 文档文件

**响应格式：**
```json
{
  "code": 0,
  "msg": "success", 
  "data": {
    "file_id": "uuid-string",
    "file_name": "original-filename.jpg",
    "file_size": 102400,
    "purpose": "avatar"
  }
}
```

### 其他接口
- `GET /api/v1/admin/files` - 获取文件列表
- `DELETE /api/v1/admin/files/:id` - 删除文件
- `DELETE /api/v1/admin/files` - 批量删除文件
- `GET /api/v1/admin/files/by-entity` - 获取实体关联的文件列表

## 📊 数据库设计

### Files表结构
```sql
CREATE TABLE files (
  id VARCHAR(36) PRIMARY KEY,           -- UUID
  object_name VARCHAR(512) NOT NULL,    -- MinIO对象名
  bucket_name VARCHAR(100) NOT NULL,    -- 存储桶名
  original_filename VARCHAR(255),       -- 原始文件名
  file_name VARCHAR(255),               -- 显示文件名
  file_size BIGINT,                     -- 文件大小
  content_type VARCHAR(100),            -- MIME类型
  purpose VARCHAR(50),                  -- 文件用途 ⭐ 新增
  uploader_id INT,                      -- 上传者ID
  status VARCHAR(20) DEFAULT 'active',  -- 文件状态
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

## 🗂️ 文件存储策略

### 对象名称规则
```
{purpose}/{年}/{月}/{日}/{uuid}.{扩展名}

示例：
avatar/2025/07/16/550e8400-e29b-41d4-a716-************.jpg
banner/2025/07/16/550e8400-e29b-41d4-a716-************.png
course/2025/07/16/550e8400-e29b-41d4-a716-************.jpg
```

## 🔧 代码更改

### 路由更改
```go
// Admin端 - 保留的接口
fileGroup.POST("/upload", fileHandler.Upload)                    // ✅ 主要上传接口
fileGroup.GET("", fileHandler.GetFileList)                       // ✅ 文件列表
fileGroup.DELETE("/:id", fileHandler.DeleteFile)                 // ✅ 删除文件
fileGroup.DELETE("", fileHandler.BatchDeleteFiles)               // ✅ 批量删除

// 注释掉的接口
// fileGroup.POST("/proxy-upload", ...)                          // ❌ 已注释
// fileGroup.POST("/presigned-upload-url", ...)                  // ❌ 已注释
// fileGroup.GET("/presigned-download-url", ...)                 // ❌ 已注释

// App端 - 移除文件上传
// appAuth.POST("/files/upload", fileHandler.Upload)             // ❌ 已注释
```

### 模型更改
```go
type File struct {
    *UUIDModel
    ObjectName       string     `json:"object_name"`
    BucketName       string     `json:"bucket_name"`
    OriginalFilename string     `json:"original_filename"`
    FileName         string     `json:"file_name"`        // ⭐ 新增
    FileSize         int64      `json:"file_size"`
    ContentType      string     `json:"content_type"`
    Purpose          string     `json:"purpose"`          // ⭐ 新增
    UploaderID       uint       `json:"uploader_id"`
    Status           FileStatus `json:"status"`
    Uploader         *User      `json:"uploader,omitempty"`
}
```

### 服务层更改
```go
// 新增方法
func (s *FileService) ProxyUploadWithPurpose(
    uploaderID uint,
    fileHeader *multipart.FileHeader,
    purpose string,
) (*model.File, error)

// 新增对象名生成方法
func (s *FileService) generateObjectNameWithPurpose(filename, purpose string) string
```

## 📋 API集合更新

### Admin API集合更新
- ✅ 保留：文件上传接口（更新为purpose参数）
- ❌ 移除：代理文件上传接口
- ❌ 移除：预签名上传URL接口
- ❌ 移除：预签名下载URL接口
- ✅ 更新：文件列表接口参数（file_type → purpose）

### App API集合更新
- ❌ 移除：整个文件上传模块

## 🎯 使用方式

### 前端上传示例
```javascript
const formData = new FormData();
formData.append('file', file);
formData.append('purpose', 'avatar');

fetch('/api/v1/admin/files/upload', {
  method: 'POST',
  body: formData,
  headers: {
    'Authorization': 'Bearer ' + token
  }
})
.then(response => response.json())
.then(data => {
  const fileId = data.data.file_id;
  // 使用 fileId 关联到用户、课程等实体
});
```

### 其他地方使用
```javascript
// 在用户表中存储头像ID
user.avatar_id = fileId;

// 在课程表中存储图片ID
course.image_id = fileId;

// 在轮播图表中存储图片ID
banner.image_id = fileId;
```

## ✅ 优势总结

1. **简单明了** - 只有一个上传接口，易于理解和使用
2. **职责清晰** - admin端负责文件管理，app端只消费
3. **数据一致** - 通过file_id关联，避免URL失效问题
4. **易于维护** - 代理上传比预签名URL更稳定
5. **扩展性好** - purpose参数便于添加新的文件类型

## 🚀 下一步

1. 测试新的文件上传接口
2. 更新前端代码使用新的API
3. 迁移现有文件数据（如果需要）
4. 更新API文档

这个设计实现了你要求的：**简洁、高效、合理的文件管理方案！** 🎉
