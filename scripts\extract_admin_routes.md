# Admin 路由提取结果

## 📋 路由器中的所有Admin路由

### 1. 仪表盘
- `GET /api/v1/admin/dashboard` - 获取仪表盘数据汇总

### 2. 批量课程管理 (/batch-courses)
- `POST /api/v1/admin/batch-courses/preview` - 批量创建课程预览
- `POST /api/v1/admin/batch-courses/create` - 批量创建课程

### 3. 用户管理 (/users)
- `GET /api/v1/admin/users` - 获取用户列表
- `POST /api/v1/admin/users` - 创建管理员用户
- `PUT /api/v1/admin/users/:id/roles` - 更新用户角色权限
- `DELETE /api/v1/admin/users/:id` - 删除用户
- `GET /api/v1/admin/users/menus` - 获取当前用户可访问的菜单树

### 4. 员工管理 (/staff)
- `GET /api/v1/admin/staff` - 获取员工列表
- `GET /api/v1/admin/staff/active` - 获取在职员工列表
- `PUT /api/v1/admin/staff/:id/status` - 更新员工状态
- `GET /api/v1/admin/staff/sales` - 获取销售员工列表

### 5. 教练状态管理 (/coach-status)
- `GET /api/v1/admin/coach-status` - 获取教练列表
- `PUT /api/v1/admin/coach-status/:id/status` - 更新教练状态

### 6. 资源分配管理 (/resource-assignments)
- `POST /api/v1/admin/resource-assignments/members` - 分配会员给销售
- `GET /api/v1/admin/resource-assignments/sales/:sales_id/members` - 获取销售负责的会员列表
- `PUT /api/v1/admin/resource-assignments/members/transfer` - 转移会员给其他销售
- `GET /api/v1/admin/resource-assignments/members/unassigned` - 获取未分配的会员列表
- `DELETE /api/v1/admin/resource-assignments/members` - 移除会员分配

### 7. 用户门店分配管理 (/user-store-assignments)
- `POST /api/v1/admin/user-store-assignments` - 分配用户到门店
- `GET /api/v1/admin/user-store-assignments/users/:user_id` - 获取用户门店分配列表
- `GET /api/v1/admin/user-store-assignments/stores/:store_id` - 获取门店的用户列表
- `DELETE /api/v1/admin/user-store-assignments` - 移除用户门店分配
- `PUT /api/v1/admin/user-store-assignments/users/:user_id/platform-access` - 更新用户平台访问权限
- `GET /api/v1/admin/user-store-assignments/users` - 根据平台获取用户列表

### 8. 角色管理 (/roles)
- `POST /api/v1/admin/roles` - 创建新角色
- `GET /api/v1/admin/roles` - 获取角色列表
- `GET /api/v1/admin/roles/:id` - 获取角色详情
- `PUT /api/v1/admin/roles/:id` - 更新角色信息
- `DELETE /api/v1/admin/roles/:id` - 删除单个角色
- `DELETE /api/v1/admin/roles` - 批量删除角色
- `GET /api/v1/admin/roles/:id/permissions` - 获取角色关联的权限列表
- `PUT /api/v1/admin/roles/:id/permissions` - 更新角色权限关联

### 9. 文件管理 (/files)
- `POST /api/v1/admin/files/upload` - 统一文件上传接口
- `GET /api/v1/admin/files` - 获取文件列表
- `DELETE /api/v1/admin/files/:id` - 删除文件
- `DELETE /api/v1/admin/files` - 批量删除文件
- `GET /api/v1/admin/files/by-entity` - 获取实体关联的文件列表

### 10. 权限管理 (/permissions)
- `GET /api/v1/admin/permissions` - 获取权限列表
- `POST /api/v1/admin/permissions` - 创建新权限
- `GET /api/v1/admin/permissions/:id` - 获取权限详情
- `PUT /api/v1/admin/permissions/:id` - 更新权限信息
- `DELETE /api/v1/admin/permissions/:id` - 删除单个权限
- `DELETE /api/v1/admin/permissions` - 批量删除权限

### 11. 菜单管理 (/menus)
- `POST /api/v1/admin/menus` - 创建菜单项
- `GET /api/v1/admin/menus` - 获取菜单列表
- `GET /api/v1/admin/menus/:id` - 获取菜单详情
- `PUT /api/v1/admin/menus/:id` - 更新菜单信息
- `DELETE /api/v1/admin/menus/:id` - 删除单个菜单
- `DELETE /api/v1/admin/menus` - 批量删除菜单

### 12. 会员卡类型管理 (/membership-types)
- `GET /api/v1/admin/membership-types` - 获取会员卡类型列表
- `GET /api/v1/admin/membership-types/:id` - 获取会员卡类型详情
- `POST /api/v1/admin/membership-types` - 创建新的会员卡类型
- `PUT /api/v1/admin/membership-types/:id` - 更新会员卡类型信息
- `DELETE /api/v1/admin/membership-types/:id` - 删除会员卡类型

### 13. 会员卡实例管理 (/membership-cards)
- `GET /api/v1/admin/membership-cards` - 获取会员卡列表
- `GET /api/v1/admin/membership-cards/:id` - 获取会员卡详情
- `GET /api/v1/admin/membership-cards/:id/transactions` - 获取会员卡交易记录
- `POST /api/v1/admin/membership-cards` - 创建新会员卡
- `POST /api/v1/admin/membership-cards/:id/sub-cards` - 创建副卡
- `GET /api/v1/admin/membership-cards/:id/sub-cards` - 获取副卡列表
- `PUT /api/v1/admin/membership-cards/:id/transfer` - 转让会员卡
- `PUT /api/v1/admin/membership-cards/:id/recharge` - 会员卡充值
- `PUT /api/v1/admin/membership-cards/:id/deduct` - 会员卡扣费
- `PUT /api/v1/admin/membership-cards/:id/freeze` - 冻结/解冻会员卡
- `PUT /api/v1/admin/membership-cards/:id/upgrade` - 升级会员卡
- `PUT /api/v1/admin/membership-cards/:id/leave` - 请假会员卡
- `PUT /api/v1/admin/membership-cards/:id/extend` - 延期会员卡

### 14. 课程分类管理 (/course-categories)
- `GET /api/v1/admin/course-categories` - 获取课程分类列表
- `GET /api/v1/admin/course-categories/all` - 获取所有启用的课程分类
- `GET /api/v1/admin/course-categories/:id` - 获取课程分类详情
- `POST /api/v1/admin/course-categories` - 创建课程分类
- `PUT /api/v1/admin/course-categories/:id` - 更新课程分类
- `DELETE /api/v1/admin/course-categories/:id` - 删除课程分类

### 15. 课程管理 (/courses)
- `GET /api/v1/admin/courses` - 获取课程列表
- `GET /api/v1/admin/courses/:id` - 获取课程详情
- `POST /api/v1/admin/courses` - 创建新课程
- `PUT /api/v1/admin/courses/:id` - 更新课程信息
- `DELETE /api/v1/admin/courses/:id` - 删除课程
- `POST /api/v1/admin/courses/batch` - 批量创建课程
- `POST /api/v1/admin/courses/schedule` - 课程排课
- `GET /api/v1/admin/courses/:id/supported-card-types` - 获取课程支持的会员卡类型

### 16. 灵活扣减系统 (/flexible)
- `GET /api/v1/admin/flexible/course-types` - 获取课程类型配置列表
- `POST /api/v1/admin/flexible/course-types` - 创建课程类型配置
- `PUT /api/v1/admin/flexible/course-types/:id` - 更新课程类型配置
- `DELETE /api/v1/admin/flexible/course-types/:id` - 删除课程类型配置
- `GET /api/v1/admin/flexible/deduction-rules` - 获取灵活扣减规则列表
- `GET /api/v1/admin/flexible/deduction-rules/:id` - 获取扣减规则详情
- `POST /api/v1/admin/flexible/deduction-rules` - 创建灵活扣减规则
- `PUT /api/v1/admin/flexible/deduction-rules/:id` - 更新灵活扣减规则
- `DELETE /api/v1/admin/flexible/deduction-rules/:id` - 删除灵活扣减规则
- `PUT /api/v1/admin/flexible/deduction-rules/:id/toggle` - 切换扣减规则状态
- `POST /api/v1/admin/flexible/test-deduction` - 测试扣减规则
- `GET /api/v1/admin/flexible/card-types/:card_type_id/supported-courses` - 获取会员卡支持的课程类型

### 17. 门店管理 (/stores)
- `GET /api/v1/admin/stores` - 获取门店列表
- `GET /api/v1/admin/stores/:id` - 获取门店详情
- `POST /api/v1/admin/stores` - 创建新门店
- `PUT /api/v1/admin/stores/:id` - 更新门店信息
- `DELETE /api/v1/admin/stores/:id` - 删除门店
- `GET /api/v1/admin/stores/:id/classrooms` - 获取门店下的教室列表

### 18. 教室管理 (/classrooms)
- `GET /api/v1/admin/classrooms` - 获取教室列表
- `GET /api/v1/admin/classrooms/:id` - 获取教室详情
- `POST /api/v1/admin/classrooms` - 创建新教室
- `PUT /api/v1/admin/classrooms/:id` - 更新教室信息
- `DELETE /api/v1/admin/classrooms/:id` - 删除教室

### 19. 教练管理 (/coaches)
- `GET /api/v1/admin/coaches` - 获取教练列表
- `GET /api/v1/admin/coaches/:id` - 获取教练详情
- `PUT /api/v1/admin/coaches/:id` - 更新教练信息
- `PUT /api/v1/admin/coaches/:id/status` - 更新教练状态

### 20. 课程排期管理 (/schedules)
- `GET /api/v1/admin/schedules` - 获取课程排期列表
- `POST /api/v1/admin/schedules` - 创建课程排期
- `PUT /api/v1/admin/schedules/:id` - 更新课程排期

### 21. 预约管理 (/bookings)
- `GET /api/v1/admin/bookings` - 获取预约列表
- `GET /api/v1/admin/bookings/:id` - 获取预约详情
- `PUT /api/v1/admin/bookings/:id/status` - 更新预约状态
- `GET /api/v1/admin/bookings/stats` - 获取预约统计数据
- `POST /api/v1/admin/bookings/auto-cancel` - 自动取消课程

### 22. 消息通知管理 (/notifications)
- `GET /api/v1/admin/notifications` - 获取消息列表
- `GET /api/v1/admin/notifications/unread-count` - 获取未读消息数量
- `GET /api/v1/admin/notifications/stats` - 获取消息统计
- `PUT /api/v1/admin/notifications/:id/read` - 标记消息为已读
- `PUT /api/v1/admin/notifications/read-all` - 标记所有消息为已读
- `DELETE /api/v1/admin/notifications/:id` - 删除消息

### 23. 轮播图管理 (/banners)
- `GET /api/v1/admin/banners` - 获取轮播图列表
- `GET /api/v1/admin/banners/:id` - 获取轮播图详情
- `POST /api/v1/admin/banners` - 创建轮播图
- `PUT /api/v1/admin/banners/:id` - 更新轮播图
- `DELETE /api/v1/admin/banners/:id` - 删除轮播图

### 24. 教练轮播图管理 (/coach-banners)
- `GET /api/v1/admin/coach-banners/coach/:coach_id` - 获取教练轮播图列表
- `GET /api/v1/admin/coach-banners/:id` - 获取教练轮播图详情
- `POST /api/v1/admin/coach-banners` - 创建教练轮播图
- `PUT /api/v1/admin/coach-banners/:id` - 更新教练轮播图
- `DELETE /api/v1/admin/coach-banners/:id` - 删除教练轮播图

### 25. 报表管理 (/reports)
- `GET /api/v1/admin/reports/card-sales` - 会员卡销售报表
- `GET /api/v1/admin/reports/bookings` - 预约报表
- `GET /api/v1/admin/reports/courses` - 课程报表
- `GET /api/v1/admin/reports/coaches` - 教练报表

### 26. 操作日志管理 (/logs)
- `GET /api/v1/admin/logs` - 获取操作日志列表
- `GET /api/v1/admin/logs/:id` - 获取操作日志详情
- `DELETE /api/v1/admin/logs/:id` - 删除操作日志
- `GET /api/v1/admin/logs/stats` - 获取操作统计
- `POST /api/v1/admin/logs/clean` - 清理旧日志
- `GET /api/v1/admin/logs/modules` - 获取日志模块列表
- `GET /api/v1/admin/logs/actions` - 获取日志动作列表

### 27. 课程评价管理 (/course-reviews)
- `GET /api/v1/admin/course-reviews` - 获取课程评价列表
- `GET /api/v1/admin/course-reviews/:id` - 获取课程评价详情
- `DELETE /api/v1/admin/course-reviews/:id` - 删除课程评价
- `GET /api/v1/admin/course-reviews/stats` - 获取评价统计

### 28. 排队管理 (/queue)
- `POST /api/v1/admin/queue/courses/:course_id/notify` - 手动处理排队通知

## 📊 统计信息
- **总路由数**: 约120个
- **路由组数**: 28个
- **HTTP方法分布**:
  - GET: 约60个
  - POST: 约25个
  - PUT: 约25个
  - DELETE: 约10个
