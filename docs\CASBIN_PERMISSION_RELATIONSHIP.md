# 🔐 Casbin 与权限系统关系详解

## 📋 核心概念理解

### 🤔 你的困惑是对的！
权限管理确实涉及两个层面：
1. **数据库存储**：权限的基础数据（名称、描述等）
2. **Casbin 策略**：权限的执行规则（谁能做什么）

## 🏗️ 双层架构设计

```
┌─────────────────────────────────────────────────────────┐
│                    权限系统架构                          │
├─────────────────────────────────────────────────────────┤
│  应用层：权限检查中间件                                  │
│  ↓                                                      │
│  Casbin 层：策略执行引擎                                │
│  ↓                                                      │
│  数据库层：权限基础数据                                  │
└─────────────────────────────────────────────────────────┘
```

## 📊 数据存储分层

### 1. 数据库层（MySQL）
存储权限的**基础信息**：

```sql
-- permissions 表：权限基础数据
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100),           -- 权限名称：如 "创建用户"
    key VARCHAR(100) UNIQUE,     -- 权限标识：如 "user:create"
    description VARCHAR(255)     -- 权限描述
);

-- roles 表：角色基础数据
CREATE TABLE roles (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100),           -- 角色名称：如 "admin"
    description VARCHAR(255)
);

-- role_permissions 表：角色-权限关联
CREATE TABLE role_permissions (
    role_id BIGINT,
    permission_id BIGINT
);
```

### 2. Casbin 层（casbin_rule 表）
存储权限的**执行策略**：

```sql
-- casbin_rule 表：Casbin 策略规则
CREATE TABLE casbin_rule (
    id BIGINT PRIMARY KEY,
    ptype VARCHAR(100),          -- 策略类型：'p' = 权限策略
    v0 VARCHAR(100),             -- 角色：如 "admin"
    v1 VARCHAR(100),             -- 权限标识：如 "user:create"
    v2 VARCHAR(100),             -- HTTP 方法：如 "POST"
    v3 VARCHAR(100),             -- 扩展字段
    v4 VARCHAR(100),             -- 扩展字段
    v5 VARCHAR(100)              -- 扩展字段
);
```

## 🔄 数据流转过程

### 1. 权限创建流程

```mermaid
graph TD
    A[管理员创建权限] --> B[保存到 permissions 表]
    B --> C[分配给角色 role_permissions]
    C --> D[生成 Casbin 策略]
    D --> E[保存到 casbin_rule 表]
    E --> F[权限生效]
```

**具体步骤**：
1. **创建权限**：`POST /api/v1/admin/permissions`
   ```json
   {
     "name": "创建用户",
     "key": "user:create",
     "description": "创建新用户"
   }
   ```

2. **数据库存储**：
   ```sql
   INSERT INTO permissions (name, key, description)
   VALUES ('创建用户', 'user:create', '创建新用户');
   ```

3. **分配给角色**：`PUT /api/v1/admin/roles/1/permissions`
   ```sql
   INSERT INTO role_permissions (role_id, permission_id)
   VALUES (1, 新权限ID);
   ```

4. **生成 Casbin 策略**：
   ```sql
   INSERT INTO casbin_rule (ptype, v0, v1, v2)
   VALUES ('p', 'admin', 'user:create', 'POST');
   ```

### 2. 权限检查流程

```mermaid
graph TD
    A[用户请求 API] --> B[JWT 中间件提取角色]
    B --> C[权限中间件检查]
    C --> D[Casbin 执行策略]
    D --> E{权限检查}
    E -->|通过| F[继续处理请求]
    E -->|拒绝| G[返回权限不足]
```

**代码实现**：
```go
// 权限检查中间件
func CheckPermissionByKey(key string, enforcer *casbin.Enforcer) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 获取用户角色
        roles := c.Get("roles").([]string)

        // 2. 获取 HTTP 方法
        action := c.Request.Method

        // 3. Casbin 检查权限
        for _, role := range roles {
            allowed, _ := enforcer.Enforce(role, key, action)
            if allowed {
                c.Next()  // 权限通过
                return
            }
        }

        // 权限不足
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
        c.Abort()
    }
}
```

## 🔗 两层数据的同步机制

### 1. 初始化同步（bootstrap）

```go
// internal/bootstrap/init.go
func InitData(db *gorm.DB, enforcer *casbin.Enforcer) {
    // 1. 创建权限基础数据
    for _, permInfo := range permissionsToSeed {
        permission := model.Permission{
            Name:        permInfo.Name,
            Key:         permInfo.Key,
            Description: permInfo.Description,
        }
        db.Create(&permission)
    }

    // 2. 分配权限给角色
    adminRole.Permissions = allPermissions
    db.Save(&adminRole)

    // 3. 生成 Casbin 策略
    for _, perm := range allPermissions {
        enforcer.AddPolicy("admin", perm.Key, permInfo.Method)
    }
}
```

### 2. 运行时同步

当角色权限发生变化时：

```go
// 更新角色权限
func (h *RoleHandler) UpdatePermissions(c *gin.Context) {
    // 1. 更新数据库关联
    db.Model(&role).Association("Permissions").Replace(&permissions)

    // 2. 同步到 Casbin
    // 删除旧策略
    enforcer.RemoveFilteredPolicy(0, role.Name)

    // 添加新策略
    for _, perm := range permissions {
        enforcer.AddPolicy(role.Name, perm.Key, "GET")
        enforcer.AddPolicy(role.Name, perm.Key, "POST")
        // ... 其他 HTTP 方法
    }
}
```

## 📋 实际数据示例

### 数据库中的权限数据
```sql
-- permissions 表
| id | name     | key         | description |
|----|----------|-------------|-------------|
| 1  | 创建用户  | user:create | 创建新用户   |
| 2  | 用户列表  | user:list   | 查看用户列表 |

-- roles 表
| id | name  | description |
|----|-------|-------------|
| 1  | admin | 管理员       |

-- role_permissions 表
| role_id | permission_id |
|---------|---------------|
| 1       | 1             |
| 1       | 2             |
```

### Casbin 中的策略数据
```sql
-- casbin_rule 表
| ptype | v0    | v1          | v2   |
|-------|-------|-------------|------|
| p     | admin | user:create | POST |
| p     | admin | user:list   | GET  |
```

## 🎯 关键理解点

### 1. 数据库 ≠ Casbin
- **数据库**：存储权限的**元数据**（名称、描述、关联关系）
- **Casbin**：存储权限的**执行规则**（谁能对什么资源执行什么操作）

### 2. 为什么需要两层？
- **灵活性**：数据库存储丰富的权限信息，Casbin 专注执行
- **性能**：Casbin 针对权限检查优化，查询速度快
- **扩展性**：可以支持复杂的权限模型（RBAC、ABAC 等）

### 3. 同步是关键
- 数据库和 Casbin 必须保持同步
- 权限变更时需要同时更新两个地方
- 系统启动时需要从数据库同步到 Casbin

## 🔧 常见问题解答

### Q1: 为什么不直接用数据库做权限检查？
**A**: Casbin 提供了强大的策略引擎，支持复杂的权限模型，性能更好。

### Q2: 如果 Casbin 和数据库不同步怎么办？
**A**: 系统提供了同步检查和修复机制，可以检测并修复不一致。

### Q3: 可以只用 Casbin 不用数据库吗？
**A**: 不建议。数据库提供了权限的元数据管理，便于维护和查询。

### Q4: 权限检查的性能如何？
**A**: Casbin 在内存中执行策略，性能很高，通常在微秒级别。

## 🔍 实际操作示例

### 创建一个新权限的完整流程

```bash
# 1. 创建权限（存储到数据库）
curl -X POST http://localhost:9095/api/v1/admin/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "删除文章",
    "key": "article:delete",
    "description": "删除文章权限"
  }'

# 2. 分配给角色（更新数据库关联 + 同步到 Casbin）
curl -X PUT http://localhost:9095/api/v1/admin/roles/1/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "permission_ids": [1, 2, 3, 新权限ID]
  }'

# 3. 权限立即生效，可以在 API 中使用
# 在路由中使用新权限
articleGroup.DELETE("/:id", middleware.CheckPermissionByKey("article:delete", enforcer), articleHandler.Delete)
```

### 数据库中的变化

```sql
-- 1. permissions 表新增记录
INSERT INTO permissions (name, key, description)
VALUES ('删除文章', 'article:delete', '删除文章权限');

-- 2. role_permissions 表新增关联
INSERT INTO role_permissions (role_id, permission_id)
VALUES (1, 新权限ID);

-- 3. casbin_rule 表新增策略
INSERT INTO casbin_rule (ptype, v0, v1, v2)
VALUES ('p', 'admin', 'article:delete', 'DELETE');
```

## 🎉 总结

我们的权限系统采用了**双层架构**：
- **数据库层**：管理权限的基础数据和关联关系
- **Casbin 层**：执行权限检查策略

这种设计既保证了数据的完整性和可维护性，又提供了高性能的权限检查能力。两层数据通过同步机制保持一致，共同构成了完整的权限管理系统。

### 🔑 关键要点
1. **权限数据存储在数据库**：便于管理和查询
2. **权限策略执行在 Casbin**：高性能的权限检查
3. **两层数据必须同步**：确保系统一致性
4. **API 操作会同时更新两层**：自动保持同步
