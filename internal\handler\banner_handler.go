package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// BannerHandler 轮播图处理器
type BannerHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewBannerHandler 创建轮播图处理器实例
func NewBannerHandler(db *gorm.DB, storage storage.Storage) *BannerHandler {
	return &BannerHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetBanners 获取轮播图列表
func (h *BannerHandler) GetBanners(c *gin.Context) {
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Banner{})

	// 位置筛选
	position := c.Query("position")
	if position != "" {
		query = query.Where("position = ?", position)
	}

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			bannerStatus := enum.BannerStatus(statusInt)
			if bannerStatus.IsValid() {
				query = query.Where("status = ?", bannerStatus)
			}
		}
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("统计轮播图总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 分页查询
	var banners []model.Banner
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&banners).Error

	if err != nil {
		log.Error("查询轮播图列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询失败"))
		return
	}

	// 转换为DTO格式，包含图片URL
	var bannerList []dto.Banner
	for _, banner := range banners {
		bannerDTO := dto.Banner{
			ID:          banner.ID,
			Title:       banner.Title,
			Image:       h.urlConverter.ConvertFileIDToURL(banner.FileID),
			LinkURL:     banner.LinkURL,
			Description: banner.Description,
			SortOrder:   banner.SortOrder,
		}
		bannerList = append(bannerList, bannerDTO)
	}

	// 构建响应
	response := dto.PageResponse{
		List:     bannerList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetBanner 获取轮播图详情
func (h *BannerHandler) GetBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var banner model.Banner
	if err := h.db.First(&banner, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "轮播图不存在"))
		} else {
			log.Error("查询轮播图失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		}
		return
	}

	// 转换为DTO格式，包含图片URL
	bannerDTO := dto.Banner{
		ID:          banner.ID,
		Title:       banner.Title,
		Image:       h.urlConverter.ConvertFileIDToURL(banner.FileID),
		LinkURL:     banner.LinkURL,
		Description: banner.Description,
		SortOrder:   banner.SortOrder,
	}

	code.AutoResponse(c, bannerDTO, nil)
}

// CreateBanner 创建轮播图
func (h *BannerHandler) CreateBanner(c *gin.Context) {
	var req dto.CreateBannerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建轮播图参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 创建轮播图
	banner := model.Banner{
		Title:       req.Title,
		FileID:      req.FileID, // 使用 FileID 替代 Image
		LinkURL:     req.LinkURL,
		Description: req.Description,
		SortOrder:   req.SortOrder,
		Position:    enum.BannerPosition(req.Position),
		Status:      enum.BannerStatusEnabled, // 默认启用
	}

	if err := h.db.Create(&banner).Error; err != nil {
		log.Error("创建轮播图失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建轮播图失败"))
		return
	}

	log.Info("创建轮播图成功", "id", banner.ID, "title", banner.Title)
	code.AutoResponse(c, banner, nil)
}

// UpdateBanner 更新轮播图
func (h *BannerHandler) UpdateBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateBannerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新轮播图参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询轮播图是否存在
	var banner model.Banner
	if err := h.db.First(&banner, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "轮播图不存在"))
		} else {
			log.Error("查询轮播图失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		}
		return
	}

	// 更新字段
	updates := dto.NewUpdateFields().
		SetIfNotEmpty("title", req.Title).
		SetIfNotEmpty("file_id", req.FileID).
		SetIfNotEmpty("link_url", req.LinkURL).
		SetIfNotEmpty("description", req.Description).
		SetIfNotNil("sort_order", req.SortOrder).
		SetIfNotNil("status", req.Status).
		SetIfNotEmpty("position", req.Position)

	if !updates.HasFields() {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "没有需要更新的字段"))
		return
	}

	if err := h.db.Model(&banner).Updates(updates.GetFields()).Error; err != nil {
		log.Error("更新轮播图失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新轮播图失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.First(&banner, id)

	log.Info("更新轮播图成功", "id", id)
	code.AutoResponse(c, banner, nil)
}

// DeleteBanner 删除轮播图
func (h *BannerHandler) DeleteBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 删除轮播图
	if err := h.db.Delete(&model.Banner{}, id).Error; err != nil {
		log.Error("删除轮播图失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除轮播图失败"))
		return
	}

	log.Info("删除轮播图成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// GetActiveBanners 获取启用的轮播图（前端使用）
func (h *BannerHandler) GetActiveBanners(c *gin.Context) {
	position := c.DefaultQuery("position", "home")

	var banners []model.Banner
	err := h.db.Where("status = ? AND position = ?", enum.BannerStatusEnabled, position).
		Order("sort_order ASC, created_at DESC").
		Find(&banners).Error

	if err != nil {
		log.Error("查询启用轮播图失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		return
	}

	// 转换为DTO格式，包含图片URL
	var bannerList []dto.Banner
	for _, banner := range banners {
		bannerDTO := dto.Banner{
			ID:          banner.ID,
			Title:       banner.Title,
			Image:       h.urlConverter.ConvertFileIDToURL(banner.FileID),
			LinkURL:     banner.LinkURL,
			Description: banner.Description,
			SortOrder:   banner.SortOrder,
		}
		bannerList = append(bannerList, bannerDTO)
	}

	code.AutoResponse(c, bannerList, nil)
}
