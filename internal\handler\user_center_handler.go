package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserCenterHandler 用户中心处理器
type UserCenterHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewUserCenterHandler 创建用户中心处理器实例
func NewUserCenterHandler(db *gorm.DB, storage storage.Storage) *UserCenterHandler {
	return &UserCenterHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetUserProfile 获取用户资料
func (h *UserCenterHandler) GetUserProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var user model.User
	err := h.db.First(&user, userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		} else {
			log.Error("查询用户失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询用户失败"))
		}
		return
	}

	// 构建用户资料响应
	var avatarURL string
	if user.AvatarID != nil {
		avatarURL = h.urlConverter.ConvertFileIDToURL(*user.AvatarID)
	}

	profile := dto.UserProfile{
		ID:       user.ID.String(),
		Username: user.Username,
		NickName: user.NickName,
		Avatar:   avatarURL,
		Gender:   int(user.Gender),
		Country:  user.Country,
		Province: user.Province,
		City:     user.City,
		IsActive: user.IsActive,
	}

	code.AutoResponse(c, profile, nil)
}

// UpdateUserProfile 更新用户资料
func (h *UserCenterHandler) UpdateUserProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.UpdateUserProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新用户资料参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询用户是否存在
	var user model.User
	err := h.db.First(&user, userID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		} else {
			log.Error("查询用户失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询用户失败"))
		}
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.NickName != "" {
		updates["nick_name"] = req.NickName
	}
	if req.Avatar != "" {
		updates["avatar_url"] = req.Avatar
	}
	if req.Gender != nil {
		updates["gender"] = *req.Gender
	}
	if req.Country != "" {
		updates["country"] = req.Country
	}
	if req.Province != "" {
		updates["province"] = req.Province
	}
	if req.City != "" {
		updates["city"] = req.City
	}

	if len(updates) > 0 {
		if err := h.db.Model(&user).Updates(updates).Error; err != nil {
			log.Error("更新用户资料失败", "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新用户资料失败"))
			return
		}
	}

	log.Info("更新用户资料成功", "user_id", userID)
	response := dto.MessageResponse{Message: "更新成功"}
	code.AutoResponse(c, response, nil)
}

// GetUserStats 获取用户统计信息
func (h *UserCenterHandler) GetUserStats(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查询预约统计
	var totalBookings int64
	h.db.Model(&model.Booking{}).Where("user_id = ?", userID).Count(&totalBookings)

	var completedBookings int64
	h.db.Model(&model.Booking{}).Where("user_id = ? AND status = 3", userID).Count(&completedBookings)

	var cancelledBookings int64
	h.db.Model(&model.Booking{}).Where("user_id = ? AND status = 4", userID).Count(&cancelledBookings)

	// 查询会员卡统计
	var totalCards int64
	h.db.Model(&model.MembershipCard{}).Where("user_id = ?", userID).Count(&totalCards)

	var activeCards int64
	h.db.Model(&model.MembershipCard{}).Where("user_id = ? AND status = 1", userID).Count(&activeCards)

	// 查询收藏统计
	var totalFavorites int64
	h.db.Model(&model.Favorite{}).Where("user_id = ?", userID).Count(&totalFavorites)

	// 构建统计响应
	stats := dto.UserStatsInfo{
		BookingStats: dto.UserBookingStatsInfo{
			Total:     int(totalBookings),
			Completed: int(completedBookings),
			Cancelled: int(cancelledBookings),
		},
		MembershipStats: dto.MembershipStats{
			TotalCards:  int(totalCards),
			ActiveCards: int(activeCards),
		},
		FavoriteStats: dto.FavoriteStats{
			Total: int(totalFavorites),
		},
	}

	code.AutoResponse(c, stats, nil)
}

// GetUserMembershipSummary 获取用户会员卡汇总
func (h *UserCenterHandler) GetUserMembershipSummary(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查询用户的会员卡
	var cards []model.MembershipCard
	err := h.db.Preload("CardType").
		Where("user_id = ? AND status = 1", userID).
		Order("created_at DESC").
		Find(&cards).Error

	if err != nil {
		log.Error("查询用户会员卡失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡失败"))
		return
	}

	// 构建会员卡汇总
	var cardSummaries []dto.MembershipCardSummary
	for _, card := range cards {
		summary := dto.MembershipCardSummary{
			ID:              card.ID,
			CardNumber:      card.CardNumber,
			TypeName:        card.CardType.Name,
			Category:        string(card.CardType.Category),
			RemainingTimes:  card.RemainingTimes,
			RemainingAmount: card.RemainingAmount,
			StartDate:       card.StartDate.Format("2006-01-02"),
			Status:          int(card.Status),
		}

		// 设置结束日期
		if !card.EndDate.IsZero() {
			endDate := card.EndDate.Format("2006-01-02")
			summary.EndDate = &endDate
		}

		// 判断卡片状态
		switch card.CardType.Category {
		case "times":
			summary.IsExpired = card.RemainingTimes <= 0
		case "period":
			summary.IsExpired = !card.EndDate.IsZero() && card.EndDate.Before(time.Now())
		case "balance":
			summary.IsExpired = card.RemainingAmount <= 0
		}

		cardSummaries = append(cardSummaries, summary)
	}

	response := dto.UserMembershipSummary{
		Cards: cardSummaries,
		Total: len(cardSummaries),
	}

	code.AutoResponse(c, response, nil)
}

// GetRecentBookings 获取最近预约
func (h *UserCenterHandler) GetRecentBookings(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查询最近的预约记录
	var bookings []model.Booking
	err := h.db.Preload("Schedule").Preload("Schedule.Course").
		Preload("Schedule.Course.Coach").Preload("Schedule.Course.Store").
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(5).
		Find(&bookings).Error

	if err != nil {
		log.Error("查询最近预约失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询预约失败"))
		return
	}

	// 构建预约列表
	var recentBookings []dto.RecentBookingItem
	for _, booking := range bookings {
		if booking.Schedule == nil || booking.Schedule.Course == nil {
			continue // 跳过无效数据
		}

		item := dto.RecentBookingItem{
			ID:         booking.ID,
			CourseName: booking.Schedule.Course.Name,
			CoachName: func() string {
				if len(booking.Schedule.Course.CoachIDs) > 0 {
					var coach model.User
					if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", booking.Schedule.Course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
						return getCoachName(&coach)
					}
				}
				return ""
			}(),
			StoreName:  booking.Schedule.Course.Store.Name,
			StartTime:  booking.Schedule.Course.StartTime.Format("2006-01-02 15:04:05"),
			Status:     int(booking.Status),
			StatusText: getBookingStatusText(int(booking.Status)),
		}
		recentBookings = append(recentBookings, item)
	}

	code.AutoResponse(c, recentBookings, nil)
}

// getBookingStatusText 获取预约状态文本
func getBookingStatusText(status int) string {
	bookingStatus := enum.BookingStatus(status)
	// 返回英文状态值，前端负责国际化处理
	return bookingStatus.String()
}
