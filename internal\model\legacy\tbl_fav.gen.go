// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblFav = "tbl_fav"

// TblFav mapped from table <tbl_fav>
type TblFav struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	MemberID   int32     `gorm:"column:member_id;not null" json:"member_id"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Tpid       int32     `gorm:"column:tpid;not null" json:"tpid"`
	Tpval      int32     `gorm:"column:tpval;not null" json:"tpval"`
}

// TableName TblFav's table name
func (*TblFav) TableName() string {
	return TableNameTblFav
}
