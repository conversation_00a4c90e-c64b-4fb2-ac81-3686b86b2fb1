#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整最终测试
验证所有修复后的功能，包括卡号生成和延期功能
"""

import requests
import time
from datetime import datetime

class CompleteFinalTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            return False
        except Exception as e:
            return False
    
    def test_card_number_generation(self) -> dict:
        """测试卡号生成修复"""
        print("\n🔢 测试卡号生成修复...")
        
        timestamp = int(time.time())
        
        # 创建用户
        user_data = {
            "username": f"cardnum_test_{timestamp}",
            "email": f"cardnum_test_{timestamp}@example.com",
            "password": "123456",
            "phone": f"138{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/admin/users", json=user_data)
        if response.status_code != 200 or response.json().get('code') != 0:
            return {'创建用户': False}
        
        user_id = response.json().get('data', {}).get('id')
        
        # 获取卡种
        response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", 
                                  params={"page": "1", "page_size": "20", "status": "1"})
        
        if response.status_code != 200 or response.json().get('code') != 0:
            return {'获取卡种': False}
        
        card_types = response.json().get('data', {}).get('list', [])
        if not card_types:
            return {'获取卡种': False}
        
        card_type = card_types[0]
        
        # 快速连续创建多张卡，测试卡号唯一性
        card_numbers = []
        success_count = 0
        
        for i in range(3):
            card_data = {
                "user_id": user_id,
                "card_type_id": card_type.get('id'),
                "start_date": f"2025-07-19T{3 + i:02d}:00:00Z",
                "purchase_price": int(card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 0,
                "remark": f"卡号测试{i+1}"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=card_data)
            
            if response.status_code == 200 and response.json().get('code') == 0:
                card_info = response.json().get('data', {})
                card_number = card_info.get('card_number')
                card_numbers.append(card_number)
                success_count += 1
                print(f"   卡{i+1}: {card_number}")
                time.sleep(0.1)  # 短暂延迟
        
        # 检查卡号唯一性
        unique_numbers = len(set(card_numbers))
        
        return {
            '创建用户': True,
            '获取卡种': True,
            '连续开卡成功': success_count == 3,
            '卡号唯一性': unique_numbers == success_count,
            '卡号生成修复': unique_numbers == success_count and success_count == 3
        }
    
    def test_card_payment_opening_fixed(self) -> dict:
        """测试修复后的会员卡支付开卡"""
        print("\n💳 测试修复后的会员卡支付开卡...")
        
        timestamp = int(time.time())
        
        # 创建用户
        user_data = {
            "username": f"payment_test_{timestamp}",
            "email": f"payment_test_{timestamp}@example.com",
            "password": "123456",
            "phone": f"139{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/admin/users", json=user_data)
        if response.status_code != 200 or response.json().get('code') != 0:
            return {'创建用户': False}
        
        user_id = response.json().get('data', {}).get('id')
        
        # 获取卡种
        response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", 
                                  params={"page": "1", "page_size": "20", "status": "1"})
        
        card_types = response.json().get('data', {}).get('list', [])
        balance_card_type = next((ct for ct in card_types if ct.get('category') == 'balance'), None)
        times_card_type = next((ct for ct in card_types if ct.get('category') == 'times'), None)
        
        if not balance_card_type or not times_card_type:
            return {'找到卡种': False}
        
        # 创建储值卡
        balance_card_data = {
            "user_id": user_id,
            "card_type_id": balance_card_type.get('id'),
            "start_date": f"2025-07-19T{6 + timestamp % 10:02d}:00:00Z",
            "purchase_price": 100000,
            "discount": 1.0,
            "payment_method": "cash",
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "支付测试储值卡"
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=balance_card_data)
        if response.status_code != 200 or response.json().get('code') != 0:
            return {'创建储值卡': False}
        
        balance_card_id = response.json().get('data', {}).get('id')
        
        # 使用储值卡支付开次数卡
        times_card_data = {
            "user_id": user_id,
            "card_type_id": times_card_type.get('id'),
            "start_date": f"2025-07-19T{7 + timestamp % 10:02d}:00:00Z",
            "purchase_price": 50000,
            "discount": 1.0,
            "payment_method": "card",
            "payment_card_id": balance_card_id,
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "修复后会员卡支付测试"
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=times_card_data)
        
        success = response.status_code == 200 and response.json().get('code') == 0
        
        if success:
            new_card_info = response.json().get('data', {})
            print(f"   ✅ 会员卡支付开卡成功！新卡号: {new_card_info.get('card_number')}")
        
        return {
            '创建用户': True,
            '找到卡种': True,
            '创建储值卡': True,
            '会员卡支付开卡': success
        }
    
    def test_extension_functionality(self) -> dict:
        """测试延期功能"""
        print("\n📅 测试延期功能...")
        
        timestamp = int(time.time())
        
        # 创建用户
        user_data = {
            "username": f"extend_test_{timestamp}",
            "email": f"extend_test_{timestamp}@example.com",
            "password": "123456",
            "phone": f"137{timestamp % 100000000:08d}",
            "role_ids": []
        }
        
        response = self.session.post(f"{self.base_url}/api/v1/admin/users", json=user_data)
        if response.status_code != 200 or response.json().get('code') != 0:
            return {'创建用户': False}
        
        user_id = response.json().get('data', {}).get('id')
        
        # 获取卡种
        response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", 
                                  params={"page": "1", "page_size": "20", "status": "1"})
        
        card_types = response.json().get('data', {}).get('list', [])
        period_card_type = next((ct for ct in card_types if ct.get('category') == 'period'), None)
        balance_card_type = next((ct for ct in card_types if ct.get('category') == 'balance'), None)
        times_card_type = next((ct for ct in card_types if ct.get('category') == 'times'), None)
        
        results = {'创建用户': True}
        
        # 测试期限卡延期
        if period_card_type:
            period_card_data = {
                "user_id": user_id,
                "card_type_id": period_card_type.get('id'),
                "start_date": f"2025-07-19T{8 + timestamp % 10:02d}:00:00Z",
                "purchase_price": int(period_card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 2,
                "remark": "延期测试期限卡"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=period_card_data)
            if response.status_code == 200 and response.json().get('code') == 0:
                period_card_id = response.json().get('data', {}).get('id')
                
                # 测试延期
                extend_data = {
                    "extend_days": 30,
                    "reason": "期限卡延期测试"
                }
                
                response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{period_card_id}/extend", 
                                          json=extend_data)
                
                results['期限卡延期'] = response.status_code == 200 and response.json().get('code') == 0
                if results['期限卡延期']:
                    print("   ✅ 期限卡延期成功")
        
        # 测试储值卡延期
        if balance_card_type:
            balance_card_data = {
                "user_id": user_id,
                "card_type_id": balance_card_type.get('id'),
                "start_date": f"2025-07-19T{9 + timestamp % 10:02d}:00:00Z",
                "purchase_price": int(balance_card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 0,
                "remark": "延期测试储值卡"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=balance_card_data)
            if response.status_code == 200 and response.json().get('code') == 0:
                balance_card_id = response.json().get('data', {}).get('id')
                
                # 测试延期
                extend_data = {
                    "extend_days": 30,
                    "reason": "储值卡延期测试"
                }
                
                response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{balance_card_id}/extend", 
                                          json=extend_data)
                
                results['储值卡延期'] = response.status_code == 200 and response.json().get('code') == 0
                if results['储值卡延期']:
                    print("   ✅ 储值卡延期成功")
        
        # 测试次数卡延期（应该失败）
        if times_card_type:
            times_card_data = {
                "user_id": user_id,
                "card_type_id": times_card_type.get('id'),
                "start_date": f"2025-07-19T{10 + timestamp % 10:02d}:00:00Z",
                "purchase_price": int(times_card_type.get('price', 0)),
                "discount": 1.0,
                "payment_method": "cash",
                "available_stores": [],
                "daily_booking_limit": 0,
                "remark": "延期测试次数卡"
            }
            
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=times_card_data)
            if response.status_code == 200 and response.json().get('code') == 0:
                times_card_id = response.json().get('data', {}).get('id')
                
                # 测试延期（应该失败）
                extend_data = {
                    "extend_days": 30,
                    "reason": "次数卡延期测试"
                }
                
                response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{times_card_id}/extend", 
                                          json=extend_data)
                
                # 次数卡延期应该失败
                results['次数卡延期拒绝'] = response.status_code != 200 or response.json().get('code') != 0
                if results['次数卡延期拒绝']:
                    print("   ✅ 次数卡正确拒绝延期（符合业务逻辑）")
        
        return results
    
    def run_complete_final_test(self):
        """运行完整最终测试"""
        print("🏆 开始完整最终功能测试...")
        print("=" * 80)
        
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return
        
        all_results = {}
        
        # 执行所有测试
        all_results.update(self.test_card_number_generation())
        all_results.update(self.test_card_payment_opening_fixed())
        all_results.update(self.test_extension_functionality())
        
        # 生成最终报告
        print("\n📊 完整最终测试结果:")
        print("=" * 80)
        
        total_tests = len(all_results)
        passed_tests = sum(1 for result in all_results.values() if result is True)
        failed_tests = sum(1 for result in all_results.values() if result is False)
        
        for test_name, result in all_results.items():
            if result is True:
                status = "✅ 通过"
            elif result is False:
                status = "❌ 失败"
            else:
                status = "⚠️  跳过"
            print(f"{test_name:<25}: {status}")
        
        print("=" * 80)
        print(f"📈 最终测试统计:")
        print(f"   总测试项: {total_tests}")
        print(f"   通过: {passed_tests}")
        print(f"   失败: {failed_tests}")
        
        if total_tests > 0:
            pass_rate = (passed_tests / total_tests) * 100
            print(f"   通过率: {pass_rate:.1f}%")
        
        # 最终结论
        print(f"\n🎯 最终结论:")
        print("=" * 80)
        
        if pass_rate >= 95:
            print("🎉 所有功能修复完成！系统完美运行！")
            print("✅ 卡号生成问题已修复")
            print("✅ 会员卡支付开卡正常")
            print("✅ 延期功能完整实现")
            print("✅ 系统可以立即投入生产使用")
        elif pass_rate >= 85:
            print("🎉 主要功能修复完成！系统运行良好！")
            print("✅ 大部分问题已解决")
            print("⚠️  少数功能需要微调")
        else:
            print("⚠️  部分功能仍需要修复")
        
        return all_results

if __name__ == "__main__":
    tester = CompleteFinalTester()
    tester.run_complete_final_test()
