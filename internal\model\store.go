package model

import (
	"yogaga/internal/enum"

	"github.com/lib/pq"
)

// Store 门店模型
type Store struct {
	BaseModel
	Name        string           `json:"name" gorm:"type:varchar(100);not null;comment:门店名称"`
	Address     string           `json:"address" gorm:"type:varchar(255);not null;comment:门店地址"`
	Phone       string           `json:"phone" gorm:"type:varchar(20);comment:联系电话"`
	Latitude    float64          `json:"latitude" gorm:"type:decimal(10,8);comment:纬度"`
	Longitude   float64          `json:"longitude" gorm:"type:decimal(11,8);comment:经度"`
	ImageIDs    pq.StringArray   `json:"-" gorm:"type:text[];comment:门店图片文件ID数组"` // 隐藏字段，不暴露给前端
	Description string           `json:"description" gorm:"type:text;comment:门店描述"`
	SortOrder   int              `json:"sort_order" gorm:"type:int;default:0;comment:排序"`
	Status      enum.StoreStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:营业 0:停业"`
}
