package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"
	"yogaga/pkg/utils"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type RoleHandler struct {
	db       *gorm.DB
	enforcer *casbin.Enforcer
}

func NewRoleHandler(db *gorm.DB, enforcer *casbin.Enforcer) *RoleHandler {
	return &RoleHandler{
		db:       db,
		enforcer: enforcer,
	}
}

// Create 创建一个新角色
func (h *RoleHandler) Create(c *gin.Context) {
	var req dto.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建角色请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 使用角色服务创建角色
	roleService := service.NewRoleService(h.db)
	role, err := roleService.CreateRole(req.Name, req.Description)
	if err != nil {
		if err == gorm.ErrDuplicatedKey {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "角色名称已存在"))
			return
		}
		log.Error("创建角色失败", "name", req.Name, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建角色失败"))
		return
	}

	log.Info("创建角色成功", "name", req.Name)
	code.AutoResponse(c, role, nil)
}

// List 列出所有角色
func (h *RoleHandler) List(c *gin.Context) {
	var roles []model.Role
	// Preload Permissions for each role
	if err := h.db.Preload("Permissions").Find(&roles).Error; err != nil {
		log.Error("查询角色列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询角色列表失败"))
		return
	}
	code.AutoResponse(c, roles, nil)
}

// Get 获取单个角色的详细信息
func (h *RoleHandler) Get(c *gin.Context) {
	roleID := cast.ToInt(c.Param("id"))
	if roleID <= 0 {
		log.Error("无效的角色ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidRoleID, "无效的角色ID"))
		return
	}

	var role model.Role
	// Preload Permissions to get associated permissions
	if err := h.db.Preload("Permissions").First(&role, roleID).Error; err != nil {
		log.Error("查询角色详情失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "角色不存在"))
		return
	}

	code.AutoResponse(c, role, nil)
}

// Update 更新角色信息
func (h *RoleHandler) Update(c *gin.Context) {
	roleID := cast.ToInt(c.Param("id"))
	if roleID <= 0 {
		log.Error("无效的角色ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidRoleID, "无效的角色ID"))
		return
	}

	var role model.Role
	if err := h.db.First(&role, roleID).Error; err != nil {
		log.Error("查找角色失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "角色不存在"))
		return
	}

	var req dto.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新角色请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	h.db.Model(&role).Updates(model.Role{Name: req.Name, Description: req.Description})
	code.AutoResponse(c, role, nil)
}

// Delete 删除一个角色（只能删除扩展角色）
func (h *RoleHandler) Delete(c *gin.Context) {
	roleID := cast.ToInt(c.Param("id"))
	if roleID <= 0 {
		log.Error("无效的角色ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidRoleID, "无效的角色ID"))
		return
	}

	// 使用角色服务删除角色
	roleService := service.NewRoleService(h.db)
	if err := roleService.DeleteRole(uint(roleID)); err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "角色不存在"))
			return
		}
		if err == gorm.ErrInvalidData {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "角色正在使用中，不能删除"))
			return
		}
		log.Error("删除角色失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除角色失败"))
		return
	}

	log.Info("角色删除成功", "role_id", roleID)
	code.AutoResponse(c, nil, nil)
}

// GetPermissions 获取角色拥有的权限
func (h *RoleHandler) GetPermissions(c *gin.Context) {
	roleID := cast.ToInt(c.Param("id"))
	if roleID <= 0 {
		log.Error("无效的角色ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidRoleID, "无效的角色ID"))
		return
	}

	var role model.Role
	if err := h.db.Preload("Permissions").First(&role, roleID).Error; err != nil {
		log.Error("查找角色失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "角色不存在"))
		return
	}

	code.AutoResponse(c, role.Permissions, nil)
}

// UpdatePermissions 更新角色拥有的权限
func (h *RoleHandler) UpdatePermissions(c *gin.Context) {
	roleID := cast.ToInt(c.Param("id"))
	if roleID <= 0 {
		log.Error("无效的角色ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidRoleID, "无效的角色ID"))
		return
	}

	var req dto.UpdateRolePermissionsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	var role model.Role
	if err := h.db.First(&role, roleID).Error; err != nil {
		log.Error("查找角色失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "角色不存在"))
		return
	}

	var permissions []model.Permission
	if err := h.db.Find(&permissions, req.PermissionIDs).Error; err != nil {
		log.Error("查找权限失败", "permission_ids", req.PermissionIDs, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "权限查询失败"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新数据库关联
	if err := tx.Model(&role).Association("Permissions").Replace(&permissions); err != nil {
		tx.Rollback()
		log.Error("更新角色权限失败", "role_id", roleID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "更新角色权限失败"))
		return
	}

	// 2. 同步到 Casbin - 删除角色的所有旧策略
	if h.enforcer != nil {
		// 删除该角色的所有权限策略
		_, err := h.enforcer.RemoveFilteredPolicy(0, role.Name)
		if err != nil {
			tx.Rollback()
			log.Error("删除角色旧策略失败", "role_name", role.Name, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "同步权限策略失败"))
			return
		}

		// 为角色添加新的权限策略
		for _, perm := range permissions {
			// 根据权限的常见 HTTP 方法添加策略
			httpMethods := []string{"GET", "POST", "PUT", "DELETE"}
			for _, method := range httpMethods {
				_, err := h.enforcer.AddPolicy(role.Name, perm.Key, method)
				if err != nil {
					tx.Rollback()
					log.Error("添加角色新策略失败", "role_name", role.Name, "permission_key", perm.Key, "method", method, "error", err)
					code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "同步权限策略失败"))
					return
				}
			}
		}

		// 保存 Casbin 策略
		err = h.enforcer.SavePolicy()
		if err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "保存权限策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新角色权限失败"))
		return
	}

	log.Info("角色权限更新成功", "role_id", roleID, "role_name", role.Name, "permission_count", len(permissions))
	code.AutoResponse(c, nil, nil)
}

// BatchDelete 批量删除角色
func (h *RoleHandler) BatchDelete(c *gin.Context) {
	// 使用通用函数解析批量ID
	ids, err := utils.ParseBatchIDs(c)
	if err != nil {
		log.Error("解析批量删除角色ID失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	if len(ids) == 0 {
		log.Error("未提供要删除的角色ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请提供要删除的角色ID"))
		return
	}

	log.Info("收到批量删除角色请求", "ids", ids)

	// 查询要删除的角色信息
	var roles []model.Role
	if err := h.db.Where("id IN ?", ids).Find(&roles).Error; err != nil {
		log.Error("查询角色失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询角色失败"))
		return
	}

	if len(roles) != len(ids) {
		log.Warn("部分角色不存在", "requested_ids", ids, "found_count", len(roles))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "部分角色不存在"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 删除数据库中的角色
	if err := tx.Delete(&model.Role{}, ids).Error; err != nil {
		tx.Rollback()
		log.Error("批量删除角色失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "批量删除角色失败"))
		return
	}

	// 2. 清理 Casbin 中的相关策略
	if h.enforcer != nil {
		for _, role := range roles {
			// 删除角色的所有权限策略
			_, err := h.enforcer.RemoveFilteredPolicy(0, role.Name)
			if err != nil {
				tx.Rollback()
				log.Error("删除角色 Casbin 策略失败", "role_name", role.Name, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "清理权限策略失败"))
				return
			}

			// 删除用户-角色关联策略
			_, err = h.enforcer.RemoveFilteredGroupingPolicy(1, role.Name)
			if err != nil {
				tx.Rollback()
				log.Error("删除用户-角色关联策略失败", "role_name", role.Name, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "清理用户角色关联失败"))
				return
			}
		}

		// 保存 Casbin 策略
		err = h.enforcer.SavePolicy()
		if err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "保存权限策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "批量删除角色失败"))
		return
	}

	log.Info("批量删除角色成功", "ids", ids, "count", len(ids))
	response := dto.MessageResponse{Message: "Roles deleted successfully"}
	code.AutoResponse(c, response, nil)
}
