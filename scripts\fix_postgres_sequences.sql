-- 修复PostgreSQL自增序列问题
-- 当手动插入数据或数据迁移后，序列值可能没有正确更新，导致主键冲突

-- 1. 修复 menus 表的序列
SELECT setval('menus_id_seq', COALESCE((SELECT MAX(id) FROM menus), 1), true);

-- 2. 修复其他可能有问题的表的序列
-- 用户表
SELECT setval('users_id_seq', COALESCE((SELECT MAX(id) FROM users), 1), true);

-- 角色表
SELECT setval('roles_id_seq', COALESCE((SELECT MAX(id) FROM roles), 1), true);

-- 权限表
SELECT setval('permissions_id_seq', COALESCE((SELECT MAX(id) FROM permissions), 1), true);

-- 门店表
SELECT setval('stores_id_seq', COALESCE((SELECT MAX(id) FROM stores), 1), true);

-- 课程分类表
SELECT setval('course_categories_id_seq', COALESCE((SELECT MAX(id) FROM course_categories), 1), true);

-- 课程表
SELECT setval('courses_id_seq', COALESCE((SELECT MAX(id) FROM courses), 1), true);

-- 教室表
SELECT setval('class_rooms_id_seq', COALESCE((SELECT MAX(id) FROM class_rooms), 1), true);

-- 课程排期表
SELECT setval('class_schedules_id_seq', COALESCE((SELECT MAX(id) FROM class_schedules), 1), true);

-- 会员卡类型表
SELECT setval('membership_card_types_id_seq', COALESCE((SELECT MAX(id) FROM membership_card_types), 1), true);

-- 会员卡表
SELECT setval('membership_cards_id_seq', COALESCE((SELECT MAX(id) FROM membership_cards), 1), true);

-- 预约表
SELECT setval('bookings_id_seq', COALESCE((SELECT MAX(id) FROM bookings), 1), true);

-- 预约队列表
SELECT setval('booking_queues_id_seq', COALESCE((SELECT MAX(id) FROM booking_queues), 1), true);

-- 预约申请表
SELECT setval('booking_applications_id_seq', COALESCE((SELECT MAX(id) FROM booking_applications), 1), true);

-- 轮播图表
SELECT setval('banners_id_seq', COALESCE((SELECT MAX(id) FROM banners), 1), true);

-- 教练轮播图表
SELECT setval('coach_banners_id_seq', COALESCE((SELECT MAX(id) FROM coach_banners), 1), true);

-- 收藏表
SELECT setval('favorites_id_seq', COALESCE((SELECT MAX(id) FROM favorites), 1), true);

-- 操作日志表
SELECT setval('operation_logs_id_seq', COALESCE((SELECT MAX(id) FROM operation_logs), 1), true);

-- 管理员通知表
SELECT setval('admin_notifications_id_seq', COALESCE((SELECT MAX(id) FROM admin_notifications), 1), true);

-- 会员卡交易记录表
SELECT setval('membership_card_transactions_id_seq', COALESCE((SELECT MAX(id) FROM membership_card_transactions), 1), true);

-- 会员卡请假记录表
SELECT setval('membership_card_leaves_id_seq', COALESCE((SELECT MAX(id) FROM membership_card_leaves), 1), true);

-- 课程类型配置表
SELECT setval('course_type_configs_id_seq', COALESCE((SELECT MAX(id) FROM course_type_configs), 1), true);

-- 灵活扣减规则表
SELECT setval('flexible_deduction_rules_id_seq', COALESCE((SELECT MAX(id) FROM flexible_deduction_rules), 1), true);

-- 用户门店分配表
SELECT setval('user_store_assignments_id_seq', COALESCE((SELECT MAX(id) FROM user_store_assignments), 1), true);

-- 会员销售分配表
SELECT setval('member_sales_assignments_id_seq', COALESCE((SELECT MAX(id) FROM member_sales_assignments), 1), true);

-- 资源分配表
SELECT setval('resource_assignments_id_seq', COALESCE((SELECT MAX(id) FROM resource_assignments), 1), true);

-- 迁移状态表
SELECT setval('migration_status_id_seq', COALESCE((SELECT MAX(id) FROM migration_status), 1), true);

-- 文件表
SELECT setval('files_id_seq', COALESCE((SELECT MAX(id) FROM files), 1), true);

-- 显示修复结果
SELECT 
    schemaname,
    tablename,
    sequencename,
    last_value
FROM pg_sequences 
WHERE schemaname = 'public'
ORDER BY tablename;
