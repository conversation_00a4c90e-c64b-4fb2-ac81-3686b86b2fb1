// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblRoom = "tbl_rooms"

// TblRoom mapped from table <tbl_rooms>
type TblRoom struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID     int32     `gorm:"column:shop_id;not null" json:"shop_id"`
	Title      string    `gorm:"column:title" json:"title"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete   int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	PeopleMax  int32     `gorm:"column:people_max" json:"people_max"`
}

// TableName TblRoom's table name
func (*TblRoom) TableName() string {
	return TableNameTblRoom
}
