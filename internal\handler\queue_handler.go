package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type QueueHandler struct {
	db                  *gorm.DB
	notificationService *service.NotificationService
}

func NewQueueHandler(db *gorm.DB, notificationService *service.NotificationService) *QueueHandler {
	return &QueueHandler{
		db:                  db,
		notificationService: notificationService,
	}
}

// JoinQueue 加入排队
func (h *QueueHandler) JoinQueue(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	var req dto.JoinQueueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定加入排队参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证课程是否存在
	var course model.Course
	err := h.db.First(&course, req.CourseID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		} else {
			log.Error("查询课程失败", "course_id", req.CourseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		}
		return
	}

	// 检查是否已经预约
	var existingBooking model.Booking
	err = h.db.Where("user_id = ? AND course_id = ? AND status IN (1, 2)", userID, req.CourseID).First(&existingBooking).Error
	if err == nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "您已经预约了该课程"))
		return
	}

	// 检查是否已经在排队
	var existingQueue model.BookingQueue
	err = h.db.Where("user_id = ? AND course_id = ? AND status = 1", userID, req.CourseID).First(&existingQueue).Error
	if err == nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "您已经在排队中"))
		return
	}

	// 获取下一个排队号
	var maxQueueNumber int
	h.db.Model(&model.BookingQueue{}).Where("course_id = ?", req.CourseID).Select("COALESCE(MAX(queue_number), 0)").Scan(&maxQueueNumber)

	// 创建排队记录
	queue := model.BookingQueue{
		UserID:      userID.(string),
		CourseID:    req.CourseID,
		QueueNumber: maxQueueNumber + 1,
		Status:      1, // 排队中
	}

	err = h.db.Create(&queue).Error
	if err != nil {
		log.Error("创建排队记录失败", "user_id", userID, "course_id", req.CourseID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "加入排队失败"))
		return
	}

	log.Info("用户加入排队成功", "user_id", userID, "course_id", req.CourseID, "queue_number", queue.QueueNumber)

	response := dto.JoinQueueResponse{
		QueueID:     queue.ID,
		QueueNumber: queue.QueueNumber,
		Message:     "加入排队成功",
	}

	code.AutoResponse(c, response, nil)
}

// GetQueueStatus 获取排队状态
func (h *QueueHandler) GetQueueStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	courseIDStr := c.Param("course_id")
	courseID := cast.ToUint(courseIDStr)
	if courseID == 0 {
		log.Error("课程ID格式错误", "course_id", courseIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程ID格式错误"))
		return
	}

	// 查询排队记录
	var queue model.BookingQueue
	err := h.db.Where("user_id = ? AND course_id = ? AND status = 1", userID, courseID).First(&queue).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "未找到排队记录"))
		} else {
			log.Error("查询排队记录失败", "user_id", userID, "course_id", courseID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排队记录失败"))
		}
		return
	}

	// 计算当前排队位置
	var currentPosition int64
	h.db.Model(&model.BookingQueue{}).
		Where("course_id = ? AND status = 1 AND queue_number <= ?", courseID, queue.QueueNumber).
		Count(&currentPosition)

	// 查询课程当前预约情况
	var currentBookings int64
	h.db.Model(&model.Booking{}).Where("course_id = ? AND status IN (1, 2)", courseID).Count(&currentBookings)

	var course model.Course
	h.db.First(&course, courseID)

	response := dto.QueueStatusResponse{
		QueueID:         queue.ID,
		QueueNumber:     queue.QueueNumber,
		CurrentPosition: int(currentPosition),
		TotalCapacity:   course.Capacity,
		CurrentBookings: int(currentBookings),
		AvailableSpots:  course.Capacity - int(currentBookings),
	}

	code.AutoResponse(c, response, nil)
}

// CancelQueue 取消排队
func (h *QueueHandler) CancelQueue(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	queueIDStr := c.Param("queue_id")
	queueID := cast.ToUint(queueIDStr)
	if queueID == 0 {
		log.Error("排队ID格式错误", "queue_id", queueIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "排队ID格式错误"))
		return
	}

	// 查询排队记录
	var queue model.BookingQueue
	err := h.db.Where("id = ? AND user_id = ? AND status = 1", queueID, userID).First(&queue).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "排队记录不存在"))
		} else {
			log.Error("查询排队记录失败", "queue_id", queueID, "user_id", userID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排队记录失败"))
		}
		return
	}

	// 更新排队状态为已取消
	err = h.db.Model(&queue).Update("status", 4).Error
	if err != nil {
		log.Error("取消排队失败", "queue_id", queueID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "取消排队失败"))
		return
	}

	log.Info("用户取消排队成功", "user_id", userID, "queue_id", queueID)

	response := dto.MessageResponse{Message: "取消排队成功"}
	code.AutoResponse(c, response, nil)
}

// GetUserQueues 获取用户的排队列表
func (h *QueueHandler) GetUserQueues(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查询用户的排队记录
	var queues []model.BookingQueue
	err := h.db.Preload("Course").Preload("Course.Coach").Preload("Course.Store").
		Where("user_id = ? AND status IN (1, 2)", userID).
		Order("created_at DESC").Find(&queues).Error

	if err != nil {
		log.Error("查询用户排队列表失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排队列表失败"))
		return
	}

	// 构建响应数据
	var queueList []dto.UserQueueInfo
	for _, queue := range queues {
		// 计算当前排队位置
		var currentPosition int64
		h.db.Model(&model.BookingQueue{}).
			Where("course_id = ? AND status = 1 AND queue_number <= ?", queue.CourseID, queue.QueueNumber).
			Count(&currentPosition)

		queueInfo := dto.UserQueueInfo{
			QueueID:    queue.ID,
			CourseID:   queue.CourseID,
			CourseName: queue.Course.Name,
			CoachName: func() string {
				if len(queue.Course.CoachIDs) > 0 {
					var coach model.User
					if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", queue.Course.CoachIDs[0], true, true).First(&coach).Error; err == nil {
						return coach.NickName
					}
				}
				return ""
			}(),
			StoreName:       queue.Course.Store.Name,
			StartTime:       queue.Course.StartTime.Format("2006-01-02 15:04:05"),
			QueueNumber:     queue.QueueNumber,
			CurrentPosition: int(currentPosition),
			Status:          int(queue.Status),
			CreatedAt:       queue.CreatedAt.Format("2006-01-02 15:04:05"),
		}

		if queue.NotifiedAt != nil {
			queueInfo.NotifiedAt = queue.NotifiedAt.Format("2006-01-02 15:04:05")
		}

		queueList = append(queueList, queueInfo)
	}

	code.AutoResponse(c, queueList, nil)
}

// ProcessQueueNotification 处理排队通知（当有空余名额时）
func (h *QueueHandler) ProcessQueueNotification(c *gin.Context) {
	courseIDStr := c.Param("course_id")
	courseID := cast.ToUint(courseIDStr)
	if courseID == 0 {
		log.Error("课程ID格式错误", "course_id", courseIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "课程ID格式错误"))
		return
	}

	// 处理排队转预约通知
	err := h.notificationService.ProcessQueueToBooking(uint(courseID))
	if err != nil {
		log.Error("处理排队通知失败", "course_id", courseID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "处理排队通知失败"))
		return
	}

	response := dto.MessageResponse{Message: "排队通知处理成功"}
	code.AutoResponse(c, response, nil)
}
