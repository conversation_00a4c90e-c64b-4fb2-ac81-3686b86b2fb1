# 门店更新接口测试脚本

这些脚本用于测试修复后的门店更新接口，特别是验证 `image_ids` 数组字段的更新功能。

## 脚本说明

### 1. test_store_update.py
完整的门店更新测试脚本，包含：
- 自动登录
- 创建测试门店
- 更新门店信息（重点测试 image_ids 字段）
- 验证更新结果
- 清理测试数据

### 2. quick_store_test.py
快速测试脚本，用于：
- 测试现有门店的更新功能
- 专门验证 image_ids 字段更新
- 简化的测试流程

## 使用方法

### 前提条件
1. 确保服务器运行在 `localhost:9095`
2. 确保有管理员账户：用户名 `admin`，密码 `admin123`
3. 安装 Python 依赖：
   ```bash
   pip install requests
   ```

### 运行测试

#### 方法1：自动启动服务器并测试（推荐）
```bash
cd scripts/py
python start_server_and_test.py
```
此方法会自动：
- 检查 Go 环境
- 启动服务器
- 运行测试
- 停止服务器

#### 方法2：手动启动服务器后测试
1. 先启动服务器：
   ```bash
   # 在项目根目录
   go run main.go
   ```

2. 然后运行测试：
   ```bash
   cd scripts/py
   python quick_store_test.py
   ```

#### 方法3：完整测试
```bash
cd scripts/py
python test_store_update.py
```

## 测试内容

### 主要测试点
1. **登录功能** - 验证管理员登录
2. **门店创建** - 测试 image_ids 字段的创建
3. **门店更新** - 重点测试 image_ids 数组字段更新
4. **数据验证** - 确认更新后的数据正确性

### 关键验证
- ✅ `image_ids` 字段能否正确接收 `[]string` 类型数据
- ✅ PostgreSQL `text[]` 类型转换是否正常
- ✅ 更新后的数据是否与预期一致

## 预期结果

### 成功输出示例
```
🧪 门店更新接口测试工具
🔐 正在登录...
✅ 登录成功
🏪 创建测试门店...
✅ 创建测试门店成功，ID: 123
🔄 更新门店 ID: 123...
   新的 image_ids: ['uuid1', 'uuid2', 'uuid3']
✅ 门店更新成功
✅ image_ids 字段更新验证成功
🗑️ 删除测试门店 ID: 123...
✅ 测试门店删除成功
🎉 测试通过！门店更新接口工作正常
```

### 失败情况
如果看到类似错误：
```
ERROR: column "image_ids" is of type text[] but expression is of type record (SQLSTATE 42804)
```
说明修复未生效，需要检查代码修复情况。

## 故障排除

### 常见问题
1. **连接失败** - 检查服务器是否运行在 9095 端口
2. **登录失败** - 确认用户名密码是否正确
3. **权限错误** - 确认 admin 用户有门店管理权限

### 调试模式
可以修改脚本中的 `base_url` 来测试不同环境：
```python
base_url = "http://your-server:port"
```

## 修复验证

这些脚本专门用于验证以下修复：
1. `internal/handler/store_handler.go` 中 `image_ids` 字段更新
2. `internal/model/store.go` 中 `ImageIDs` 字段类型定义
3. PostgreSQL 数组类型的正确处理

如果测试通过，说明 PostgreSQL 数组类型问题已经完全修复。
