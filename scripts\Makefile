# Admin API 一致性检查 Makefile
# 提供便捷的命令来运行各种一致性检查

.PHONY: help check quick-check full-check ci-check clean install

# 默认目标
help:
	@echo "🔍 Admin API 一致性检查工具"
	@echo "================================"
	@echo ""
	@echo "可用命令:"
	@echo "  make check        - 运行快速一致性检查 (Python版本)"
	@echo "  make quick-check  - 运行快速一致性检查 (Python版本)"
	@echo "  make full-check   - 运行完整一致性检查 (Go版本)"
	@echo "  make ci-check     - CI模式检查 (失败时退出码非0)"
	@echo "  make install      - 安装依赖和工具"
	@echo "  make clean        - 清理临时文件"
	@echo ""
	@echo "报告文件:"
	@echo "  scripts/quick_consistency_report.json  - 快速检查报告"
	@echo "  scripts/consistency_report.json        - 完整检查报告"
	@echo "  scripts/consistency_report.html        - HTML格式报告"

# 快速检查 (默认)
check: quick-check

# Python版本快速检查
quick-check:
	@echo "🚀 运行快速一致性检查..."
	@python scripts/quick_consistency_check.py

# Go版本完整检查
full-check:
	@echo "🚀 运行完整一致性检查..."
	@if [ -f "scripts/admin_api_consistency_checker.go" ]; then \
		echo "编译检查工具..."; \
		go build -o scripts/consistency_checker scripts/admin_api_consistency_checker.go; \
		echo "运行检查..."; \
		./scripts/consistency_checker; \
		rm -f scripts/consistency_checker; \
	else \
		echo "❌ 找不到Go检查工具源码"; \
		exit 1; \
	fi

# PowerShell版本完整检查 (Windows)
full-check-ps:
	@echo "🚀 运行PowerShell版本完整检查..."
	@powershell -ExecutionPolicy Bypass -File scripts/run_consistency_check.ps1

# CI模式检查
ci-check:
	@echo "🚀 运行CI模式一致性检查..."
	@python scripts/quick_consistency_check.py
	@if [ $$? -ne 0 ]; then \
		echo "❌ CI检查失败"; \
		exit 1; \
	else \
		echo "✅ CI检查通过"; \
	fi

# 生成HTML报告
html-report: full-check
	@echo "📄 生成HTML报告..."
	@powershell -ExecutionPolicy Bypass -File scripts/run_consistency_check.ps1 -OutputFormat html

# 安装依赖
install:
	@echo "📦 检查依赖..."
	@echo "检查Python..."
	@python --version || (echo "❌ 需要安装Python"; exit 1)
	@echo "检查Go..."
	@go version || (echo "❌ 需要安装Go"; exit 1)
	@echo "✅ 所有依赖已就绪"

# 清理临时文件
clean:
	@echo "🧹 清理临时文件..."
	@rm -f scripts/consistency_checker
	@rm -f scripts/consistency_checker.exe
	@rm -f scripts/quick_consistency_report.json
	@rm -f scripts/consistency_report.json
	@rm -f scripts/consistency_report.html
	@echo "✅ 清理完成"

# 验证必要文件
verify-files:
	@echo "📋 验证必要文件..."
	@if [ ! -f "routers/router.go" ]; then \
		echo "❌ 缺少路由器文件: routers/router.go"; \
		exit 1; \
	fi
	@if [ ! -f "api-collections/admin/admin_api.json" ]; then \
		echo "❌ 缺少API文档文件: api-collections/admin/admin_api.json"; \
		exit 1; \
	fi
	@echo "✅ 所有必要文件存在"

# 运行所有检查
all-checks: verify-files quick-check full-check
	@echo "🎉 所有检查完成！"

# 开发模式 - 监控文件变化并自动检查
watch:
	@echo "👀 监控模式 - 文件变化时自动检查..."
	@echo "监控文件: routers/router.go, api-collections/admin/admin_api.json"
	@echo "按 Ctrl+C 停止监控"
	@while true; do \
		inotifywait -e modify routers/router.go api-collections/admin/admin_api.json 2>/dev/null && \
		echo "📁 检测到文件变化，运行检查..." && \
		make quick-check; \
		sleep 2; \
	done

# 生成检查统计
stats:
	@echo "📊 生成一致性统计..."
	@if [ -f "scripts/quick_consistency_report.json" ]; then \
		python -c "import json; data=json.load(open('scripts/quick_consistency_report.json')); print(f'一致性率: {data[\"consistency_rate\"]:.1f}%'); print(f'匹配接口: {data[\"matched_count\"]}/{data[\"total_routes\"]}')"; \
	else \
		echo "❌ 请先运行检查生成报告"; \
	fi
