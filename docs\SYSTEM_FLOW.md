# 系统交互流程图

## 🔄 完整的权限验证流程

```mermaid
graph TD
    A[用户登录] --> B[验证用户名密码]
    B --> C[查询用户角色]
    C --> D[生成 JWT Token]
    D --> E[返回 Token 给前端]
    
    E --> F[前端请求 API]
    F --> G[JWT 认证中间件]
    G --> H[解析 Token 获取角色]
    H --> I[权限检查中间件]
    I --> J[Casbin 权限验证]
    J --> K{有权限?}
    K -->|是| L[继续处理请求]
    K -->|否| M[返回 403 错误]
    
    L --> N[处理业务逻辑]
    N --> O[返回响应]
```

## 🎯 菜单权限过滤流程

```mermaid
graph TD
    A[用户请求菜单] --> B[JWT 认证获取角色]
    B --> C[查询所有菜单]
    C --> D[构建菜单树]
    D --> E[开始权限过滤]
    
    E --> F{菜单类型}
    F -->|目录| G[检查是否有权限]
    F -->|页面| H[检查权限或公共访问]
    F -->|按钮| I[必须有权限]
    
    G --> J{有子菜单可访问?}
    J -->|是| K[显示目录]
    J -->|否| L{目录是公共的?}
    L -->|是| K
    L -->|否| M[隐藏目录]
    
    H --> N{有权限或公共?}
    N -->|是| O[显示页面]
    N -->|否| P[隐藏页面]
    
    I --> Q{有权限?}
    Q -->|是| R[显示按钮]
    Q -->|否| S[隐藏按钮]
    
    K --> T[递归处理子菜单]
    O --> T
    R --> T
    T --> U[返回过滤后的菜单树]
```

## 🏗️ 数据关系图

```mermaid
erDiagram
    User ||--o{ UserRole : has
    Role ||--o{ UserRole : belongs_to
    Role ||--o{ RolePermission : has
    Permission ||--o{ RolePermission : belongs_to
    Menu ||--o| Permission : may_require
    Menu ||--o{ Menu : parent_child
    
    User {
        uint id PK
        string username
        string platform
        bool is_active
    }
    
    Role {
        uint id PK
        string name
        string description
    }
    
    Permission {
        uint id PK
        string name
        string key
        string description
    }
    
    Menu {
        uint id PK
        string title
        string path
        int type
        uint parent_id FK
        string permission_key FK
        int sort
        bool hidden
        int status
    }
    
    UserRole {
        uint user_id FK
        uint role_id FK
    }
    
    RolePermission {
        uint role_id FK
        uint permission_id FK
    }
    
    CasbinRule {
        string ptype
        string v0
        string v1
        string v2
    }
```

## 🔐 权限检查时序图

```mermaid
sequenceDiagram
    participant F as Frontend
    participant A as AuthMiddleware
    participant P as PermissionMiddleware
    participant C as Casbin
    participant H as Handler
    participant DB as Database
    
    F->>A: HTTP Request + JWT Token
    A->>A: 解析 JWT Token
    A->>A: 提取用户角色 ["admin", "editor"]
    A->>F: 设置 roles 到 context
    
    F->>P: 继续请求处理
    P->>P: 获取权限标识 "user:create"
    P->>P: 获取 HTTP 方法 "POST"
    
    loop 检查每个角色
        P->>C: Enforce("admin", "user:create", "POST")
        C->>DB: 查询策略规则
        DB-->>C: 返回策略结果
        C-->>P: 返回权限结果 (true/false)
        
        alt 权限通过
            P->>H: 继续处理请求
            break
        else 权限拒绝
            P->>P: 检查下一个角色
        end
    end
    
    alt 所有角色都无权限
        P-->>F: 返回 403 权限不足
    else 有权限
        H->>DB: 执行业务逻辑
        DB-->>H: 返回结果
        H-->>F: 返回成功响应
    end
```

## 🎨 菜单渲染流程

```mermaid
graph LR
    A[前端启动] --> B[用户登录]
    B --> C[获取 JWT Token]
    C --> D[请求用户菜单]
    D --> E[服务端权限过滤]
    E --> F[返回可访问菜单]
    F --> G[前端渲染菜单]
    
    G --> H[用户点击菜单]
    H --> I[路由跳转]
    I --> J[页面权限检查]
    J --> K{有权限?}
    K -->|是| L[渲染页面]
    K -->|否| M[显示无权限页面]
    
    L --> N[页面内按钮权限检查]
    N --> O[显示/隐藏操作按钮]
```

## 📊 系统架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        前端层 (Frontend)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   登录页面   │  │   菜单组件   │  │   权限指令   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                        网关层 (Gateway)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  JWT 认证   │  │  权限检查   │  │  日志记录   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                       业务层 (Business)                     │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │  用户管理   │  │  角色管理   │  │  菜单管理   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                       数据层 (Data)                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   MySQL     │  │   Casbin    │  │    Redis    │        │
│  │   数据库    │  │   策略库    │  │    缓存     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 🔄 权限更新流程

```mermaid
graph TD
    A[管理员修改权限] --> B[更新角色权限关联]
    B --> C[更新 Casbin 策略]
    C --> D[清除相关缓存]
    D --> E[通知在线用户]
    E --> F[用户重新获取菜单]
    F --> G[前端更新界面]
```

## 🎯 关键设计决策

### 1. 为什么选择 Casbin？
- **灵活性**：支持多种访问控制模型 (ACL, RBAC, ABAC)
- **性能**：内置缓存机制，支持高并发
- **可扩展**：支持自定义匹配器和策略
- **多语言**：支持多种编程语言

### 2. 为什么菜单权限可选？
- **灵活性**：支持公共菜单（如首页、帮助）
- **用户体验**：避免过度的权限控制
- **维护性**：减少不必要的权限配置

### 3. 为什么支持多角色？
- **现实需求**：用户在组织中可能有多个职责
- **权限灵活性**：权限取并集，提供更大的灵活性
- **扩展性**：支持复杂的组织结构

### 4. JWT vs Session？
- **无状态**：JWT 不需要服务器存储状态
- **分布式**：适合微服务架构
- **性能**：减少数据库查询
- **安全性**：支持过期时间和刷新机制
