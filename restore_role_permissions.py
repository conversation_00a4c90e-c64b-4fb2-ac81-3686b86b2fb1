#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
角色权限分配恢复脚本
使用方法: python restore_role_permissions.py
"""

import psycopg2
import yaml
import sys
import os

def load_config():
    """加载配置文件"""
    config_path = "config.yaml"
    if not os.path.exists(config_path):
        print("❌ 找不到config.yaml文件")
        sys.exit(1)
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    return config['Database']

def get_db_connection(db_config):
    """获取数据库连接"""
    try:
        conn = psycopg2.connect(
            host=db_config['Host'],
            port=db_config['Port'],
            user=db_config['UserName'],
            password=db_config['Password'],
            database=db_config['DBName']
        )
        return conn
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        sys.exit(1)

def restore_role_permissions(conn):
    """恢复角色权限分配"""
    cursor = conn.cursor()
    
    try:
        print("🔄 开始恢复角色权限分配...")
        
        # 开始事务
        cursor.execute("BEGIN;")
        
        # 1. 清理现有数据
        print("🧹 清理现有角色权限关联...")
        cursor.execute("DELETE FROM role_permissions;")
        
        print("🧹 清理Casbin权限策略...")
        cursor.execute("DELETE FROM casbin_rule WHERE ptype = 'p';")
        
        # 2. 恢复角色权限分配
        print("📝 恢复角色权限分配...")
        
        # 超级管理员 (ID: 1) - 所有权限
        super_admin_permissions = [
            1, 2, 3, 4, 5, 11, 12, 13, 14, 15, 16, 17, 21, 22, 23, 24, 25, 31, 32, 33, 34, 35,
            41, 42, 43, 44, 51, 52, 53, 54, 61, 62, 63, 64, 71, 72, 73, 74,
            81, 82, 83, 84, 85, 86, 91, 92, 93,
            101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112
        ]
        
        # 店长 (ID: 2)
        manager_permissions = [
            4, 5, 14, 24, 25, 34, 35, 44, 51, 52, 53, 54, 61, 62, 63, 64, 71, 72, 73, 74,
            81, 82, 83, 84, 85, 86, 91, 92, 93, 101, 105, 106, 111, 112
        ]
        
        # 销售 (ID: 3)
        sales_permissions = [
            4, 5, 24, 25, 34, 35, 44, 54, 61, 62, 64, 71, 72, 74, 91, 92,
            101, 105, 106, 112
        ]
        
        # 前台 (ID: 4)
        receptionist_permissions = [
            4, 5, 24, 25, 34, 35, 44, 54, 61, 62, 64, 74, 91,
            101, 105, 106, 112
        ]
        
        # 教练 (ID: 5)
        coach_permissions = [
            4, 5, 24, 25, 34, 35, 44, 54, 61, 64, 91,
            101, 105, 106, 112
        ]
        
        # 插入角色权限分配
        role_permissions = [
            (1, "超级管理员", super_admin_permissions),
            (2, "店长", manager_permissions),
            (3, "销售", sales_permissions),
            (4, "前台", receptionist_permissions),
            (5, "教练", coach_permissions)
        ]
        
        total_assignments = 0
        for role_id, role_name, permissions in role_permissions:
            for perm_id in permissions:
                cursor.execute(
                    "INSERT INTO role_permissions (role_id, permission_id) VALUES (%s, %s)",
                    (role_id, perm_id)
                )
                total_assignments += 1
            print(f"✅ {role_name}: {len(permissions)}个权限")
        
        # 3. 恢复Casbin策略
        print("🔧 恢复Casbin策略...")
        
        # 权限映射 (permission_id -> (key, method))
        permission_mapping = {
            1: ('user:create', 'POST'), 2: ('user:update_roles', 'PUT'), 3: ('user:delete', 'DELETE'),
            4: ('user:list', 'GET'), 5: ('user:detail', 'GET'),
            11: ('role:create', 'POST'), 12: ('role:update', 'PUT'), 13: ('role:delete', 'DELETE'),
            14: ('role:list', 'GET'), 15: ('role:update_permissions', 'PUT'), 16: ('role:get', 'GET'),
            17: ('role:get_permissions', 'GET'),
            21: ('permission:create', 'POST'), 22: ('permission:update', 'PUT'), 23: ('permission:delete', 'DELETE'),
            24: ('permission:list', 'GET'), 25: ('permission:view', 'GET'),
            31: ('menu:create', 'POST'), 32: ('menu:update', 'PUT'), 33: ('menu:delete', 'DELETE'),
            34: ('menu:list', 'GET'), 35: ('menu:get', 'GET'),
            41: ('store:create', 'POST'), 42: ('store:update', 'PUT'), 43: ('store:delete', 'DELETE'),
            44: ('store:list', 'GET'),
            51: ('course:create', 'POST'), 52: ('course:update', 'PUT'), 53: ('course:delete', 'DELETE'),
            54: ('course:list', 'GET'),
            61: ('booking:create', 'POST'), 62: ('booking:update', 'PUT'), 63: ('booking:cancel', 'PUT'),
            64: ('booking:list', 'GET'),
            71: ('membership:create', 'POST'), 72: ('membership:update', 'PUT'), 73: ('membership:delete', 'DELETE'),
            74: ('membership:list', 'GET'),
            81: ('file:upload', 'POST'), 82: ('file:delete', 'DELETE'), 83: ('file:list', 'GET'),
            84: ('file:get_presigned_upload_url', 'GET'), 85: ('file:get_presigned_download_url', 'GET'),
            86: ('file:proxy_upload', 'POST'),
            91: ('dashboard:view', 'GET'), 92: ('report:view', 'GET'), 93: ('data:export', 'POST'),
            101: ('flexible:course_types:list', 'GET'), 102: ('flexible:course_types:create', 'POST'),
            103: ('flexible:course_types:update', 'PUT'), 104: ('flexible:course_types:delete', 'DELETE'),
            105: ('flexible:deduction_rules:list', 'GET'), 106: ('flexible:deduction_rules:detail', 'GET'),
            107: ('flexible:deduction_rules:create', 'POST'), 108: ('flexible:deduction_rules:update', 'PUT'),
            109: ('flexible:deduction_rules:delete', 'DELETE'), 110: ('flexible:deduction_rules:toggle', 'PUT'),
            111: ('flexible:test_deduction', 'POST'), 112: ('flexible:card_supported_courses', 'GET')
        }
        
        total_policies = 0
        for role_id, role_name, permissions in role_permissions:
            for perm_id in permissions:
                if perm_id in permission_mapping:
                    perm_key, method = permission_mapping[perm_id]
                    cursor.execute(
                        "INSERT INTO casbin_rule (ptype, v0, v1, v2) VALUES ('p', %s, %s, %s)",
                        (role_name, perm_key, method)
                    )
                    total_policies += 1
        
        # 提交事务
        cursor.execute("COMMIT;")
        
        print(f"✅ 恢复完成: {total_assignments}个角色权限分配, {total_policies}个Casbin策略")
        
        # 验证结果
        print("\n📊 验证结果:")
        cursor.execute("""
            SELECT r.name as role_name, COUNT(rp.permission_id) as permission_count
            FROM roles r
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            GROUP BY r.id, r.name
            ORDER BY r.id
        """)
        
        results = cursor.fetchall()
        for role_name, count in results:
            print(f"  {role_name}: {count}个权限")
        
    except Exception as e:
        cursor.execute("ROLLBACK;")
        print(f"❌ 恢复失败: {e}")
        sys.exit(1)
    finally:
        cursor.close()

def main():
    print("🔄 角色权限分配恢复脚本")
    print("=" * 40)
    
    # 加载配置
    print("📁 加载配置文件...")
    db_config = load_config()
    
    # 连接数据库
    print("🔗 连接数据库...")
    conn = get_db_connection(db_config)
    
    try:
        # 执行恢复
        restore_role_permissions(conn)
        print("\n🎉 角色权限分配恢复完成！")
        print("✨ 建议重启应用以确保权限生效")
        
    finally:
        conn.close()

if __name__ == "__main__":
    main()
