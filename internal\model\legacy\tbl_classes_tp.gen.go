// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblClassesTp = "tbl_classes_tp"

// TblClassesTp mapped from table <tbl_classes_tp>
type TblClassesTp struct {
	ID               int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CategoryID       bool      `gorm:"column:category_id;not null;default:1" json:"category_id"`
	ShopID           int32     `gorm:"column:shop_id" json:"shop_id"`
	ShopIds          string    `gorm:"column:shop_ids" json:"shop_ids"`
	Title            string    `gorm:"column:title" json:"title"`
	Feature          string    `gorm:"column:feature" json:"feature"`
	Note             string    `gorm:"column:note" json:"note"`
	Notice           string    `gorm:"column:notice" json:"notice"`
	CreateTime       time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Picture          string    `gorm:"column:picture" json:"picture"`
	PictureOri       string    `gorm:"column:picture_ori" json:"picture_ori"`
	DetailTopFile    string    `gorm:"column:detail_top_file" json:"detail_top_file"`
	IsHome           bool      `gorm:"column:is_home;not null;default:1" json:"is_home"`
	IsRecommend      bool      `gorm:"column:is_recommend;not null;default:1" json:"is_recommend"`
	IsDelete         int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	Minute           int32     `gorm:"column:minute" json:"minute"`
	Difficult        int32     `gorm:"column:difficult;default:1" json:"difficult"`
	PeopleNum        int32     `gorm:"column:people_num" json:"people_num"`
	PeopleMin        int32     `gorm:"column:people_min" json:"people_min"`
	IsCancel         bool      `gorm:"column:is_cancel;not null" json:"is_cancel"`
	CancelLateMinute int32     `gorm:"column:cancel_late_minute;not null" json:"cancel_late_minute"`
}

// TableName TblClassesTp's table name
func (*TblClassesTp) TableName() string {
	return TableNameTblClassesTp
}
