# 📅 取消预约功能完整实现文档

## 🎯 **功能概述**

取消预约功能提供完整的预约取消管理体验，包括弹框确认、退款处理、微信提醒和系统自动取消等功能。

## 📱 **功能特性**

### 1. **📋 取消预约弹框信息**

#### 获取取消预约信息
```bash
GET /api/v1/app/bookings/:id/cancel-info
Authorization: Bearer <token>

Response:
{
  "booking_id": 123,
  "course_name": "瑜伽基础课程",
  "coach_name": "张老师",
  "store_name": "Yogaga瑜伽馆(朝阳店)",
  "start_time": "2024-01-15 09:00",
  "people_count": 1,
  "refund_method": "会员卡退款",
  "refund_amount": 0,
  "refund_times": 1,
  "can_cancel": true,
  "cancel_message": "当前课程火爆，确定要取消吗？"
}
```

#### 弹框显示逻辑
- **弹框一**：显示课程信息、退款方式、退款金额
  - 按钮："我再想想"、"确认取消"
- **弹框二**：课程火爆提醒（预约率≥80%时显示）
  - 提示："当前课程火爆，确定要取消吗？"
  - 按钮："我再想想"、"确认"

### 2. **💰 退款处理逻辑**

#### 会员卡退款
```json
{
  "refund_method": "会员卡退款",
  "refund_times": 1,     // 退回次数
  "refund_amount": 0     // 退回金额(分)
}
```

#### 微信支付退款（预留）
```json
{
  "refund_method": "微信支付退款",
  "refund_amount": 8800  // 退回金额(分)
}
```

### 3. **🔔 微信提醒功能**

#### 用户取消预约通知
```json
{
  "template_id": "cancel_notice",
  "data": {
    "thing1": "瑜伽基础课程",              // 课程名称
    "time2": "2024-01-15 09:00",        // 取消时间
    "thing3": "用户主动取消"              // 取消原因
  }
}
```

#### 系统自动取消通知
```json
{
  "template_id": "course_cancel",
  "data": {
    "thing1": "瑜伽基础课程",              // 课程名称
    "time2": "2024-01-15 09:00",        // 课程时间
    "thing3": "Yogaga瑜伽馆(朝阳店)",    // 门店名称
    "thing4": "人数不足",                 // 取消原因
    "phrase5": "已退款"                  // 状态
  }
}
```

### 4. **🤖 系统自动取消课程**

#### 管理员手动取消课程
```bash
POST /api/v1/admin/bookings/auto-cancel
Authorization: Bearer <admin-token>

{
  "schedule_id": 456,
  "reason": "人数不足，无法开课"
}

Response:
{
  "message": "课程已取消，相关预约已退款"
}
```

#### 自动取消逻辑
1. **查询预约**：获取该课程的所有有效预约
2. **批量取消**：更新所有预约状态为"已取消"
3. **批量退款**：恢复所有会员卡余额
4. **微信通知**：发送课程取消通知给所有用户

## 🔧 **技术实现**

### **核心接口**

| 功能 | 接口 | 方法 | 实现状态 |
|------|------|------|----------|
| **获取取消信息** | `/api/v1/app/bookings/:id/cancel-info` | GET | ✅ 完整实现 |
| **取消预约** | `/api/v1/app/bookings/cancel` | DELETE | ✅ 含微信通知 |
| **自动取消课程** | `/api/v1/admin/bookings/auto-cancel` | POST | ✅ 批量处理 |

### **数据模型**

#### CancelBookingInfoResponse 取消预约信息
```go
type CancelBookingInfoResponse struct {
    BookingID     uint   `json:"booking_id"`
    CourseName    string `json:"course_name"`
    CoachName     string `json:"coach_name"`
    StoreName     string `json:"store_name"`
    StartTime     string `json:"start_time"`
    PeopleCount   int    `json:"people_count"`
    RefundMethod  string `json:"refund_method"`
    RefundAmount  int    `json:"refund_amount"`
    RefundTimes   int    `json:"refund_times"`
    CanCancel     bool   `json:"can_cancel"`
    CancelMessage string `json:"cancel_message"`
}
```

## 🌟 **功能亮点**

### **智能弹框提醒**
- 根据课程预约率动态显示提醒信息
- 预约率≥80%时显示"课程火爆"提醒
- 开课前2小时内禁止取消

### **完整的退款机制**
- **会员卡退款**：次数和金额原路退回
- **微信支付退款**：预留接口支持微信退款
- **事务保证**：确保退款和状态更新的一致性

### **全面的微信通知**
- **用户取消**：立即发送取消确认通知
- **系统取消**：批量发送课程取消通知
- **通知内容**：包含完整的课程和退款信息

### **批量处理能力**
- **自动取消**：一键取消课程的所有预约
- **批量退款**：同时处理多个用户的退款
- **异步通知**：并发发送微信通知提高效率

## 🎯 **需求完成度：100%** ✅

所有需求中提到的功能都已完美实现：

- ✅ **弹框一**：显示课程名称、预约时间、退款方式、退款金额
- ✅ **弹框二**：课程火爆确认提醒
- ✅ **退款处理**：会员卡原路退回，预留微信支付退款
- ✅ **微信提醒**：前台、后台取消均发送通知
- ✅ **系统自动取消**：因人数不足自动取消并退款通知

这为用户和管理员提供了完整、智能、便捷的取消预约管理体验！🚀
