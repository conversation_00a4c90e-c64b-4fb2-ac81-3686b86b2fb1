package middleware

import (
	"fmt"
	"yogaga/pkg/code"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
)

// CheckPermissionByKey creates a middleware that checks for a specific permission key.
// It enforces that the user's role has the permission for the given key and HTTP method.
func CheckPermissionByKey(key string, enforcer *casbin.Enforcer) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 1. Get user roles from context (from JWT middleware)
		rolesInterface, exists := c.Get("roles")
		if !exists {
			log.Warn("用户未登录或无角色信息", "key", key)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
			c.Abort()
			return
		}

		// 2. 转换角色列表
		var roles []string
		switch r := rolesInterface.(type) {
		case []string:
			roles = r
		case string:
			roles = []string{r}
		default:
			log.Error("无效的角色类型", "roles", rolesInterface, "type", fmt.Sprintf("%T", rolesInterface))
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限验证失败"))
			c.Abort()
			return
		}

		// 3. Get the action (HTTP method)
		action := c.Request.Method

		// 4. 检查用户的任一角色是否有权限
		var allowed bool
		var lastErr error
		for _, role := range roles {
			result, err := enforcer.Enforce(role, key, action)
			if err != nil {
				lastErr = err
				log.Error("权限检查失败", "role", role, "key", key, "action", action, "error", err)
				continue
			}
			if result {
				allowed = true
				log.Debug("权限检查通过", "role", role, "key", key, "action", action)
				break
			}
		}

		if lastErr != nil && !allowed {
			// 如果有错误且没有权限，返回错误
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限检查失败"))
			c.Abort()
			return
		}

		if allowed {
			c.Next()
		} else {
			// 详细信息记录到日志
			log.Warn("权限被拒绝", "roles", roles, "key", key, "action", action)
			// 给前端返回简洁错误
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
			c.Abort()
		}
	}
}
