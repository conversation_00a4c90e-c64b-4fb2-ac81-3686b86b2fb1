package worker

import (
	"context"
	"encoding/json"
	"time"
	"yogaga/internal/model"
	"yogaga/internal/service"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

// BookingWorker 预约任务处理器
type BookingWorker struct {
	db             *gorm.DB
	bookingService *service.BookingService
	server         *asynq.Server
}

// NewBookingWorker 创建预约任务处理器
func NewBookingWorker(db *gorm.DB, bookingService *service.BookingService, redisOpt asynq.RedisClientOpt) *BookingWorker {
	server := asynq.NewServer(redisOpt, asynq.Config{
		Concurrency: 10, // 并发处理数量
		Queues: map[string]int{
			"booking": 6, // 预约队列，高优先级
			"notify":  3, // 通知队列，中优先级
			"cleanup": 1, // 清理队列，低优先级
		},
		ErrorHandler: asynq.ErrorHandlerFunc(func(ctx context.Context, task *asynq.Task, err error) {
			log.Error("任务处理失败", "task_type", task.Type(), "error", err)
		}),
		Logger: &AsynqLogger{},
	})

	return &BookingWorker{
		db:             db,
		bookingService: bookingService,
		server:         server,
	}
}

// Start 启动任务处理器
func (w *BookingWorker) Start() error {
	mux := asynq.NewServeMux()

	// 注册任务处理器
	mux.HandleFunc(service.TypeBookingProcess, w.bookingService.ProcessBookingTask)
	mux.HandleFunc(service.TypeBookingCancel, w.handleCancelBooking)
	mux.HandleFunc(service.TypeQueueNotify, w.handleQueueNotify)
	mux.HandleFunc(service.TypeClassReminder, w.handleClassReminder)
	mux.HandleFunc(service.TypeProcessNoShow, w.handleProcessNoShow)

	log.Info("启动预约任务处理器")
	return w.server.Run(mux)
}

// Stop 停止任务处理器
func (w *BookingWorker) Stop() {
	log.Info("停止预约任务处理器")
	w.server.Stop()
	w.server.Shutdown()
}

// handleCancelBooking 处理取消预约任务
func (w *BookingWorker) handleCancelBooking(ctx context.Context, t *asynq.Task) error {
	log.Info("处理取消预约任务", "task_type", t.Type())

	// 解析任务数据
	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析取消预约任务数据失败", "error", err)
		return err
	}

	bookingID := uint(payload["booking_id"].(float64))
	reason := payload["reason"].(string)

	// 执行取消逻辑
	var booking model.Booking
	if err := w.db.First(&booking, bookingID).Error; err != nil {
		log.Error("查询预约记录失败", "booking_id", bookingID, "error", err)
		return err
	}

	// 退款处理
	if err := w.bookingService.RefundBooking(&booking); err != nil {
		log.Error("退款处理失败", "booking_id", bookingID, "error", err)
		return err
	}

	// 更新预约状态
	updates := map[string]interface{}{
		"status":        4, // 已取消
		"cancel_reason": reason,
		"cancel_time":   time.Now(),
	}

	if err := w.db.Model(&booking).Updates(updates).Error; err != nil {
		log.Error("更新预约状态失败", "booking_id", bookingID, "error", err)
		return err
	}

	log.Info("取消预约处理完成", "booking_id", bookingID)
	return nil
}

// handleQueueNotify 处理排队通知任务
func (w *BookingWorker) handleQueueNotify(ctx context.Context, t *asynq.Task) error {
	log.Info("处理排队通知任务", "task_type", t.Type())

	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析排队通知任务数据失败", "error", err)
		return err
	}

	scheduleID := uint(payload["schedule_id"].(float64))

	// 处理排队通知
	if err := w.bookingService.ProcessQueueNotification(scheduleID); err != nil {
		log.Error("处理排队通知失败", "schedule_id", scheduleID, "error", err)
		return err
	}

	log.Info("排队通知处理完成", "schedule_id", scheduleID)
	return nil
}

// handleClassReminder 处理课程提醒任务
func (w *BookingWorker) handleClassReminder(ctx context.Context, t *asynq.Task) error {
	log.Info("处理课程提醒任务", "task_type", t.Type())

	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析课程提醒任务数据失败", "error", err)
		return err
	}

	scheduleID := uint(payload["schedule_id"].(float64))

	// 查询该课程的所有预约用户
	var bookings []model.Booking
	err := w.db.Preload("User").Preload("Schedule.Course").
		Where("schedule_id = ? AND status IN (1, 2)", scheduleID).
		Find(&bookings).Error

	if err != nil {
		log.Error("查询课程预约失败", "schedule_id", scheduleID, "error", err)
		return err
	}

	// 发送提醒通知（这里可以集成微信消息推送）
	for _, booking := range bookings {
		log.Info("发送课程提醒",
			"user_id", booking.UserID,
			"course_name", booking.Schedule.Course.Name,
			"start_time", booking.Schedule.StartTime.Format("2006-01-02 15:04:05"))

		// 实际项目中这里会调用微信消息推送API
		// wechatService.SendTemplateMessage(booking.User.OpenID, reminderTemplate)
	}

	log.Info("课程提醒处理完成", "schedule_id", scheduleID, "user_count", len(bookings))
	return nil
}

// handleProcessNoShow 处理未到课任务
func (w *BookingWorker) handleProcessNoShow(ctx context.Context, t *asynq.Task) error {
	log.Info("处理未到课任务", "task_type", t.Type())

	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析未到课任务数据失败", "error", err)
		return err
	}

	scheduleID := uint(payload["schedule_id"].(float64))

	// 查询该课程开始后30分钟仍未签到的预约
	var bookings []model.Booking
	err := w.db.Where("schedule_id = ? AND status = 1", scheduleID).Find(&bookings).Error
	if err != nil {
		log.Error("查询未签到预约失败", "schedule_id", scheduleID, "error", err)
		return err
	}

	// 将未签到的预约标记为未到课
	for _, booking := range bookings {
		updates := map[string]interface{}{
			"status": 5, // 未到课
		}

		if err := w.db.Model(&booking).Updates(updates).Error; err != nil {
			log.Error("更新未到课状态失败", "booking_id", booking.ID, "error", err)
			continue
		}

		log.Info("标记未到课", "booking_id", booking.ID, "user_id", booking.UserID)
	}

	log.Info("未到课处理完成", "schedule_id", scheduleID, "no_show_count", len(bookings))
	return nil
}

// AsynqLogger Asynq日志适配器
type AsynqLogger struct{}

func (l *AsynqLogger) Debug(args ...interface{}) {
	log.Debug("asynq", "message", args)
}

func (l *AsynqLogger) Info(args ...interface{}) {
	log.Info("asynq", "message", args)
}

func (l *AsynqLogger) Warn(args ...interface{}) {
	log.Warn("asynq", "message", args)
}

func (l *AsynqLogger) Error(args ...interface{}) {
	log.Error("asynq", "message", args)
}

func (l *AsynqLogger) Fatal(args ...interface{}) {
	log.Error("asynq fatal", "message", args)
}
