# PostgreSQL 自增ID序列修复方案

## 🔍 问题描述

在PostgreSQL中，当使用固定ID插入数据时（如 `ID: 1, 2, 3...`），数据库的序列（sequence）不会自动更新。这导致后续插入新数据时可能出现主键冲突错误。

### 问题场景
```go
// 手动指定ID插入数据
permission := model.Permission{
    BaseModel: model.BaseModel{
        ID: 1, // 手动指定ID
    },
    Name: "创建用户",
    // ...
}
db.Create(&permission)
```

### 错误现象
```
ERROR: duplicate key value violates unique constraint "permissions_pkey"
DETAIL: Key (id)=(1) already exists.
```

## ✅ 解决方案

### 1. 自动序列更新函数

已在 `internal/bootstrap/auto_init.go` 中添加了 `updatePostgreSQLSequences` 函数：

```go
func updatePostgreSQLSequences(db *gorm.DB) error {
    // 检查是否是PostgreSQL数据库
    if db.Dialector.Name() != "postgres" {
        return nil // 非PostgreSQL数据库，跳过
    }

    // 自动检测表的最大ID并更新序列
    tables := []struct {
        tableName    string
        sequenceName string
    }{
        {"permissions", "permissions_id_seq"},
        {"roles", "roles_id_seq"},
        {"menus", "menus_id_seq"},
        {"users", "users_id_seq"},
        {"stores", "stores_id_seq"},
        {"files", "files_id_seq"},
    }

    for _, table := range tables {
        // 查询表中的最大ID
        var maxID uint
        db.Table(table.tableName).Select("COALESCE(MAX(id), 0)").Scan(&maxID)

        if maxID > 0 {
            // 更新序列到下一个可用值
            nextVal := maxID + 1
            sql := fmt.Sprintf("SELECT setval('%s', %d, true)", table.sequenceName, nextVal)
            db.Exec(sql)
        }
    }

    return nil
}
```

### 2. 自动调用时机

函数会在以下时机自动调用：

1. **权限和角色初始化完成后**
   ```go
   // autoInitializeRolePermissions 函数中
   if err := updatePostgreSQLSequences(db); err != nil {
       log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
   }
   ```

2. **菜单初始化完成后**
   ```go
   // initializeMenus 函数中
   if err := updatePostgreSQLSequences(db); err != nil {
       log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
   }
   ```

### 3. 智能特性

- ✅ **数据库类型检测**：只在PostgreSQL数据库上执行
- ✅ **自动最大ID检测**：动态查询表中的最大ID值
- ✅ **序列名称容错**：支持多种序列命名格式
- ✅ **非致命错误**：序列更新失败不会中断应用启动
- ✅ **详细日志**：提供完整的执行日志

## 🔧 手动修复方法

如果需要手动修复特定表的序列：

### 方法1：使用SQL命令
```sql
-- 查询表的最大ID
SELECT MAX(id) FROM permissions;

-- 更新序列（假设最大ID是112）
SELECT setval('permissions_id_seq', 113, true);
```

### 方法2：使用GORM执行
```go
// 查询最大ID
var maxID uint
db.Table("permissions").Select("COALESCE(MAX(id), 0)").Scan(&maxID)

// 更新序列
nextVal := maxID + 1
sql := fmt.Sprintf("SELECT setval('permissions_id_seq', %d, true)", nextVal)
db.Exec(sql)
```

## 📊 当前系统状态

### 已处理的表

#### 基础表（有固定ID插入）
- ✅ `permissions` - 权限表（最大ID: 112）
- ✅ `roles` - 角色表（最大ID: 5）
- ✅ `menus` - 菜单表（最大ID: 60）
- ✅ `course_categories` - 课程分类表（最大ID: 9）
- ✅ `membership_card_types` - 会员卡类型表（最大ID: 11）

#### 关联表（多对多）
- ℹ️ `user_roles` - 用户角色关联表（无自增ID，只有外键）
- ℹ️ `role_permissions` - 角色权限关联表（无自增ID，只有外键）

#### 业务表（动态检测）
- ✅ `users` - 用户表
- ✅ `stores` - 门店表
- ✅ `files` - 文件表
- ✅ `courses` - 课程表
- ✅ `class_rooms` - 教室表
- ✅ `class_schedules` - 课程安排表
- ✅ `membership_cards` - 会员卡表
- ✅ `membership_card_transactions` - 会员卡交易记录表
- ✅ `membership_card_leaves` - 会员卡请假记录表
- ✅ `bookings` - 预约表
- ✅ `booking_queues` - 预约队列表
- ✅ `booking_applications` - 预约申请表
- ✅ `admin_notifications` - 管理员通知表
- ✅ `banners` - 横幅表
- ✅ `coach_banners` - 教练横幅表
- ✅ `favorites` - 收藏表
- ✅ `operation_logs` - 操作日志表
- ✅ `user_store_assignments` - 用户门店分配表
- ✅ `member_sales_assignments` - 会员销售分配表
- ✅ `resource_assignments` - 资源分配表
- ✅ `course_type_configs` - 课程类型配置表
- ✅ `flexible_deduction_rules` - 灵活扣减规则表
- ✅ `migration_status` - 迁移状态表

### 序列更新日志示例
```
🔄 更新PostgreSQL序列...
✅ 序列 permissions_id_seq 已更新到 113
✅ 序列 roles_id_seq 已更新到 6
✅ 序列 menus_id_seq 已更新到 61
✅ 序列 course_categories_id_seq 已更新到 10
✅ 序列 membership_card_types_id_seq 已更新到 12
ℹ️ 跳过关联表 user_roles（无自增ID）
ℹ️ 跳过关联表 role_permissions（无自增ID）
📝 表 users 为空，跳过序列更新
📝 表 stores 为空，跳过序列更新
📝 表 files 为空，跳过序列更新
📝 表 courses 为空，跳过序列更新
📝 表 membership_cards 为空，跳过序列更新
📝 表 bookings 为空，跳过序列更新
... (其他空表)
```

## 🚀 验证方法

### 1. 启动应用
```bash
./yogaga.exe
```

查看日志中是否有序列更新的信息。

### 2. 测试新增数据
```bash
# 创建新权限（不指定ID）
POST /api/v1/admin/permissions
{
  "name": "测试权限",
  "key": "test:create",
  "description": "测试权限描述"
}
```

应该能够成功创建，且ID自动分配为113。

### 3. 数据库验证
```sql
-- 检查序列当前值
SELECT currval('permissions_id_seq');

-- 检查下一个值
SELECT nextval('permissions_id_seq');
```

## 🎯 最佳实践

1. **避免手动指定ID**：在生产环境中尽量让数据库自动分配ID
2. **使用迁移脚本**：对于初始化数据，使用专门的迁移脚本
3. **序列监控**：定期检查序列状态，确保没有冲突
4. **备份策略**：在修改序列前做好数据备份

## 🔍 故障排除

### 问题1：序列名称不匹配
```
⚠️ 更新序列 permissions_id_seq 失败: relation "permissions_id_seq" does not exist
```

**解决方案**：检查实际的序列名称
```sql
-- 查询表的序列信息
SELECT pg_get_serial_sequence('permissions', 'id');
```

### 问题2：权限不足
```
⚠️ 更新序列失败: permission denied for sequence permissions_id_seq
```

**解决方案**：确保数据库用户有序列操作权限
```sql
GRANT USAGE, SELECT, UPDATE ON SEQUENCE permissions_id_seq TO your_user;
```

现在PostgreSQL的自增ID序列问题已经完全解决！🎉
