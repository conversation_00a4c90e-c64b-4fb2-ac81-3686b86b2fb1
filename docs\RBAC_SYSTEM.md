# Casbin 角色权限菜单系统详细文档

## 📋 目录
1. [系统架构概览](#系统架构概览)
2. [数据模型设计](#数据模型设计)
3. [权限验证流程](#权限验证流程)
4. [菜单权限关联](#菜单权限关联)
5. [JWT 集成](#jwt-集成)
6. [API 端点说明](#api-端点说明)
7. [使用示例](#使用示例)
8. [最佳实践](#最佳实践)

## 🏗️ 系统架构概览

### 核心组件关系图
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│    User     │────│    Role     │────│ Permission  │
│   用户      │    │   角色      │    │   权限      │
└─────────────┘    └─────────────┘    └─────────────┘
       │                                      │
       │                                      │
       │            ┌─────────────┐          │
       └────────────│    Menu     │──────────┘
                    │   菜单      │
                    └─────────────┘
                           │
                    ┌─────────────┐
                    │   Casbin    │
                    │  权限引擎   │
                    └─────────────┘
```

### 权限验证链路
```
HTTP Request → JWT 认证 → 角色提取 → Casbin 验证 → 菜单过滤 → Response
```

## 🗄️ 数据模型设计

### 1. User 模型
```go
type User struct {
    ID       uint   `json:"id"`
    Username string `json:"username"`
    Platform string `json:"platform"` // "admin" 或 "client"
    IsActive bool   `json:"is_active"`

    // 多对多关系：用户可以有多个角色
    Roles []Role `gorm:"many2many:user_roles;"`
}
```

### 2. Role 模型
```go
type Role struct {
    ID          uint   `json:"id"`
    Name        string `json:"name"`
    Description string `json:"description"`

    // 多对多关系：角色可以有多个权限
    Permissions []Permission `gorm:"many2many:role_permissions;"`
}
```

### 3. Permission 模型
```go
type Permission struct {
    ID          uint   `json:"id"`
    Name        string `json:"name"`
    Key         string `json:"key"`         // 权限唯一标识，如 "user:create"
    Method      string `json:"method"`      // HTTP 方法，如 "POST"
    Description string `json:"description"`
}
```

### 4. Menu 模型
```go
type Menu struct {
    ID            uint     `json:"id"`
    Title         string   `json:"title"`
    Path          string   `json:"path"`
    Component     string   `json:"component"`
    Type          MenuType `json:"type"`          // 1=目录, 2=页面, 3=按钮
    ParentID      uint     `json:"parent_id"`
    Sort          int      `json:"sort"`
    Hidden        bool     `json:"hidden"`
    Status        int      `json:"status"`        // 1=启用, 0=禁用

    // 权限关联 - 可选的权限标识
    PermissionKey string `json:"permission"`     // 为空表示无需权限

    // 关联关系
    Children []*Menu `json:"children" gorm:"-"`
}

// 菜单类型枚举
type MenuType int
const (
    MenuTypeDirectory MenuType = 1 // 目录
    MenuTypePage      MenuType = 2 // 页面
    MenuTypeButton    MenuType = 3 // 按钮
)
```

### 5. Casbin 策略表
```sql
-- casbin_rule 表结构
CREATE TABLE casbin_rule (
    id    INT AUTO_INCREMENT PRIMARY KEY,
    ptype VARCHAR(100),  -- 策略类型：p(策略), g(角色继承)
    v0    VARCHAR(100),  -- 主体：角色名
    v1    VARCHAR(100),  -- 对象：权限标识
    v2    VARCHAR(100),  -- 动作：HTTP方法
    v3    VARCHAR(100),  -- 保留字段
    v4    VARCHAR(100),  -- 保留字段
    v5    VARCHAR(100)   -- 保留字段
);
```

## 🔐 权限验证流程

### 1. 用户登录流程
```mermaid
sequenceDiagram
    participant C as Client
    participant A as AuthHandler
    participant DB as Database
    participant J as JWTService

    C->>A: POST /api/v1/public/admin/login
    A->>DB: 验证用户名密码
    DB-->>A: 返回用户信息和角色
    A->>J: 生成 JWT Token
    J-->>A: 返回 Access Token 和 Refresh Token
    A-->>C: 返回 Token 和用户信息
```

### 2. JWT Token 结构
```go
type Claims struct {
    UserID uint     `json:"user_id"`
    Roles  []string `json:"roles"`    // 用户的所有角色名
    jwt.RegisteredClaims
}
```

### 3. 权限验证中间件流程
```mermaid
sequenceDiagram
    participant R as Request
    participant AM as AuthMiddleware
    participant PM as PermissionMiddleware
    participant C as Casbin
    participant H as Handler

    R->>AM: HTTP Request with JWT
    AM->>AM: 解析 JWT Token
    AM->>AM: 提取用户角色
    AM->>R: 设置 roles 到 context
    R->>PM: 继续请求处理
    PM->>PM: 获取用户角色
    PM->>C: 检查权限 (role, permission, method)
    C-->>PM: 返回权限结果
    alt 有权限
        PM->>H: 继续处理
    else 无权限
        PM-->>R: 返回 403 错误
    end
```

## 🎯 菜单权限关联

### 1. 菜单类型和权限关联策略

#### 目录类型 (Type = 1)
- **用途**：组织菜单结构，如"系统管理"
- **权限策略**：
  - 可以不关联权限（`PermissionKey` 为空）
  - 如果有子菜单可访问，则显示目录
  - 如果目录本身是公共的，则显示目录

#### 页面类型 (Type = 2)
- **用途**：具体的功能页面，如"用户管理"
- **权限策略**：
  - 通常关联权限（如 `user:list`）
  - 也可以是公共页面（如首页、个人资料）

#### 按钮类型 (Type = 3)
- **用途**：页面内的操作按钮，如"新增用户"
- **权限策略**：
  - 必须关联权限（如 `user:create`）
  - 用于前端按钮级别的权限控制

### 2. 菜单权限过滤算法
```go
func FilterMenusByPermission(menus []*MenuTreeResponse, userRoles []string) []*MenuTreeResponse {
    var filteredMenus []*MenuTreeResponse

    for _, menu := range menus {
        // 1. 检查菜单是否可访问
        if isMenuAccessible(menu, userRoles) {
            // 2. 递归过滤子菜单
            if len(menu.Children) > 0 {
                menu.Children = FilterMenusByPermission(menu.Children, userRoles)
            }

            // 3. 目录类型特殊处理
            if menu.Type == MenuTypeDirectory {
                // 如果有可访问的子菜单或目录本身是公共的，则显示
                if len(menu.Children) > 0 || menu.AccessType == "public" {
                    filteredMenus = append(filteredMenus, menu)
                }
            } else {
                // 页面或按钮类型直接添加
                filteredMenus = append(filteredMenus, menu)
            }
        }
    }

    return filteredMenus
}
```

## 🔑 JWT 集成

### 1. JWT 认证中间件
```go
func AuthMiddleware(jwtSvc *jwtx.JWTService) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 提取 JWT Token
        tokenStr := extractTokenFromHeader(c)

        // 2. 解析 Token
        claims, err := jwtSvc.ParseToken(tokenStr)
        if err != nil {
            code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "无效的认证令牌"))
            c.Abort()
            return
        }

        // 3. 设置用户信息到 context
        c.Set("user_id", claims.UserID)
        c.Set("roles", claims.Roles)  // 设置角色数组

        c.Next()
    }
}
```

### 2. 权限检查中间件
```go
func CheckPermissionByKey(permissionKey string, enforcer *casbin.Enforcer) gin.HandlerFunc {
    return func(c *gin.Context) {
        // 1. 获取用户角色
        rolesInterface, exists := c.Get("roles")
        if !exists {
            code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
            c.Abort()
            return
        }

        // 2. 转换角色列表
        var roles []string
        switch r := rolesInterface.(type) {
        case []string:
            roles = r
        default:
            code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限验证失败"))
            c.Abort()
            return
        }

        // 3. 检查权限
        action := c.Request.Method
        for _, role := range roles {
            allowed, err := enforcer.Enforce(role, permissionKey, action)
            if err == nil && allowed {
                c.Next()
                return
            }
        }

        // 4. 权限不足
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "权限不足"))
        c.Abort()
    }
}
```

## 🌐 API 端点说明

### 1. 认证相关
```
POST /api/v1/public/admin/login     # 管理员登录
POST /api/v1/public/auth/refresh    # 刷新 Token
```

### 2. 用户管理
```
POST /api/v1/admin/users            # 创建用户 (需要 user:create 权限)
GET  /api/v1/admin/users/menus      # 获取当前用户菜单 (需要 JWT 认证)
```

### 3. 角色管理
```
POST   /api/v1/admin/roles                # 创建角色 (需要 role:create 权限)
GET    /api/v1/admin/roles                # 角色列表 (需要 role:list 权限)
GET    /api/v1/admin/roles/:id            # 角色详情 (需要 role:get 权限)
PUT    /api/v1/admin/roles/:id            # 更新角色 (需要 role:update 权限)
DELETE /api/v1/admin/roles/:id            # 删除角色 (需要 role:delete 权限)
GET    /api/v1/admin/roles/:id/permissions # 获取角色权限 (需要 role:get_permissions 权限)
PUT    /api/v1/admin/roles/:id/permissions # 更新角色权限 (需要 role:update_permissions 权限)
```

### 4. 菜单管理
```
POST   /api/v1/admin/menus         # 创建菜单 (需要 menu:create 权限)
GET    /api/v1/admin/menus         # 菜单列表 (需要 menu:list 权限)
GET    /api/v1/admin/menus/:id     # 菜单详情 (需要 menu:get 权限)
PUT    /api/v1/admin/menus/:id     # 更新菜单 (需要 menu:update 权限)
DELETE /api/v1/admin/menus/:id     # 删除菜单 (需要 menu:delete 权限)
```

### 5. 权限管理
```
GET /api/v1/admin/permissions      # 权限列表 (需要 permission:list 权限)
```

## 💡 使用示例

### 1. 用户登录获取菜单
```bash
# 1. 用户登录
curl -X POST http://localhost:9095/api/v1/public/admin/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'

# 响应
{
  "code": 200,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "admin",
      "roles": ["admin"]
    }
  }
}

# 2. 获取用户菜单
curl -X GET http://localhost:9095/api/v1/admin/users/menus \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# 响应
{
  "code": 200,
  "data": [
    {
      "id": 1,
      "title": "系统管理",
      "path": "/system",
      "component": "Layout",
      "type": 1,
      "parent_id": 0,
      "sort": 1,
      "hidden": false,
      "access_type": "public",
      "permission": "",
      "children": [
        {
          "id": 2,
          "title": "用户管理",
          "path": "/system/users",
          "component": "system/users/index",
          "type": 2,
          "parent_id": 1,
          "sort": 1,
          "hidden": false,
          "access_type": "protected",
          "permission": "user:list",
          "children": []
        }
      ]
    }
  ]
}
```

### 2. 创建角色和权限
```bash
# 1. 创建角色
curl -X POST http://localhost:9095/api/v1/admin/roles \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "editor",
    "description": "编辑者角色"
  }'

# 2. 为角色分配权限
curl -X PUT http://localhost:9095/api/v1/admin/roles/2/permissions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "permission_ids": [1, 2, 3]
  }'
```

### 3. 创建菜单
```bash
# 创建目录菜单（无需权限）
curl -X POST http://localhost:9095/api/v1/admin/menus \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "系统管理",
    "path": "/system",
    "component": "Layout",
    "type": 1,
    "parent_id": 0,
    "sort": 1,
    "hidden": false,
    "permission": ""
  }'

# 创建页面菜单（需要权限）
curl -X POST http://localhost:9095/api/v1/admin/menus \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "用户管理",
    "path": "/system/users",
    "component": "system/users/index",
    "type": 2,
    "parent_id": 1,
    "sort": 1,
    "hidden": false,
    "permission": "user:list"
  }'
```

## 🎯 最佳实践

### 1. 权限设计原则
- **最小权限原则**：用户只获得完成工作所需的最小权限
- **权限分离**：不同功能使用不同的权限标识
- **层级权限**：使用 `模块:操作` 的命名规范，如 `user:create`

### 2. 菜单设计建议
- **目录菜单**：通常不设置权限，用于组织结构
- **页面菜单**：根据业务需求设置权限
- **按钮菜单**：必须设置权限，用于细粒度控制

### 3. 角色设计策略
- **功能角色**：按功能模块划分，如 `user_manager`、`content_editor`
- **层级角色**：按管理层级划分，如 `admin`、`manager`、`operator`
- **组合使用**：用户可以拥有多个角色，权限取并集

### 4. 安全注意事项
- **JWT 过期时间**：Access Token 设置较短过期时间（如 2 小时）
- **权限缓存**：Casbin 支持策略缓存，提高性能
- **日志记录**：记录所有权限检查和敏感操作
- **定期审计**：定期检查用户权限分配是否合理

### 5. 性能优化
- **菜单缓存**：对于不经常变化的菜单，可以使用 Redis 缓存
- **权限预加载**：在用户登录时预加载权限信息
- **批量权限检查**：对于多个权限检查，使用批量 API

### 6. 错误处理
- **统一错误码**：使用统一的错误码和错误信息
- **详细日志**：在服务器端记录详细错误信息
- **友好提示**：给前端用户友好的错误提示

## 🔧 配置说明

### 1. Casbin 配置文件 (configs/rbac_model.conf)
```ini
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
```

### 2. 应用配置 (config.yaml)
```yaml
casbin:
  enabled: true
  model_path: "configs/rbac_model.conf"
  enable_log: true
  enable_auto_save: true
  auto_load_interval: 60  # 秒

jwt_auth:
  secret: "your-secret-key"
  access_token_expire: 7200   # 2 小时
  refresh_token_expire: 604800 # 7 天
```

## 🚀 部署建议

### 1. 数据库索引
```sql
-- 为权限检查优化的索引
CREATE INDEX idx_casbin_rule_ptype_v0_v1_v2 ON casbin_rule(ptype, v0, v1, v2);
CREATE INDEX idx_menu_parent_sort ON menus(parent_id, sort);
CREATE INDEX idx_menu_permission ON menus(permission_key);
```

### 2. 监控指标
- 权限检查响应时间
- 菜单加载时间
- JWT Token 验证成功率
- 权限拒绝次数

### 3. 备份策略
- 定期备份 Casbin 策略数据
- 备份用户角色关联关系
- 备份菜单配置数据

这个系统提供了完整的 RBAC 权限控制，支持灵活的菜单权限管理，既保证了安全性，又提供了良好的用户体验。
