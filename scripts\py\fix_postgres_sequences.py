#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL序列修复脚本
解决手动插入数据或数据迁移后序列值没有正确更新导致的主键冲突问题
"""

import psycopg2
import os
import sys
from typing import List, Tuple

# 需要修复序列的表名列表
TABLES = [
    "menus",
    "users", 
    "roles",
    "permissions",
    "stores",
    "course_categories",
    "courses",
    "class_rooms",
    "class_schedules",
    "membership_card_types",
    "membership_cards",
    "bookings",
    "booking_queues", 
    "booking_applications",
    "banners",
    "coach_banners",
    "favorites",
    "operation_logs",
    "admin_notifications",
    "membership_card_transactions",
    "membership_card_leaves",
    "course_type_configs",
    "flexible_deduction_rules",
    "user_store_assignments",
    "member_sales_assignments", 
    "resource_assignments",
    "migration_status",
    "files"
]

class PostgreSQLSequenceFixer:
    def __init__(self):
        self.connection = None
        self.cursor = None

    def connect(self, host="localhost", port="5432", user="postgres", password="", database="yogaga"):
        """连接PostgreSQL数据库"""
        try:
            self.connection = psycopg2.connect(
                host=host,
                port=port,
                user=user,
                password=password,
                database=database
            )
            self.cursor = self.connection.cursor()
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                )
            """, (table_name,))
            return self.cursor.fetchone()[0]
        except Exception as e:
            print(f"❌ 检查表 {table_name} 是否存在失败: {e}")
            return False

    def sequence_exists(self, sequence_name: str) -> bool:
        """检查序列是否存在"""
        try:
            self.cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.sequences 
                    WHERE sequence_name = %s
                )
            """, (sequence_name,))
            return self.cursor.fetchone()[0]
        except Exception as e:
            print(f"❌ 检查序列 {sequence_name} 是否存在失败: {e}")
            return False

    def fix_sequence(self, table_name: str) -> bool:
        """修复指定表的序列"""
        sequence_name = f"{table_name}_id_seq"
        
        # 检查表是否存在
        if not self.table_exists(table_name):
            print(f"⚠️ 表 {table_name} 不存在，跳过")
            return True
        
        # 检查序列是否存在
        if not self.sequence_exists(sequence_name):
            print(f"⚠️ 序列 {sequence_name} 不存在，跳过")
            return True
        
        try:
            # 修复序列
            sql = f"SELECT setval('{sequence_name}', COALESCE((SELECT MAX(id) FROM {table_name}), 1), true)"
            self.cursor.execute(sql)
            new_value = self.cursor.fetchone()[0]
            print(f"✅ 修复表 {table_name} 的序列成功，当前值: {new_value}")
            return True
        except Exception as e:
            print(f"❌ 修复表 {table_name} 的序列失败: {e}")
            return False

    def fix_all_sequences(self) -> Tuple[int, int]:
        """修复所有表的序列"""
        success_count = 0
        total_count = len(TABLES)
        
        print(f"🔧 开始修复 {total_count} 个表的序列...")
        print("=" * 60)
        
        for table_name in TABLES:
            if self.fix_sequence(table_name):
                success_count += 1
        
        # 提交事务
        self.connection.commit()
        
        return success_count, total_count

    def show_sequence_status(self):
        """显示序列状态"""
        try:
            self.cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    sequencename,
                    last_value
                FROM pg_sequences 
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)
            
            sequences = self.cursor.fetchall()
            
            print("\n📊 序列状态:")
            print("=" * 80)
            print(f"{'表名':<25} {'序列名':<30} {'当前值':<10}")
            print("-" * 80)
            
            for schema, table, sequence, last_value in sequences:
                print(f"{table:<25} {sequence:<30} {last_value:<10}")
                
        except Exception as e:
            print(f"❌ 查询序列状态失败: {e}")

    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.connection:
            self.connection.close()
        print("✅ 数据库连接已关闭")

def get_db_config():
    """获取数据库配置"""
    config = {
        'host': os.getenv('DB_HOST', 'localhost'),
        'port': os.getenv('DB_PORT', '5432'),
        'user': os.getenv('DB_USER', 'postgres'),
        'password': os.getenv('DB_PASSWORD', ''),
        'database': os.getenv('DB_NAME', 'yogaga')
    }
    
    # 如果没有设置密码，提示用户输入
    if not config['password']:
        import getpass
        config['password'] = getpass.getpass("请输入数据库密码: ")
    
    return config

def main():
    """主函数"""
    print("🔧 PostgreSQL序列修复工具")
    print("=" * 60)
    
    # 获取数据库配置
    db_config = get_db_config()
    
    # 创建修复器
    fixer = PostgreSQLSequenceFixer()
    
    try:
        # 连接数据库
        if not fixer.connect(**db_config):
            sys.exit(1)
        
        # 修复所有序列
        success_count, total_count = fixer.fix_all_sequences()
        
        print("\n" + "=" * 60)
        print(f"🎉 序列修复完成！成功: {success_count}/{total_count}")
        
        # 显示序列状态
        fixer.show_sequence_status()
        
        if success_count == total_count:
            print("\n✅ 所有序列修复成功！现在可以正常创建新记录了。")
        else:
            print(f"\n⚠️ 有 {total_count - success_count} 个序列修复失败，请检查错误信息。")
            
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断操作")
    except Exception as e:
        print(f"\n❌ 发生未知错误: {e}")
    finally:
        fixer.close()

if __name__ == "__main__":
    main()
