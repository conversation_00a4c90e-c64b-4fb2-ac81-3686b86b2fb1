package main

import (
	"fmt"
	"log"

	"github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// 简化的Store模型用于调试
type Store struct {
	ID       uint           `gorm:"primarykey"`
	Name     string         `gorm:"type:varchar(100)"`
	ImageIDs pq.StringArray `gorm:"type:text[]"`
}

// 简化的File模型用于调试
type File struct {
	ID           string `gorm:"primarykey"`
	OriginalName string `gorm:"type:varchar(255)"`
	Status       string `gorm:"type:varchar(20)"`
}

func main() {
	// 初始化数据库连接
	dsn := "host=************ user=yogaga password=QjZ2xyD42bPYKTyx dbname=yogaga port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 查询有图片的门店
	var stores []Store
	err = db.Where("image_ids IS NOT NULL").Find(&stores).Error
	if err != nil {
		log.Fatal("查询门店失败:", err)
	}

	fmt.Printf("找到 %d 个门店\n", len(stores))
	fmt.Println("===========================================")

	for _, store := range stores {
		fmt.Printf("门店ID: %d\n", store.ID)
		fmt.Printf("门店名称: %s\n", store.Name)
		fmt.Printf("原始ImageIDs: %+v\n", store.ImageIDs)
		fmt.Printf("ImageIDs类型: %T\n", store.ImageIDs)
		fmt.Printf("ImageIDs长度: %d\n", len(store.ImageIDs))

		// 检查是否为空
		if store.ImageIDs == nil {
			fmt.Println("ImageIDs 为 nil")
		} else if len(store.ImageIDs) == 0 {
			fmt.Println("ImageIDs 为空数组")
		} else {
			fmt.Println("ImageIDs 内容:")
			for i, id := range store.ImageIDs {
				fmt.Printf("  [%d]: %s\n", i, id)
			}
		}
		fmt.Println("-------------------------------------------")
	}

	// 特别检查你提到的那两个ID
	targetIDs := []string{"fc9f5789-e880-4ce5-a650-45794b926911", "245791a9-e50b-4289-812d-583622b5d23b"}
	fmt.Println("\n检查特定的文件ID:")
	for _, targetID := range targetIDs {
		var count int64
		db.Model(&Store{}).Where("image_ids @> ?", pq.Array([]string{targetID})).Count(&count)
		fmt.Printf("包含文件ID %s 的门店数量: %d\n", targetID, count)

		if count > 0 {
			var store Store
			db.Where("image_ids @> ?", pq.Array([]string{targetID})).First(&store)
			fmt.Printf("  门店: %s (ID: %d)\n", store.Name, store.ID)
			fmt.Printf("  所有图片ID: %+v\n", store.ImageIDs)
		}
	}

	// 检查文件表中是否存在这些文件
	fmt.Println("\n检查文件表:")
	for _, targetID := range targetIDs {
		var file File
		err := db.Where("id = ?", targetID).First(&file).Error
		if err == gorm.ErrRecordNotFound {
			fmt.Printf("文件ID %s 在文件表中不存在\n", targetID)
		} else if err != nil {
			fmt.Printf("查询文件ID %s 时出错: %v\n", targetID, err)
		} else {
			fmt.Printf("文件ID %s 存在: %s (状态: %s)\n", targetID, file.OriginalName, file.Status)
		}
	}
}
