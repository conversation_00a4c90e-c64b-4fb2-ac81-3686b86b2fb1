package model

// Permission 权限模型
type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"type:varchar(100);comment:权限名称 (e.g., 创建角色)"`
	Key         string `json:"key" gorm:"column:key;type:varchar(100);uniqueIndex;not null;comment:权限唯一健 (e.g., role:create)"`
	Description string `json:"description" gorm:"type:varchar(255);comment:权限描述"`
	// 注意：菜单通过 Menu.PermissionKey 外键关联到 Permission.Key，不使用多对多关系
}
