APP:
  Name: yogaga
  Port: 9095

JwtAuth:
  AccessSecret: "aSuperSecretKeyForJwtExample" # 请修改为更安全的密钥
  AccessExpire: 7200 # 2 hours in seconds
  RefreshSecret: "anotherSuperSecretKeyForRefreshExample" # 请修改为更安全的密钥
  RefreshExpire: 604800 # 7 days in seconds

Driver: mysql

Database:
  UserName: yogaga
  Password: "password" # 填写你的数据库密码
  DBName: yogaga
  Host: "mysql" # 在容器化环境中通常是服务名
  Port: 3306
  TablePrefix: ""
  Charset: utf8mb4
  ParseTime: true
  Loc: "Asia/Shanghai" # Go Timezone, e.g., "Asia/Shanghai"

Redis:
  "default":
    Addr: "redis:6379"
    Password: ""
    DB: 0
    MinIdleConn: 200
    DialTimeout: 60s
    ReadTimeout: 5s
    WriteTimeout: 5s
    PoolSize: 100
    PoolTimeout: 240s
    EnableTrace: true

Storage:
  Type: minio
  Endpoint: "minio:9000"
  AccessKeyID: "minioadmin"
  AccessKeySecret: "minioadmin"
  BucketName: "starter-test"
  UseSSL: false

Casbin:
  Enabled: true
  ModelPath: "/etc/yogaga/rbac_model.conf" # 容器内路径, 由 Dockerfile 复制
  PolicyTable: casbin_rule
  AutoLoadInterval: 10
  EnableLog: true
  EnableAutoSave: true

Log:
  Level: info                 # 日志级别: debug, info, warn, error
  Output: [console, file]     # 输出方式: console, file
  FileConfig:
    Path: ./logs/app.log      # 日志文件路径
    MaxSize: 100              # 每个日志文件的最大大小（MB）
    MaxAge: 7                 # 日志文件保留天数
    MaxBackups: 10            # 保留的旧日志文件最大数量
    Compress: true            # 是否压缩旧日志文件

WeChat:
  AppID: "your_wechat_mini_program_appid"     # 微信小程序 AppID
  AppSecret: "your_wechat_mini_program_secret" # 微信小程序 AppSecret
  CourseReminderTemplateID: "your_course_reminder_template_id"    # 开课提醒模板ID
  CancelNoticeTemplateID: "your_cancel_notice_template_id"        # 取消通知模板ID
  QueueNotificationTemplateID: "your_queue_notification_template_id" # 排队通知模板ID
