package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/jwtx"
	"yogaga/pkg/storage"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type UserHandler struct {
	db           *gorm.DB
	jwtSvc       *jwtx.Service
	enforcer     *casbin.Enforcer
	urlConverter URLConverter
}

func NewUserHandler(db *gorm.DB, jwtSvc *jwtx.Service, enforcer *casbin.Enforcer, storage storage.Storage) *UserHandler {
	return &UserHandler{
		db:           db,
		jwtSvc:       jwtSvc,
		enforcer:     enforcer,
		urlConverter: NewURLConverter(db, storage),
	}
}

// AdminLogin handles admin platform user authentication.
func (h *UserHandler) AdminLogin(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定登录请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	var user model.User
	// Find user by username and check if they have admin platform access
	if err := h.db.Preload("Roles").Where("username = ?", req.Username).First(&user).Error; err != nil {
		log.Error("用户登录失败", "username", req.Username, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNotFound, "用户名或密码错误"))
		return
	}

	// Check if user has admin platform access
	if !user.CanAccessPlatform(enum.PlatformAdmin) {
		log.Warn("用户无管理后台访问权限", "username", req.Username, "platform_access", user.PlatformAccess)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoPermission, "无权限访问管理后台"))
		return
	}

	match, err := user.CheckPassword(req.Password)
	if err != nil {
		// This could be due to an invalid hash format in the DB
		log.Error("密码验证失败", "username", req.Username, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserPasswordError, "用户名或密码错误"))
		return
	}
	if !match {
		log.Warn("密码不匹配", "username", req.Username)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserPasswordError, "用户名或密码错误"))
		return
	}

	accessToken, refreshToken, err := h.jwtSvc.GenerateTokens(&user)
	if err != nil {
		log.Error("生成令牌失败", "username", req.Username, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "登录失败"))
		return
	}

	// 构建角色名称列表
	roleNames := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roleNames[i] = role.Name
	}

	// 构建用户信息
	platforms := user.GetAccessiblePlatforms()
	platformStrs := make([]string, len(platforms))
	for i, p := range platforms {
		platformStrs[i] = string(p)
	}

	userInfo := &dto.UserInfo{
		ID:             user.ID.String(),
		Username:       user.Username,
		Email:          user.Email,
		PlatformAccess: int(user.PlatformAccess),
		Platforms:      platformStrs,
		Roles:          roleNames,
	}

	response := dto.LoginResponse{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		TokenType:        "Bearer",
		ExpiresIn:        h.jwtSvc.GetAccessExpireSeconds(),
		RefreshExpiresIn: h.jwtSvc.GetRefreshExpireSeconds(),
		Scope:            "admin", // 管理员权限范围
		UserInfo:         userInfo,
	}
	code.AutoResponse(c, response, nil)
}

// RefreshToken handles token refresh using a valid refresh token
func (h *UserHandler) RefreshToken(c *gin.Context) {
	var req dto.RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定刷新令牌请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 解析刷新令牌获取用户ID
	userID, err := h.jwtSvc.ParseRefreshToken(req.RefreshToken)
	if err != nil {
		log.Error("解析刷新令牌失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "无效的刷新令牌"))
		return
	}

	// 根据用户ID查找用户信息
	var user model.User
	if err := h.db.Preload("Roles").Where("id = ?", userID).First(&user).Error; err != nil {
		log.Error("查找用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNotFound, "用户不存在"))
		return
	}

	// 检查用户是否仍然活跃
	if !user.IsActive {
		log.Warn("用户账户已禁用", "user_id", userID, "username", user.Username)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserDisabled, "用户账户已禁用"))
		return
	}

	// 生成新的令牌对
	accessToken, refreshToken, err := h.jwtSvc.GenerateTokens(&user)
	if err != nil {
		log.Error("生成新令牌失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorUnknown, "刷新令牌失败"))
		return
	}

	// 构建角色名称列表
	roleNames := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roleNames[i] = role.Name
	}

	// 构建用户信息
	platforms := user.GetAccessiblePlatforms()
	platformStrs := make([]string, len(platforms))
	for i, p := range platforms {
		platformStrs[i] = string(p)
	}

	userInfo := &dto.UserInfo{
		ID:             user.ID.String(),
		Username:       user.Username,
		Email:          user.Email,
		PlatformAccess: int(user.PlatformAccess),
		Platforms:      platformStrs,
		Roles:          roleNames,
	}

	response := dto.LoginResponse{
		AccessToken:      accessToken,
		RefreshToken:     refreshToken,
		TokenType:        "Bearer",
		ExpiresIn:        h.jwtSvc.GetAccessExpireSeconds(),
		RefreshExpiresIn: h.jwtSvc.GetRefreshExpireSeconds(),
		Scope:            user.PlatformAccess.String(), // 使用用户平台访问权限作为权限范围
		UserInfo:         userInfo,
	}
	code.AutoResponse(c, response, nil)
}

// GetUsers 获取用户列表
func (h *UserHandler) GetUsers(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 设置默认值
	req.SetDefaults()

	var users []model.User
	var total int64

	// 获取用户总数
	if err := h.db.Model(&model.User{}).Count(&total).Error; err != nil {
		log.Error("获取用户总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取用户总数失败"))
		return
	}

	// 获取用户列表
	if err := h.db.Preload("Roles").Offset(req.GetOffset()).Limit(req.PageSize).Find(&users).Error; err != nil {
		log.Error("获取用户列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取用户列表失败"))
		return
	}

	response := dto.PageResponse{
		List:     users,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// AdminCreateUser handles the creation of a new admin user by another admin.
func (h *UserHandler) AdminCreateUser(c *gin.Context) {
	var req dto.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建用户请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	var roles []model.Role
	if len(req.RoleIDs) > 0 {
		if err := h.db.Find(&roles, req.RoleIDs).Error; err != nil {
			log.Error("查找角色失败", "role_ids", req.RoleIDs, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "角色不存在"))
			return
		}
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	newUser := model.User{
		Username:       req.Username,
		PlatformAccess: enum.PlatformAccessAdmin, // 默认只能访问管理端
		Password:       req.Password,
		Email:          req.Email,
		Phone:          req.Phone, // 添加手机号字段
		Roles:          roles,
	}

	// 1. 创建用户到数据库
	if err := tx.Create(&newUser).Error; err != nil {
		tx.Rollback()
		log.Error("创建用户失败", "username", req.Username, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建用户失败"))
		return
	}

	// 2. 同步用户-角色关联到 Casbin
	if h.enforcer != nil && len(roles) > 0 {
		for _, role := range roles {
			_, err := h.enforcer.AddRoleForUser(req.Username, role.Name)
			if err != nil {
				tx.Rollback()
				log.Error("添加用户角色到 Casbin 失败", "username", req.Username, "role", role.Name, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "同步用户角色失败"))
				return
			}
		}

		// 保存 Casbin 策略
		err := h.enforcer.SavePolicy()
		if err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "保存用户角色策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建用户失败"))
		return
	}

	log.Info("用户创建成功", "username", req.Username, "role_count", len(roles))

	// Do not return password hash in the response
	newUser.Password = ""
	code.AutoResponse(c, newUser, nil)
}

// UpdateUserRoles 更新用户角色
func (h *UserHandler) UpdateUserRoles(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		log.Error("无效的用户ID参数", "id", userID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的用户ID"))
		return
	}

	var req dto.UpdateUserRolesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新用户角色请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查找用户
	var user model.User
	if err := h.db.Preload("Roles").Where("id = ?", userID).First(&user).Error; err != nil {
		log.Error("查找用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		return
	}

	// 查找新角色
	var newRoles []model.Role
	if len(req.RoleIDs) > 0 {
		if err := h.db.Find(&newRoles, req.RoleIDs).Error; err != nil {
			log.Error("查找角色失败", "role_ids", req.RoleIDs, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "角色查询失败"))
			return
		}
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 更新数据库中的用户角色关联
	if err := tx.Model(&user).Association("Roles").Replace(&newRoles); err != nil {
		tx.Rollback()
		log.Error("更新用户角色失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新用户角色失败"))
		return
	}

	// 2. 同步到 Casbin
	if h.enforcer != nil {
		// 删除用户的所有旧角色
		_, err := h.enforcer.DeleteRolesForUser(user.Username)
		if err != nil {
			tx.Rollback()
			log.Error("删除用户旧角色失败", "username", user.Username, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "同步用户角色失败"))
			return
		}

		// 添加新角色
		for _, role := range newRoles {
			_, err := h.enforcer.AddRoleForUser(user.Username, role.Name)
			if err != nil {
				tx.Rollback()
				log.Error("添加用户新角色失败", "username", user.Username, "role", role.Name, "error", err)
				code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "同步用户角色失败"))
				return
			}
		}

		// 保存 Casbin 策略
		err = h.enforcer.SavePolicy()
		if err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "保存用户角色策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新用户角色失败"))
		return
	}

	log.Info("用户角色更新成功", "user_id", userID, "username", user.Username, "role_count", len(newRoles))
	code.AutoResponse(c, nil, nil)
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID := c.Param("id")
	if userID == "" {
		log.Error("无效的用户ID参数", "id", userID)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的用户ID"))
		return
	}

	// 查找用户
	var user model.User
	if err := h.db.Where("id = ?", userID).First(&user).Error; err != nil {
		log.Error("查找用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 1. 删除数据库中的用户
	if err := tx.Delete(&user).Error; err != nil {
		tx.Rollback()
		log.Error("删除用户失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除用户失败"))
		return
	}

	// 2. 清理 Casbin 中的用户角色关联
	if h.enforcer != nil {
		_, err := h.enforcer.DeleteRolesForUser(user.Username)
		if err != nil {
			tx.Rollback()
			log.Error("删除用户 Casbin 角色失败", "username", user.Username, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "清理用户角色关联失败"))
			return
		}

		// 保存 Casbin 策略
		err = h.enforcer.SavePolicy()
		if err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "保存策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除用户失败"))
		return
	}

	log.Info("用户删除成功", "user_id", userID, "username", user.Username)
	code.AutoResponse(c, nil, nil)
}

// GetCoaches 获取教练列表
func (h *UserHandler) GetCoaches(c *gin.Context) {
	var coaches []model.User

	// 查询所有教练用户（使用 is_coach 字段）
	err := h.db.Where("is_coach = ? AND is_active = ?", true, true).Find(&coaches).Error
	if err != nil {
		log.Error("查询教练列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练列表失败"))
		return
	}

	// 构建响应数据
	var coachList []dto.CoachInfo
	for _, coach := range coaches {
		var avatarURL string
		if coach.AvatarID != nil {
			avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
		}

		coachInfo := dto.CoachInfo{
			ID:          coach.ID.String(),
			NickName:    coach.NickName,
			Avatar:      avatarURL,
			Gender:      int(coach.Gender),
			Description: coach.CoachDescription,
			Experience:  coach.Experience,
			Specialty:   coach.Speciality,
		}
		coachList = append(coachList, coachInfo)
	}

	code.AutoResponse(c, coachList, nil)
}

// GetCoach 获取教练详情
func (h *UserHandler) GetCoach(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教练ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", id, true, true).First(&coach).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		} else {
			log.Error("查询教练详情失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练详情失败"))
		}
		return
	}

	// 构建教练详情响应
	var avatarURL string
	if coach.AvatarID != nil {
		avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
	}

	coachDetail := dto.CoachDetail{
		ID:          coach.ID.String(),
		NickName:    coach.NickName,
		Avatar:      avatarURL,
		Gender:      int(coach.Gender),
		Description: coach.CoachDescription,
		Experience:  coach.Experience,
		Specialty:   coach.Speciality,
		Country:     coach.Country,
		Province:    coach.Province,
		City:        coach.City,
	}

	code.AutoResponse(c, coachDetail, nil)
}

// GetAdminCoaches 获取教练列表（后台管理）
func (h *UserHandler) GetAdminCoaches(c *gin.Context) {
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 构建查询条件
	query := h.db.Model(&model.User{}).Where("is_coach = ?", true)

	// 搜索条件
	search := c.Query("search")
	if search != "" {
		query = query.Where("nick_name LIKE ? OR username LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 状态筛选
	status := c.Query("status")
	if status != "" {
		if status == "active" {
			query = query.Where("is_active = ?", true)
		} else if status == "inactive" {
			query = query.Where("is_active = ?", false)
		}
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("查询教练总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练列表失败"))
		return
	}

	// 分页查询 - 按排序字段和创建时间排序
	var coaches []model.User
	offset := (req.Page - 1) * req.PageSize
	err := query.Offset(offset).Limit(req.PageSize).Order("sort_order ASC, created_at DESC").Find(&coaches).Error
	if err != nil {
		log.Error("查询教练列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练列表失败"))
		return
	}

	// 构建响应数据
	var coachList []dto.AdminCoachInfo
	for _, coach := range coaches {
		var avatarURL string
		if coach.AvatarID != nil {
			avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
		}

		coachInfo := dto.AdminCoachInfo{
			ID:          coach.ID.String(),
			Username:    coach.Username,
			NickName:    coach.NickName,
			Avatar:      avatarURL,
			Gender:      int(coach.Gender),
			Description: coach.CoachDescription,
			Experience:  coach.Experience,
			Specialty:   coach.Speciality,
			IsActive:    coach.IsActive,
			CreatedAt:   coach.CreatedAt.Format("2006-01-02 15:04:05"),
		}
		coachList = append(coachList, coachInfo)
	}

	response := dto.PageResponse{
		List:     coachList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetAdminCoach 获取教练详情（后台管理）
func (h *UserHandler) GetAdminCoach(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		log.Error("教练ID格式错误", "id", idStr, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ?", id, true).First(&coach).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		} else {
			log.Error("查询教练详情失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练详情失败"))
		}
		return
	}

	// 构建教练详情响应
	var avatarURL string
	if coach.AvatarID != nil {
		avatarURL = h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
	}

	coachDetail := dto.AdminCoachDetail{
		ID:          coach.ID.String(),
		Username:    coach.Username,
		NickName:    coach.NickName,
		Avatar:      avatarURL,
		Gender:      int(coach.Gender),
		Description: coach.CoachDescription,
		Experience:  coach.Experience,
		Specialty:   coach.Speciality,
		Country:     coach.Country,
		Province:    coach.Province,
		City:        coach.City,
		IsActive:    coach.IsActive,
		CreatedAt:   coach.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   coach.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	code.AutoResponse(c, coachDetail, nil)
}

// UpdateCoach 更新教练信息（后台管理）
func (h *UserHandler) UpdateCoach(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教练ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateCoachRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新教练参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询教练是否存在
	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ?", id, true).First(&coach).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		} else {
			log.Error("查询教练失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练失败"))
		}
		return
	}

	// 更新教练信息
	updates := dto.NewUpdateFields().
		SetIfNotEmpty("nick_name", req.NickName).
		SetIfNotEmpty("description", req.Description).
		Set("experience", req.Experience).
		SetIfNotEmpty("specialty", req.Specialty).
		SetIfNotEmpty("avatar_url", req.Avatar).
		Set("gender", req.Gender).
		SetIfNotEmpty("country", req.Country).
		SetIfNotEmpty("province", req.Province).
		SetIfNotEmpty("city", req.City)

	if err := h.db.Model(&coach).Updates(updates.GetFields()).Error; err != nil {
		log.Error("更新教练信息失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新教练信息失败"))
		return
	}

	log.Info("更新教练信息成功", "id", id, "nick_name", req.NickName)
	response := dto.MessageResponse{Message: "更新成功"}
	code.AutoResponse(c, response, nil)
}

// UpdateCoachStatus 更新教练状态（后台管理）
func (h *UserHandler) UpdateCoachStatus(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("教练ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateCoachStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新教练状态参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询教练是否存在
	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ?", id, true).First(&coach).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		} else {
			log.Error("查询教练失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询教练失败"))
		}
		return
	}

	// 更新教练状态
	if err := h.db.Model(&coach).Update("is_active", req.IsActive).Error; err != nil {
		log.Error("更新教练状态失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新教练状态失败"))
		return
	}

	statusText := "启用"
	if !req.IsActive {
		statusText = "禁用"
	}

	log.Info("更新教练状态成功", "id", id, "status", statusText)
	response := dto.MessageResponse{Message: "状态更新成功"}
	code.AutoResponse(c, response, nil)
}
