# 🔄 strconv 到 cast 库迁移指南

## 📋 概述

根据项目新规范，**禁止使用 strconv 进行类型转换，必须优先使用 github.com/spf13/cast 库**。

## 🎯 迁移原则

### ✅ 需要替换的 strconv 使用

```go
// ❌ 禁止使用
strconv.Atoi(s)
strconv.ParseInt(s, 10, 64)
strconv.ParseUint(s, 10, 32)
strconv.ParseUint(s, 10, 64)
strconv.ParseFloat(s, 64)
strconv.ParseBool(s)

// ✅ 应该使用
cast.ToInt(s)
cast.ToInt64(s)
cast.ToUint(s)
cast.ToUint64(s)
cast.ToFloat64(s)
cast.ToBool(s)
```

### ⚠️ 可以保留的 strconv 使用

```go
// ✅ 格式化输出可以保留
strconv.FormatInt(i, 10)
strconv.Itoa(i)
strconv.FormatFloat(f, 'f', 2, 64)
strconv.FormatBool(b)
```

## 🔧 常见替换模式

### 1. URL 参数解析

```go
// ❌ 旧方式
idStr := c.Param("id")
id, err := strconv.ParseUint(idStr, 10, 32)
if err != nil {
    code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
    return
}

// ✅ 新方式
idStr := c.Param("id")
id := cast.ToUint(idStr)
if id == 0 {
    code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
    return
}
```

### 2. 查询参数解析

```go
// ❌ 旧方式
page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

// ✅ 新方式
page := cast.ToInt(c.DefaultQuery("page", "1"))
pageSize := cast.ToInt(c.DefaultQuery("page_size", "20"))
```

### 3. 条件查询参数

```go
// ❌ 旧方式
status := c.Query("status")
if status != "" {
    if statusInt, err := strconv.Atoi(status); err == nil {
        query = query.Where("status = ?", statusInt)
    }
}

// ✅ 新方式
status := c.Query("status")
if status != "" {
    if statusInt := cast.ToInt(status); statusInt > 0 {
        query = query.Where("status = ?", statusInt)
    }
}
```

## 📊 cast 库优势

### 1. 更安全的错误处理
- cast 在转换失败时返回类型的零值，而不是 panic
- 无需手动处理错误返回值
- 更优雅地处理边界情况

### 2. 更简洁的代码
- 减少样板代码
- 无需显式错误处理
- 代码更易读

### 3. 一致性
- 跨不同数据类型的一致性转换接口
- 统一的命名规范（ToInt, ToUint, ToString 等）

### 4. 零值友好
- nil 值安全处理
- 空字符串返回零值
- 无效输入返回零值而不是错误

## 🔍 需要迁移的文件列表

基于当前检查，以下文件需要迁移：

### Handler 文件
- `internal/handler/admin_booking_handler.go`
- `internal/handler/booking.go`
- `internal/handler/checkin_handler.go` (部分)
- `internal/handler/favorite_handler.go`
- `internal/handler/flexible_deduction_handler.go`
- `internal/handler/membership_card.go`
- `internal/handler/queue_handler.go`
- `internal/handler/resource_assignment_handler.go`
- `internal/handler/schedule_handler.go`
- `internal/handler/share_handler.go` (部分)
- `internal/handler/user_store_assignment_handler.go`

## 📝 迁移检查清单

### 每个文件迁移后检查：
- [ ] 添加 `"github.com/spf13/cast"` import
- [ ] 移除不必要的 `"strconv"` import
- [ ] 所有 `strconv.ParseXxx` 替换为 `cast.ToXxx`
- [ ] 错误处理改为零值检查
- [ ] 保留格式化相关的 strconv 使用
- [ ] 测试功能是否正常

### 全局检查：
- [ ] 运行 `go build` 确保编译通过
- [ ] 运行测试确保功能正常
- [ ] 检查是否有遗漏的 strconv 使用

## 🚀 执行步骤

1. **批量查找**: 使用 `grep -r "strconv\." internal/` 查找所有使用
2. **逐文件替换**: 按照上述模式逐个文件替换
3. **测试验证**: 每个文件替换后进行测试
4. **最终检查**: 确保所有 strconv 解析类使用都已替换

## ⚡ 快速替换命令

```bash
# 查找所有需要替换的 strconv 使用
grep -r "strconv\." internal/ --include="*.go" | grep -E "(ParseInt|ParseUint|Atoi|ParseFloat|ParseBool)"

# 检查是否还有遗漏
grep -r "strconv\." internal/ --include="*.go" | grep -v -E "(FormatInt|Itoa|FormatFloat|FormatBool)"
```
