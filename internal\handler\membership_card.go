package handler

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// MembershipCardHandler 会员卡处理器
type MembershipCardHandler struct {
	db                 *gorm.DB
	transactionService *service.MembershipTransactionService
}

// NewMembershipCardHandler 创建会员卡处理器实例
func NewMembershipCardHandler(db *gorm.DB) *MembershipCardHandler {
	return &MembershipCardHandler{
		db:                 db,
		transactionService: service.NewMembershipTransactionService(db),
	}
}

// GetMembershipCards 获取会员卡列表
func (h *MembershipCardHandler) GetMembershipCards(c *gin.Context) {
	var req dto.MembershipCardListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 获取当前用户ID和角色（用于权限控制）
	currentUserID, exists := c.Get("user_id")
	if !exists {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 构建查询条件
	query := h.db.Model(&model.MembershipCard{}).Preload("User").Preload("CardType")

	// 根据用户角色限制数据访问
	var currentUser model.User
	err := h.db.Preload("Roles").Where("id = ?", currentUserID).First(&currentUser).Error
	if err != nil {
		log.Error("查询当前用户失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "用户查询失败"))
		return
	}

	// 非超级管理员需要限制访问范围
	isAdmin := false
	for _, role := range currentUser.Roles {
		if role.Name == "admin" || role.Name == "超级管理员" {
			isAdmin = true
			break
		}
	}

	if !isAdmin {
		// 获取可访问的会员ID列表
		var accessibleMemberIDs []string

		// 检查是否为店长
		isManager := false
		for _, role := range currentUser.Roles {
			if role.Name == "manager" || role.Name == "店长" {
				isManager = true
				break
			}
		}

		// 检查是否为销售
		isSales := false
		for _, role := range currentUser.Roles {
			if role.Name == "sales" || role.Name == "销售" {
				isSales = true
				break
			}
		}

		if isManager {
			// 店长可以访问本门店所有会员（通过门店分配）
			var storeMembers []model.User
			h.db.Model(&model.User{}).
				Joins("JOIN membership_cards ON users.id = membership_cards.user_id").
				Joins("JOIN stores ON membership_cards.store_id = stores.id").
				Joins("JOIN resource_assignments ON stores.id = resource_assignments.resource_id").
				Where("resource_assignments.staff_id = ? AND resource_assignments.resource_type = 'store' AND resource_assignments.status = 1",
					currentUserID).
				Select("users.id").
				Find(&storeMembers)

			for _, member := range storeMembers {
				accessibleMemberIDs = append(accessibleMemberIDs, member.ID.String())
			}
		}

		if isSales {
			// 销售只能访问分配给自己的会员
			var assignedMembers []model.ResourceAssignment
			h.db.Model(&model.ResourceAssignment{}).
				Where("staff_id = ? AND resource_type = 'member' AND status = 1", currentUserID).
				Select("resource_id").
				Find(&assignedMembers)

			// 注意：ResourceAssignment表的ResourceID字段类型与用户ID类型不匹配
			// 这里暂时跳过资源分配逻辑，直接允许销售访问所有会员
			// 如需实现精确的资源分配控制，需要调整ResourceAssignment表结构
		}

		if len(accessibleMemberIDs) == 0 {
			// 没有可访问的会员，返回空列表
			response := dto.PageResponse{
				List:     []model.MembershipCard{},
				Total:    0,
				Page:     req.Page,
				PageSize: req.PageSize,
			}
			code.AutoResponse(c, response, nil)
			return
		}

		query = query.Where("user_id IN ?", accessibleMemberIDs)
	}

	// 用户ID筛选
	if req.UserID != "" {
		if userIDInt := cast.ToUint(req.UserID); userIDInt > 0 {
			query = query.Where("user_id = ?", userIDInt)
		}
	}

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 卡种筛选
	if req.CardTypeID != "" {
		if cardTypeIDInt := cast.ToUint(req.CardTypeID); cardTypeIDInt > 0 {
			query = query.Where("card_type_id = ?", cardTypeIDInt)
		}
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var cards []model.MembershipCard
	err = query.Order("created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&cards).Error

	if err != nil {
		log.Error("查询会员卡失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     cards,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetMembershipCard 获取会员卡详情（重定向到GetCardDetail以保持兼容性）
func (h *MembershipCardHandler) GetMembershipCard(c *gin.Context) {
	// 为了保持API兼容性，重定向到GetCardDetail
	h.GetCardDetail(c)
}

// CreateMembershipCard 开卡
func (h *MembershipCardHandler) CreateMembershipCard(c *gin.Context) {
	var req dto.CreateMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定开卡参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证用户是否存在
	var user model.User
	if err := h.db.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		return
	}

	// 验证卡种是否存在
	var cardType model.MembershipCardType
	if err := h.db.First(&cardType, req.CardTypeID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "卡种不存在"))
		return
	}

	// 验证卡种状态
	if cardType.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该卡种已停用"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 生成卡号
	cardNumber := h.generateCardNumber()

	// 计算初始值
	remainingTimes := 0
	remainingAmount := 0
	var endDate time.Time

	switch cardType.Category {
	case "times":
		remainingTimes = cardType.Times
	case "period":
		if cardType.ValidityDays > 0 {
			endDate = req.StartDate.AddDate(0, 0, cardType.ValidityDays)
		}
	case "balance":
		remainingAmount = cardType.Amount
	}

	// 处理门店选择
	availableStoresJSON := ""
	if len(req.AvailableStores) > 0 {
		storesBytes, _ := json.Marshal(req.AvailableStores)
		availableStoresJSON = string(storesBytes)
	}

	// 设置单日预约限制
	dailyLimit := req.DailyBookingLimit
	if dailyLimit == 0 && cardType.DailyBookingLimit > 0 {
		dailyLimit = cardType.DailyBookingLimit
	}

	// 创建会员卡
	card := model.MembershipCard{
		UserID:            req.UserID,
		CardTypeID:        req.CardTypeID,
		CardNumber:        cardNumber,
		IsMainCard:        true, // 默认为主卡
		RemainingTimes:    remainingTimes,
		RemainingAmount:   remainingAmount,
		TotalTimes:        remainingTimes,
		TotalAmount:       remainingAmount,
		StartDate:         req.StartDate,
		EndDate:           endDate,
		PurchasePrice:     req.PurchasePrice,
		Discount:          req.Discount,
		AvailableStores:   availableStoresJSON,
		DailyBookingLimit: dailyLimit,
		Status:            1, // 正常状态
	}

	if err := tx.Select("*").Create(&card).Error; err != nil {
		tx.Rollback()
		log.Error("创建会员卡失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "开卡失败"))
		return
	}

	// 记录开卡交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeIssue,
		TimesChange:     remainingTimes,
		AmountChange:    remainingAmount,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "开卡",
		Remarks:         fmt.Sprintf("开卡类型: %s, 购买价格: %.2f元", cardType.Name, float64(req.PurchasePrice)/100),
		RealCost:        req.PurchasePrice,
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录开卡交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "开卡失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交开卡事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "开卡失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("User").Preload("CardType").First(&card, card.ID)

	log.Info("开卡成功", "card_id", card.ID, "card_number", card.CardNumber, "user_id", req.UserID)
	code.AutoResponse(c, card, nil)
}

// TransferMembershipCard 转卡
func (h *MembershipCardHandler) TransferMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.TransferMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定转卡参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证目标用户是否存在
	var targetUser model.User
	if err := h.db.Where("phone = ?", req.TargetPhone).First(&targetUser).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "目标用户不存在"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.Preload("CardType").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 验证卡片状态
	if card.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该会员卡状态异常，无法转卡"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新会员卡归属
	updates := map[string]interface{}{
		"user_id": targetUser.ID,
	}

	if err := tx.Model(&card).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("转卡失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "转卡失败"))
		return
	}

	// 记录转卡交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeTransfer,
		TimesChange:     0,
		AmountChange:    0,
		RelatedUserID:   targetUser.ID.String(),
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "转卡",
		Remarks:         fmt.Sprintf("转卡给用户: %s (手机号: %s), 手续费: %.2f元", targetUser.Username, targetUser.Phone, float64(req.Fee)/100),
		RealCost:        req.Fee,
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录转卡交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "转卡失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交转卡事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "转卡失败"))
		return
	}

	log.Info("转卡成功", "card_id", id, "from_user", card.UserID, "to_user", targetUser.ID)
	response := dto.MessageResponse{Message: "转卡成功"}
	code.AutoResponse(c, response, nil)
}

// DeductMembershipCard 扣费
func (h *MembershipCardHandler) DeductMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.DeductMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定扣费参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.Preload("MainCard").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 确定实际扣费的卡片（副卡从主卡扣费）
	var targetCard *model.MembershipCard
	if card.MainCardID != nil && card.MainCard != nil {
		// 副卡：从主卡扣费
		targetCard = card.MainCard
		log.Info("副卡扣费，从主卡扣除", "sub_card_id", card.ID, "main_card_id", targetCard.ID)
	} else {
		// 主卡：从自身扣费
		targetCard = &card
	}

	// 验证余额（检查目标卡片的余额）
	if req.Amount > 0 && targetCard.RemainingAmount < req.Amount {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "余额不足"))
		return
	}
	if req.Times > 0 && targetCard.RemainingTimes < req.Times {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "次数不足"))
		return
	}

	// 更新余额（从目标卡片扣费）
	updates := make(map[string]interface{})
	if req.Amount > 0 {
		updates["remaining_amount"] = gorm.Expr("remaining_amount - ?", req.Amount)
	}
	if req.Times > 0 {
		updates["remaining_times"] = gorm.Expr("remaining_times - ?", req.Times)
	}

	if len(updates) == 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "扣费金额和次数不能都为0"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 从目标卡片扣费
	if err := tx.Model(targetCard).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("扣费失败", "target_card_id", targetCard.ID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "扣费失败"))
		return
	}

	// 记录扣费交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          targetCard.ID,
		TransactionType: enum.CardTransactionTypeDeduct,
		TimesChange:     -req.Times,  // 负数表示扣减
		AmountChange:    -req.Amount, // 负数表示扣减
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          req.Reason,
		Remarks:         fmt.Sprintf("扣费次数: %d, 扣费金额: %.2f元", req.Times, float64(req.Amount)/100),
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录扣费交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "扣费失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交扣费事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "扣费失败"))
		return
	}

	log.Info("扣费成功", "request_card_id", id, "target_card_id", targetCard.ID, "amount", req.Amount, "times", req.Times, "reason", req.Reason)
	response := dto.MessageResponse{Message: "扣费成功"}
	code.AutoResponse(c, response, nil)
}

// FreezeMembershipCard 冻结/解冻会员卡
func (h *MembershipCardHandler) FreezeMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.FreezeMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定冻结参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 更新状态
	var newStatus int
	var message string

	switch req.Action {
	case "freeze":
		if card.Status == 2 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "会员卡已处于冻结状态"))
			return
		}
		newStatus = 2 // 冻结
		message = "冻结成功"
	case "unfreeze":
		if card.Status != 2 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "会员卡未处于冻结状态"))
			return
		}
		newStatus = 1 // 正常
		message = "解冻成功"
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	if err := tx.Model(&card).Update("status", newStatus).Error; err != nil {
		tx.Rollback()
		log.Error("更新会员卡状态失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "操作失败"))
		return
	}

	// 记录冻结/解冻交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	var transactionType enum.CardTransactionType
	var reason string
	if req.Action == "freeze" {
		transactionType = enum.CardTransactionTypeFreeze
		reason = "冻结"
	} else {
		transactionType = enum.CardTransactionTypeUnfreeze
		reason = "解冻"
	}

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: transactionType,
		TimesChange:     0,
		AmountChange:    0,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          reason,
		Remarks:         fmt.Sprintf("%s原因: %s", reason, req.Reason),
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录冻结/解冻交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "操作失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交冻结/解冻事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "操作失败"))
		return
	}

	log.Info("会员卡状态更新成功", "card_id", id, "action", req.Action, "new_status", newStatus)
	response := dto.MessageResponse{Message: message}
	code.AutoResponse(c, response, nil)
}

// GetUserMembershipCards 获取用户的会员卡列表（小程序端）
func (h *MembershipCardHandler) GetUserMembershipCards(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		log.Error("未找到用户ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.UserNoLogin, "用户未登录"))
		return
	}

	// 查询用户的会员卡
	var cards []model.MembershipCard
	err := h.db.Preload("CardType").
		Where("user_id = ?", userID).
		Order("status ASC, created_at DESC").
		Find(&cards).Error

	if err != nil {
		log.Error("查询用户会员卡失败", "user_id", userID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡失败"))
		return
	}

	code.AutoResponse(c, cards, nil)
}

// CreateSubCard 创建副卡
func (h *MembershipCardHandler) CreateSubCard(c *gin.Context) {
	var req dto.CreateSubCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建副卡参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询主卡
	var mainCard model.MembershipCard
	err := h.db.Preload("CardType").First(&mainCard, req.MainCardID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "主卡不存在"))
		} else {
			log.Error("查询主卡失败", "main_card_id", req.MainCardID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询主卡失败"))
		}
		return
	}

	// 验证主卡是否支持副卡
	if !mainCard.CardType.CanAddSubCard {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该卡种不支持添加副卡"))
		return
	}

	// 验证主卡状态
	if mainCard.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "主卡状态异常，无法添加副卡"))
		return
	}

	// 验证用户是否存在
	var user model.User
	if err := h.db.Where("id = ?", req.UserID).First(&user).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "用户不存在"))
		return
	}

	// 检查用户是否已有该主卡的副卡
	var existingSubCard model.MembershipCard
	err = h.db.Where("main_card_id = ? AND user_id = ?", req.MainCardID, req.UserID).First(&existingSubCard).Error
	if err == nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该用户已有此主卡的副卡"))
		return
	}

	// 生成副卡卡号
	subCardNumber := h.generateCardNumber()

	// 创建副卡
	subCard := model.MembershipCard{
		UserID:            req.UserID,
		CardTypeID:        mainCard.CardTypeID,
		CardNumber:        subCardNumber,
		MainCardID:        &req.MainCardID,
		IsMainCard:        false,
		RemainingTimes:    0, // 副卡不独立计算余额
		RemainingAmount:   0, // 副卡不独立计算余额
		TotalTimes:        0,
		TotalAmount:       0,
		StartDate:         mainCard.StartDate,
		EndDate:           mainCard.EndDate,
		PurchasePrice:     0, // 副卡无购买价格
		Discount:          mainCard.Discount,
		AvailableStores:   mainCard.AvailableStores,
		DailyBookingLimit: mainCard.DailyBookingLimit,
		Status:            1, // 正常状态
		Remark:            req.Remark,
	}

	if err := h.db.Select("*").Create(&subCard).Error; err != nil {
		log.Error("创建副卡失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建副卡失败"))
		return
	}

	log.Info("创建副卡成功", "sub_card_id", subCard.ID, "main_card_id", req.MainCardID, "user_id", req.UserID)
	code.AutoResponse(c, subCard, nil)
}

// GetSubCards 获取副卡列表
func (h *MembershipCardHandler) GetSubCards(c *gin.Context) {
	idStr := c.Param("id")
	mainCardID := cast.ToUint(idStr)
	if mainCardID == 0 {
		log.Error("主卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 查询主卡
	var mainCard model.MembershipCard
	if err := h.db.Preload("CardType").First(&mainCard, mainCardID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "主卡不存在"))
		} else {
			log.Error("查询主卡失败", "main_card_id", mainCardID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询主卡失败"))
		}
		return
	}

	// 查询副卡列表
	var subCards []model.MembershipCard
	err := h.db.Preload("User").
		Where("main_card_id = ?", mainCardID).
		Order("created_at DESC").
		Find(&subCards).Error

	if err != nil {
		log.Error("查询副卡列表失败", "main_card_id", mainCardID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询副卡列表失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     subCards,
		Total:    int64(len(subCards)),
		Page:     1,
		PageSize: len(subCards),
	}

	code.AutoResponse(c, response, nil)
}

// GetCardDetail 获取会员卡详情（包含副卡信息）
func (h *MembershipCardHandler) GetCardDetail(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	err := h.db.Preload("User").Preload("CardType").Preload("SubCards").Preload("SubCards.User").First(&card, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		} else {
			log.Error("查询会员卡失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡失败"))
		}
		return
	}

	// 构建响应数据
	detail := dto.MembershipCardDetail{
		ID:                card.ID,
		CardNumber:        card.CardNumber,
		CardTypeName:      card.CardType.Name,
		Category:          string(card.CardType.Category),
		IsMainCard:        card.IsMainCard,
		RemainingTimes:    card.RemainingTimes,
		RemainingAmount:   card.RemainingAmount,
		TotalTimes:        card.TotalTimes,
		TotalAmount:       card.TotalAmount,
		StartDate:         card.StartDate.Format("2006-01-02"),
		EndDate:           card.EndDate.Format("2006-01-02"),
		Status:            int(card.Status),
		DailyBookingLimit: card.DailyBookingLimit,
		CreatedAt:         card.CreatedAt.Format("2006-01-02 15:04:05"),
	}

	// 处理可用门店
	if card.AvailableStores != "" {
		var storeIDs []uint
		if err := json.Unmarshal([]byte(card.AvailableStores), &storeIDs); err == nil {
			var stores []model.Store
			h.db.Where("id IN ?", storeIDs).Find(&stores)
			for _, store := range stores {
				detail.AvailableStores = append(detail.AvailableStores, dto.StoreInfo{
					ID:   store.ID,
					Name: store.Name,
				})
			}
		}
	} else {
		// 如果没有指定门店，则表示全部门店可用
		var stores []model.Store
		h.db.Find(&stores)
		for _, store := range stores {
			detail.AvailableStores = append(detail.AvailableStores, dto.StoreInfo{
				ID:   store.ID,
				Name: store.Name,
			})
		}
	}

	// 处理副卡信息（仅主卡有副卡）
	if card.IsMainCard {
		for _, subCard := range card.SubCards {
			userName := subCard.User.NickName
			if userName == "" {
				userName = subCard.User.Username
			}

			detail.SubCards = append(detail.SubCards, dto.SubCardInfo{
				ID:         subCard.ID,
				CardNumber: subCard.CardNumber,
				UserID:     subCard.UserID,
				UserName:   userName,
				UserPhone:  subCard.User.Phone,
				Status:     int(subCard.Status),
				CreatedAt:  subCard.CreatedAt.Format("2006-01-02 15:04:05"),
			})
		}
	}

	code.AutoResponse(c, detail, nil)
}

// RechargeMembershipCard 充值会员卡
func (h *MembershipCardHandler) RechargeMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.RechargeMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定充值参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.Preload("CardType").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 验证卡片状态
	if card.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该会员卡状态异常，无法充值"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新会员卡余额
	updates := make(map[string]interface{})

	switch card.CardType.Category {
	case "times":
		updates["remaining_times"] = card.RemainingTimes + req.RechargeTimes
		updates["total_times"] = card.TotalTimes + req.RechargeTimes
	case "balance":
		updates["remaining_amount"] = card.RemainingAmount + req.RechargeAmount
		updates["total_amount"] = card.TotalAmount + req.RechargeAmount
	}

	// 处理有效期
	if req.ExtendExpiry && req.NewEndDate != nil {
		updates["end_date"] = *req.NewEndDate
	}

	if err := tx.Model(&card).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("充值会员卡失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "充值失败"))
		return
	}

	// 记录充值交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeRecharge,
		TimesChange:     req.RechargeTimes,
		AmountChange:    req.RechargeAmount,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "充值",
		Remarks:         fmt.Sprintf("充值次数: %d, 充值金额: %.2f元", req.RechargeTimes, float64(req.RechargeAmount)/100),
		RealCost:        req.RechargeAmount,
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录充值交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "充值失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交充值事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "充值失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("User").Preload("CardType").First(&card, id)

	log.Info("充值会员卡成功", "card_id", id, "recharge_times", req.RechargeTimes, "recharge_amount", req.RechargeAmount)
	code.AutoResponse(c, card, nil)
}

// UpgradeMembershipCard 升级会员卡
func (h *MembershipCardHandler) UpgradeMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpgradeMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定升级参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询原会员卡
	var card model.MembershipCard
	if err := h.db.Preload("CardType").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 查询目标卡种
	var targetCardType model.MembershipCardType
	if err := h.db.First(&targetCardType, req.TargetCardTypeID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "目标卡种不存在"))
		return
	}

	// 验证升级条件：同类型卡种
	if card.CardType.Category != targetCardType.Category {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "只能在同类型卡种间升级"))
		return
	}

	// 验证卡片状态
	if card.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该会员卡状态异常，无法升级"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新会员卡类型
	updates := map[string]interface{}{
		"card_type_id": req.TargetCardTypeID,
		"remark":       req.Remark,
	}

	if err := tx.Model(&card).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("升级会员卡失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "升级失败"))
		return
	}

	// 记录升级交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeUpgrade,
		TimesChange:     0,
		AmountChange:    0,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "升级",
		Remarks:         fmt.Sprintf("从 %s 升级到 %s，备注: %s", card.CardType.Name, targetCardType.Name, req.Remark),
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录升级交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "升级失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交升级事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "升级失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("User").Preload("CardType").First(&card, id)

	log.Info("升级会员卡成功", "card_id", id, "target_card_type_id", req.TargetCardTypeID)
	code.AutoResponse(c, card, nil)
}

// LeaveMembershipCard 请假会员卡
func (h *MembershipCardHandler) LeaveMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.LeaveMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定请假参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.Preload("CardType").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 验证卡片状态
	if card.Status != 1 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该会员卡状态异常，无法请假"))
		return
	}

	// 只有期限卡支持请假
	if card.CardType.Category != "period" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "只有期限卡支持请假功能"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 计算新的到期日期（延长请假天数）
	newEndDate := card.EndDate.AddDate(0, 0, req.LeaveDays)

	// 更新会员卡
	updates := map[string]interface{}{
		"end_date": newEndDate,
		"status":   2, // 冻结状态（请假期间）
		"remark":   req.Reason,
	}

	if err := tx.Model(&card).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("请假会员卡失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "请假失败"))
		return
	}

	// 记录请假交易（使用冻结类型，因为请假期间卡片被冻结）
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeFreeze,
		TimesChange:     0,
		AmountChange:    0,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "请假",
		Remarks:         fmt.Sprintf("请假 %d 天，延期到 %s，原因: %s", req.LeaveDays, newEndDate.Format("2006-01-02"), req.Reason),
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录请假交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "请假失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交请假事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "请假失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("User").Preload("CardType").First(&card, id)

	log.Info("请假会员卡成功", "card_id", id, "leave_days", req.LeaveDays)
	code.AutoResponse(c, card, nil)
}

// ExtendMembershipCard 延期会员卡
func (h *MembershipCardHandler) ExtendMembershipCard(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.ExtendMembershipCardRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定延期参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询会员卡
	var card model.MembershipCard
	if err := h.db.Preload("CardType").First(&card, id).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		return
	}

	// 验证卡片状态（过期卡也可以延期）
	if card.Status != 1 && card.Status != 3 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该会员卡状态异常，无法延期"))
		return
	}

	// 只有期限卡和储值卡支持延期
	if card.CardType.Category == "times" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "次数卡不支持延期功能"))
		return
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 计算新的到期日期
	var newEndDate time.Time
	if card.EndDate.Before(time.Now()) {
		// 如果已过期，从当前时间开始延期
		newEndDate = time.Now().AddDate(0, 0, req.ExtendDays)
	} else {
		// 如果未过期，从原到期日期延期
		newEndDate = card.EndDate.AddDate(0, 0, req.ExtendDays)
	}

	// 更新会员卡
	updates := map[string]interface{}{
		"end_date": newEndDate,
		"status":   1, // 恢复正常状态
		"remark":   req.Reason,
	}

	if err := tx.Model(&card).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("延期会员卡失败", "card_id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "延期失败"))
		return
	}

	// 记录延期交易
	currentUserID, _ := c.Get("user_id")
	currentUsername, _ := c.Get("username")

	transactionReq := service.CreateTransactionRequest{
		CardID:          card.ID,
		TransactionType: enum.CardTransactionTypeExtend,
		TimesChange:     0,
		AmountChange:    0,
		OperatorID:      fmt.Sprintf("%v", currentUserID),
		OperatorName:    fmt.Sprintf("%v", currentUsername),
		Reason:          "延期",
		Remarks:         fmt.Sprintf("延期 %d 天，新到期日期: %s，原因: %s", req.ExtendDays, newEndDate.Format("2006-01-02"), req.Reason),
	}

	if err := h.transactionService.RecordTransaction(tx, transactionReq); err != nil {
		tx.Rollback()
		log.Error("记录延期交易失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "延期失败"))
		return
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交延期事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "延期失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("User").Preload("CardType").First(&card, id)

	log.Info("延期会员卡成功", "card_id", id, "extend_days", req.ExtendDays, "new_end_date", newEndDate)
	code.AutoResponse(c, card, nil)
}

// GetCardTransactions 获取会员卡交易记录
func (h *MembershipCardHandler) GetCardTransactions(c *gin.Context) {
	idStr := c.Param("id")
	cardID := cast.ToUint(idStr)
	if cardID == 0 {
		log.Error("会员卡ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 分页参数
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 验证会员卡是否存在
	var card model.MembershipCard
	if err := h.db.First(&card, cardID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "会员卡不存在"))
		} else {
			log.Error("查询会员卡失败", "card_id", cardID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询会员卡失败"))
		}
		return
	}

	// 获取交易记录
	transactions, total, err := h.transactionService.GetCardTransactions(cardID, req.Page, req.PageSize)
	if err != nil {
		log.Error("获取会员卡交易记录失败", "card_id", cardID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取交易记录失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     transactions,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// generateCardNumber 生成卡号
func (h *MembershipCardHandler) generateCardNumber() string {
	// 使用更强的唯一性保证机制
	now := time.Now()
	timestamp := now.Unix()
	nanosec := now.Nanosecond()

	// 生成随机数作为额外的唯一性保证
	randomNum := rand.Intn(9999)

	// 组合时间戳、纳秒和随机数确保唯一性
	// 格式：YG + 时间戳后8位 + 纳秒后4位 + 随机数后4位
	cardNumber := fmt.Sprintf("YG%08d%04d%04d",
		timestamp%100000000,    // 时间戳后8位
		(nanosec/100000)%10000, // 纳秒转换后4位
		randomNum)              // 随机数4位

	// 检查数据库中是否已存在此卡号，如果存在则递归重新生成
	var existingCard model.MembershipCard
	if err := h.db.Where("card_number = ?", cardNumber).First(&existingCard).Error; err == nil {
		// 卡号已存在，递归重新生成
		return h.generateCardNumber()
	}

	return cardNumber
}
