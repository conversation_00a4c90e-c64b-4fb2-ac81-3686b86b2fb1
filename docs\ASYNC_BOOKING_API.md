# Yogaga异步预约系统API文档

## 🎯 系统特点

基于**Asynq消息队列**的异步预约系统，解决了高并发场景下的预约冲突问题：

- ✅ **并发安全**：消息队列串行处理，避免超售
- ✅ **用户友好**：立即返回申请结果，异步处理
- ✅ **状态追踪**：实时查询处理状态和结果
- ✅ **智能排队**：课程满员自动排队，有空位时通知

## 📱 API接口

### 1. 提交预约申请

**接口**：`POST /api/v1/app/bookings`

**请求头**：
```
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json
```

**请求体**：
```json
{
  "schedule_id": 123,
  "membership_card_id": 456,  // 可选，不传则自动选择最优卡
  "people_count": 1
}
```

**响应**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "task_id": "asynq:task:01HXXX",
    "status": "pending",
    "message": "预约申请已提交，请稍候查看处理结果",
    "estimated_time": 5
  }
}
```

### 2. 查询预约状态

**接口**：`GET /api/v1/app/bookings/status/{task_id}`

**响应示例**：

#### 处理中
```json
{
  "code": 200,
  "data": {
    "task_id": "asynq:task:01HXXX",
    "status": "processing",
    "result": "正在处理预约申请...",
    "created_at": "2024-01-15 10:30:00",
    "updated_at": "2024-01-15 10:30:05"
  }
}
```

#### 预约成功
```json
{
  "code": 200,
  "data": {
    "task_id": "asynq:task:01HXXX",
    "status": "completed",
    "result": "预约成功",
    "booking_id": 789,
    "booking_info": {
      "id": 789,
      "booking_number": "BK202401151030123456",
      "schedule_info": {
        "id": 123,
        "course_name": "哈他瑜伽",
        "coach_name": "张老师",
        "store_name": "朝阳店",
        "start_time": "2024-01-16 19:00:00",
        "end_time": "2024-01-16 20:00:00",
        "duration": 60
      },
      "people_count": 1,
      "status": 1,
      "status_text": "已预约",
      "can_cancel": true,
      "created_at": "2024-01-15 10:30:05"
    }
  }
}
```

#### 课程满员排队
```json
{
  "code": 200,
  "data": {
    "task_id": "asynq:task:01HXXX",
    "status": "queued",
    "result": "课程已满，已加入排队，当前排队位置：3",
    "queue_position": 3,
    "created_at": "2024-01-15 10:30:00"
  }
}
```

#### 预约失败
```json
{
  "code": 200,
  "data": {
    "task_id": "asynq:task:01HXXX",
    "status": "failed",
    "result": "开课前2小时内不可预约",
    "error_code": "TIME_LIMIT_EXCEEDED",
    "created_at": "2024-01-15 10:30:00"
  }
}
```

### 3. 预约列表

**接口**：`GET /api/v1/app/bookings`

**查询参数**：
```
?type=upcoming&page=1&page_size=10&status=1
```

**响应**：
```json
{
  "code": 200,
  "data": {
    "list": [
      {
        "id": 789,
        "booking_number": "BK202401151030123456",
        "schedule_info": {
          "course_name": "哈他瑜伽",
          "coach_name": "张老师",
          "start_time": "2024-01-16 19:00:00"
        },
        "status": 1,
        "status_text": "已预约",
        "can_cancel": true
      }
    ],
    "total": 1,
    "page": 1,
    "page_size": 10,
    "statistics": {
      "total_classes": 15,
      "total_days": 12,
      "upcoming_classes": 3
    }
  }
}
```

### 4. 取消预约

**接口**：`DELETE /api/v1/app/bookings/cancel`

**请求体**：
```json
{
  "booking_id": 789,
  "reason": "临时有事"
}
```

### 5. 评价预约

**接口**：`POST /api/v1/app/bookings/rate`

**请求体**：
```json
{
  "booking_id": 789,
  "rating": 5,
  "comment": "教练很专业，课程很棒！"
}
```

## 🔄 预约流程

### 用户端流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant A as API
    participant Q as 消息队列
    participant W as Worker

    U->>A: 1. 提交预约申请
    A->>Q: 2. 创建异步任务
    A->>U: 3. 返回task_id
    
    loop 查询状态
        U->>A: 4. 查询处理状态
        A->>U: 5. 返回当前状态
    end
    
    Q->>W: 6. 处理预约任务
    W->>W: 7. 业务逻辑处理
    W->>A: 8. 更新处理结果
```

### 系统处理流程
```mermaid
graph TB
    A[接收预约申请] --> B[创建申请记录]
    B --> C[生成异步任务]
    C --> D[返回task_id]
    
    E[Worker处理] --> F{检查课程容量}
    F -->|有空位| G[选择会员卡]
    F -->|已满员| H[加入排队]
    
    G --> I[计算扣费]
    I --> J[创建预约记录]
    J --> K[更新课程人数]
    K --> L[扣除会员卡余额]
    L --> M[预约成功]
    
    H --> N[排队成功]
```

## 🎯 状态说明

| 状态 | 说明 | 用户操作 |
|------|------|----------|
| `pending` | 申请已提交，等待处理 | 等待或查询状态 |
| `processing` | 正在处理中 | 等待处理完成 |
| `completed` | 预约成功 | 可查看预约详情 |
| `queued` | 已加入排队 | 等待排队转预约通知 |
| `failed` | 预约失败 | 查看失败原因 |

## 💡 使用建议

### 前端实现
```javascript
// 1. 提交预约
const submitBooking = async (scheduleId, peopleCount) => {
  const response = await fetch('/api/v1/app/bookings', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      schedule_id: scheduleId,
      people_count: peopleCount
    })
  });
  
  const result = await response.json();
  return result.data.task_id;
};

// 2. 轮询查询状态
const pollBookingStatus = async (taskId) => {
  const maxAttempts = 12; // 最多查询12次
  const interval = 2000;  // 每2秒查询一次
  
  for (let i = 0; i < maxAttempts; i++) {
    const response = await fetch(`/api/v1/app/bookings/status/${taskId}`);
    const result = await response.json();
    
    if (result.data.status === 'completed') {
      return { success: true, booking: result.data.booking_info };
    } else if (result.data.status === 'failed') {
      return { success: false, error: result.data.result };
    } else if (result.data.status === 'queued') {
      return { success: false, queued: true, position: result.data.queue_position };
    }
    
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  return { success: false, timeout: true };
};
```

## 🚀 系统优势

1. **高并发处理**：消息队列串行处理，避免竞态条件
2. **用户体验好**：立即响应，异步处理，状态可查
3. **业务逻辑清晰**：智能卡选择、自动排队、状态管理
4. **扩展性强**：支持延时任务、批量处理、监控告警
5. **数据一致性**：事务保证，状态可追溯

这个异步预约系统完美解决了高并发预约的技术难题，同时提供了优秀的用户体验！
