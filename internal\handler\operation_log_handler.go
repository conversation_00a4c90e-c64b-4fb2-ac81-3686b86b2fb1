package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/service"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OperationLogHandler 操作日志处理器
type OperationLogHandler struct {
	db         *gorm.DB
	logService *service.OperationLogService
}

// NewOperationLogHandler 创建操作日志处理器实例
func NewOperationLogHandler(db *gorm.DB) *OperationLogHandler {
	return &OperationLogHandler{
		db:         db,
		logService: service.NewOperationLogService(db),
	}
}

// GetOperationLogs 获取操作日志列表
func (h *OperationLogHandler) GetOperationLogs(c *gin.Context) {
	// 解析查询参数
	var req dto.OperationLogListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 解析日期
	var startDate, endDate time.Time
	if req.StartDate != "" {
		if date, err := time.Parse("2006-01-02", req.StartDate); err == nil {
			startDate = date
		}
	}
	if req.EndDate != "" {
		if date, err := time.Parse("2006-01-02", req.EndDate); err == nil {
			endDate = date.Add(24 * time.Hour) // 包含当天
		}
	}

	// 查询日志
	logs, total, err := h.logService.GetOperationLogs(req.Page, req.PageSize, req.UserID, req.Module, req.Action, req.Status, startDate, endDate)
	if err != nil {
		log.Error("查询操作日志失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询操作日志失败"))
		return
	}

	// 构建响应
	response := dto.PageResponse{
		List:     logs,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetOperationLogDetail 获取操作日志详情
func (h *OperationLogHandler) GetOperationLogDetail(c *gin.Context) {
	logIDStr := c.Param("id")
	if logIDStr == "" {
		log.Error("日志ID不能为空", "id", logIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "日志ID不能为空"))
		return
	}

	// 查询日志详情
	logDetail, err := h.logService.GetOperationLogDetail(logIDStr)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "操作日志不存在"))
			return
		}
		log.Error("查询操作日志详情失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询操作日志详情失败"))
		return
	}

	code.AutoResponse(c, logDetail, nil)
}

// DeleteOperationLog 删除操作日志（仅超级管理员）
func (h *OperationLogHandler) DeleteOperationLog(c *gin.Context) {
	logIDStr := c.Param("id")
	if logIDStr == "" {
		log.Error("日志ID不能为空", "id", logIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "日志ID不能为空"))
		return
	}

	// 检查用户权限（只有超级管理员可以删除日志）
	// 这里可以添加权限检查逻辑

	// 删除日志
	if err := h.logService.DeleteOperationLog(logIDStr); err != nil {
		log.Error("删除操作日志失败", "log_id", logIDStr, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除操作日志失败"))
		return
	}

	log.Info("删除操作日志成功", "log_id", logIDStr)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// GetOperationStats 获取操作统计
func (h *OperationLogHandler) GetOperationStats(c *gin.Context) {
	startDateStr := c.DefaultQuery("start_date", time.Now().AddDate(0, 0, -30).Format("2006-01-02"))
	endDateStr := c.DefaultQuery("end_date", time.Now().Format("2006-01-02"))

	// 解析日期
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "开始日期格式错误"))
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "结束日期格式错误"))
		return
	}
	endDate = endDate.Add(24 * time.Hour) // 包含当天

	// 获取统计数据
	stats, err := h.logService.GetOperationStats(startDate, endDate)
	if err != nil {
		log.Error("获取操作统计失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取操作统计失败"))
		return
	}

	code.AutoResponse(c, stats, nil)
}

// CleanOldLogs 清理旧日志
func (h *OperationLogHandler) CleanOldLogs(c *gin.Context) {
	var req dto.CleanLogsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定清理日志参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 默认保留90天
	if req.KeepDays <= 0 {
		req.KeepDays = 90
	}

	// 清理旧日志
	err := h.logService.CleanOldLogs(req.KeepDays)
	if err != nil {
		log.Error("清理旧日志失败", "keep_days", req.KeepDays, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "清理旧日志失败"))
		return
	}

	log.Info("清理旧日志成功", "keep_days", req.KeepDays)
	response := dto.MessageResponse{Message: "清理成功"}
	code.AutoResponse(c, response, nil)
}

// GetLogModules 获取日志模块列表
func (h *OperationLogHandler) GetLogModules(c *gin.Context) {
	modules := []map[string]string{
		{"value": "user", "label": "用户管理"},
		{"value": "role", "label": "角色管理"},
		{"value": "permission", "label": "权限管理"},
		{"value": "menu", "label": "菜单管理"},
		{"value": "store", "label": "门店管理"},
		{"value": "course", "label": "课程管理"},
		{"value": "schedule", "label": "排课管理"},
		{"value": "booking", "label": "预约管理"},
		{"value": "membership_card", "label": "会员卡管理"},
		{"value": "coach", "label": "教练管理"},
		{"value": "report", "label": "报表管理"},
		{"value": "system", "label": "系统管理"},
	}

	code.AutoResponse(c, modules, nil)
}

// GetLogActions 获取日志动作列表
func (h *OperationLogHandler) GetLogActions(c *gin.Context) {
	actions := []map[string]string{
		{"value": "create", "label": "创建"},
		{"value": "update", "label": "更新"},
		{"value": "delete", "label": "删除"},
		{"value": "view", "label": "查看"},
		{"value": "login", "label": "登录"},
		{"value": "logout", "label": "登出"},
		{"value": "export", "label": "导出"},
		{"value": "import", "label": "导入"},
	}

	code.AutoResponse(c, actions, nil)
}
