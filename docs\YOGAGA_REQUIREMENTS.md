序号	一级功能点	二级功能点	需求描述
1	首页	首次登录【新用户登录】	用户隐私保护提示，同意进入小程序，不同意退出小程序。定位【获取到用户的登录位置】、姓名、手机号【获取用户的微信账号信息】
		广告弹窗	后台设置后，当用户进入小程序即出现广告弹窗，支持超级管理员自主修改
		轮播图	后台上传图片后在进行轮播，并可以点击，进入对应的页面，如公众号，报名页面等
		我们的教练（查看免登录）	显示后台设置的教练名称和头像。
			点击后，展示对应轮播图，教练简介，可收藏可分享
			根据日历日期显示该教练的相关课程，并可以点击预约报名-人数报满后禁止提交
		门店精选（查看免登录）	展示所有门店，图片、与用户距离、名称、地址
			点击后，显示门店信息，轮播图、距离、位置、电话，可收藏可分享
			展示该门店的教练，点击后可以查看详情
			根据日历日期显示本周该门店的所有课程，并可以点击预约报名-人数报满后禁止提交
		课程推荐（查看免登录）	课程列表显示，图片/名称，可收藏
			点击后，显示该课程的详细信息，例如课程名称、课程介绍，可收藏可分享
			显示该课程的日历排期，点击有排课的日期，即显示对应教练的排课报名入口，并显示队教练排课的门店位置信息
		扫码签到功能	用户通过微信扫Classpath生成的二维码后显示用户报名的对应门店和课程信息,点击后可以跳转到小程序的课程中。限制条件：本人账号，开课前后90分钟内，与门店距离不超过300米# Yogaga小程序需求分析文档

## 📋 项目概述

基于微信小程序的瑜伽健身课程预约管理系统，包含用户端（小程序）和管理端（PC后台）两个部分。

## 🎯 核心功能架构

```mermaid
graph TB
    A[Yogaga小程序系统] --> B[用户端-小程序]
    A --> C[管理端-PC后台]

    B --> D[首页功能]
    B --> E[课程管理]
    B --> F[我的预约]
    B --> G[个人中心]

    C --> H[门店管理]
    C --> I[会员管理]
    C --> J[课程管理]
    C --> K[数据统计]
```

---

## 📱 用户端（小程序端）功能详情

### 1. 首页模块

#### 1.1 用户登录与隐私
- **首次登录流程**
  - 用户隐私保护提示
  - 同意条款进入，不同意则退出
  - 获取用户定位信息
  - 获取微信账号信息（姓名、手机号）

#### 1.2 营销功能
- **广告弹窗**
  - 后台可配置广告内容
  - 支持超级管理员自主修改

- **轮播图**
  - 后台上传图片进行轮播展示
  - 支持点击跳转（公众号、报名页面等）

#### 1.3 核心展示功能

##### 我们的教练（免登录查看）
- **教练列表**：显示教练名称和头像
- **教练详情**：
  - 轮播图展示
  - 教练简介
  - 收藏和分享功能
- **课程安排**：
  - 日历显示教练课程
  - 支持预约报名
  - 人数报满后禁止提交

##### 门店精选（免登录查看）
- **门店列表**：
  - 门店图片
  - 与用户距离
  - 门店名称和地址
- **门店详情**：
  - 轮播图、距离、位置、电话
  - 收藏和分享功能
  - 门店教练展示
  - 本周课程安排和预约

##### 课程推荐（免登录查看）
- **课程列表**：课程图片/名称，支持收藏
- **课程详情**：
  - 课程名称、介绍
  - 收藏和分享功能
  - 日历排期显示
  - 教练排课报名入口
  - 门店位置信息

#### 1.4 扫码签到功能
- **功能限制**：
  - 本人账号验证
  - 开课前后90分钟内有效
  - 与门店距离不超过300米
- **显示内容**：用户报名的门店和课程信息

### 2. 课程模块（免登录）

#### 2.1 课程浏览
- 日历显示各门店可报名课程
- 门店收藏功能

#### 2.2 筛选功能
- 按门店筛选
- 按教练筛选
- 按课程类型筛选
- 按课程级别筛选

#### 2.3 预约入口
- 点击日期显示教练和门店排课
- 直接进入报名流程

### 3. 我的预约模块

#### 3.1 预约概览
- **统计信息**：上课次数、上课天数
- **预约列表**：用户预约的课程信息
- **取消规则**：开课两小时外可取消

#### 3.2 预约管理

##### 开课提醒
- 课程开课前2小时微信提醒

##### 预约课程
- **卡种选择**：
  - 优先级：期限卡 > 其他卡种
  - 按有效期临近排序
  - 显示扣卡次数/金额
- **人数选择**：支持多人共享（如共享储值卡）
- **提醒机制**：前台、后台预约均发送微信提醒

##### 历史预约
- 查看预约历史
- 课程评分功能（五星评价+评价内容）

##### 预约详情
- **课程状态**：未开始/已结束
- **课程信息**：名称、人数、时间、门店、地址、电话
- **订单信息**：编号、总价、实付金额、支付时间、支付方式

##### 取消预约
- **确认流程**：
  - 弹框一：显示课程信息、退款方式、退款金额
  - 弹框二：二次确认
- **退款机制**：
  - 微信支付 → 微信退款
  - 会员卡支付 → 原卡退款
- **提醒机制**：取消成功发送微信提醒
- **自动取消**：人数不足时系统自动取消并退款

### 4. 个人中心模块

#### 4.1 个人信息管理
- **可修改信息**：头像、昵称、性别、生日
- **固定信息**：手机号（绑定后不可修改）
- **登录方式**：
  - 手机号+密码
  - 手机号+验证码
- **账户切换**：支持多手机号切换登录

#### 4.2 会员卡管理
- **卡片信息**：卡类型、有效期、剩余余额、适用门店
- **历史查询**：查看历史会员卡
- **到期提醒**：
  - 前60天到期提醒
  - 后台和微信同步提醒
- **扣除提醒**：期限卡年度未签到超过2次触发有效期扣除

#### 4.3 更多功能
- **我的收藏**：教练、课程、门店收藏列表
- **官方入口**：公众号入口、视频号推荐
- **客服功能**：添加店长微信咨询
- **条款政策**：PDF文档显示，支持管理员修改

#### 4.4 客服与反馈
- **建议/投诉**：
  - 选择门店
  - 填写投诉内容
  - 自动发送给店长微信
  - 后台生成报表
  - 登记信息：姓名、手机号、内容、可沟通时间

#### 4.5 教练入口
- 查看前60天和后15天的课程信息
- 查询所有自己的教课记录

---

## 💻 管理端（PC后台）功能详情

### 1. 系统登录
- 管理员手机号+密码登录

### 2. 总部管理（Yogaga首页）

#### 2.1 门店管理
- **门店信息**：图片、名称、电话、地址
- **操作功能**：添加/修改/删除门店
- **权限控制**：点击门店进入对应后台

#### 2.2 教练管理
- **教练信息**：头像、姓名、性别、排序、联系方式、是否首页显示
- **状态管理**：在职/离职状态
- **详情管理**：教练详情轮播图设置
- **权限控制**：离职教练不出现在课程管理选项

#### 2.3 首页轮播图
- 小程序首页轮播图管理
- 图片上传和展示控制

#### 2.4 管理员管理
- **管理员信息**：姓名、手机号、类别、门店、状态、最后登录时间
- **操作功能**：添加/修改/删除管理员、修改密码

### 3. 门店后台系统

#### 3.1 仪表盘
- 数据汇总展示
- 相关图表信息

#### 3.2 用户管理

##### 会员管理
- 所有会员信息管理

##### 会员卡绑定信息
- 会员办理的会员卡信息

##### 普通用户管理
- 小程序用户信息管理

#### 3.3 会员卡管理

##### 会员卡种类管理
- **卡种信息**：所有会员卡种类
- **状态控制**：启用/停用功能
- **权限控制**：停用卡种不出现在课程管理选项

##### 会员卡大类管理
- **次数卡**：按次数扣费
- **期限卡**：按时间期限
- **储值卡**：按金额扣费（支持共享）

##### 开卡功能
- **支付方式**：
  - 线下支付（手工录入）
  - 其他会员卡支付
- **有效期管理**：录入开卡日期和有效天数
- **ID生成**：每张卡单独生成ID号
- **折扣计算**：根据充值金额和实收金额计算

##### 其他卡务操作
- **转卡**：输入转入手机号、手续费、备注
- **充值**：支持线下支付和会员卡支付
- **扣费**：卡片扣费管理
- **升级**：同类型卡种变更
- **请假**：设置请假天数，自动顺延
- **停用**：卡片停用管理
- **延期**：到期后延期使用
- **预约详情**：查看会员预约操作详情

#### 3.4 教室管理
- 教室信息管理
- 容纳人数设置（按门店）

#### 3.5 课程管理

##### 排课管理
- **课程上线**：添加新课程，勾选对应会员卡种
- **冲突校验**：自动校验教室、老师时间冲突
- **批量操作**：支持批量上课程（除私教外）
- **信息修改**：课程种类、时间、教练、扣卡规则
- **搜索功能**：支持搜索课程、教练
- **权限控制**：课程开始后不可修改

##### 课程类型管理
- **二级分类**：具体课程类型
- **扣卡规则**：按门店设置扣卡规则

##### 课程归属管理
- **一级分类**：瑜伽团课、普拉提小班、精品小班、私教

#### 3.6 排队管理
- 课程预约满后开启排队
- 产生空位时自动提醒排队用户

#### 3.7 员工管理

##### 员工信息
- **状态管理**：在职/离职状态
- **权限控制**：离职员工不出现在销售选择场景

##### 职位管理
- 店长/销售/前台/教练/总监等职位设置
- 后续根据职位设置权限

##### 资源划分
- **权限分配**：
  - 超级管理员设置资源划分权限
  - 店长分配门店资源给销售
  - 销售查询名下会员信息
  - 店长查询门店所有会员信息
  - 超级管理员查询所有信息

#### 3.8 权限管理
- 各类型管理员权限设置

#### 3.9 课程表
- 本周/本月全览课程表
- 根据系统课表自动生成

#### 3.10 数据统计

##### 会员卡销售统计
- 根据开卡数据统计销售金额

##### 会员卡预约报表
- **详细数据**：会员姓名、手机号、会员卡ID、卡名称、预约时间、扣卡次数/金额、实际耗卡金额、取消时间、备注

##### 课程报表
- 每节课生成单独ID号
- 查看开课率等数据

##### 其他报表
- 预约报表
- 评分报表

#### 3.11 日志管理
- **操作记录**：所有操作留痕
- **记录内容**：操作时间、操作人、操作内容

#### 3.12 课程评价
- 显示所有课程评价内容

---

## 🎫 会员卡种类清单

### 次数卡类型
- 企业课次卡（60mins）
- Yogaga月缴（普拉提小班10次/5次）
- Yogaga月缴（瑜伽团课10次/5次）
- Yogaga瑜伽团课卡
- Yogaga小班课卡
- Yogaga普拉提器械卡
- Yogaga团课通卡
- Yogaga特惠团课卡
- Yogaga舞蹈卡

### 期限卡类型
- Yogaga瑜伽&普拉提期限卡（太阳宫店/长寿路店/书城店/全市通）
- Yogaga瑜伽期限卡（太阳宫店/长寿路店/书城店/全市通）

### 私教卡类型
- Yogaga标准私教卡（1V1/1V2）
- Yogaga明星私教卡（1V1/1V2）
- Yogaga私教通卡（1V1）

### 储值卡类型
- Yogaga储值卡
- Yogaga共享储值卡

### 特殊卡类型
- Yogaga体验卡（员工代约/太阳宫预售）
- Yogaga老师卡
- Yogaga瑜伽垫存放卡
- Yogaga场地租赁卡

---

## 🔧 技术实现要点

### 核心功能优先级
1. **用户认证系统**：微信登录、用户信息管理
2. **课程预约系统**：课程展示、预约、取消
3. **会员卡系统**：卡种管理、扣费规则
4. **门店管理系统**：多门店支持
5. **数据统计系统**：报表和分析

### 关键技术挑战
1. **多门店权限控制**
2. **复杂的会员卡扣费规则**
3. **实时课程预约和排队机制**
4. **微信消息推送集成**
5. **地理位置验证（签到功能）**

### 数据库设计重点
1. **用户表**：支持多手机号绑定
2. **门店表**：多门店架构
3. **课程表**：复杂的排课规则
4. **会员卡表**：多种卡类型和扣费规则
5. **预约表**：预约状态和历史记录

这个需求文档展现了一个功能完整的瑜伽健身管理系统，涵盖了用户端体验和管理端运营的各个方面。建议按模块逐步开发，优先实现核心的用户认证和课程预约功能。
