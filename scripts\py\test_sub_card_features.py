#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
副卡功能完整测试脚本
测试主副卡功能的所有核心特性
"""

import requests
import json
import time
from datetime import datetime, timedelta

class SubCardTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        self.main_user_id = None
        self.sub_user_id = None
        self.main_card_id = None
        self.sub_card_id = None

    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")

        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}

        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False

    def create_test_users(self) -> bool:
        """创建测试用户（主卡用户和副卡用户）"""
        print("\n👥 创建测试用户...")

        timestamp = int(time.time())

        # 创建主卡用户
        main_user_data = {
            "username": f"main_user_{timestamp}",
            "password": "123456",
            "email": f"main_user_{timestamp}@example.com",
            "phone": f"138{timestamp % 100000000:08d}",
            "role_ids": []
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/users", json=main_user_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.main_user_id = result.get('data', {}).get('id')
                    print(f"✅ 主卡用户创建成功，ID: {self.main_user_id}")
                else:
                    print(f"❌ 主卡用户创建失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 主卡用户创建失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 主卡用户创建异常: {e}")
            return False

        # 创建副卡用户
        sub_user_data = {
            "username": f"sub_user_{timestamp}",
            "password": "123456",
            "email": f"sub_user_{timestamp}@example.com",
            "phone": f"139{timestamp % 100000000:08d}",
            "role_ids": []
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/users", json=sub_user_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.sub_user_id = result.get('data', {}).get('id')
                    print(f"✅ 副卡用户创建成功，ID: {self.sub_user_id}")
                    return True
                else:
                    print(f"❌ 副卡用户创建失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 副卡用户创建失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 副卡用户创建异常: {e}")
            return False

    def get_card_types(self) -> list:
        """获取支持副卡的卡种"""
        print("\n🎴 获取支持副卡的卡种...")

        try:
            # 添加分页参数
            params = {
                "page": 1,
                "page_size": 20,
                "status": 1  # 只获取启用的卡种
            }
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_types = result.get('data', {}).get('list', [])
                    # 查找支持副卡的卡种（通常是储值卡）
                    sub_card_types = [ct for ct in card_types if ct.get('can_add_sub_card', False)]
                    if sub_card_types:
                        print(f"✅ 找到 {len(sub_card_types)} 个支持副卡的卡种")
                        return sub_card_types
                    else:
                        print("⚠️ 没有找到支持副卡的卡种，使用储值卡")
                        # 查找储值卡
                        balance_cards = [ct for ct in card_types if ct.get('category') == 'balance']
                        return balance_cards
                else:
                    print(f"❌ 获取卡种失败: {result.get('msg')}")
                    return []
            else:
                print(f"❌ 获取卡种失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return []
        except Exception as e:
            print(f"❌ 获取卡种异常: {e}")
            return []

    def create_main_card(self, card_type_id: int) -> bool:
        """创建主卡（储值卡）"""
        print("\n💳 创建主卡...")

        card_data = {
            "user_id": self.main_user_id,
            "card_type_id": card_type_id,
            "purchase_price": 100000,  # 1000元
            "payment_method": "cash",
            "start_date": datetime.now().isoformat() + "Z",  # ISO格式
            "discount": 0.9,  # 0-1之间的值
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "主卡测试"
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=card_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_info = result.get('data', {})
                    self.main_card_id = card_info.get('id')
                    card_number = card_info.get('card_number')
                    print(f"✅ 主卡创建成功，ID: {self.main_card_id}, 卡号: {card_number}")
                    return True
                else:
                    print(f"❌ 主卡创建失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 主卡创建失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 主卡创建异常: {e}")
            return False

    def create_sub_card(self) -> bool:
        """创建副卡"""
        print("\n🎫 创建副卡...")

        sub_card_data = {
            "main_card_id": self.main_card_id,
            "user_id": self.sub_user_id,
            "remark": "副卡测试"
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards/{self.main_card_id}/sub-cards", json=sub_card_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    sub_card_info = result.get('data', {})
                    self.sub_card_id = sub_card_info.get('id')
                    card_number = sub_card_info.get('card_number')
                    print(f"✅ 副卡创建成功，ID: {self.sub_card_id}, 卡号: {card_number}")
                    return True
                else:
                    print(f"❌ 副卡创建失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 副卡创建失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 副卡创建异常: {e}")
            return False

    def get_sub_cards(self) -> bool:
        """获取副卡列表"""
        print("\n📋 获取副卡列表...")

        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{self.main_card_id}/sub-cards")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    sub_cards = result.get('data', {}).get('list', [])
                    print(f"✅ 获取到 {len(sub_cards)} 张副卡")
                    for card in sub_cards:
                        print(f"   副卡ID: {card.get('id')}, 卡号: {card.get('card_number')}, 用户: {card.get('user', {}).get('nick_name')}")
                    return True
                else:
                    print(f"❌ 获取副卡列表失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取副卡列表失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取副卡列表异常: {e}")
            return False

    def get_card_detail(self, card_id: int) -> bool:
        """获取卡片详情（包含副卡信息）"""
        print(f"\n🔍 获取卡片详情 (ID: {card_id})...")

        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_info = result.get('data', {})
                    print(f"✅ 卡片详情获取成功")
                    print(f"   卡号: {card_info.get('card_number')}")
                    print(f"   是否主卡: {card_info.get('is_main_card')}")
                    print(f"   剩余金额: {card_info.get('remaining_amount', 0) / 100}元")

                    sub_cards = card_info.get('sub_cards', [])
                    if sub_cards:
                        print(f"   副卡数量: {len(sub_cards)}")
                        for sub_card in sub_cards:
                            print(f"     副卡: {sub_card.get('card_number')} (用户: {sub_card.get('user', {}).get('nick_name')})")

                    return True
                else:
                    print(f"❌ 获取卡片详情失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取卡片详情失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取卡片详情异常: {e}")
            return False

    def test_sub_card_recharge(self) -> bool:
        """测试主卡充值（副卡应该共享余额）"""
        print("\n💰 测试主卡充值...")

        recharge_data = {
            "recharge_times": 0,
            "recharge_amount": 50000,  # 500元
            "payment_method": "offline",  # 必须是 offline 或 card
            "actual_amount": 50000,  # 实际支付金额
            "extend_expiry": False,
            "remark": "主卡充值测试"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.main_card_id}/recharge", json=recharge_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 主卡充值成功")
                    return True
                else:
                    print(f"❌ 主卡充值失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 主卡充值失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 主卡充值异常: {e}")
            return False

    def test_sub_card_deduction(self) -> bool:
        """测试副卡扣费（应该从主卡余额扣除）"""
        print("\n💸 测试副卡扣费...")

        deduction_data = {
            "amount": 10000,  # 100元
            "times": 0,
            "reason": "副卡扣费测试",
            "remark": "测试副卡扣费功能"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.sub_card_id}/deduct", json=deduction_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 副卡扣费成功")
                    return True
                else:
                    print(f"❌ 副卡扣费失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 副卡扣费失败: {response.status_code}")
                try:
                    error_detail = response.json()
                    print(f"   错误详情: {error_detail}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
        except Exception as e:
            print(f"❌ 副卡扣费异常: {e}")
            return False

    def test_card_transactions(self, card_id: int, card_type: str) -> bool:
        """测试卡片交易记录"""
        print(f"\n📊 测试{card_type}交易记录...")

        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    transactions = result.get('data', {}).get('list', [])
                    print(f"✅ {card_type}交易记录获取成功，共 {len(transactions)} 条记录")
                    for trans in transactions:
                        print(f"   {trans.get('transaction_type')} - {trans.get('amount_change', 0) / 100}元 - {trans.get('reason')}")
                    return True
                else:
                    print(f"❌ {card_type}交易记录获取失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ {card_type}交易记录获取失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ {card_type}交易记录获取异常: {e}")
            return False

    def run_complete_test(self) -> bool:
        """运行完整的副卡功能测试"""
        print("🚀 开始副卡功能完整测试...")
        print("=" * 80)

        # 1. 登录
        if not self.login():
            return False

        # 2. 创建测试用户
        if not self.create_test_users():
            return False

        # 3. 获取支持副卡的卡种
        card_types = self.get_card_types()
        if not card_types:
            return False

        # 选择第一个支持副卡的卡种
        card_type = card_types[0]
        print(f"📋 使用卡种: {card_type.get('name')} (ID: {card_type.get('id')})")

        # 4. 创建主卡
        if not self.create_main_card(card_type.get('id')):
            return False

        # 5. 创建副卡
        if not self.create_sub_card():
            return False

        # 6. 获取副卡列表
        if not self.get_sub_cards():
            return False

        # 7. 获取主卡详情（包含副卡信息）
        if not self.get_card_detail(self.main_card_id):
            return False

        # 8. 获取副卡详情
        if not self.get_card_detail(self.sub_card_id):
            return False

        # 9. 测试主卡充值
        if not self.test_sub_card_recharge():
            return False

        # 10. 再次查看主卡详情（验证充值后余额）
        print("\n🔄 充值后查看主卡详情...")
        if not self.get_card_detail(self.main_card_id):
            return False

        # 11. 测试副卡扣费
        if not self.test_sub_card_deduction():
            return False

        # 12. 再次查看主卡和副卡详情（验证扣费后余额）
        print("\n🔄 扣费后查看主卡详情...")
        if not self.get_card_detail(self.main_card_id):
            return False

        print("\n🔄 扣费后查看副卡详情...")
        if not self.get_card_detail(self.sub_card_id):
            return False

        # 13. 查看交易记录
        if not self.test_card_transactions(self.main_card_id, "主卡"):
            return False

        if not self.test_card_transactions(self.sub_card_id, "副卡"):
            return False

        print("\n" + "=" * 80)
        print("🎉 副卡功能测试全部通过！")
        print("✅ 主副卡创建正常")
        print("✅ 副卡列表获取正常")
        print("✅ 卡片详情查询正常")
        print("✅ 主卡充值功能正常")
        print("✅ 副卡扣费功能正常")
        print("✅ 余额共享机制正常")
        print("✅ 交易记录功能正常")

        return True

def main():
    """主函数"""
    tester = SubCardTester()
    success = tester.run_complete_test()

    if success:
        print("\n🏆 副卡功能测试成功完成！")
        exit(0)
    else:
        print("\n💥 副卡功能测试失败！")
        exit(1)

if __name__ == "__main__":
    main()
