#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证 PostgreSQL 数组字段修复的测试脚本
专门测试 image_ids 和 coach_ids 字段的更新功能
"""

import requests
import json
import uuid
import sys

class FixVerificationTester:
    def __init__(self, base_url="http://localhost:9095"):
        self.base_url = base_url
        self.session = requests.Session()
        self.access_token = None
    
    def check_server(self):
        """检查服务器是否运行"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=3)
            return True
        except:
            return False
    
    def login(self):
        """登录获取令牌"""
        try:
            response = self.session.post(f"{self.base_url}/api/v1/auth/login", json={
                "username": "admin",
                "password": "admin123"
            }, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == 200:
                    self.access_token = data["data"]["access_token"]
                    self.session.headers.update({
                        "Authorization": f"Bearer {self.access_token}",
                        "Content-Type": "application/json"
                    })
                    return True
            return False
        except:
            return False
    
    def test_store_image_ids_update(self):
        """测试门店 image_ids 字段更新"""
        print("🏪 测试门店 image_ids 字段更新...")
        
        # 1. 创建测试门店
        test_image_ids = [str(uuid.uuid4()), str(uuid.uuid4())]
        create_data = {
            "name": "测试门店-修复验证",
            "address": "测试地址",
            "phone": "13800138000",
            "latitude": 39.9042,
            "longitude": 116.4074,
            "image_ids": test_image_ids,
            "description": "用于验证修复的测试门店",
            "sort_order": 999
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/stores", json=create_data)
            if response.status_code != 200:
                print(f"   ❌ 创建门店失败: {response.text}")
                return False
            
            result = response.json()
            if result.get("code") != 200:
                print(f"   ❌ 创建门店失败: {result}")
                return False
            
            store_id = result["data"]["id"]
            print(f"   ✅ 创建门店成功，ID: {store_id}")
            
            # 2. 更新门店 image_ids（关键测试）
            new_image_ids = [str(uuid.uuid4()), str(uuid.uuid4()), str(uuid.uuid4())]
            update_data = {
                "name": "测试门店-已更新",
                "image_ids": new_image_ids,  # 这是关键测试字段
                "description": "更新后的描述"
            }
            
            print(f"   🔄 更新 image_ids: {new_image_ids}")
            response = self.session.put(f"{self.base_url}/api/v1/admin/stores/{store_id}", json=update_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print("   ✅ 门店更新成功")
                    
                    # 验证更新结果
                    detail_response = self.session.get(f"{self.base_url}/api/v1/admin/stores/{store_id}")
                    if detail_response.status_code == 200:
                        detail_result = detail_response.json()
                        if detail_result.get("code") == 200:
                            actual_image_ids = detail_result["data"].get("image_ids", [])
                            if actual_image_ids == new_image_ids:
                                print("   ✅ image_ids 字段验证成功")
                                success = True
                            else:
                                print(f"   ❌ image_ids 字段验证失败")
                                print(f"      期望: {new_image_ids}")
                                print(f"      实际: {actual_image_ids}")
                                success = False
                        else:
                            success = False
                    else:
                        success = False
                else:
                    print(f"   ❌ 更新失败: {result}")
                    success = False
            else:
                print(f"   ❌ 更新请求失败: {response.status_code} - {response.text}")
                success = False
            
            # 3. 清理测试数据
            self.session.delete(f"{self.base_url}/api/v1/admin/stores/{store_id}")
            return success
            
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            return False
    
    def test_course_coach_ids_update(self):
        """测试课程 coach_ids 字段更新"""
        print("📚 测试课程 coach_ids 字段更新...")
        
        try:
            # 1. 获取现有课程
            response = self.session.get(f"{self.base_url}/api/v1/admin/courses?page=1&page_size=1")
            if response.status_code != 200:
                print("   ⚠️ 无法获取课程列表，跳过课程测试")
                return True
            
            result = response.json()
            if result.get("code") != 200 or not result.get("data", {}).get("list"):
                print("   ⚠️ 没有找到课程，跳过课程测试")
                return True
            
            course_id = result["data"]["list"][0]["id"]
            print(f"   📖 使用课程 ID: {course_id}")
            
            # 2. 更新课程 coach_ids（关键测试）
            new_coach_ids = [str(uuid.uuid4()), str(uuid.uuid4())]
            update_data = {
                "name": "测试课程-修复验证",
                "coach_ids": new_coach_ids,  # 这是关键测试字段
                "description": "用于验证修复的课程"
            }
            
            print(f"   🔄 更新 coach_ids: {new_coach_ids}")
            response = self.session.put(f"{self.base_url}/api/v1/admin/courses/{course_id}", json=update_data)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 200:
                    print("   ✅ 课程更新成功")
                    return True
                else:
                    print(f"   ❌ 课程更新失败: {result}")
                    return False
            else:
                print(f"   ❌ 课程更新请求失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"   ❌ 课程测试异常: {e}")
            return False
    
    def run_verification(self):
        """运行完整验证"""
        print("🔍 PostgreSQL 数组字段修复验证")
        print("=" * 50)
        
        # 1. 检查服务器
        if not self.check_server():
            print("❌ 服务器未运行，请先启动服务器：")
            print("   go run main.go")
            return False
        print("✅ 服务器连接正常")
        
        # 2. 登录
        if not self.login():
            print("❌ 登录失败，请检查用户名密码")
            return False
        print("✅ 登录成功")
        
        # 3. 测试门店 image_ids 更新
        store_test_success = self.test_store_image_ids_update()
        
        # 4. 测试课程 coach_ids 更新
        course_test_success = self.test_course_coach_ids_update()
        
        # 5. 总结结果
        print("\n" + "=" * 50)
        print("📊 验证结果:")
        print(f"   门店 image_ids 更新: {'✅ 通过' if store_test_success else '❌ 失败'}")
        print(f"   课程 coach_ids 更新: {'✅ 通过' if course_test_success else '❌ 失败'}")
        
        overall_success = store_test_success and course_test_success
        
        if overall_success:
            print("\n🎉 所有测试通过！PostgreSQL 数组字段修复成功")
        else:
            print("\n❌ 部分测试失败！请检查修复代码")
        
        return overall_success

def main():
    """主函数"""
    tester = FixVerificationTester()
    success = tester.run_verification()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
