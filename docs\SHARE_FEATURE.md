# 📤 分享功能实现文档

## 🎯 **功能概述**

分享功能允许用户将课程、教练、门店信息分享给其他用户，支持生成分享链接、分享文案，并记录分享统计数据。

## 🔧 **技术实现**

### **核心接口**

| 接口 | 方法 | 功能描述 |
|------|------|----------|
| `/api/v1/app/share/courses/:id` | POST | 分享课程 |
| `/api/v1/app/share/coaches/:id` | POST | 分享教练 |
| `/api/v1/app/share/stores/:id` | POST | 分享门店 |
| `/api/v1/app/share/stats` | GET | 获取分享统计 |

### **数据模型**

#### ShareRecord 分享记录
```go
type ShareRecord struct {
    BaseModel
    UserID    uint   // 分享用户ID
    ShareType string // 分享类型：course, coach, store
    TargetID  uint   // 分享目标ID
}
```

#### ShareResponse 分享响应
```go
type ShareResponse struct {
    Type        string // 分享类型
    Title       string // 分享标题
    Description string // 分享描述
    Image       string // 分享图片
    URL         string // 小程序页面路径
    ShareText   string // 分享文案
}
```

## 📱 **使用示例**

### 1. **分享课程**
```bash
POST /api/v1/app/share/courses/123
Authorization: Bearer <token>

Response:
{
  "type": "course",
  "title": "瑜伽基础课程",
  "description": "适合初学者的瑜伽课程",
  "image": "/images/default-course.jpg",
  "url": "/pages/course/detail?id=123",
  "share_text": "推荐一个超棒的瑜伽课程：瑜伽基础课程，教练：张老师，快来一起练习吧！"
}
```

### 2. **分享教练**
```bash
POST /api/v1/app/share/coaches/456
Authorization: Bearer <token>

Response:
{
  "type": "coach",
  "title": "张老师",
  "description": "资深瑜伽教练，10年教学经验",
  "image": "https://avatar.url",
  "url": "/pages/coach/detail?id=456",
  "share_text": "推荐一位专业的瑜伽教练：张老师，经验丰富，快来体验吧！"
}
```

### 3. **分享门店**
```bash
POST /api/v1/app/share/stores/789
Authorization: Bearer <token>

Response:
{
  "type": "store",
  "title": "Yogaga瑜伽馆(朝阳店)",
  "description": "环境优雅的专业瑜伽馆",
  "image": "/images/default-store.jpg",
  "url": "/pages/store/detail?id=789",
  "share_text": "推荐一家优质的瑜伽馆：Yogaga瑜伽馆(朝阳店)，地址：朝阳区xxx，环境很棒哦！"
}
```

### 4. **获取分享统计**
```bash
GET /api/v1/app/share/stats?type=course&target_id=123
Authorization: Bearer <token>

Response:
{
  "total_shares": 156,
  "today_shares": 12
}
```

## 🌟 **功能特色**

### 1. **智能文案生成**
- 根据分享类型自动生成吸引人的分享文案
- 包含关键信息：名称、教练、地址等
- 支持自定义文案模板

### 2. **小程序页面路径**
- 生成标准的小程序页面路径
- 支持参数传递
- 便于小程序内部跳转

### 3. **分享统计**
- 记录每次分享行为
- 统计总分享次数和今日分享次数
- 支持按类型和目标ID查询

### 4. **数据完整性**
- 验证分享目标是否存在
- 记录分享用户信息
- 支持分享行为追踪

## 🔄 **与现有功能的集成**

### **收藏 + 分享**
用户可以先收藏喜欢的内容，然后分享给朋友：
1. 收藏课程/教练/门店
2. 在收藏列表中分享
3. 生成分享链接和文案

### **详情页分享**
在课程、教练、门店详情页都可以直接分享：
- 课程详情页 → 分享课程
- 教练详情页 → 分享教练  
- 门店详情页 → 分享门店

## 📊 **数据流程**

```mermaid
graph TD
    A[用户点击分享] --> B[验证目标存在]
    B --> C[生成分享信息]
    C --> D[记录分享行为]
    D --> E[返回分享数据]
    E --> F[前端展示分享界面]
```

## 🎉 **总结**

分享功能的实现完善了瑜伽馆小程序的社交属性，用户可以：

- ✅ **轻松分享**：一键生成分享内容
- ✅ **智能文案**：自动生成吸引人的分享文案  
- ✅ **数据统计**：了解内容的受欢迎程度
- ✅ **无缝集成**：与收藏、详情页完美结合

这为瑜伽馆的口碑传播和用户增长提供了有力支持！🚀
