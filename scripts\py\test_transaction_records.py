#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试会员卡交易记录功能
"""

import requests
import json
from datetime import datetime, timedelta

class TransactionRecordTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        self.test_card_id = None

    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")

        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}

        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False

    def get_existing_user(self) -> str:
        """获取一个存在的用户ID"""
        try:
            params = {"page": 1, "page_size": 1}
            response = self.session.get(f"{self.base_url}/api/v1/admin/users", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    users = result.get('data', {}).get('list', [])
                    if users:
                        return users[0]['id']
            return None
        except Exception as e:
            print(f"❌ 获取用户异常: {e}")
            return None

    def create_test_card(self) -> bool:
        """创建测试会员卡"""
        print("\n💳 创建测试会员卡...")

        # 先获取一个存在的用户
        user_id = self.get_existing_user()
        if not user_id:
            print("❌ 没有找到可用的用户")
            return False

        # 先获取可用的卡种
        try:
            params = {"page": 1, "page_size": 10}
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_types = result.get('data', {}).get('list', [])
                    if not card_types:
                        print("❌ 没有可用的卡种")
                        return False
                    card_type_id = card_types[0]['id']
                else:
                    print(f"❌ 获取卡种失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取卡种失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取卡种异常: {e}")
            return False

        # 创建会员卡
        card_data = {
            "user_id": user_id,
            "card_type_id": card_type_id,
            "purchase_price": 100000,  # 1000元
            "payment_method": "cash",
            "start_date": datetime.now().isoformat() + "Z",
            "discount": 1.0,
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "交易记录测试卡"
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=card_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_info = result.get('data', {})
                    self.test_card_id = card_info.get('id')
                    print(f"✅ 测试卡创建成功，ID: {self.test_card_id}")
                    return True
                else:
                    print(f"❌ 创建测试卡失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 创建测试卡失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 创建测试卡异常: {e}")
            return False

    def test_recharge_transaction(self) -> bool:
        """测试充值交易记录"""
        print("\n🧪 测试充值交易记录...")

        recharge_data = {
            "recharge_amount": 0,      # 次数卡不充值金额
            "recharge_times": 5,       # 充值5次
            "payment_method": "offline",  # 线下支付
            "actual_amount": 50000,    # 实际支付金额（500元）
            "extend_expiry": False,
            "remark": "测试充值"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/recharge", json=recharge_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 充值成功")
                    return True
                else:
                    print(f"❌ 充值失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 充值失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 充值异常: {e}")
            return False

    def test_deduct_transaction(self) -> bool:
        """测试扣费交易记录"""
        print("\n🧪 测试扣费交易记录...")

        deduct_data = {
            "amount": 0,      # 次数卡不扣费金额
            "times": 2,       # 扣费2次
            "reason": "测试扣费"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/deduct", json=deduct_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 扣费成功")
                    return True
                else:
                    print(f"❌ 扣费失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 扣费失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 扣费异常: {e}")
            return False

    def test_freeze_unfreeze_transaction(self) -> bool:
        """测试冻结/解冻交易记录"""
        print("\n🧪 测试冻结/解冻交易记录...")

        # 冻结
        freeze_data = {
            "action": "freeze",
            "reason": "测试冻结"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/freeze", json=freeze_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 冻结成功")
                else:
                    print(f"❌ 冻结失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 冻结失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 冻结异常: {e}")
            return False

        # 解冻
        unfreeze_data = {
            "action": "unfreeze",
            "reason": "测试解冻"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/freeze", json=unfreeze_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 解冻成功")
                    return True
                else:
                    print(f"❌ 解冻失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 解冻失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 解冻异常: {e}")
            return False

    def test_extend_transaction(self) -> bool:
        """测试延期交易记录"""
        print("\n🧪 测试延期交易记录...")

        extend_data = {
            "extend_days": 30,
            "reason": "测试延期原因"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/extend", json=extend_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 延期成功")
                    return True
                else:
                    error_msg = result.get('msg', '')
                    if "次数卡不支持延期功能" in error_msg:
                        print("⚠️ 次数卡不支持延期功能，跳过延期测试")
                        return True  # 这是正常的业务逻辑，不算失败
                    else:
                        print(f"❌ 延期失败: {error_msg}")
                        return False
            else:
                # 检查400错误的响应内容
                try:
                    result = response.json()
                    error_msg = result.get('msg', '')
                    if "次数卡不支持延期功能" in error_msg:
                        print("⚠️ 次数卡不支持延期功能，跳过延期测试")
                        return True  # 这是正常的业务逻辑，不算失败
                except:
                    pass
                print(f"❌ 延期失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 延期异常: {e}")
            return False

    def check_transaction_records(self) -> bool:
        """检查交易记录"""
        print("\n📋 检查交易记录...")

        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{self.test_card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    transactions = result.get('data', {}).get('list', [])
                    print(f"✅ 找到 {len(transactions)} 条交易记录")

                    # 统计交易类型
                    transaction_types = {}
                    for transaction in transactions:
                        trans_type = transaction.get('transaction_type')
                        transaction_types[trans_type] = transaction_types.get(trans_type, 0) + 1

                    print("\n📊 交易类型统计:")
                    for trans_type, count in transaction_types.items():
                        print(f"  {trans_type}: {count} 条")

                    # 显示最近的交易记录
                    print("\n📝 最近的交易记录:")
                    for transaction in transactions[:5]:  # 显示前5条
                        print(f"  - {transaction.get('transaction_type')}: {transaction.get('reason')} ({transaction.get('created_at')})")

                    return True
                else:
                    print(f"❌ 获取交易记录失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取交易记录失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取交易记录异常: {e}")
            return False

    def run_test(self):
        """运行完整测试"""
        print("🚀 开始测试会员卡交易记录功能...")
        print("=" * 80)

        # 1. 登录
        if not self.login():
            return False

        # 2. 创建测试卡
        if not self.create_test_card():
            return False

        # 3. 测试各种操作的交易记录
        tests = [
            ("充值", self.test_recharge_transaction),
            ("扣费", self.test_deduct_transaction),
            ("冻结/解冻", self.test_freeze_unfreeze_transaction),
            ("延期", self.test_extend_transaction),
        ]

        for test_name, test_func in tests:
            if not test_func():
                print(f"❌ {test_name}测试失败")
                return False

        # 4. 检查交易记录
        if not self.check_transaction_records():
            return False

        print("\n" + "=" * 80)
        print("✅ 会员卡交易记录功能测试完成！")
        print("\n💡 测试结果:")
        print("1. 开卡操作 - 应该有交易记录")
        print("2. 充值操作 - 应该有交易记录")
        print("3. 扣费操作 - 应该有交易记录")
        print("4. 冻结操作 - 应该有交易记录")
        print("5. 解冻操作 - 应该有交易记录")
        print("6. 延期操作 - 应该有交易记录")

        return True

def main():
    """主函数"""
    tester = TransactionRecordTester()
    success = tester.run_test()

    if success:
        print("\n🏆 交易记录功能测试成功！")
        exit(0)
    else:
        print("\n💥 交易记录功能测试失败！")
        exit(1)

if __name__ == "__main__":
    main()
