package main

import (
	"fmt"
	"log"
	"yogaga/internal/database"
	"yogaga/internal/model"

	"github.com/go-delve/delve/pkg/config"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔧 开始修复副卡数据一致性...")

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.InitDB(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 修复副卡数据
	if err := fixSubCardData(db); err != nil {
		log.Fatalf("修复副卡数据失败: %v", err)
	}

	fmt.Println("✅ 副卡数据修复完成！")
}

func fixSubCardData(db *gorm.DB) error {
	fmt.Println("\n📋 检查当前副卡数据...")

	// 1. 查找所有副卡（有 main_card_id 的卡片）
	var subCards []model.MembershipCard
	err := db.Where("main_card_id IS NOT NULL").Find(&subCards).Error
	if err != nil {
		return fmt.Errorf("查询副卡失败: %v", err)
	}

	fmt.Printf("找到 %d 张副卡\n", len(subCards))

	if len(subCards) == 0 {
		fmt.Println("没有找到副卡，无需修复")
		return nil
	}

	// 2. 检查和修复每张副卡
	var fixedCount int
	for _, card := range subCards {
		fmt.Printf("\n🔍 检查副卡 ID:%d, 卡号:%s\n", card.ID, card.CardNumber)

		needsUpdate := false
		updates := make(map[string]interface{})

		// 检查 IsMainCard 字段
		if card.IsMainCard {
			fmt.Printf("  ⚠️ IsMainCard 应该为 false，当前为 true\n")
			updates["is_main_card"] = false
			needsUpdate = true
		}

		// 检查 RemainingAmount 字段
		if card.RemainingAmount > 0 {
			fmt.Printf("  ⚠️ RemainingAmount 应该为 0，当前为 %d\n", card.RemainingAmount)
			updates["remaining_amount"] = 0
			needsUpdate = true
		}

		// 检查 RemainingTimes 字段
		if card.RemainingTimes > 0 {
			fmt.Printf("  ⚠️ RemainingTimes 应该为 0，当前为 %d\n", card.RemainingTimes)
			updates["remaining_times"] = 0
			needsUpdate = true
		}

		// 检查 TotalAmount 字段
		if card.TotalAmount > 0 {
			fmt.Printf("  ⚠️ TotalAmount 应该为 0，当前为 %d\n", card.TotalAmount)
			updates["total_amount"] = 0
			needsUpdate = true
		}

		// 检查 TotalTimes 字段
		if card.TotalTimes > 0 {
			fmt.Printf("  ⚠️ TotalTimes 应该为 0，当前为 %d\n", card.TotalTimes)
			updates["total_times"] = 0
			needsUpdate = true
		}

		// 检查 PurchasePrice 字段
		if card.PurchasePrice > 0 {
			fmt.Printf("  ⚠️ PurchasePrice 应该为 0，当前为 %d\n", card.PurchasePrice)
			updates["purchase_price"] = 0
			needsUpdate = true
		}

		// 如果需要更新，执行更新
		if needsUpdate {
			err := db.Model(&card).Updates(updates).Error
			if err != nil {
				fmt.Printf("  ❌ 更新副卡 %d 失败: %v\n", card.ID, err)
				continue
			}
			fmt.Printf("  ✅ 副卡 %d 修复成功\n", card.ID)
			fixedCount++
		} else {
			fmt.Printf("  ✅ 副卡 %d 数据正常\n", card.ID)
		}
	}

	fmt.Printf("\n📊 修复统计:\n")
	fmt.Printf("  总副卡数量: %d\n", len(subCards))
	fmt.Printf("  修复数量: %d\n", fixedCount)
	fmt.Printf("  正常数量: %d\n", len(subCards)-fixedCount)

	// 3. 验证修复结果
	fmt.Println("\n🔍 验证修复结果...")
	return verifySubCardData(db)
}

func verifySubCardData(db *gorm.DB) error {
	// 查找所有副卡
	var subCards []model.MembershipCard
	err := db.Where("main_card_id IS NOT NULL").Find(&subCards).Error
	if err != nil {
		return fmt.Errorf("验证查询副卡失败: %v", err)
	}

	var problemCount int
	for _, card := range subCards {
		hasProblems := false

		if card.IsMainCard {
			fmt.Printf("❌ 副卡 %d IsMainCard 仍为 true\n", card.ID)
			hasProblems = true
		}

		if card.RemainingAmount > 0 {
			fmt.Printf("❌ 副卡 %d RemainingAmount 仍为 %d\n", card.ID, card.RemainingAmount)
			hasProblems = true
		}

		if card.RemainingTimes > 0 {
			fmt.Printf("❌ 副卡 %d RemainingTimes 仍为 %d\n", card.ID, card.RemainingTimes)
			hasProblems = true
		}

		if card.TotalAmount > 0 {
			fmt.Printf("❌ 副卡 %d TotalAmount 仍为 %d\n", card.ID, card.TotalAmount)
			hasProblems = true
		}

		if card.TotalTimes > 0 {
			fmt.Printf("❌ 副卡 %d TotalTimes 仍为 %d\n", card.ID, card.TotalTimes)
			hasProblems = true
		}

		if card.PurchasePrice > 0 {
			fmt.Printf("❌ 副卡 %d PurchasePrice 仍为 %d\n", card.ID, card.PurchasePrice)
			hasProblems = true
		}

		if hasProblems {
			problemCount++
		}
	}

	if problemCount == 0 {
		fmt.Println("✅ 所有副卡数据验证通过！")
	} else {
		fmt.Printf("❌ 仍有 %d 张副卡存在问题\n", problemCount)
	}

	return nil
}
