# 前端静态内容指南

## 📱 界面功能分析总结

根据提供的界面截图，以下是对各个功能模块的分析和建议：

## ✅ **可以前端写死的功能**

### 1. **联系我们页面** 📞
**位置**: 右下角界面
**内容**: 
- 客服电话: `************`
- 邮箱地址: `<EMAIL>`
- 公司地址: `北京市朝阳区瑜伽大厦8层`
- 营业时间: `周一至周日 9:00-21:00`
- 微信二维码: 静态图片资源

**实现方式**: 
```javascript
const contactInfo = {
  phone: "************",
  email: "<EMAIL>", 
  address: "北京市朝阳区瑜伽大厦8层",
  businessHours: "周一至周日 9:00-21:00",
  wechatQR: "/images/wechat-qr.png"
}
```

### 2. **隐私设置页面** 🔒
**位置**: 右上角界面
**内容**:
- 隐私政策文本
- 用户协议文本
- 设置选项 (推送通知、位置权限等)

**实现方式**:
```javascript
const privacySettings = {
  privacyPolicy: "隐私政策全文...",
  userAgreement: "用户协议全文...",
  settings: {
    pushNotification: true,
    locationPermission: false
  }
}
```

### 3. **关于我们页面** 📖
**内容**:
- 公司介绍
- 企业文化
- 发展历程
- 团队介绍

**实现方式**: 静态 HTML 页面或 Markdown 文件

### 4. **帮助中心** ❓
**内容**:
- 常见问题 FAQ
- 使用教程
- 操作指南

## 🔄 **需要接口支持的功能**

### 1. **同一位老师的页面** (教练详情)
**接口**: ✅ 已实现
- `GET /api/v1/app/coaches` - 教练列表
- `GET /api/v1/app/coaches/:id` - 教练详情

**数据结构**:
```json
{
  "id": 1,
  "nick_name": "FENG老师",
  "avatar_url": "/images/coach1.jpg",
  "gender": 2,
  "description": "资深瑜伽教练，10年教学经验",
  "experience": 10,
  "specialty": "哈他瑜伽、阴瑜伽",
  "country": "中国",
  "province": "北京市",
  "city": "朝阳区"
}
```

### 2. **同一种类的页面** (课程分类)
**接口**: ✅ 已实现
- `GET /api/v1/app/course-categories` - 课程分类树

### 3. **同一个店的页面** (门店详情)
**接口**: ✅ 已实现
- `GET /api/v1/app/stores` - 门店列表
- `GET /api/v1/app/stores/:id` - 门店详情

## 📋 **实现建议**

### 前端静态内容管理
1. **配置文件方式**:
   ```javascript
   // config/staticContent.js
   export const STATIC_CONTENT = {
     contact: { /* 联系信息 */ },
     privacy: { /* 隐私政策 */ },
     about: { /* 关于我们 */ }
   }
   ```

2. **本地存储方式**:
   - 使用 localStorage 存储用户设置
   - 使用 sessionStorage 存储临时数据

3. **资源文件方式**:
   - 将长文本内容放在 `/assets/content/` 目录
   - 使用 Markdown 格式便于维护

### 动态内容接口调用
1. **教练相关**:
   ```javascript
   // 获取教练列表
   const coaches = await api.get('/coaches')
   
   // 获取教练详情
   const coachDetail = await api.get(`/coaches/${id}`)
   ```

2. **课程分类**:
   ```javascript
   // 获取课程分类
   const categories = await api.get('/course-categories')
   ```

3. **门店信息**:
   ```javascript
   // 获取门店列表
   const stores = await api.get('/stores')
   
   // 获取门店详情
   const storeDetail = await api.get(`/stores/${id}`)
   ```

## 🎯 **总结**

- **静态内容** (联系我们、隐私设置、关于我们): 前端写死，提高加载速度
- **动态内容** (教练、课程、门店): 使用接口，保证数据实时性
- **用户设置**: 本地存储，提升用户体验
- **长文本内容**: 使用配置文件或资源文件管理

这样的设计既保证了性能，又保持了灵活性！ 🚀
