// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblUser = "tbl_users"

// TblUser mapped from table <tbl_users>
type TblUser struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Openid     string    `gorm:"column:openid" json:"openid"`
	Unionid    string    `gorm:"column:unionid" json:"unionid"`
	Headimgurl string    `gorm:"column:headimgurl" json:"headimgurl"`
	Nickname   string    `gorm:"column:nickname" json:"nickname"`
	Realname   string    `gorm:"column:realname" json:"realname"`
	Mobile     string    `gorm:"column:mobile" json:"mobile"`
	Regtime    time.Time `gorm:"column:regtime;not null;default:CURRENT_TIMESTAMP" json:"regtime"`
	Province   string    `gorm:"column:province" json:"province"`
	City       string    `gorm:"column:city" json:"city"`
	RealID     string    `gorm:"column:realID" json:"realID"`
	IDcard     string    `gorm:"column:IDcard" json:"IDcard"`
	Account    string    `gorm:"column:Account" json:"Account"`
	Address    string    `gorm:"column:address" json:"address"`
	IsVip      bool      `gorm:"column:is_vip;not null" json:"is_vip"`
	IsForm2    bool      `gorm:"column:is_form2;not null" json:"is_form2"`
	IsOver     bool      `gorm:"column:is_over;not null" json:"is_over"`
	Res        string    `gorm:"column:res" json:"res"`
	SessionKey string    `gorm:"column:session_key" json:"session_key"`
	Points     int32     `gorm:"column:points;not null" json:"points"`
	PointsAll  int32     `gorm:"column:points_all;not null" json:"points_all"`
	Lianxudays int32     `gorm:"column:lianxudays;not null" json:"lianxudays"`
	Sex        string    `gorm:"column:sex" json:"sex"`
	Birth      string    `gorm:"column:birth" json:"birth"`
	Career     string    `gorm:"column:career" json:"career"`
	Country    string    `gorm:"column:country" json:"country"`
}

// TableName TblUser's table name
func (*TblUser) TableName() string {
	return TableNameTblUser
}
