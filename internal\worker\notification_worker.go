package worker

import (
	"context"
	"encoding/json"
	"yogaga/internal/service"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
)

// NotificationWorker 消息推送任务处理器
type NotificationWorker struct {
	notificationService *service.NotificationService
}

// NewNotificationWorker 创建消息推送任务处理器
func NewNotificationWorker(notificationService *service.NotificationService) *NotificationWorker {
	return &NotificationWorker{
		notificationService: notificationService,
	}
}

// RegisterTasks 注册任务处理器
func (w *NotificationWorker) RegisterTasks(mux *asynq.ServeMux) {
	// 注册开课提醒任务
	mux.HandleFunc("course:reminder", w.HandleCourseReminder)

	// 注册排队通知任务
	mux.HandleFunc("queue:notification", w.HandleQueueNotification)

	// 注册预约取消通知任务
	mux.HandleFunc("booking:cancel", w.HandleBookingCancelNotification)
}

// HandleCourseReminder 处理开课提醒任务
func (w *NotificationWorker) HandleCourseReminder(ctx context.Context, t *asynq.Task) error {
	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析开课提醒任务参数失败", "error", err)
		return err
	}

	log.Info("处理开课提醒任务", "payload", payload)

	// 调用通知服务处理开课提醒
	err := w.notificationService.ProcessCourseReminder(payload)
	if err != nil {
		log.Error("处理开课提醒失败", "error", err)
		return err
	}

	log.Info("开课提醒处理成功")
	return nil
}

// HandleQueueNotification 处理排队通知任务
func (w *NotificationWorker) HandleQueueNotification(ctx context.Context, t *asynq.Task) error {
	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析排队通知任务参数失败", "error", err)
		return err
	}

	log.Info("处理排队通知任务", "payload", payload)

	courseID, ok := payload["course_id"].(float64)
	if !ok {
		log.Error("无效的课程ID")
		return asynq.SkipRetry
	}

	// 调用通知服务处理排队转预约
	err := w.notificationService.ProcessQueueToBooking(uint(courseID))
	if err != nil {
		log.Error("处理排队通知失败", "error", err)
		return err
	}

	log.Info("排队通知处理成功")
	return nil
}

// HandleBookingCancelNotification 处理预约取消通知任务
func (w *NotificationWorker) HandleBookingCancelNotification(ctx context.Context, t *asynq.Task) error {
	var payload map[string]interface{}
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		log.Error("解析预约取消通知任务参数失败", "error", err)
		return err
	}

	log.Info("处理预约取消通知任务", "payload", payload)

	bookingID, ok := payload["booking_id"].(float64)
	if !ok {
		log.Error("无效的预约ID")
		return asynq.SkipRetry
	}

	reason, ok := payload["reason"].(string)
	if !ok {
		reason = "课程取消"
	}

	// 调用通知服务发送取消通知
	err := w.notificationService.SendBookingCancelNotification(uint(bookingID), reason)
	if err != nil {
		log.Error("发送预约取消通知失败", "error", err)
		return err
	}

	log.Info("预约取消通知发送成功")
	return nil
}
