package dto

import (
	"time"
)

const (
	DefaultPageSize = 20
	MaxPageSize     = 100
	MinPage         = 1
)

// User related DTOs
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// WeChat Mini Program related DTOs
type WeChatLoginRequest struct {
	Code string `json:"code" binding:"required"` // 微信小程序登录凭证
}

type WeChatUserInfo struct {
	NickName string `json:"nick_name"` // 用户昵称
	Avatar   string `json:"avatar"`    // 用户头像
	Gender   int    `json:"gender"`    // 用户性别，值为1时是男性，值为2时是女性，值为0时是未知
	Country  string `json:"country"`   // 用户所在国家
	Province string `json:"province"`  // 用户所在省份
	City     string `json:"city"`      // 用户所在城市
	Language string `json:"language"`  // 用户的语言，简体中文为zh_CN
}

type LoginResponse struct {
	AccessToken      string    `json:"access_token"`        // 访问令牌
	RefreshToken     string    `json:"refresh_token"`       // 刷新令牌
	TokenType        string    `json:"token_type"`          // 令牌类型，通常是 "Bearer"
	ExpiresIn        int64     `json:"expires_in"`          // 访问令牌过期时间（秒）
	RefreshExpiresIn int64     `json:"refresh_expires_in"`  // 刷新令牌过期时间（秒）
	Scope            string    `json:"scope,omitempty"`     // 权限范围（可选）
	UserInfo         *UserInfo `json:"user_info,omitempty"` // 用户基本信息（可选）
}

// UserInfo 用户基本信息
type UserInfo struct {
	ID             string   `json:"id"` // 改为string以支持UUID
	Username       string   `json:"username"`
	Email          string   `json:"email,omitempty"`
	PlatformAccess int      `json:"platform_access"`     // 平台访问权限位掩码
	Platforms      []string `json:"platforms,omitempty"` // 可访问的平台列表
	Roles          []string `json:"roles,omitempty"`     // 角色名称列表
}

type CreateUserRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Email    string `json:"email" binding:"required,email"`
	Phone    string `json:"phone"` // 手机号字段
	RoleIDs  []uint `json:"role_ids"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// LogoutRequest 登出请求
type LogoutRequest struct {
	RefreshToken string `json:"refresh_token,omitempty"` // 可选，用于撤销刷新令牌
}

// Common response DTOs
type MessageResponse struct {
	Message string `json:"message"`
}

type StatusResponse struct {
	Status string `json:"status"`
}

type HealthzResponse struct {
	Status     string `json:"status"`
	CommitHash string `json:"commit_hash"`
	BuildTime  string `json:"build_time"`
	Platform   string `json:"platform"`
}

// Role related DTOs
type CreateRoleRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
}

type UpdateRoleRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
}

type UpdateRolePermissionsRequest struct {
	PermissionIDs []uint `json:"permission_ids"`
}

// Permission related DTOs
type CreatePermissionRequest struct {
	Name        string `json:"name" binding:"required"`
	Key         string `json:"key" binding:"required"`
	Description string `json:"description"`
}

type UpdatePermissionRequest struct {
	Name        string `json:"name"`
	Key         string `json:"key"`
	Description string `json:"description"`
}

// User role management DTOs
type UpdateUserRolesRequest struct {
	RoleIDs []uint `json:"role_ids"`
}

// File related DTOs (additional to existing file DTOs)
type GetPresignedURLRequest struct {
	ObjectName string `json:"object_name" form:"object_name" binding:"required"`
}

type PresignedUploadURLResponse struct {
	UploadURL string `json:"upload_url"`
}

type PresignedDownloadURLResponse struct {
	DownloadURL string `json:"download_url"`
}

// Menu related DTOs
type CreateMenuRequest struct {
	Title         string `json:"title" binding:"required"`
	Path          string `json:"path"`
	Component     string `json:"component"`
	Redirect      string `json:"redirect"`
	Type          int    `json:"type" binding:"required,min=1,max=3"`
	Icon          string `json:"icon"`
	SvgIcon       string `json:"svg_icon"`
	ParentID      uint   `json:"parent_id"`
	Sort          int    `json:"sort"`
	Hidden        bool   `json:"hidden"`
	KeepAlive     bool   `json:"keep_alive"`
	Breadcrumb    bool   `json:"breadcrumb"`
	ShowInTabs    bool   `json:"show_in_tabs"`
	AlwaysShow    bool   `json:"always_show"`
	Affix         bool   `json:"affix"`
	ActiveMenu    string `json:"active_menu"`
	Status        int    `json:"status"`
	PermissionKey string `json:"permission"`
}

type UpdateMenuRequest struct {
	CreateMenuRequest
}

type MenuTreeResponse struct {
	ID            uint                `json:"id"`
	Title         string              `json:"title"`
	Path          string              `json:"path"`
	Component     string              `json:"component"`
	Redirect      string              `json:"redirect"`
	Type          int                 `json:"type"`
	Icon          string              `json:"icon"`
	SvgIcon       string              `json:"svg_icon"`
	ParentID      uint                `json:"parent_id"`
	Sort          int                 `json:"sort"`
	Hidden        bool                `json:"hidden"`
	KeepAlive     bool                `json:"keep_alive"`
	Breadcrumb    bool                `json:"breadcrumb"`
	ShowInTabs    bool                `json:"show_in_tabs"`
	AlwaysShow    bool                `json:"always_show"`
	Affix         bool                `json:"affix"`
	ActiveMenu    string              `json:"active_menu"`
	Status        int                 `json:"status"`
	PermissionKey string              `json:"permission"`
	AccessType    string              `json:"access_type"` // "public" 或 "protected"
	Children      []*MenuTreeResponse `json:"children,omitempty"`
}

// Booking related DTOs

// BookingRequest 预约请求
type BookingRequest struct {
	ScheduleID       uint `json:"schedule_id" binding:"required"`      // 课程排期ID
	MembershipCardID uint `json:"membership_card_id"`                  // 会员卡ID（可选，不传则自动选择）
	PeopleCount      int  `json:"people_count" binding:"min=1,max=10"` // 预约人数
}

// BookingResponse 预约响应
type BookingResponse struct {
	BookingNumber  string             `json:"booking_number"`  // 预约单号
	ScheduleInfo   ScheduleInfo       `json:"schedule_info"`   // 课程信息
	MembershipCard MembershipCardInfo `json:"membership_card"` // 使用的会员卡
	DeductInfo     DeductInfo         `json:"deduct_info"`     // 扣费信息
	Status         int                `json:"status"`          // 预约状态
}

// AsyncBookingResponse 异步预约响应
type AsyncBookingResponse struct {
	TaskID        string `json:"task_id"`        // 任务ID
	Status        string `json:"status"`         // 处理状态
	Message       string `json:"message"`        // 提示消息
	EstimatedTime int    `json:"estimated_time"` // 预计处理时间(秒)
}

// BookingStatusResponse 预约状态查询响应
type BookingStatusResponse struct {
	TaskID        string       `json:"task_id"`        // 任务ID
	Status        string       `json:"status"`         // 处理状态
	Result        string       `json:"result"`         // 处理结果
	BookingID     uint         `json:"booking_id"`     // 预约ID(成功时)
	QueuePosition int          `json:"queue_position"` // 排队位置(排队时)
	ErrorCode     string       `json:"error_code"`     // 错误码
	BookingInfo   *BookingInfo `json:"booking_info"`   // 预约详情(成功时)
	CreatedAt     string       `json:"created_at"`     // 申请时间
	UpdatedAt     string       `json:"updated_at"`     // 更新时间
}

// ScheduleInfo 课程排期信息
type ScheduleInfo struct {
	ID           uint   `json:"id"`
	CourseName   string `json:"course_name"`
	CoachName    string `json:"coach_name"`
	StoreName    string `json:"store_name"`
	StoreAddress string `json:"store_address"`
	StartTime    string `json:"start_time"`
	EndTime      string `json:"end_time"`
	Duration     int    `json:"duration"`
	MaxCapacity  int    `json:"max_capacity"`
	CurrentCount int    `json:"current_count"`
}

// MembershipCardInfo 会员卡信息
type MembershipCardInfo struct {
	ID              uint   `json:"id"`
	CardNumber      string `json:"card_number"`
	CardTypeName    string `json:"card_type_name"`
	Category        string `json:"category"`
	RemainingTimes  int    `json:"remaining_times"`
	RemainingAmount int    `json:"remaining_amount"`
	EndDate         string `json:"end_date"`
}

// DeductInfo 扣费信息
type DeductInfo struct {
	DeductTimes  int `json:"deduct_times"`  // 扣除次数
	DeductAmount int `json:"deduct_amount"` // 扣除金额(分)
	ActualAmount int `json:"actual_amount"` // 实际消费金额(分)
}

// CancelBookingRequest 取消预约请求
type CancelBookingRequest struct {
	BookingID uint   `json:"booking_id" binding:"required"`
	Reason    string `json:"reason" binding:"max=255"` // 取消原因
}

// CancelBookingInfoResponse 取消预约信息响应
type CancelBookingInfoResponse struct {
	BookingID     uint   `json:"booking_id"`
	CourseName    string `json:"course_name"`
	CoachName     string `json:"coach_name"`
	StoreName     string `json:"store_name"`
	StartTime     string `json:"start_time"`
	PeopleCount   int    `json:"people_count"`
	RefundMethod  string `json:"refund_method"`  // 退款方式
	RefundAmount  int    `json:"refund_amount"`  // 退款金额(分)
	RefundTimes   int    `json:"refund_times"`   // 退款次数
	CanCancel     bool   `json:"can_cancel"`     // 是否可以取消
	CancelMessage string `json:"cancel_message"` // 取消提示信息
}

// AutoCancelCourseRequest 自动取消课程请求
type AutoCancelCourseRequest struct {
	ScheduleID uint   `json:"schedule_id" binding:"required"`
	Reason     string `json:"reason" binding:"required,max=255"`
}

// AttendanceStatsResponse 出勤统计响应
type AttendanceStatsResponse struct {
	UserID           uint    `json:"user_id"`
	CardID           uint    `json:"card_id"`
	CardTypeName     string  `json:"card_type_name"`
	TotalBookings    int     `json:"total_bookings"`    // 总预约次数
	AttendedBookings int     `json:"attended_bookings"` // 已签到次数
	NoShowBookings   int     `json:"no_show_bookings"`  // 未到课次数
	AttendanceRate   float64 `json:"attendance_rate"`   // 出勤率
}

// NotificationStatsResponse 消息统计响应
type NotificationStatsResponse struct {
	TypeStats   []TypeStat `json:"type_stats"`
	UnreadCount int64      `json:"unread_count"`
	TodayCount  int64      `json:"today_count"`
}

// TypeStat 类型统计
type TypeStat struct {
	Type  string `json:"type"`
	Count int64  `json:"count"`
}

// Banner related DTOs

// CreateBannerRequest 创建轮播图请求
type CreateBannerRequest struct {
	Title       string `json:"title" binding:"required,max=100"`
	FileID      string `json:"file_id" binding:"required,len=36"` // 使用 FileID 替代 Image
	LinkURL     string `json:"link_url" binding:"max=500"`        // 点击跳转链接
	Description string `json:"description" binding:"max=500"`     // 轮播图描述
	SortOrder   int    `json:"sort_order" binding:"min=0"`
	Position    string `json:"position" binding:"required,oneof=home coach store"`
}

// UpdateBannerRequest 更新轮播图请求
type UpdateBannerRequest struct {
	Title       string `json:"title" binding:"omitempty,max=100"`
	FileID      string `json:"file_id" binding:"omitempty,len=36"` // 使用 FileID 替代 Image
	LinkURL     string `json:"link_url" binding:"omitempty,max=500"`
	Description string `json:"description" binding:"omitempty,max=500"`
	SortOrder   *int   `json:"sort_order" binding:"omitempty,min=0"`
	Status      *int   `json:"status" binding:"omitempty,oneof=0 1"`
	Position    string `json:"position" binding:"omitempty,oneof=home coach store"`
}

// CreateCoachBannerRequest 创建教练轮播图请求
type CreateCoachBannerRequest struct {
	CoachID   string `json:"coach_id" binding:"required,len=36"`
	FileID    string `json:"file_id" binding:"required,len=36"` // 使用文件ID
	Title     string `json:"title" binding:"max=100"`
	SortOrder int    `json:"sort_order" binding:"min=0"`
}

// UpdateCoachBannerRequest 更新教练轮播图请求
type UpdateCoachBannerRequest struct {
	FileID    string `json:"file_id" binding:"omitempty,len=36"` // 使用文件ID
	Title     string `json:"title" binding:"omitempty,max=100"`
	SortOrder *int   `json:"sort_order" binding:"omitempty,min=0"`
	Status    *int   `json:"status" binding:"omitempty,oneof=0 1"`
}

// Dashboard related DTOs

// DashboardData 仪表盘数据
type DashboardData struct {
	UserStats      UserStats       `json:"user_stats"`
	CardStats      CardStats       `json:"card_stats"`
	BookingStats   BookingStats    `json:"booking_stats"`
	RevenueStats   RevenueStats    `json:"revenue_stats"`
	ExpiringCards  []ExpiringCard  `json:"expiring_cards"`
	PopularCourses []PopularCourse `json:"popular_courses"`
	LastUpdateTime time.Time       `json:"last_update_time"`
}

// UserStats 用户统计
type UserStats struct {
	TotalUsers    int64 `json:"total_users"`
	TodayNewUsers int64 `json:"today_new_users"`
	MonthNewUsers int64 `json:"month_new_users"`
	ActiveUsers   int64 `json:"active_users"`
}

// CardStats 会员卡统计
type CardStats struct {
	TotalCards    int64 `json:"total_cards"`
	TodayNewCards int64 `json:"today_new_cards"`
	MonthNewCards int64 `json:"month_new_cards"`
	ActiveCards   int64 `json:"active_cards"`
	ExpiringCards int64 `json:"expiring_cards"`
}

// BookingStats 预约统计
type BookingStats struct {
	TotalBookings     int64 `json:"total_bookings"`
	TodayBookings     int64 `json:"today_bookings"`
	MonthBookings     int64 `json:"month_bookings"`
	CompletedBookings int64 `json:"completed_bookings"`
}

// RevenueStats 收入统计
type RevenueStats struct {
	TodayRevenue int `json:"today_revenue"`
	MonthRevenue int `json:"month_revenue"`
	TotalRevenue int `json:"total_revenue"`
}

// ExpiringCard 即将到期的会员卡
type ExpiringCard struct {
	CardID       uint   `json:"card_id"`
	CardNumber   string `json:"card_number"`
	UserName     string `json:"user_name"`
	CardTypeName string `json:"card_type_name"`
	EndDate      string `json:"end_date"`
	DaysLeft     int    `json:"days_left"`
}

// PopularCourse 热门课程
type PopularCourse struct {
	ID           uint   `json:"id"`
	Name         string `json:"name"`
	BookingCount int64  `json:"booking_count"`
}

// Membership Card Operation DTOs

// RechargeMembershipCardRequest 充值会员卡请求
type RechargeMembershipCardRequest struct {
	RechargeTimes  int        `json:"recharge_times" binding:"min=0"`
	RechargeAmount int        `json:"recharge_amount" binding:"min=0"`
	PaymentMethod  string     `json:"payment_method" binding:"required,oneof=offline card"`
	PaymentCardID  *uint      `json:"payment_card_id"`
	ActualAmount   int        `json:"actual_amount" binding:"required,min=0"`
	ExtendExpiry   bool       `json:"extend_expiry"`
	NewEndDate     *time.Time `json:"new_end_date"`
	Remark         string     `json:"remark" binding:"max=500"`
}

// UpgradeMembershipCardRequest 升级会员卡请求
type UpgradeMembershipCardRequest struct {
	TargetCardTypeID uint   `json:"target_card_type_id" binding:"required"`
	Remark           string `json:"remark" binding:"max=500"`
}

// LeaveMembershipCardRequest 请假会员卡请求
type LeaveMembershipCardRequest struct {
	LeaveDays int    `json:"leave_days" binding:"required,min=1,max=365"`
	Reason    string `json:"reason" binding:"required,max=500"`
}

// ExtendMembershipCardRequest 延期会员卡请求
type ExtendMembershipCardRequest struct {
	ExtendDays int    `json:"extend_days" binding:"required,min=1,max=365"`
	Reason     string `json:"reason" binding:"required,max=500"`
}

// Batch Course Creation DTOs

// 注意：CourseDeductionRule 已废弃，请使用灵活扣减系统

// WeeklyScheduleItem 每周特定日期的课程配置
type WeeklyScheduleItem struct {
	Weekday     int      `json:"weekday" binding:"required,min=0,max=6"`     // 0=周日, 1=周一, ..., 6=周六
	CourseName  string   `json:"course_name" binding:"required"`             // 课程主题名称
	Description string   `json:"description,omitempty"`                      // 课程介绍/描述
	CoachIDs    []string `json:"coach_ids" binding:"required,min=1"`         // 教练ID数组，支持多教练
	Duration    int      `json:"duration" binding:"required,min=15,max=300"` // 课程时长(分钟)，每节课可以不同
	ClassRoomID *uint    `json:"classroom_id,omitempty"`                     // 可选：指定教室
}

// CourseScheduleItem 具体课程时间安排（用于单个课程创建）
type CourseScheduleItem struct {
	StartTime    time.Time `json:"start_time" binding:"required"` // 具体的开始时间
	EndTime      time.Time `json:"end_time" binding:"required"`   // 具体的结束时间
	NameOverride *string   `json:"name_override,omitempty"`       // 可选：覆盖基础课程名称
	CoachIDs     []string  `json:"coach_ids,omitempty"`           // 可选：覆盖基础教练（支持多教练）
	ClassRoomID  *uint     `json:"classroom_id,omitempty"`        // 可选：覆盖基础教室
}

// CourseSchedulingRequest 课程排课请求（基于已创建的课程进行排课）
type CourseSchedulingRequest struct {
	// 周期范围设置（支持跨多周）
	StartDate string `json:"start_date" binding:"required"` // 开始日期 "2025-07-17"
	EndDate   string `json:"end_date" binding:"required"`   // 结束日期 "2025-07-27"

	// 统一开始时间设置
	StartTime string `json:"start_time" binding:"required"` // 统一开始时间 "12:00"

	// 每周排课配置（基于已创建的课程）
	WeeklySchedules []WeeklySchedulingItem `json:"weekly_schedules" binding:"required,min=1"`

	// 门店ID
	StoreID uint `json:"store_id" binding:"required"` // 门店ID
}

// WeeklySchedulingItem 每周排课配置
type WeeklySchedulingItem struct {
	Weekday     int      `json:"weekday" binding:"required,min=0,max=6"` // 0=周日, 1=周一, ..., 6=周六
	CourseID    string   `json:"course_id" binding:"required,len=36"`    // 已创建的课程ID
	CoachIDs    []string `json:"coach_ids" binding:"required,min=1"`     // 教练ID数组，支持多教练
	ClassRoomID *uint    `json:"classroom_id,omitempty"`                 // 可选：指定教室
}

// 保留原有的批量创建请求（向后兼容）
// BatchCreateCoursesRequest 批量创建课程请求（一步完成课程创建+排课）
// 支持跨多周的周期范围，如：2025-07-17 到 2025-07-27
type BatchCreateCoursesRequest struct {
	// 周期范围设置（支持跨多周）
	StartDate string `json:"start_date" binding:"required"` // 开始日期 "2025-07-17"
	EndDate   string `json:"end_date" binding:"required"`   // 结束日期 "2025-07-27"

	// 统一开始时间设置
	StartTime string `json:"start_time" binding:"required"` // 统一开始时间 "12:00"

	// 每周课程配置（在指定周期范围内，每个选中的星期都会创建课程）
	WeeklySchedules []WeeklyScheduleItem `json:"weekly_schedules" binding:"required,min=1"`

	// 通用课程信息
	GlobalDescription string `json:"global_description,omitempty"`         // 全局课程描述
	Capacity          int    `json:"capacity" binding:"required,min=1"`    // 课程容量
	Price             int    `json:"price" binding:"required,min=0"`       // 课程价格(分)
	Level             int    `json:"level" binding:"required,min=1,max=5"` // 课程难度等级
	CategoryID        uint   `json:"category_id" binding:"required"`       // 课程分类ID
	StoreID           uint   `json:"store_id" binding:"required"`          // 门店ID
	Type              int    `json:"type" binding:"required"`              // 课程类型

	// 注意：扣卡方式配置已迁移到灵活扣减系统，请使用相关接口单独配置
}

// CourseSchedule 课程时间安排
type CourseSchedule struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// TimeConflict 时间冲突
type TimeConflict struct {
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
	Message   string    `json:"message"`
}

// ConflictInfo 冲突信息
type ConflictInfo struct {
	Date         string `json:"date"`          // 冲突日期
	Time         string `json:"time"`          // 冲突时间段
	ConflictType string `json:"conflict_type"` // 冲突类型：coach_busy, classroom_occupied
	Details      string `json:"details"`       // 冲突详情
}

// CourseSchedulingResponse 课程排课响应
type CourseSchedulingResponse struct {
	Message          string `json:"message"`
	ScheduledCount   int    `json:"scheduled_count"`
	ScheduledCourses any    `json:"scheduled_courses"`
}

// BookCourseRequest 预约课程请求
type BookCourseRequest struct {
	UserID           string `json:"user_id" binding:"required"`
	CourseID         uint   `json:"course_id" binding:"required"`
	MembershipCardID uint   `json:"membership_card_id" binding:"required"`
	BookingTime      string `json:"booking_time" binding:"required"` // RFC3339格式
	PeopleCount      int    `json:"people_count,omitempty"`          // 预约人数，默认为1
}

// BookCourseResponse 预约课程响应
type BookCourseResponse struct {
	Message         string  `json:"message"`
	BookingID       uint    `json:"booking_id"`
	DeductedAmount  float64 `json:"deducted_amount"`
	DeductedTimes   int     `json:"deducted_times"`
	RemainingAmount float64 `json:"remaining_amount"`
	RemainingTimes  int     `json:"remaining_times"`
}

// BookingSuccessResponse 预订成功响应
type BookingSuccessResponse struct {
	BookingID       uint    `json:"booking_id"`
	Message         string  `json:"message"`
	DeductedAmount  float64 `json:"deducted_amount"`
	DeductedTimes   int     `json:"deducted_times"`
	RemainingAmount float64 `json:"remaining_amount"`
	RemainingTimes  int     `json:"remaining_times"`
}

// BookingQueueResponse 预订排队响应
type BookingQueueResponse struct {
	QueuePosition int    `json:"queue_position"`
	Message       string `json:"message"`
}

// BatchCreateCoursesResponse 批量创建课程响应
type BatchCreateCoursesResponse struct {
	Message        string `json:"message"`
	CreatedCount   int    `json:"created_count"`
	CreatedCourses any    `json:"created_courses"`
}

// BatchCreateConflictResponse 批量创建冲突响应
type BatchCreateConflictResponse struct {
	Message   string         `json:"message"`
	Conflicts []ConflictInfo `json:"conflicts"`
}

// BatchCreatePreviewResponse 批量创建预览响应
type BatchCreatePreviewResponse struct {
	TotalCourses    int              `json:"total_courses"`
	CourseSchedules []CourseSchedule `json:"course_schedules"`
	Conflicts       []ConflictInfo   `json:"conflicts"`
	HasConflicts    bool             `json:"has_conflicts"`
}

// BookingListRequest 预约列表请求
type BookingListRequest struct {
	PageRequest
	Status int    `json:"status" form:"status"` // 预约状态筛选
	Type   string `json:"type" form:"type"`     // 类型: upcoming, history, all
}

// BookingListResponse 预约列表响应
type BookingListResponse struct {
	List       []BookingInfo    `json:"list"`
	Total      int64            `json:"total"`
	Page       int              `json:"page"`
	PageSize   int              `json:"page_size"`
	Statistics UserBookingStats `json:"statistics"`
}

// BookingInfo 预约信息
type BookingInfo struct {
	ID            uint         `json:"id"`
	BookingNumber string       `json:"booking_number"`
	ScheduleInfo  ScheduleInfo `json:"schedule_info"`
	PeopleCount   int          `json:"people_count"`
	Status        int          `json:"status"`
	StatusText    string       `json:"status_text"`
	CanCancel     bool         `json:"can_cancel"` // 是否可以取消
	CanRate       bool         `json:"can_rate"`   // 是否可以评价
	Rating        int          `json:"rating"`     // 评分
	Comment       string       `json:"comment"`    // 评价
	CreatedAt     string       `json:"created_at"`
}

// UserBookingStats 用户预约统计
type UserBookingStats struct {
	TotalClasses    int `json:"total_classes"`    // 总上课次数
	TotalDays       int `json:"total_days"`       // 总上课天数
	UpcomingClasses int `json:"upcoming_classes"` // 即将上课数量
}

// BookingDetailResponse 预约详情响应
type BookingDetailResponse struct {
	// 基本信息
	ID            uint   `json:"id"`
	BookingNumber string `json:"booking_number"` // 订单编号
	Status        int    `json:"status"`
	StatusText    string `json:"status_text"`
	PeopleCount   int    `json:"people_count"`
	CreatedAt     string `json:"created_at"`

	// 课程信息
	Course CourseDetailInfo `json:"course"`

	// 订单信息
	Order OrderInfo `json:"order"`

	// 会员卡信息
	MembershipCard BookingMembershipCardInfo `json:"membership_card"`

	// 操作权限
	CanCancel bool `json:"can_cancel"`
	CanRate   bool `json:"can_rate"`

	// 评价信息
	Rating  int    `json:"rating"`
	Comment string `json:"comment"`
}

// CourseDetailInfo 课程详情信息
type CourseDetailInfo struct {
	ID           uint                   `json:"id"`
	Name         string                 `json:"name"`
	StartTime    string                 `json:"start_time"`
	EndTime      string                 `json:"end_time"`
	Duration     int                    `json:"duration"`
	Coach        BookingCoachInfo       `json:"coach"`
	Store        BookingStoreDetailInfo `json:"store"`
	CourseStatus string                 `json:"course_status"` // 课程未开始/课程已结束
}

// BookingCoachInfo 预约教练信息
type BookingCoachInfo struct {
	ID     string `json:"id"` // 改为string以支持UUID
	Name   string `json:"name"`
	Avatar string `json:"avatar"` // 统一使用avatar
}

// BookingStoreDetailInfo 预约门店详情信息
type BookingStoreDetailInfo struct {
	ID      uint   `json:"id"`
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

// OrderInfo 订单信息
type OrderInfo struct {
	BookingNumber string `json:"booking_number"` // 订单编号
	TotalPrice    int    `json:"total_price"`    // 课程总价(分)
	ActualAmount  int    `json:"actual_amount"`  // 实付金额(分)
	PaymentTime   string `json:"payment_time"`   // 支付时间
	PaymentMethod string `json:"payment_method"` // 支付方式
	DeductTimes   int    `json:"deduct_times"`   // 扣除次数
	DeductAmount  int    `json:"deduct_amount"`  // 扣除金额(分)
}

// BookingMembershipCardInfo 预约会员卡信息
type BookingMembershipCardInfo struct {
	ID       uint   `json:"id"`
	Name     string `json:"name"`
	CardType string `json:"card_type"`
}

// AvailableCardsResponse 可用会员卡响应
type AvailableCardsResponse struct {
	Cards []AvailableCard `json:"cards"`
}

// AvailableCard 可用会员卡
type AvailableCard struct {
	ID              uint   `json:"id"`
	CardNumber      string `json:"card_number"`
	CardTypeName    string `json:"card_type_name"`
	Category        string `json:"category"`
	RemainingTimes  int    `json:"remaining_times"`
	RemainingAmount int    `json:"remaining_amount"`
	EndDate         string `json:"end_date"`
	Priority        int    `json:"priority"`      // 优先级（系统计算）
	CanUse          bool   `json:"can_use"`       // 是否可用
	DeductTimes     int    `json:"deduct_times"`  // 预计扣除次数
	DeductAmount    int    `json:"deduct_amount"` // 预计扣除金额
}

// RateBookingRequest 评价预约请求
type RateBookingRequest struct {
	BookingID uint   `json:"booking_id" binding:"required"`
	Rating    int    `json:"rating" binding:"required,min=1,max=5"`
	Comment   string `json:"comment" binding:"max=500"`
}

// Membership Type related DTOs

// CreateMembershipTypeRequest 创建会员卡类型请求
type CreateMembershipTypeRequest struct {
	Name              string  `json:"name" binding:"required,max=100"`
	Category          string  `json:"category" binding:"required,oneof=times period balance"`
	Scope             string  `json:"scope" binding:"required,oneof=group private small universal"`
	Description       string  `json:"description" binding:"max=500"`
	ValidityDays      int     `json:"validity_days" binding:"min=0"`
	Times             int     `json:"times" binding:"min=0"`
	Amount            int     `json:"amount" binding:"min=0"`
	Price             int     `json:"price" binding:"required,min=0"`
	Discount          float64 `json:"discount" binding:"min=0,max=1"`
	Shareable         bool    `json:"shareable"`
	CanSetExpiry      bool    `json:"can_set_expiry"`
	CanSelectStores   bool    `json:"can_select_stores"`
	CanAddSubCard     bool    `json:"can_add_sub_card"`
	CanSetDailyLimit  bool    `json:"can_set_daily_limit"`
	DailyBookingLimit int     `json:"daily_booking_limit" binding:"min=0"`
	// CourseDeductRules string  `json:"course_deduct_rules"` // 已废弃，使用灵活扣减系统
	ApplicableCourses string `json:"applicable_courses"`
	SortOrder         int    `json:"sort_order" binding:"min=0"`
}

// UpdateMembershipTypeRequest 更新会员卡类型请求
type UpdateMembershipTypeRequest struct {
	Name              string   `json:"name" binding:"omitempty,max=100"`
	Category          string   `json:"category" binding:"omitempty,oneof=times period balance"`
	Scope             string   `json:"scope" binding:"omitempty,oneof=group private small universal"`
	Description       string   `json:"description" binding:"omitempty,max=500"`
	ValidityDays      *int     `json:"validity_days" binding:"omitempty,min=0"`
	Times             *int     `json:"times" binding:"omitempty,min=0"`
	Amount            *int     `json:"amount" binding:"omitempty,min=0"`
	Price             *int     `json:"price" binding:"omitempty,min=0"`
	Discount          *float64 `json:"discount" binding:"omitempty,min=0,max=1"`
	Shareable         *bool    `json:"shareable"`
	CanSetExpiry      *bool    `json:"can_set_expiry"`
	CanSelectStores   *bool    `json:"can_select_stores"`
	CanAddSubCard     *bool    `json:"can_add_sub_card"`
	CanSetDailyLimit  *bool    `json:"can_set_daily_limit"`
	DailyBookingLimit *int     `json:"daily_booking_limit" binding:"omitempty,min=0"`
	// CourseDeductRules string   `json:"course_deduct_rules"` // 已废弃，使用灵活扣减系统
	ApplicableCourses string `json:"applicable_courses"`
	SortOrder         *int   `json:"sort_order" binding:"omitempty,min=0"`
	Status            *int   `json:"status" binding:"omitempty,oneof=0 1"`
}

// Membership Card related DTOs

// CreateMembershipCardRequest 开卡请求
type CreateMembershipCardRequest struct {
	UserID            string    `json:"user_id" binding:"required,len=36"`
	CardTypeID        uint      `json:"card_type_id" binding:"required"`
	StartDate         time.Time `json:"start_date" binding:"required"`
	PurchasePrice     int       `json:"purchase_price" binding:"required,min=0"`
	Discount          float64   `json:"discount" binding:"min=0,max=1"`
	PaymentMethod     string    `json:"payment_method" binding:"max=50"`
	AvailableStores   []uint    `json:"available_stores"`                    // 可用门店ID列表，空表示全部门店
	DailyBookingLimit int       `json:"daily_booking_limit" binding:"min=0"` // 单日预约课程上限
	Remark            string    `json:"remark" binding:"max=500"`
}

// TransferMembershipCardRequest 转卡请求
type TransferMembershipCardRequest struct {
	TargetPhone string `json:"target_phone" binding:"required"`
	Fee         int    `json:"fee" binding:"min=0"`
	Remark      string `json:"remark" binding:"max=500"`
}

// DeductMembershipCardRequest 扣费请求
type DeductMembershipCardRequest struct {
	Amount int    `json:"amount" binding:"min=0"`
	Times  int    `json:"times" binding:"min=0"`
	Reason string `json:"reason" binding:"required,max=255"`
	Remark string `json:"remark" binding:"max=500"`
}

// FreezeMembershipCardRequest 冻结/解冻请求
type FreezeMembershipCardRequest struct {
	Action string `json:"action" binding:"required,oneof=freeze unfreeze"`
	Days   int    `json:"days" binding:"min=0"`
	Reason string `json:"reason" binding:"required,max=255"`
}

// CreateSubCardRequest 创建副卡请求
type CreateSubCardRequest struct {
	MainCardID uint   `json:"main_card_id" binding:"required"`
	UserID     string `json:"user_id" binding:"required,len=36"`
	Remark     string `json:"remark" binding:"max=500"`
}

// SubCardInfo 副卡信息
type SubCardInfo struct {
	ID         uint   `json:"id"`
	CardNumber string `json:"card_number"`
	UserID     string `json:"user_id"`
	UserName   string `json:"user_name"`
	UserPhone  string `json:"user_phone"`
	Status     int    `json:"status"`
	CreatedAt  string `json:"created_at"`
}

// StoreInfo 门店信息
type StoreInfo struct {
	ID   uint   `json:"id"`
	Name string `json:"name"`
}

// MembershipCardDetail 会员卡详情（包含副卡信息）
type MembershipCardDetail struct {
	ID                uint          `json:"id"`
	CardNumber        string        `json:"card_number"`
	CardTypeName      string        `json:"card_type_name"`
	Category          string        `json:"category"`
	IsMainCard        bool          `json:"is_main_card"`
	RemainingTimes    int           `json:"remaining_times"`
	RemainingAmount   int           `json:"remaining_amount"`
	TotalTimes        int           `json:"total_times"`
	TotalAmount       int           `json:"total_amount"`
	StartDate         string        `json:"start_date"`
	EndDate           string        `json:"end_date"`
	Status            int           `json:"status"`
	AvailableStores   []StoreInfo   `json:"available_stores"`
	DailyBookingLimit int           `json:"daily_booking_limit"`
	SubCards          []SubCardInfo `json:"sub_cards"`
	CreatedAt         string        `json:"created_at"`
}

// Common DTOs

// PageRequest 分页请求
type PageRequest struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// SetDefaults 设置默认值
func (p *PageRequest) SetDefaults() {
	if p.Page <= 0 {
		p.Page = MinPage
	}
	if p.PageSize <= 0 {
		p.PageSize = DefaultPageSize
	}
	if p.PageSize > MaxPageSize {
		p.PageSize = MaxPageSize
	}
}

// GetOffset 计算偏移量
func (p *PageRequest) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// CommonListRequest 通用列表请求（包含分页和搜索）
type CommonListRequest struct {
	PageRequest
	Search   string `json:"search" form:"search"`     // 搜索关键词
	Status   string `json:"status" form:"status"`     // 状态筛选
	StoreID  string `json:"store_id" form:"store_id"` // 门店筛选
	CoachID  string `json:"coach_id" form:"coach_id"` // 教练筛选
	RoleID   string `json:"role_id" form:"role_id"`   // 角色筛选
	Keyword  string `json:"keyword" form:"keyword"`   // 关键词（别名）
	UserID   string `json:"user_id" form:"user_id"`   // 用户筛选
	Platform string `json:"platform" form:"platform"` // 平台筛选
}

// CourseListRequest 课程列表请求
type CourseListRequest struct {
	CommonListRequest
	ClassRoomID string `json:"classroom_id" form:"classroom_id"` // 教室筛选
	Type        string `json:"type" form:"type"`                 // 课程类型筛选
	Level       string `json:"level" form:"level"`               // 课程级别筛选
	CategoryID  string `json:"category_id" form:"category_id"`   // 课程分类筛选
	StartDate   string `json:"start_date" form:"start_date"`     // 开始日期
	EndDate     string `json:"end_date" form:"end_date"`         // 结束日期
	PriceMin    string `json:"price_min" form:"price_min"`       // 最低价格
	PriceMax    string `json:"price_max" form:"price_max"`       // 最高价格
}

// MembershipCardListRequest 会员卡列表请求
type MembershipCardListRequest struct {
	CommonListRequest
	CardTypeID string `json:"card_type_id" form:"card_type_id"` // 卡种筛选
}

// OperationLogListRequest 操作日志列表请求
type OperationLogListRequest struct {
	CommonListRequest
	Module    string `json:"module" form:"module"`         // 模块筛选
	Action    string `json:"action" form:"action"`         // 操作筛选
	StartDate string `json:"start_date" form:"start_date"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date"`     // 结束日期
}

// MembershipTypeListRequest 会员卡类型列表请求
type MembershipTypeListRequest struct {
	CommonListRequest
	Category string `json:"category" form:"category"` // 分类筛选
}

// CourseCategoryListRequest 课程分类列表请求
type CourseCategoryListRequest struct {
	CommonListRequest
	Name string `json:"name" form:"name"` // 名称搜索
}

// PageResponse 分页响应
type PageResponse struct {
	List     any   `json:"list"`
	Total    int64 `json:"total"`
	Page     int   `json:"page"`
	PageSize int   `json:"page_size"`
}

// Course Category related DTOs

// CreateCourseCategoryRequest 创建课程分类请求（扁平化）
type CreateCourseCategoryRequest struct {
	Name        string `json:"name" binding:"required,max=50"`
	Description string `json:"description" binding:"max=500"`
	SortOrder   int    `json:"sort_order" binding:"min=0"`
}

// UpdateCourseCategoryRequest 更新课程分类请求
type UpdateCourseCategoryRequest struct {
	Name        string `json:"name" binding:"omitempty,max=50"`
	Description string `json:"description" binding:"omitempty,max=500"`
	SortOrder   *int   `json:"sort_order" binding:"omitempty,min=0"`
	Status      *int   `json:"status" binding:"omitempty,oneof=0 1"`
}

// Course related DTOs

// CreateCourseRequest 创建课程请求（分离式创建，只包含基本信息）
type CreateCourseRequest struct {
	Name        string `json:"name" binding:"required,max=100"`
	Description string `json:"description" binding:"max=500"`
	Duration    int    `json:"duration" binding:"required,min=15,max=480"`
	Capacity    int    `json:"capacity" binding:"required,min=1,max=100"`
	Price       int    `json:"price" binding:"required,min=0"`
	Level       int    `json:"level" binding:"required,min=1,max=3"`
	CategoryID  uint   `json:"category_id" binding:"required"`
	StoreID     uint   `json:"store_id" binding:"required"`
	Type        int    `json:"type" binding:"required,oneof=1 2"`

	// 扣减规则（直接绑定到课程）
	DeductionRules []CourseDeductionRuleItem `json:"deduction_rules,omitempty"`
}

// CourseDeductionRuleItem 课程扣减规则项
type CourseDeductionRuleItem struct {
	CardTypeID         uint   `json:"card_type_id" binding:"required"`                             // 会员卡类型ID
	DeductionType      string `json:"deduction_type" binding:"required,oneof=amount times period"` // 扣减类型：amount金额 times次数 period期限卡
	DeductionAmount    int    `json:"deduction_amount,omitempty"`                                  // 扣减金额(分)
	DeductionTimes     int    `json:"deduction_times,omitempty"`                                   // 扣减次数
	PerPersonDeduction bool   `json:"per_person_deduction,omitempty"`                              // 是否按人数计算扣减
	Description        string `json:"description,omitempty"`                                       // 扣减说明
}

// CourseSupportedCardTypesResponse 课程支持的会员卡类型响应
type CourseSupportedCardTypesResponse struct {
	CourseID   uint                              `json:"course_id"`
	CourseName string                            `json:"course_name"`
	CardTypes  []CourseSupportedCardTypeResponse `json:"card_types"`
	TotalTypes int                               `json:"total_types"`
}

// CourseSupportedCardTypeResponse 课程支持的会员卡类型响应项
type CourseSupportedCardTypeResponse struct {
	ID           uint   `json:"id"`
	CardTypeID   uint   `json:"card_type_id"`
	CardTypeName string `json:"card_type_name"`
	Category     string `json:"category"`
	IsEnabled    bool   `json:"is_enabled"`
	SortOrder    int    `json:"sort_order"`
	CreatedAt    string `json:"created_at"`
}

// UpdateCourseRequest 更新课程请求
type UpdateCourseRequest struct {
	Name        string     `json:"name" binding:"omitempty,max=100"`
	Description string     `json:"description" binding:"omitempty,max=1000"`
	StartTime   *time.Time `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Duration    *int       `json:"duration" binding:"omitempty,min=1,max=300"`
	Capacity    *int       `json:"capacity" binding:"omitempty,min=1,max=100"`
	Price       *int       `json:"price" binding:"omitempty,min=0"`
	CoachIDs    []string   `json:"coach_ids,omitempty"` // 教练ID数组，支持多教练
	StoreID     *uint      `json:"store_id"`
	ClassRoomID *uint      `json:"classroom_id"` // 教室ID
	Type        *int       `json:"type" binding:"omitempty,oneof=1 2"`
	Status      *int       `json:"status" binding:"omitempty,oneof=0 1"`
}

// Store related DTOs

// CreateStoreRequest 创建门店请求
type CreateStoreRequest struct {
	Name        string   `json:"name" binding:"required,max=100"`
	Address     string   `json:"address" binding:"required,max=255"`
	Phone       string   `json:"phone" binding:"max=20"`
	Latitude    float64  `json:"latitude" binding:"min=-90,max=90"`
	Longitude   float64  `json:"longitude" binding:"min=-180,max=180"`
	ImageIDs    []string `json:"image_ids" binding:"dive,len=36"` // 使用文件ID数组替代Images字符串
	Description string   `json:"description" binding:"max=1000"`
	SortOrder   int      `json:"sort_order" binding:"min=0"`
}

// UpdateStoreRequest 更新门店请求
type UpdateStoreRequest struct {
	Name        string   `json:"name" binding:"omitempty,max=100"`
	Address     string   `json:"address" binding:"omitempty,max=255"`
	Phone       string   `json:"phone" binding:"omitempty,max=20"`
	Latitude    *float64 `json:"latitude" binding:"omitempty,min=-90,max=90"`
	Longitude   *float64 `json:"longitude" binding:"omitempty,min=-180,max=180"`
	ImageIDs    []string `json:"image_ids" binding:"dive,len=36"` // 使用文件ID数组替代Images字符串
	Description string   `json:"description" binding:"omitempty,max=1000"`
	SortOrder   *int     `json:"sort_order" binding:"omitempty,min=0"`
	Status      *int     `json:"status" binding:"omitempty,oneof=0 1"`
}

// StoreWithDistance 带距离信息的门店
type StoreWithDistance struct {
	ID          uint     `json:"id"`
	Name        string   `json:"name"`
	Address     string   `json:"address"`
	Phone       string   `json:"phone"`
	Latitude    float64  `json:"latitude"`
	Longitude   float64  `json:"longitude"`
	ImageURLs   []string `json:"image_urls"` // 直接返回MinIO预签名URL数组，避免后台重定向
	Description string   `json:"description"`
	Status      int      `json:"status"`
	SortOrder   int      `json:"sort_order"`
	Distance    *float64 `json:"distance,omitempty"` // 距离，单位：公里
}

// ClassRoom related DTOs

// CreateClassRoomRequest 创建教室请求
type CreateClassRoomRequest struct {
	StoreID     uint   `json:"store_id" binding:"required"`
	Name        string `json:"name" binding:"required,max=50"`
	Capacity    int    `json:"capacity" binding:"required,min=1,max=200"`
	Equipment   string `json:"equipment"`
	Description string `json:"description" binding:"max=1000"`
}

// UpdateClassRoomRequest 更新教室请求
type UpdateClassRoomRequest struct {
	Name        string `json:"name" binding:"omitempty,max=50"`
	Capacity    *int   `json:"capacity" binding:"omitempty,min=1,max=200"`
	Equipment   string `json:"equipment"`
	Description string `json:"description" binding:"omitempty,max=1000"`
	Status      *int   `json:"status" binding:"omitempty,oneof=0 1"`
}

// Home related DTOs

// HomeData 首页数据
type HomeData struct {
	Banners         []Banner         `json:"banners"`
	FeaturedCourses []FeaturedCourse `json:"featured_courses"`
	FeaturedCoaches []FeaturedCoach  `json:"featured_coaches"`
	NearbyStores    []NearbyStore    `json:"nearby_stores"`
}

// Banner 轮播图
type Banner struct {
	ID          uint   `json:"id"`
	Title       string `json:"title"`
	Image       string `json:"image"`
	LinkURL     string `json:"link_url,omitempty"`
	Description string `json:"description,omitempty"`
	SortOrder   int    `json:"sort_order"`
}

// FeaturedCourse 精选课程
type FeaturedCourse struct {
	ID          uint    `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Duration    int     `json:"duration"`
	Price       float64 `json:"price"`
	CoachName   string  `json:"coach_name"`
	StoreName   string  `json:"store_name"`
	StartTime   string  `json:"start_time"`
}

// FeaturedCoach 推荐教练
type FeaturedCoach struct {
	ID          string `json:"id"` // 改为string以支持UUID
	Name        string `json:"name"`
	Avatar      string `json:"avatar"`
	Experience  int    `json:"experience"`
	Speciality  string `json:"speciality"`
	Description string `json:"description"`
}

// NearbyStore 附近门店
type NearbyStore struct {
	ID          uint     `json:"id"`
	Name        string   `json:"name"`
	Address     string   `json:"address"`
	Phone       string   `json:"phone"`
	Images      []string `json:"images"` // 修改为字符串数组
	Description string   `json:"description"`
}

// Advertisement 广告
type Advertisement struct {
	ID       uint   `json:"id"`
	Title    string `json:"title"`
	Content  string `json:"content"`
	Image    string `json:"image"`
	LinkType string `json:"link_type"`
	LinkID   uint   `json:"link_id"`
	LinkURL  string `json:"link_url,omitempty"`
	ShowType string `json:"show_type"` // popup, banner
	Status   int    `json:"status"`
}

// FeaturedContent 精选内容
type FeaturedContent struct {
	Courses []FeaturedCourse `json:"courses"`
	Coaches []FeaturedCoach  `json:"coaches"`
}

// Favorite related DTOs

// AddFavoriteRequest 添加收藏请求
type AddFavoriteRequest struct {
	FavoriteType string `json:"favorite_type" binding:"required,oneof=course coach store"`
	TargetID     string `json:"target_id" binding:"required"`
}

// FavoriteCheckResponse 收藏检查响应
type FavoriteCheckResponse struct {
	IsFavorited bool `json:"is_favorited"`
}

// Share related DTOs

// ShareResponse 分享响应
type ShareResponse struct {
	Type        string `json:"type"`        // 分享类型：course, coach, store
	Title       string `json:"title"`       // 分享标题
	Description string `json:"description"` // 分享描述
	Image       string `json:"image"`       // 分享图片
	URL         string `json:"url"`         // 分享链接（小程序页面路径）
	ShareText   string `json:"share_text"`  // 分享文案
}

// ShareStatsResponse 分享统计响应
type ShareStatsResponse struct {
	TotalShares int `json:"total_shares"` // 总分享次数
	TodayShares int `json:"today_shares"` // 今日分享次数
}

// FavoriteItem 收藏项目
type FavoriteItem struct {
	ID           uint                `json:"id"`
	FavoriteType string              `json:"favorite_type"`
	TargetID     string              `json:"target_id"`
	CreatedAt    string              `json:"created_at"`
	CourseInfo   *FavoriteCourseInfo `json:"course_info,omitempty"`
	CoachInfo    *FavoriteCoachInfo  `json:"coach_info,omitempty"`
	StoreInfo    *FavoriteStoreInfo  `json:"store_info,omitempty"`
}

// FavoriteCourseInfo 收藏的课程信息
type FavoriteCourseInfo struct {
	ID          uint    `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Duration    int     `json:"duration"`
	Price       float64 `json:"price"`
	CoachName   string  `json:"coach_name"`
	StoreName   string  `json:"store_name"`
}

// FavoriteCoachInfo 收藏的教练信息
type FavoriteCoachInfo struct {
	ID          string `json:"id"` // 改为string以支持UUID
	Name        string `json:"name"`
	Avatar      string `json:"avatar"` // 统一使用avatar
	Experience  int    `json:"experience"`
	Speciality  string `json:"speciality"`
	Description string `json:"description"`
}

// FavoriteStoreInfo 收藏的门店信息
type FavoriteStoreInfo struct {
	ID          uint     `json:"id"`
	Name        string   `json:"name"`
	Address     string   `json:"address"`
	Phone       string   `json:"phone"`
	Images      []string `json:"images"` // 修改为字符串数组
	Description string   `json:"description"`
}

// App Course related DTOs

// AppCourseItem 小程序端课程列表项
type AppCourseItem struct {
	ID             uint    `json:"id"`
	Name           string  `json:"name"`
	Description    string  `json:"description"`
	StartTime      string  `json:"start_time"`
	EndTime        string  `json:"end_time"`
	Duration       int     `json:"duration"`
	Capacity       int     `json:"capacity"`
	EnrollCount    int     `json:"enroll_count"`
	AvailableSpots int     `json:"available_spots"`
	Price          float64 `json:"price"`
	Type           int     `json:"type"`
	CoachName      string  `json:"coach_name"`
	CoachAvatar    string  `json:"coach_avatar"`
	StoreName      string  `json:"store_name"`
	StoreAddress   string  `json:"store_address"`
	CanBook        bool    `json:"can_book"`
}

// AppCourseDetail 小程序端课程详情
type AppCourseDetail struct {
	ID             uint         `json:"id"`
	Name           string       `json:"name"`
	Description    string       `json:"description"`
	StartTime      string       `json:"start_time"`
	EndTime        string       `json:"end_time"`
	Duration       int          `json:"duration"`
	Capacity       int          `json:"capacity"`
	EnrollCount    int          `json:"enroll_count"`
	AvailableSpots int          `json:"available_spots"`
	Price          float64      `json:"price"`
	Type           int          `json:"type"`
	Status         int          `json:"status"`
	CoachInfo      AppCoachInfo `json:"coach_info"`
	StoreInfo      AppStoreInfo `json:"store_info"`
	CanBook        bool         `json:"can_book"`
}

// AppCoachInfo 小程序端教练信息
type AppCoachInfo struct {
	ID          string `json:"id"` // 改为string以支持UUID
	Name        string `json:"name"`
	Avatar      string `json:"avatar"` // 统一使用avatar
	Experience  int    `json:"experience"`
	Speciality  string `json:"speciality"`
	Description string `json:"description"`
}

// CoachInfo 教练列表信息
type CoachInfo struct {
	ID          string `json:"id"` // 改为string以支持UUID
	NickName    string `json:"nick_name"`
	Avatar      string `json:"avatar"`
	Gender      int    `json:"gender"`
	Description string `json:"description"`
	Experience  int    `json:"experience"`
	Specialty   string `json:"specialty"`
}

// CoachDetail 教练详情信息
type CoachDetail struct {
	ID          string `json:"id"` // 改为string以支持UUID
	NickName    string `json:"nick_name"`
	Avatar      string `json:"avatar"`
	Gender      int    `json:"gender"`
	Description string `json:"description"`
	Experience  int    `json:"experience"`
	Specialty   string `json:"specialty"`
	Country     string `json:"country"`
	Province    string `json:"province"`
	City        string `json:"city"`
}

// AdminCoachInfo 后台教练列表信息
type AdminCoachInfo struct {
	ID          string `json:"id"` // 改为string以支持UUID
	Username    string `json:"username"`
	NickName    string `json:"nick_name"`
	Avatar      string `json:"avatar"`
	Gender      int    `json:"gender"`
	Description string `json:"description"`
	Experience  int    `json:"experience"`
	Specialty   string `json:"specialty"`
	IsActive    bool   `json:"is_active"`
	CreatedAt   string `json:"created_at"`
}

// AdminCoachDetail 后台教练详情信息
type AdminCoachDetail struct {
	ID          string `json:"id"` // 改为string以支持UUID
	Username    string `json:"username"`
	NickName    string `json:"nick_name"`
	Avatar      string `json:"avatar"`
	Gender      int    `json:"gender"`
	Description string `json:"description"`
	Experience  int    `json:"experience"`
	Specialty   string `json:"specialty"`
	Country     string `json:"country"`
	Province    string `json:"province"`
	City        string `json:"city"`
	IsActive    bool   `json:"is_active"`
	CreatedAt   string `json:"created_at"`
	UpdatedAt   string `json:"updated_at"`
}

// UpdateCoachRequest 更新教练信息请求
type UpdateCoachRequest struct {
	NickName    string `json:"nick_name" binding:"required"`
	Description string `json:"description"`
	Experience  int    `json:"experience"`
	Specialty   string `json:"specialty"`
	Avatar      string `json:"avatar"`
	Gender      int    `json:"gender"`
	Country     string `json:"country"`
	Province    string `json:"province"`
	City        string `json:"city"`
}

// UpdateCoachStatusRequest 更新教练状态请求
type UpdateCoachStatusRequest struct {
	IsActive bool `json:"is_active" binding:"required"`
}

// CoachBannerResponse 教练轮播图响应
type CoachBannerResponse struct {
	ID        uint   `json:"id"`
	CoachID   string `json:"coach_id"`
	Title     string `json:"title"`
	Image     string `json:"image"`
	SortOrder int    `json:"sort_order"`
	Status    int    `json:"status"`
	CreatedAt string `json:"created_at"`
	UpdatedAt string `json:"updated_at"`
}

// ActiveCoachBannerResponse 启用的教练轮播图响应
type ActiveCoachBannerResponse struct {
	ID        uint   `json:"id"`
	Title     string `json:"title"`
	Image     string `json:"image"`
	SortOrder int    `json:"sort_order"`
}

// StoreResponse 门店响应
type StoreResponse struct {
	ID          uint     `json:"id"`
	CreatedAt   string   `json:"created_at"`
	UpdatedAt   string   `json:"updated_at"`
	DeletedAt   *string  `json:"deleted_at"`
	Name        string   `json:"name"`
	Address     string   `json:"address"`
	Phone       string   `json:"phone"`
	Latitude    float64  `json:"latitude"`
	Longitude   float64  `json:"longitude"`
	Description string   `json:"description"`
	SortOrder   int      `json:"sort_order"`
	Status      int      `json:"status"`
	Images      []string `json:"images"`
}

// UpdateFields 通用更新字段结构体，用于替代map[string]interface{}
type UpdateFields struct {
	Fields map[string]interface{} `json:"-"`
}

// NewUpdateFields 创建新的更新字段实例
func NewUpdateFields() *UpdateFields {
	return &UpdateFields{
		Fields: make(map[string]interface{}),
	}
}

// Set 设置字段值
func (u *UpdateFields) Set(key string, value interface{}) *UpdateFields {
	u.Fields[key] = value
	return u
}

// SetIfNotEmpty 如果值不为空则设置字段
func (u *UpdateFields) SetIfNotEmpty(key string, value string) *UpdateFields {
	if value != "" {
		u.Fields[key] = value
	}
	return u
}

// SetIfNotNil 如果指针不为nil则设置字段
func (u *UpdateFields) SetIfNotNil(key string, value interface{}) *UpdateFields {
	if value != nil {
		u.Fields[key] = value
	}
	return u
}

// GetFields 获取字段映射
func (u *UpdateFields) GetFields() map[string]interface{} {
	return u.Fields
}

// HasFields 检查是否有字段需要更新
func (u *UpdateFields) HasFields() bool {
	return len(u.Fields) > 0
}

// AdminScheduleInfo 后台排课信息
type AdminScheduleInfo struct {
	ID        uint    `json:"id"`
	Name      string  `json:"name"`
	StartTime string  `json:"start_time"`
	EndTime   string  `json:"end_time"`
	Duration  int     `json:"duration"`
	Capacity  int     `json:"capacity"`
	Price     float64 `json:"price"`
	Status    int     `json:"status"`
	CoachName string  `json:"coach_name"`
	StoreName string  `json:"store_name"`
}

// CreateScheduleRequest 创建排课请求
type CreateScheduleRequest struct {
	Name        string    `json:"name" binding:"required"`
	Description string    `json:"description"`
	StartTime   time.Time `json:"start_time" binding:"required"`
	EndTime     time.Time `json:"end_time" binding:"required"`
	Duration    int       `json:"duration" binding:"required"`
	Capacity    int       `json:"capacity" binding:"required"`
	Price       float64   `json:"price" binding:"required"`
	CoachID     string    `json:"coach_id" binding:"required,len=36"`
	StoreID     uint      `json:"store_id" binding:"required"`
	ClassRoomID *uint     `json:"class_room_id"`
	Type        int       `json:"type" binding:"required"`
}

// UpdateScheduleRequest 更新排课请求
type UpdateScheduleRequest struct {
	Name        string     `json:"name"`
	Description string     `json:"description"`
	StartTime   *time.Time `json:"start_time"`
	EndTime     *time.Time `json:"end_time"`
	Duration    *int       `json:"duration"`
	Capacity    *int       `json:"capacity"`
	Price       *float64   `json:"price"`
	Status      *int       `json:"status"`
}

// AdminBookingInfo 后台预约列表信息
type AdminBookingInfo struct {
	ID            uint    `json:"id"`
	UserName      string  `json:"user_name"`
	CourseName    string  `json:"course_name"`
	CoachName     string  `json:"coach_name"`
	StoreName     string  `json:"store_name"`
	StartTime     string  `json:"start_time"`
	Status        int     `json:"status"`
	PaymentMethod string  `json:"payment_method"`
	Amount        float64 `json:"amount"`
	CardType      string  `json:"card_type"`
	CreatedAt     string  `json:"created_at"`
}

// AdminBookingDetail 后台预约详情信息
type AdminBookingDetail struct {
	ID            uint    `json:"id"`
	UserName      string  `json:"user_name"`
	UserPhone     string  `json:"user_phone"`
	CourseName    string  `json:"course_name"`
	CoachName     string  `json:"coach_name"`
	StoreName     string  `json:"store_name"`
	StartTime     string  `json:"start_time"`
	EndTime       string  `json:"end_time"`
	Status        int     `json:"status"`
	PaymentMethod string  `json:"payment_method"`
	Amount        float64 `json:"amount"`
	CardType      string  `json:"card_type"`
	CardBalance   float64 `json:"card_balance"`
	CreatedAt     string  `json:"created_at"`
	UpdatedAt     string  `json:"updated_at"`
}

// UpdateBookingStatusRequest 更新预约状态请求
type UpdateBookingStatusRequest struct {
	Status int `json:"status" binding:"required"`
}

// BookingStatusStat 预约状态统计
type BookingStatusStat struct {
	Status int   `json:"status"`
	Count  int64 `json:"count"`
}

// BookingStatsResponse 预约统计响应
type BookingStatsResponse struct {
	StatusStats []BookingStatusStat `json:"status_stats"`
	TodayCount  int64               `json:"today_count"`
}

// CardSalesStat 会员卡销售统计
type CardSalesStat struct {
	CardType    string  `json:"card_type"`
	Count       int64   `json:"count"`
	TotalAmount float64 `json:"total_amount"`
}

// CardSalesReportResponse 会员卡销售报表响应
type CardSalesReportResponse struct {
	StartDate   string          `json:"start_date"`
	EndDate     string          `json:"end_date"`
	CardSales   []CardSalesStat `json:"card_sales"`
	TotalCount  int64           `json:"total_count"`
	TotalAmount float64         `json:"total_amount"`
}

// BookingReportStat 预约报表统计
type BookingReportStat struct {
	Date           string  `json:"date"`
	TotalCount     int64   `json:"total_count"`
	PendingCount   int64   `json:"pending_count"`
	ConfirmedCount int64   `json:"confirmed_count"`
	CompletedCount int64   `json:"completed_count"`
	CancelledCount int64   `json:"cancelled_count"`
	TotalAmount    float64 `json:"total_amount"`
}

// BookingReportResponse 预约报表响应
type BookingReportResponse struct {
	StartDate      string              `json:"start_date"`
	EndDate        string              `json:"end_date"`
	DailyStats     []BookingReportStat `json:"daily_stats"`
	TotalBookings  int64               `json:"total_bookings"`
	TotalPending   int64               `json:"total_pending"`
	TotalConfirmed int64               `json:"total_confirmed"`
	TotalCompleted int64               `json:"total_completed"`
	TotalCancelled int64               `json:"total_cancelled"`
	TotalRevenue   float64             `json:"total_revenue"`
}

// CourseReportStat 课程报表统计
type CourseReportStat struct {
	CourseName     string  `json:"course_name"`
	CoachName      string  `json:"coach_name"`
	StoreName      string  `json:"store_name"`
	BookingCount   int64   `json:"booking_count"`
	CompletedCount int64   `json:"completed_count"`
	AvgRating      float64 `json:"avg_rating"`
}

// CourseReportResponse 课程报表响应
type CourseReportResponse struct {
	StartDate   string             `json:"start_date"`
	EndDate     string             `json:"end_date"`
	CourseStats []CourseReportStat `json:"course_stats"`
}

// CoachReportStat 教练报表统计
type CoachReportStat struct {
	CoachName      string  `json:"coach_name"`
	CourseCount    int64   `json:"course_count"`
	BookingCount   int64   `json:"booking_count"`
	CompletedCount int64   `json:"completed_count"`
	AvgRating      float64 `json:"avg_rating"`
	TotalRevenue   float64 `json:"total_revenue"`
}

// CoachReportResponse 教练报表响应
type CoachReportResponse struct {
	StartDate  string            `json:"start_date"`
	EndDate    string            `json:"end_date"`
	CoachStats []CoachReportStat `json:"coach_stats"`
}

// QRCodeData 二维码数据
type QRCodeData struct {
	Type        string  `json:"type"`         // 二维码类型
	CourseID    uint    `json:"course_id"`    // 课程ID
	CheckinCode string  `json:"checkin_code"` // 签到码
	StoreID     uint    `json:"store_id"`     // 门店ID
	Latitude    float64 `json:"latitude"`     // 门店纬度
	Longitude   float64 `json:"longitude"`    // 门店经度
	ExpireTime  int64   `json:"expire_time"`  // 过期时间戳
}

// QRCodeResponse 二维码响应
type QRCodeResponse struct {
	QRData     QRCodeData `json:"qr_data"`
	ExpireTime string     `json:"expire_time"`
}

// CheckinRequest 签到请求
type CheckinRequest struct {
	QRData    QRCodeData `json:"qr_data" binding:"required"`
	Latitude  float64    `json:"latitude" binding:"required"`  // 用户当前纬度
	Longitude float64    `json:"longitude" binding:"required"` // 用户当前经度
}

// CheckinResponse 签到响应
type CheckinResponse struct {
	Message     string `json:"message"`
	CheckinTime string `json:"checkin_time"`
}

// CheckinStatusResponse 签到状态响应
type CheckinStatusResponse struct {
	HasCheckedIn bool   `json:"has_checked_in"`
	CheckinTime  string `json:"checkin_time"`
}

// JoinQueueRequest 加入排队请求
type JoinQueueRequest struct {
	CourseID uint `json:"course_id" binding:"required"`
}

// JoinQueueResponse 加入排队响应
type JoinQueueResponse struct {
	QueueID     uint   `json:"queue_id"`
	QueueNumber int    `json:"queue_number"`
	Message     string `json:"message"`
}

// QueueStatusResponse 排队状态响应
type QueueStatusResponse struct {
	QueueID         uint `json:"queue_id"`
	QueueNumber     int  `json:"queue_number"`
	CurrentPosition int  `json:"current_position"`
	TotalCapacity   int  `json:"total_capacity"`
	CurrentBookings int  `json:"current_bookings"`
	AvailableSpots  int  `json:"available_spots"`
}

// UserQueueInfo 用户排队信息
type UserQueueInfo struct {
	QueueID         uint   `json:"queue_id"`
	CourseID        uint   `json:"course_id"`
	CourseName      string `json:"course_name"`
	CoachName       string `json:"coach_name"`
	StoreName       string `json:"store_name"`
	StartTime       string `json:"start_time"`
	QueueNumber     int    `json:"queue_number"`
	CurrentPosition int    `json:"current_position"`
	Status          int    `json:"status"`
	CreatedAt       string `json:"created_at"`
	NotifiedAt      string `json:"notified_at"`
}

// AppStoreInfo 小程序端门店信息
type AppStoreInfo struct {
	ID        uint    `json:"id"`
	Name      string  `json:"name"`
	Address   string  `json:"address"`
	Phone     string  `json:"phone"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// DateCoursesResponse 日期课程响应
type DateCoursesResponse struct {
	Date    string          `json:"date"`
	Courses []AppCourseItem `json:"courses"`
	Total   int             `json:"total"`
}

// CourseCalendarResponse 课程日历响应
type CourseCalendarResponse struct {
	Month    string         `json:"month"`
	Calendar map[string]int `json:"calendar"` // 日期 -> 课程数量
}

// User Center related DTOs

// UserProfile 用户资料
type UserProfile struct {
	ID       string `json:"id"` // 改为string以支持UUID
	Username string `json:"username"`
	NickName string `json:"nick_name"`
	Avatar   string `json:"avatar"`
	Gender   int    `json:"gender"`
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	IsActive bool   `json:"is_active"`
}

// UpdateUserProfileRequest 更新用户资料请求
type UpdateUserProfileRequest struct {
	NickName string `json:"nick_name" binding:"omitempty,max=50"`
	Avatar   string `json:"avatar" binding:"omitempty,max=255"`
	Gender   *int   `json:"gender" binding:"omitempty,oneof=0 1 2"`
	Country  string `json:"country" binding:"omitempty,max=50"`
	Province string `json:"province" binding:"omitempty,max=50"`
	City     string `json:"city" binding:"omitempty,max=50"`
}

// UserStatsInfo 用户统计信息
type UserStatsInfo struct {
	BookingStats    UserBookingStatsInfo `json:"booking_stats"`
	MembershipStats MembershipStats      `json:"membership_stats"`
	FavoriteStats   FavoriteStats        `json:"favorite_stats"`
}

// UserBookingStatsInfo 用户预约统计信息
type UserBookingStatsInfo struct {
	Total     int `json:"total"`
	Completed int `json:"completed"`
	Cancelled int `json:"cancelled"`
}

// MembershipStats 会员卡统计
type MembershipStats struct {
	TotalCards  int `json:"total_cards"`
	ActiveCards int `json:"active_cards"`
}

// FavoriteStats 收藏统计
type FavoriteStats struct {
	Total int `json:"total"`
}

// MembershipCardSummary 会员卡汇总
type MembershipCardSummary struct {
	ID              uint    `json:"id"`
	CardNumber      string  `json:"card_number"`
	TypeName        string  `json:"type_name"`
	Category        string  `json:"category"`
	RemainingTimes  int     `json:"remaining_times"`
	RemainingAmount int     `json:"remaining_amount"`
	StartDate       string  `json:"start_date"`
	EndDate         *string `json:"end_date,omitempty"`
	Status          int     `json:"status"`
	IsExpired       bool    `json:"is_expired"`
}

// UserMembershipSummary 用户会员卡汇总
type UserMembershipSummary struct {
	Cards []MembershipCardSummary `json:"cards"`
	Total int                     `json:"total"`
}

// RecentBookingItem 最近预约项目
type RecentBookingItem struct {
	ID         uint   `json:"id"`
	CourseName string `json:"course_name"`
	CoachName  string `json:"coach_name"`
	StoreName  string `json:"store_name"`
	StartTime  string `json:"start_time"`
	Status     int    `json:"status"`
	StatusText string `json:"status_text"`
}

// Staff Management DTOs (基于现有users和roles表)

// UpdateStaffStatusRequest 更新员工状态请求
type UpdateStaffStatusRequest struct {
	IsActive bool `json:"is_active" binding:"required"`
}

// Resource Assignment DTOs

// AssignMembersRequest 分配会员给销售请求
type AssignMembersRequest struct {
	SalesStaffID string   `json:"sales_staff_id" binding:"required,len=36"`
	MemberIDs    []string `json:"member_ids" binding:"required,min=1"`
	Remark       string   `json:"remark" binding:"max=500"`
}

// TransferMemberRequest 转移会员请求
type TransferMemberRequest struct {
	FromSalesStaffID   string `json:"from_sales_staff_id" binding:"required,len=36"`
	TargetSalesStaffID string `json:"target_sales_staff_id" binding:"required,len=36"`
	MemberID           string `json:"member_id" binding:"required,len=36"`
	Remark             string `json:"remark" binding:"max=500"`
}

// RemoveMemberAssignmentRequest 移除会员分配请求
type RemoveMemberAssignmentRequest struct {
	SalesStaffID string `json:"sales_staff_id" binding:"required,len=36"`
	MemberID     string `json:"member_id" binding:"required,len=36"`
}

// User Store Assignment DTOs

// AssignUserToStoreRequest 分配用户到门店请求
type AssignUserToStoreRequest struct {
	UserID  string `json:"user_id" binding:"required"` // 改为string以支持UUID
	StoreID uint   `json:"store_id" binding:"required"`
	Remark  string `json:"remark" binding:"max=500"`
}

// RemoveUserStoreAssignmentRequest 移除用户门店分配请求
type RemoveUserStoreAssignmentRequest struct {
	UserID  string `json:"user_id" binding:"required"` // 改为string以支持UUID
	StoreID uint   `json:"store_id" binding:"required"`
}

// UpdateUserPlatformAccessRequest 更新用户平台访问权限请求
type UpdateUserPlatformAccessRequest struct {
	PlatformAccess string `json:"platform_access" binding:"required"`
}

// Operation Log DTOs

// CleanLogsRequest 清理日志请求
type CleanLogsRequest struct {
	KeepDays int `json:"keep_days" binding:"min=1,max=365"` // 保留天数，1-365天
}

// Course Review DTOs

// CourseReviewListRequest 课程评价列表请求
type CourseReviewListRequest struct {
	CourseID  uint   `json:"course_id"`
	CoachID   string `json:"coach_id" binding:"omitempty,len=36"`
	StoreID   uint   `json:"store_id"`
	Rating    int    `json:"rating"`     // 评分筛选 1-5
	StartDate string `json:"start_date"` // 开始日期
	EndDate   string `json:"end_date"`   // 结束日期
	Page      int    `json:"page"`
	PageSize  int    `json:"page_size"`
}

// CourseReviewResponse 课程评价响应
type CourseReviewResponse struct {
	ID         uint   `json:"id"`
	BookingID  uint   `json:"booking_id"`
	UserID     string `json:"user_id"`
	UserName   string `json:"user_name"`
	UserPhone  string `json:"user_phone"`
	CourseID   uint   `json:"course_id"`
	CourseName string `json:"course_name"`
	CoachID    string `json:"coach_id"`
	CoachName  string `json:"coach_name"`
	StoreID    uint   `json:"store_id"`
	StoreName  string `json:"store_name"`
	Rating     int    `json:"rating"`
	Comment    string `json:"comment"`
	CreatedAt  string `json:"created_at"`
	UpdatedAt  string `json:"updated_at"`
}
