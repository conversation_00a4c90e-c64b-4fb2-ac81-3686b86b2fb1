#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理数据库中不用的模型表
"""

import psycopg2
import yaml
import os
from typing import List, Dict

class DatabaseTableCleaner:
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.db_config = None
        self.connection = None

        # 当前系统中使用的表（根据模型定义）
        self.used_tables = {
            # 核心业务表
            'users',
            'roles',
            'permissions',
            'menus',
            'stores',
            'files',

            # 课程相关
            'course_categories',
            'courses',
            # 'course_deduction_rules', # 已废弃，使用灵活扣减系统
            'course_type_configs',
            'flexible_deduction_rules',
            'class_rooms',
            'class_schedules',

            # 会员卡相关
            'membership_card_types',
            'membership_cards',
            'membership_card_transactions',
            'membership_card_leaves',

            # 预约相关
            'bookings',
            'booking_queues',
            'booking_applications',

            # 其他功能
            'admin_notifications',
            'banners',
            'coach_banners',
            'favorites',
            'operation_logs',

            # 分配关系表
            'user_store_assignments',
            'member_sales_assignments',
            'resource_assignments',

            # 系统表
            'migration_status',
        }

        # 已知的废弃表（可以安全删除）
        self.deprecated_tables = {
            # 旧的副卡相关表
            'membership_sub_cards',
            'membership_card_extensions',

            # 旧的测试表
            'test_tables',
            'temp_tables',

            # 旧的legacy表（如果确认不用）
            # 注意：legacy表可能还在使用，需要谨慎
        }

    def load_config(self) -> bool:
        """加载数据库配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)

            db_config = config.get('Database', {})
            self.db_config = {
                'host': db_config.get('Host', 'localhost'),
                'port': db_config.get('Port', 5432),  # PostgreSQL默认端口
                'user': db_config.get('UserName', ''),
                'password': db_config.get('Password', ''),
                'dbname': db_config.get('DBName', ''),  # PostgreSQL使用dbname
            }

            print(f"✅ 配置加载成功: {self.db_config['host']}:{self.db_config['port']}/{self.db_config['dbname']}")
            return True

        except Exception as e:
            print(f"❌ 加载配置失败: {e}")
            return False

    def connect_database(self) -> bool:
        """连接数据库"""
        try:
            self.connection = psycopg2.connect(**self.db_config)
            print("✅ 数据库连接成功")
            return True
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False

    def get_all_tables(self) -> List[str]:
        """获取数据库中所有表"""
        try:
            cursor = self.connection.cursor()
            # PostgreSQL查询所有表
            cursor.execute("""
                SELECT tablename
                FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)
            tables = [table[0] for table in cursor.fetchall()]
            cursor.close()

            print(f"📋 数据库中共有 {len(tables)} 个表")
            return tables

        except Exception as e:
            print(f"❌ 获取表列表失败: {e}")
            return []

    def analyze_tables(self, all_tables: List[str]) -> Dict:
        """分析表的使用情况"""
        print("\n🔍 分析表的使用情况...")

        analysis = {
            'used': [],           # 正在使用的表
            'unused': [],         # 未使用的表
            'deprecated': [],     # 已废弃的表
            'legacy': [],         # Legacy表
            'unknown': []         # 未知用途的表
        }

        for table in all_tables:
            if table in self.used_tables:
                analysis['used'].append(table)
                print(f"✅ {table} - 正在使用")
            elif table in self.deprecated_tables:
                analysis['deprecated'].append(table)
                print(f"🗑️ {table} - 已废弃")
            elif table.startswith('tbl_'):
                analysis['legacy'].append(table)
                print(f"📦 {table} - Legacy表")
            else:
                analysis['unknown'].append(table)
                print(f"❓ {table} - 未知用途")

        return analysis

    def get_table_info(self, table_name: str) -> Dict:
        """获取表的详细信息"""
        try:
            cursor = self.connection.cursor()

            # 获取表结构（列数）
            cursor.execute("""
                SELECT COUNT(*)
                FROM information_schema.columns
                WHERE table_name = %s AND table_schema = 'public'
            """, (table_name,))
            column_count = cursor.fetchone()[0]

            # 获取行数
            cursor.execute(f'SELECT COUNT(*) FROM "{table_name}"')
            row_count = cursor.fetchone()[0]

            # 获取表大小（PostgreSQL）
            cursor.execute("""
                SELECT
                    ROUND(pg_total_relation_size(%s) / 1024.0 / 1024.0, 2) AS size_mb
            """, (table_name,))

            size_result = cursor.fetchone()
            size_mb = size_result[0] if size_result else 0

            cursor.close()

            return {
                'columns': column_count,
                'rows': row_count,
                'size_mb': float(size_mb) if size_mb else 0
            }

        except Exception as e:
            print(f"❌ 获取表 {table_name} 信息失败: {e}")
            return {'columns': 0, 'rows': 0, 'size_mb': 0}

    def generate_cleanup_sql(self, analysis: Dict) -> str:
        """生成清理SQL"""
        print("\n📝 生成清理SQL...")

        sql_statements = []
        sql_statements.append("-- 数据库表清理SQL")
        sql_statements.append("-- 执行前请务必备份数据库！")
        sql_statements.append("")

        # 处理已废弃的表
        if analysis['deprecated']:
            sql_statements.append("-- 1. 删除已确认废弃的表")
            for table in analysis['deprecated']:
                info = self.get_table_info(table)
                sql_statements.append(f"-- 表 {table}: {info['rows']} 行, {info['size_mb']} MB")
                sql_statements.append(f"DROP TABLE IF EXISTS `{table}`;")
            sql_statements.append("")

        # 处理未知用途的表（需要手动确认）
        if analysis['unknown']:
            sql_statements.append("-- 2. 未知用途的表（需要手动确认后删除）")
            for table in analysis['unknown']:
                info = self.get_table_info(table)
                sql_statements.append(f"-- 表 {table}: {info['rows']} 行, {info['size_mb']} MB")
                sql_statements.append(f"-- DROP TABLE IF EXISTS `{table}`;  -- 请确认后取消注释")
            sql_statements.append("")

        # Legacy表的处理建议
        if analysis['legacy']:
            sql_statements.append("-- 3. Legacy表（建议保留，除非确认不再需要）")
            total_legacy_size = 0
            for table in analysis['legacy']:
                info = self.get_table_info(table)
                total_legacy_size += info['size_mb']
                sql_statements.append(f"-- 表 {table}: {info['rows']} 行, {info['size_mb']} MB")
                sql_statements.append(f"-- DROP TABLE IF EXISTS `{table}`;  -- Legacy表，谨慎删除")
            sql_statements.append(f"-- Legacy表总大小: {total_legacy_size:.2f} MB")
            sql_statements.append("")

        return "\n".join(sql_statements)

    def print_summary(self, analysis: Dict):
        """打印分析摘要"""
        print("\n" + "=" * 80)
        print("📊 数据库表分析摘要")
        print("=" * 80)

        print(f"✅ 正在使用的表: {len(analysis['used'])} 个")
        for table in analysis['used']:
            info = self.get_table_info(table)
            print(f"   {table}: {info['rows']} 行, {info['size_mb']} MB")

        print(f"\n🗑️ 已废弃的表: {len(analysis['deprecated'])} 个")
        deprecated_size = 0
        for table in analysis['deprecated']:
            info = self.get_table_info(table)
            deprecated_size += info['size_mb']
            print(f"   {table}: {info['rows']} 行, {info['size_mb']} MB")

        print(f"\n❓ 未知用途的表: {len(analysis['unknown'])} 个")
        unknown_size = 0
        for table in analysis['unknown']:
            info = self.get_table_info(table)
            unknown_size += info['size_mb']
            print(f"   {table}: {info['rows']} 行, {info['size_mb']} MB")

        print(f"\n📦 Legacy表: {len(analysis['legacy'])} 个")
        legacy_size = 0
        for table in analysis['legacy']:
            info = self.get_table_info(table)
            legacy_size += info['size_mb']
            print(f"   {table}: {info['rows']} 行, {info['size_mb']} MB")

        print(f"\n💾 存储空间统计:")
        print(f"   可安全删除: {deprecated_size:.2f} MB")
        print(f"   需确认删除: {unknown_size:.2f} MB")
        print(f"   Legacy表: {legacy_size:.2f} MB")
        print(f"   潜在可释放空间: {deprecated_size + unknown_size:.2f} MB")

    def run_cleanup_analysis(self):
        """运行清理分析"""
        print("🧹 开始数据库表清理分析...")
        print("=" * 80)

        # 1. 加载配置
        if not self.load_config():
            return False

        # 2. 连接数据库
        if not self.connect_database():
            return False

        try:
            # 3. 获取所有表
            all_tables = self.get_all_tables()
            if not all_tables:
                return False

            # 4. 分析表
            analysis = self.analyze_tables(all_tables)

            # 5. 生成清理SQL
            cleanup_sql = self.generate_cleanup_sql(analysis)

            # 6. 保存SQL到文件
            sql_file = "scripts/cleanup_unused_tables.sql"
            try:
                with open(sql_file, 'w', encoding='utf-8') as f:
                    f.write(cleanup_sql)
                print(f"\n📄 清理SQL已保存到: {sql_file}")
            except Exception as e:
                print(f"❌ 保存SQL文件失败: {e}")

            # 7. 打印摘要
            self.print_summary(analysis)

            print("\n" + "=" * 80)
            print("🎯 清理建议:")
            print("1. 请先备份数据库")
            print(f"2. 检查生成的SQL文件: {sql_file}")
            print("3. 确认要删除的表后执行SQL")
            print("4. Legacy表建议保留，除非确认不再需要")

            return True

        finally:
            if self.connection:
                self.connection.close()
                print("\n🔌 数据库连接已关闭")

def main():
    """主函数"""
    cleaner = DatabaseTableCleaner()
    success = cleaner.run_cleanup_analysis()

    if success:
        print("\n🏆 数据库表清理分析完成！")
        exit(0)
    else:
        print("\n💥 数据库表清理分析失败！")
        exit(1)

if __name__ == "__main__":
    main()
