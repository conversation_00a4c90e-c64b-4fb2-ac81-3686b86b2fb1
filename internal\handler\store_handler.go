package handler

import (
	"context"
	"math"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/lib/pq"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// StoreHandler 门店处理器
type StoreHandler struct {
	db      *gorm.DB
	storage storage.Storage
}

// NewStoreHandler 创建门店处理器实例
func NewStoreHandler(db *gorm.DB, storage storage.Storage) *StoreHandler {
	return &StoreHandler{
		db:      db,
		storage: storage,
	}
}

// convertFileIDsToURLs 将文件ID数组转换为MinIO预签名URL数组
func (h *StoreHandler) convertFileIDsToURLs(fileIDs []string) []string {
	if len(fileIDs) == 0 || h.db == nil || h.storage == nil {
		return []string{}
	}

	// 使用批量优化函数
	urlMap, err := BatchLoadDirectFileURLs(context.Background(), h.db, h.storage, fileIDs)
	if err != nil {
		log.Error("批量生成文件URL失败", "error", err)
		return []string{}
	}

	// 按原始顺序返回URL数组
	urls := make([]string, 0, len(fileIDs))
	for _, fileID := range fileIDs {
		if url, exists := urlMap[fileID]; exists {
			urls = append(urls, url)
		}
	}

	return urls
}

// convertStringArrayToURLs 将pq.StringArray转换为URL数组
func (h *StoreHandler) convertStringArrayToURLs(fileIDs pq.StringArray) []string {
	if len(fileIDs) == 0 {
		return []string{}
	}
	return h.convertFileIDsToURLs([]string(fileIDs))
}

// GetStores 获取门店列表（优化版本，批量预生成图片URL）
func (h *StoreHandler) GetStores(c *gin.Context) {
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Store{})

	// 状态筛选
	status := c.Query("status")
	if status != "" {
		if statusInt := cast.ToInt(status); statusInt > 0 {
			storeStatus := enum.StoreStatus(statusInt)
			if storeStatus.IsValid() {
				query = query.Where("status = ?", storeStatus)
			}
		}
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ? OR address LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询
	var stores []model.Store
	err := query.Order("sort_order ASC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&stores).Error

	if err != nil {
		log.Error("查询门店失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询门店失败"))
		return
	}

	// 为每个门店动态生成images，隐藏image_ids
	storeList := h.buildStoreListWithImageURLs(stores)

	// 构建响应
	response := dto.PageResponse{
		List:     storeList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// buildStoreListWithImageURLs 为门店列表动态生成images，隐藏image_ids
func (h *StoreHandler) buildStoreListWithImageURLs(stores []model.Store) []dto.StoreResponse {
	if len(stores) == 0 {
		return []dto.StoreResponse{}
	}

	result := make([]dto.StoreResponse, len(stores))
	for i, store := range stores {
		var deletedAt *string
		if store.DeletedAt.Valid {
			deletedAtStr := store.DeletedAt.Time.Format("2006-01-02 15:04:05")
			deletedAt = &deletedAtStr
		}

		result[i] = dto.StoreResponse{
			ID:          store.ID,
			CreatedAt:   store.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   store.UpdatedAt.Format("2006-01-02 15:04:05"),
			DeletedAt:   deletedAt,
			Name:        store.Name,
			Address:     store.Address,
			Phone:       store.Phone,
			Latitude:    store.Latitude,
			Longitude:   store.Longitude,
			Description: store.Description,
			SortOrder:   store.SortOrder,
			Status:      int(store.Status),
			Images:      h.convertStringArrayToURLs(store.ImageIDs),
		}
	}

	return result
}

// buildStoreListWithDistance 批量构建带距离信息的门店列表（简化版本）
func (h *StoreHandler) buildStoreListWithDistance(stores []model.Store, userLat, userLng float64, hasLocation bool) []dto.StoreWithDistance {
	if len(stores) == 0 {
		return []dto.StoreWithDistance{}
	}

	// 构建结果，统一处理图片URL
	result := make([]dto.StoreWithDistance, len(stores))
	for i, store := range stores {
		storeInfo := dto.StoreWithDistance{
			ID:          store.ID,
			Name:        store.Name,
			Address:     store.Address,
			Phone:       store.Phone,
			Latitude:    store.Latitude,
			Longitude:   store.Longitude,
			ImageURLs:   h.convertStringArrayToURLs(store.ImageIDs), // 统一转换图片URL
			Description: store.Description,
			Status:      int(store.Status),
			SortOrder:   store.SortOrder,
		}

		// 计算距离
		if hasLocation && store.Latitude != 0 && store.Longitude != 0 {
			distance := h.calculateDistance(userLat, userLng, store.Latitude, store.Longitude)
			storeInfo.Distance = &distance
		}

		result[i] = storeInfo
	}

	return result
}

// GetStoresWithDistance 获取门店列表（带距离计算）
func (h *StoreHandler) GetStoresWithDistance(c *gin.Context) {
	// 获取用户位置
	latStr := c.Query("latitude")
	lngStr := c.Query("longitude")

	var userLat, userLng float64
	var err error

	if latStr != "" && lngStr != "" {
		userLat = cast.ToFloat64(latStr)
		if userLat == 0 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "纬度格式错误"))
			return
		}

		userLng = cast.ToFloat64(lngStr)
		if userLng == 0 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "经度格式错误"))
			return
		}
	}

	// 查询所有启用的门店
	var stores []model.Store
	err = h.db.Where("status = 1").Order("sort_order ASC").Find(&stores).Error
	if err != nil {
		log.Error("查询门店失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询门店失败"))
		return
	}

	// 构建响应，包含距离信息和图片URL
	storeList := h.buildStoreListWithDistance(stores, userLat, userLng, latStr != "" && lngStr != "")

	// 如果有位置信息，按距离排序
	if latStr != "" && lngStr != "" {
		// 这里可以实现按距离排序的逻辑
		// 简单起见，保持原有排序
	}

	code.AutoResponse(c, storeList, nil)
}

// GetStore 获取门店详情
func (h *StoreHandler) GetStore(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("门店ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var store model.Store
	if err := h.db.First(&store, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		} else {
			log.Error("查询门店失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询门店失败"))
		}
		return
	}

	// 转换为DTO格式，包含图片URL
	type StoreResponse struct {
		ID          uint     `json:"id"`
		Name        string   `json:"name"`
		Address     string   `json:"address"`
		Phone       string   `json:"phone"`
		Latitude    float64  `json:"latitude"`
		Longitude   float64  `json:"longitude"`
		Images      []string `json:"images"`
		Description string   `json:"description"`
		SortOrder   int      `json:"sort_order"`
		Status      int      `json:"status"`
		CreatedAt   string   `json:"created_at"`
		UpdatedAt   string   `json:"updated_at"`
	}

	storeResponse := StoreResponse{
		ID:          store.ID,
		Name:        store.Name,
		Address:     store.Address,
		Phone:       store.Phone,
		Latitude:    store.Latitude,
		Longitude:   store.Longitude,
		Images:      h.convertFileIDsToURLs([]string(store.ImageIDs)),
		Description: store.Description,
		SortOrder:   store.SortOrder,
		Status:      int(store.Status),
		CreatedAt:   store.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt:   store.UpdatedAt.Format("2006-01-02 15:04:05"),
	}

	code.AutoResponse(c, storeResponse, nil)
}

// CreateStore 创建门店
func (h *StoreHandler) CreateStore(c *gin.Context) {
	var req dto.CreateStoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建门店参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 创建门店
	store := model.Store{
		Name:        req.Name,
		Address:     req.Address,
		Phone:       req.Phone,
		Latitude:    req.Latitude,
		Longitude:   req.Longitude,
		ImageIDs:    pq.StringArray(req.ImageIDs), // 转换为pq.StringArray
		Description: req.Description,
		SortOrder:   req.SortOrder,
		Status:      enum.StoreStatusOpen, // 默认营业
	}

	if err := h.db.Create(&store).Error; err != nil {
		log.Error("创建门店失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建门店失败"))
		return
	}

	log.Info("创建门店成功", "id", store.ID, "name", store.Name)
	code.AutoResponse(c, store, nil)
}

// UpdateStore 更新门店
func (h *StoreHandler) UpdateStore(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("门店ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateStoreRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新门店参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询门店是否存在
	var store model.Store
	if err := h.db.First(&store, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		} else {
			log.Error("查询门店失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询门店失败"))
		}
		return
	}

	// 更新字段
	updates := dto.NewUpdateFields().
		SetIfNotEmpty("name", req.Name).
		SetIfNotEmpty("address", req.Address).
		SetIfNotEmpty("phone", req.Phone).
		SetIfNotNil("latitude", req.Latitude).
		SetIfNotNil("longitude", req.Longitude).
		SetIfNotEmpty("description", req.Description).
		SetIfNotNil("sort_order", req.SortOrder).
		SetIfNotNil("status", req.Status)

	// 特殊处理ImageIDs
	if req.ImageIDs != nil {
		updates.Set("image_ids", pq.StringArray(req.ImageIDs))
	}

	if !updates.HasFields() {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "没有需要更新的字段"))
		return
	}

	if err := h.db.Model(&store).Updates(updates.GetFields()).Error; err != nil {
		log.Error("更新门店失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新门店失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.First(&store, id)

	log.Info("更新门店成功", "id", id)
	code.AutoResponse(c, store, nil)
}

// DeleteStore 删除门店
func (h *StoreHandler) DeleteStore(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("门店ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 检查是否有关联的课程
	var courseCount int64
	h.db.Model(&model.Course{}).Where("store_id = ?", id).Count(&courseCount)
	if courseCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该门店下还有课程，无法删除"))
		return
	}

	// 检查是否有关联的教室
	var classroomCount int64
	h.db.Model(&model.ClassRoom{}).Where("store_id = ?", id).Count(&classroomCount)
	if classroomCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该门店下还有教室，无法删除"))
		return
	}

	// 删除门店
	if err := h.db.Delete(&model.Store{}, id).Error; err != nil {
		log.Error("删除门店失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除门店失败"))
		return
	}

	log.Info("删除门店成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// calculateDistance 计算两点间距离（单位：公里）
func (h *StoreHandler) calculateDistance(lat1, lng1, lat2, lng2 float64) float64 {
	const earthRadius = 6371 // 地球半径，单位：公里

	// 转换为弧度
	lat1Rad := lat1 * math.Pi / 180
	lng1Rad := lng1 * math.Pi / 180
	lat2Rad := lat2 * math.Pi / 180
	lng2Rad := lng2 * math.Pi / 180

	// 计算差值
	deltaLat := lat2Rad - lat1Rad
	deltaLng := lng2Rad - lng1Rad

	// 使用Haversine公式计算距离
	a := math.Sin(deltaLat/2)*math.Sin(deltaLat/2) +
		math.Cos(lat1Rad)*math.Cos(lat2Rad)*
			math.Sin(deltaLng/2)*math.Sin(deltaLng/2)
	c := 2 * math.Atan2(math.Sqrt(a), math.Sqrt(1-a))

	distance := earthRadius * c
	return math.Round(distance*100) / 100 // 保留两位小数
}
