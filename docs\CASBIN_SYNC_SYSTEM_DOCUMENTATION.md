# 🔄 Casbin 权限同步系统完整文档

## 📋 系统概述

本文档详细说明了 Yogaga 项目中 **权限、角色、菜单与 Casbin 的同步系统**，确保数据库中的权限数据与 Casbin 策略引擎保持实时同步。

## 🏗️ 系统架构

### 双层同步架构

```
┌─────────────────────────────────────────────────────────┐
│                    权限系统架构                          │
├─────────────────────────────────────────────────────────┤
│  应用层：权限检查中间件                                  │
│  ↓                                                      │
│  Casbin 层：策略执行引擎 ←→ 实时同步 ←→ 事务保证          │
│  ↓                                                      │
│  数据库层：权限基础数据                                  │
└─────────────────────────────────────────────────────────┘
```

### 核心组件

1. **数据库层**：存储角色、权限、用户、菜单的基础数据
2. **Casbin 层**：高性能的权限策略执行引擎
3. **同步层**：确保数据库与 Casbin 的实时同步
4. **应用层**：权限检查中间件和业务逻辑

## 🔗 数据关联关系

### 实体关系图

```
用户 (Users)
├── 多对多 → 角色 (Roles)
│   └── 多对多 → 权限 (Permissions)
│       └── 一对多 → 菜单 (Menus) [可选关联]
└── 直接访问 → 菜单 (通过角色权限)
```

### 数据库表结构

| 表名 | 说明 | 关键字段 |
|------|------|----------|
| `users` | 用户表 | `id`, `username`, `platform` |
| `roles` | 角色表 | `id`, `name`, `description` |
| `permissions` | 权限表 | `id`, `name`, `key`, `method` |
| `menus` | 菜单表 | `id`, `title`, `type`, `permission_id` |
| `user_roles` | 用户-角色关联表 | `user_id`, `role_id` |
| `role_permissions` | 角色-权限关联表 | `role_id`, `permission_id` |

### Casbin 策略结构

| 策略类型 | 格式 | 说明 | 示例 |
|----------|------|------|------|
| 权限策略 (p) | `p, role, permission, method` | 角色对权限的访问控制 | `p, admin, user:create, POST` |
| 用户角色 (g) | `g, user, role` | 用户与角色的关联 | `g, admin, admin` |

## 🔄 同步机制详解

### 1. 角色权限同步

#### 触发时机
- 角色权限更新时 (`PUT /api/v1/admin/roles/:id/permissions`)
- 角色删除时 (`DELETE /api/v1/admin/roles/:id`)

#### 同步流程
```go
// 角色权限更新同步
func (h *RoleHandler) UpdatePermissions(c *gin.Context) {
    // 1. 开始数据库事务
    tx := h.db.Begin()

    // 2. 更新数据库关联
    tx.Model(&role).Association("Permissions").Replace(&permissions)

    // 3. 同步到 Casbin
    h.enforcer.RemoveFilteredPolicy(0, role.Name)  // 删除旧策略
    for _, perm := range permissions {
        for _, method := range httpMethods {
            h.enforcer.AddPolicy(role.Name, perm.Key, method)  // 添加新策略
        }
    }
    h.enforcer.SavePolicy()  // 保存策略

    // 4. 提交事务
    tx.Commit()
}
```

#### 策略生成规则
- 每个角色-权限关联生成 4 条 Casbin 策略（GET, POST, PUT, DELETE）
- 格式：`p, {role_name}, {permission_key}, {http_method}`

### 2. 用户角色同步

#### 触发时机
- 用户创建时 (`POST /api/v1/admin/users`)
- 用户角色更新时 (`PUT /api/v1/admin/users/:id/roles`)
- 用户删除时 (`DELETE /api/v1/admin/users/:id`)

#### 同步流程
```go
// 用户创建时同步
func (h *UserHandler) AdminCreateUser(c *gin.Context) {
    // 1. 创建用户到数据库
    tx.Create(&newUser)

    // 2. 同步用户-角色关联到 Casbin
    for _, role := range roles {
        h.enforcer.AddRoleForUser(req.Username, role.Name)
    }
    h.enforcer.SavePolicy()
}

// 用户角色更新时同步
func (h *UserHandler) UpdateUserRoles(c *gin.Context) {
    // 1. 更新数据库关联
    tx.Model(&user).Association("Roles").Replace(&newRoles)

    // 2. 同步到 Casbin
    h.enforcer.DeleteRolesForUser(user.Username)  // 删除旧角色
    for _, role := range newRoles {
        h.enforcer.AddRoleForUser(user.Username, role.Name)  // 添加新角色
    }
    h.enforcer.SavePolicy()
}
```

### 3. 权限更新同步

#### 触发时机
- 权限标识更新时 (`PUT /api/v1/admin/permissions/:id`)

#### 同步流程
```go
// 权限更新时同步 Casbin
func (h *PermissionHandler) Update(c *gin.Context) {
    // 如果权限标识发生变化，需要同步更新 Casbin 策略
    if req.Key != "" && req.Key != oldKey {
        // 获取所有使用该权限的角色
        var roles []model.Role
        tx.Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
           Where("role_permissions.permission_id = ?", permissionID).
           Find(&roles)

        // 为每个角色更新 Casbin 策略
        for _, role := range roles {
            for _, method := range httpMethods {
                h.enforcer.RemovePolicy(role.Name, oldKey, method)
                h.enforcer.AddPolicy(role.Name, req.Key, method)
            }
        }
        h.enforcer.SavePolicy()
    }
}
```

### 4. 菜单权限关联

#### 菜单与权限的关系
- 菜单可以**可选地**关联权限 (`permission_id` 字段)
- 用户通过角色获得权限，进而访问关联的菜单
- 菜单访问逻辑：`用户 → 角色 → 权限 → 菜单`

#### 菜单访问检查
```go
// 获取用户可访问的菜单
func (h *MenuHandler) GetUserMenus(c *gin.Context) {
    // 1. 获取用户角色
    userRoles := getUserRoles(username)

    // 2. 获取角色权限
    var permissions []string
    for _, role := range userRoles {
        rolePerms := h.enforcer.GetPermissionsForUser(role)
        permissions = append(permissions, rolePerms...)
    }

    // 3. 根据权限过滤菜单
    accessibleMenus := filterMenusByPermissions(permissions)

    return accessibleMenus
}
```

## 🛠️ 同步工具

### 1. 完整同步工具 (`sync_casbin_database.go`)

**功能**：完整重建 Casbin 策略，确保数据库与 Casbin 完全同步

**使用场景**：
- 系统初始化
- 数据不一致修复
- 定期同步检查

**执行步骤**：
1. 清理所有 Casbin 策略
2. 从数据库重建角色-权限策略
3. 同步用户-角色关联
4. 保存策略到数据库
5. 验证同步结果

### 2. 详细分析工具 (`detailed_sync_analysis.go`)

**功能**：详细分析数据库与 Casbin 的同步状态

**输出内容**：
- 数据库中的角色-权限关联详情
- 数据库中的用户-角色关联详情
- Casbin 中的权限策略详情
- Casbin 中的用户-角色关联详情
- 一致性检查结果

### 3. 孤立数据检查工具 (`check_orphaned_data.go`)

**功能**：检查数据库中的孤立数据

**检查内容**：
- 指向已删除角色的角色-权限关联
- 指向已删除用户的用户-角色关联
- 指向已删除角色的用户-角色关联

### 4. 孤立数据清理工具 (`cleanup_orphaned_data.go`)

**功能**：清理数据库中的孤立数据

**清理操作**：
```sql
-- 清理孤立的角色-权限关联
DELETE FROM role_permissions
WHERE role_id NOT IN (SELECT id FROM roles WHERE deleted_at IS NULL);

-- 清理孤立的用户-角色关联
DELETE FROM user_roles
WHERE user_id NOT IN (SELECT id FROM users WHERE deleted_at IS NULL);

DELETE FROM user_roles
WHERE role_id NOT IN (SELECT id FROM roles WHERE deleted_at IS NULL);
```

### 5. 功能测试工具 (`comprehensive_sync_check.go`)

**功能**：全面测试同步功能

**测试场景**：
- 创建多个角色并分配不同权限
- 创建用户并分配不同角色组合
- 动态更改用户角色
- 修改角色权限
- 删除角色和用户

## 🔧 处理器同步实现

### RoleHandler 同步实现

```go
type RoleHandler struct {
    db       *gorm.DB
    enforcer *casbin.Enforcer  // 新增 Casbin 执行器
}

// 构造函数更新
func NewRoleHandler(db *gorm.DB, enforcer *casbin.Enforcer) *RoleHandler {
    return &RoleHandler{
        db:       db,
        enforcer: enforcer,
    }
}
```

### UserHandler 同步实现

```go
type UserHandler struct {
    db       *gorm.DB
    jwtSvc   *jwtx.Service
    enforcer *casbin.Enforcer  // 新增 Casbin 执行器
}

// 构造函数更新
func NewUserHandler(db *gorm.DB, jwtSvc *jwtx.Service, enforcer *casbin.Enforcer) *UserHandler {
    return &UserHandler{
        db:       db,
        jwtSvc:   jwtSvc,
        enforcer: enforcer,
    }
}
```

### PermissionHandler 同步实现

```go
type PermissionHandler struct {
    db       *gorm.DB
    enforcer *casbin.Enforcer  // 新增 Casbin 执行器
}

// 构造函数更新
func NewPermissionHandler(db *gorm.DB, enforcer *casbin.Enforcer) *PermissionHandler {
    return &PermissionHandler{
        db:       db,
        enforcer: enforcer,
    }
}
```

## 🚀 API 接口

### 角色管理接口

| 方法 | 路径 | 说明 | 权限要求 |
|------|------|------|----------|
| POST | `/api/v1/admin/roles` | 创建角色 | `role:create` |
| GET | `/api/v1/admin/roles` | 角色列表 | `role:list` |
| GET | `/api/v1/admin/roles/:id` | 角色详情 | `role:get` |
| PUT | `/api/v1/admin/roles/:id` | 更新角色 | `role:update` |
| DELETE | `/api/v1/admin/roles/:id` | 删除角色 | `role:delete` |
| GET | `/api/v1/admin/roles/:id/permissions` | 获取角色权限 | `role:get_permissions` |
| PUT | `/api/v1/admin/roles/:id/permissions` | 更新角色权限 | `role:update_permissions` |

### 用户管理接口

| 方法 | 路径 | 说明 | 权限要求 |
|------|------|------|----------|
| POST | `/api/v1/admin/users` | 创建用户 | `user:create` |
| PUT | `/api/v1/admin/users/:id/roles` | 更新用户角色 | `user:update_roles` |
| DELETE | `/api/v1/admin/users/:id` | 删除用户 | `user:delete` |
| GET | `/api/v1/admin/users/menus` | 获取用户菜单 | JWT 认证 |

### 权限管理接口

| 方法 | 路径 | 说明 | 权限要求 |
|------|------|------|----------|
| GET | `/api/v1/admin/permissions` | 权限列表 | `permission:list` |
| POST | `/api/v1/admin/permissions` | 创建权限 | `permission:create` |
| GET | `/api/v1/admin/permissions/:id` | 权限详情 | `permission:view` |
| PUT | `/api/v1/admin/permissions/:id` | 更新权限 | `permission:update` |
| DELETE | `/api/v1/admin/permissions/:id` | 删除权限 | `permission:delete` |

### 菜单管理接口

| 方法 | 路径 | 说明 | 权限要求 |
|------|------|------|----------|
| POST | `/api/v1/admin/menus` | 创建菜单 | `menu:create` |
| GET | `/api/v1/admin/menus` | 菜单列表 | `menu:list` |
| GET | `/api/v1/admin/menus/:id` | 菜单详情 | `menu:get` |
| PUT | `/api/v1/admin/menus/:id` | 更新菜单 | `menu:update` |
| DELETE | `/api/v1/admin/menus/:id` | 删除菜单 | `menu:delete` |

## ⚡ 性能优化

### 1. 权限检查性能
- 使用 Casbin 内存策略引擎，权限检查性能极高
- 避免每次权限检查都查询数据库

### 2. 同步性能
- 使用数据库事务确保数据一致性
- 批量操作减少数据库交互次数
- 只在必要时触发同步操作

### 3. 缓存策略
- Casbin 自带策略缓存
- 策略变更时自动刷新缓存

## 🔒 安全保障

### 1. 事务安全
- 所有同步操作使用数据库事务
- 失败时自动回滚，确保数据一致性

### 2. 权限验证
- 所有管理接口都需要相应权限
- 使用 JWT + Casbin 双重验证

### 3. 数据完整性
- 外键约束确保数据关联正确
- 软删除避免数据丢失

## 📊 监控与维护

### 1. 同步状态监控
- 定期运行同步检查工具
- 监控数据库与 Casbin 的一致性

### 2. 日志记录
- 详细记录所有同步操作
- 权限检查日志便于问题排查

### 3. 定期维护
- 定期清理孤立数据
- 定期验证同步状态

## 🎯 最佳实践

### 1. 开发规范
- 所有涉及权限的操作必须同步到 Casbin
- 使用事务确保数据一致性
- 详细的错误处理和日志记录

### 2. 部署建议
- 系统启动时运行同步检查
- 定期备份权限数据
- 监控同步状态

### 3. 故障处理
- 发现不一致时立即运行同步工具
- 使用详细分析工具定位问题
- 清理孤立数据后重新同步

## 📈 系统状态

### 当前同步状态
- ✅ **数据完全同步**：数据库与 Casbin 保持 100% 一致
- ✅ **实时同步**：所有操作立即同步到 Casbin
- ✅ **事务安全**：失败时自动回滚
- ✅ **完整覆盖**：涵盖所有权限相关操作
- ✅ **性能优化**：权限检查使用高性能的 Casbin

### 系统指标
- 角色数量: 11
- 用户数量: 1
- 角色-权限关联: 16
- 用户-角色关联: 1
- Casbin 权限策略: 64
- Casbin 用户角色关联: 1
- 同步状态: ✅ 完全同步

## 🚨 常见问题与解决方案

### 1. 数据不一致问题

**问题现象**：权限检查失败，但数据库中有相应的权限关联

**解决步骤**：
```bash
# 1. 检查同步状态
go run sync_casbin_database.go

# 2. 如果发现不一致，检查孤立数据
go run check_orphaned_data.go

# 3. 清理孤立数据
go run cleanup_orphaned_data.go

# 4. 重新同步
go run sync_casbin_database.go
```

### 2. 权限策略数量不匹配

**问题原因**：
- 孤立的角色-权限关联
- 软删除的角色仍有权限关联
- 同步过程中断

**解决方案**：
1. 运行孤立数据检查工具
2. 清理孤立数据
3. 重新执行完整同步

### 3. 用户角色关联缺失

**问题原因**：
- 用户创建时同步失败
- 角色更新时同步中断
- 软删除的用户仍有角色关联

**解决方案**：
1. 检查用户-角色关联表
2. 清理指向已删除用户/角色的关联
3. 重新同步用户-角色关联

### 4. 新权限不生效

**问题原因**：
- 权限创建后未分配给角色
- 角色权限更新后未同步到 Casbin
- 服务器未重新加载策略

**解决方案**：
1. 确认权限已分配给相应角色
2. 检查角色权限更新是否成功
3. 重启服务器或手动重新加载策略

## 🔧 故障排查指南

### 1. 权限检查失败

**排查步骤**：
```bash
# 检查用户是否有相应角色
go run detailed_sync_analysis.go | grep "用户名"

# 检查角色是否有相应权限
go run detailed_sync_analysis.go | grep "角色名"

# 检查 Casbin 策略是否存在
go run check_casbin_policies.go | grep "权限标识"
```

### 2. 同步工具执行失败

**可能原因**：
- 数据库连接问题
- Casbin 配置错误
- 数据库表结构不匹配

**排查方法**：
1. 检查数据库连接配置
2. 验证 Casbin 模型文件
3. 确认数据库表结构完整

### 3. 性能问题

**优化建议**：
- 定期清理孤立数据
- 避免频繁的完整同步
- 使用批量操作减少数据库交互

## 📚 开发指南

### 1. 添加新权限

**步骤**：
1. 在 `internal/bootstrap/init.go` 中添加权限定义
2. 运行系统初始化或手动创建权限
3. 将权限分配给相应角色
4. 验证权限生效

**示例**：
```go
// 在权限种子数据中添加
{Name: "导出数据", Key: "data:export", Method: "GET", Description: "导出系统数据"},
```

### 2. 创建新角色

**步骤**：
1. 通过 API 创建角色
2. 分配相应权限
3. 将角色分配给用户
4. 测试权限功能

### 3. 菜单权限关联

**步骤**：
1. 创建菜单时指定 `permission_id`（可选）
2. 确保权限已分配给相应角色
3. 用户通过角色获得菜单访问权限

### 4. 自定义权限检查

**中间件使用**：
```go
// 检查特定权限
router.GET("/api/data", middleware.CheckPermissionByKey("data:export", enforcer), handler.ExportData)

// 检查多个权限（任一满足）
router.GET("/api/admin", middleware.CheckAnyPermission([]string{"admin:read", "admin:write"}, enforcer), handler.AdminPanel)
```

## 🔄 版本升级指南

### 从旧版本升级

**升级步骤**：
1. 备份现有权限数据
2. 更新代码到新版本
3. 运行数据库迁移
4. 执行完整同步
5. 验证功能正常

**注意事项**：
- 升级前务必备份数据
- 测试环境先验证升级流程
- 升级后立即运行同步检查

### 配置文件更新

**Casbin 模型文件** (`configs/rbac_model.conf`)：
```ini
[request_definition]
r = sub, obj, act

[policy_definition]
p = sub, obj, act

[role_definition]
g = _, _

[policy_effect]
e = some(where (p.eft == allow))

[matchers]
m = g(r.sub, p.sub) && r.obj == p.obj && r.act == p.act
```

## 📋 维护清单

### 日常维护

- [ ] 检查同步状态
- [ ] 清理孤立数据
- [ ] 监控权限检查日志
- [ ] 验证新功能权限

### 周期性维护

- [ ] 完整同步检查
- [ ] 性能监控
- [ ] 数据备份
- [ ] 安全审计

### 紧急维护

- [ ] 权限失效处理
- [ ] 数据不一致修复
- [ ] 系统故障恢复
- [ ] 安全事件响应

## 🎯 未来规划

### 短期目标

1. **性能优化**
   - 实现增量同步
   - 优化批量操作
   - 添加缓存机制

2. **功能增强**
   - 权限继承机制
   - 动态权限分配
   - 权限审计日志

3. **工具完善**
   - Web 界面管理工具
   - 自动化测试套件
   - 监控告警系统

### 长期目标

1. **架构升级**
   - 微服务权限中心
   - 分布式权限管理
   - 多租户支持

2. **安全增强**
   - 权限变更审批流程
   - 敏感操作二次验证
   - 权限滥用检测

3. **生态集成**
   - 第三方系统集成
   - SSO 单点登录
   - API 网关集成

---

**文档版本**：v1.0
**最后更新**：2025-07-11
**维护状态**：✅ 活跃维护
**系统状态**：✅ 生产就绪
