# Yogaga数据模型结构文档

## 📁 模型文件组织

我们已经将原本集中在一个文件中的模型按照功能模块进行了合理分割，提高了代码的可维护性和可读性。

### 🗂️ 文件结构

```
internal/model/
├── base.go              # 基础模型和公共字段
├── user.go              # 用户模型（包含教练信息）
├── role.go              # 角色权限相关
├── permission.go        # 权限模型
├── menu.go              # 菜单模型
├── file.go              # 文件模型
├── store.go             # 门店模型
├── course.go            # 课程模型
├── course_category.go   # 课程分类模型 ✨新增
├── classroom.go         # 教室模型 ✨新增
├── schedule.go          # 课程排期模型 ✨新增
├── membership.go        # 会员卡相关模型 ✨新增
└── booking.go           # 预约相关模型
```

## 📊 模型关系图

```mermaid
erDiagram
    User ||--o{ Booking : "用户预约"
    User ||--o{ MembershipCard : "拥有会员卡"
    User ||--o{ ClassSchedule : "教练排课"
    
    Store ||--o{ ClassRoom : "门店教室"
    Store ||--o{ ClassSchedule : "门店排课"
    
    Course ||--o{ ClassSchedule : "课程排期"
    CourseCategory ||--o{ Course : "课程分类"
    
    ClassSchedule ||--o{ Booking : "排期预约"
    ClassSchedule ||--o{ BookingQueue : "排期排队"
    
    MembershipCardType ||--o{ MembershipCard : "卡种实例"
    MembershipCard ||--o{ Booking : "会员卡预约"
    
    Booking ||--o{ BookingApplication : "预约申请"
```

## 🔧 核心模型详解

### 1. **用户模型 (user.go)**
```go
type User struct {
    // 基础用户信息
    Username, Email, Phone, Status
    
    // 微信小程序字段
    WeChatOpenID, NickName, AvatarUrl, Gender
    
    // 教练专属字段 ✨新增
    CoachDescription  // 教练简介
    CoachImages       // 教练图片
    Experience        // 从业年限
    Speciality        // 专业特长
    IsHomepage        // 是否首页显示
}
```

**设计优势**：
- ✅ 统一用户管理，教练作为用户角色存在
- ✅ 避免数据冗余，简化关联关系
- ✅ 支持用户身份切换（普通用户↔教练）

### 2. **课程分类 (course_category.go)**
```go
type CourseCategory struct {
    Name, ParentID, Level
    Description, SortOrder, Status
}
```

**功能特点**：
- 支持多级分类（一级、二级）
- 树形结构管理
- 排序和状态控制

### 3. **教室管理 (classroom.go)**
```go
type ClassRoom struct {
    StoreID, Name, Capacity
    Equipment, Description, Status
}
```

**业务价值**：
- 精确容量管理
- 设备信息记录
- 门店教室关联

### 4. **课程排期 (schedule.go)**
```go
type ClassSchedule struct {
    StoreID, CourseID, CoachID, ClassRoomID
    StartTime, EndTime
    MaxCapacity, CurrentCount, WaitingCount
    Price, Status
}
```

**核心功能**：
- 完整的排期信息
- 实时人数统计
- 排队人数追踪
- 状态流转管理

### 5. **会员卡系统 (membership.go)**
```go
// 卡种定义
type MembershipCardType struct {
    Name, Category, Description
    ValidityDays, Times, Amount
    Discount, Shareable
}

// 卡片实例
type MembershipCard struct {
    UserID, CardTypeID, CardNumber
    RemainingTimes, RemainingAmount
    StartDate, EndDate, Status
}
```

**商业特性**：
- 多种卡类型（次数卡、期限卡、储值卡）
- 灵活的折扣机制
- 共享使用支持
- 完整的生命周期管理

### 6. **预约系统 (booking.go)**
```go
// 预约记录
type Booking struct {
    UserID, ScheduleID, MembershipCardID
    BookingNumber, PeopleCount
    DeductTimes, DeductAmount
    Status, Rating, Comment
}

// 预约申请（异步处理）
type BookingApplication struct {
    UserID, ScheduleID, TaskID
    Status, Result, BookingID
    QueuePosition, ErrorCode
}

// 排队管理
type BookingQueue struct {
    UserID, ScheduleID, QueueNumber
    Status, NotifiedAt
}
```

**技术特色**：
- 异步预约处理
- 完整状态追踪
- 智能排队机制
- 评价系统集成

## 🎯 设计原则

### 1. **单一职责**
- 每个文件专注一个业务领域
- 模型职责清晰，便于维护

### 2. **关联合理**
- 外键关系明确
- 避免循环依赖
- 支持延迟加载

### 3. **扩展友好**
- 预留扩展字段
- 状态枚举设计
- 版本兼容考虑

### 4. **业务导向**
- 贴近实际业务场景
- 支持复杂业务逻辑
- 数据完整性保证

## 📈 优化效果

### 代码组织
| 指标 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| 文件行数 | 300+ | 50-100 | 可读性↑ |
| 模型查找 | 困难 | 直观 | 效率↑ |
| 维护成本 | 高 | 低 | 成本↓ |
| 团队协作 | 冲突多 | 冲突少 | 协作↑ |

### 业务支持
- ✅ **教练管理**：统一到用户表，角色化管理
- ✅ **课程体系**：分类→课程→排期，层次清晰
- ✅ **会员系统**：卡种→卡片，灵活配置
- ✅ **预约流程**：申请→处理→完成，状态完整

## 🚀 最佳实践

### 1. **模型命名**
- 使用业务术语，见名知意
- 遵循Go命名规范
- 保持一致性

### 2. **字段设计**
- 合理的数据类型选择
- 必要的索引和约束
- 完善的注释说明

### 3. **关联设计**
- 明确的外键关系
- 合理的预加载策略
- 避免N+1查询问题

### 4. **扩展性**
- 预留状态字段
- 灵活的JSON字段
- 版本兼容设计

这种模型组织方式不仅提高了代码的可维护性，还为Yogaga业务的快速发展提供了坚实的数据基础！🎉
