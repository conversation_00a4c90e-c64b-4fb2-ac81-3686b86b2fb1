package model

import (
	"time"
)

// ResourceAssignment 资源分配表
type ResourceAssignment struct {
	BaseModel
	StaffID      string    `json:"staff_id" gorm:"type:char(36);not null;comment:员工ID"`
	ResourceType string    `json:"resource_type" gorm:"type:varchar(20);not null;comment:资源类型 member/store"`
	ResourceID   string    `json:"resource_id" gorm:"type:varchar(36);not null;comment:资源ID"`
	AssignedBy   string    `json:"assigned_by" gorm:"type:char(36);not null;comment:分配人ID"`
	AssignedAt   time.Time `json:"assigned_at" gorm:"not null;comment:分配时间"`
	Remark       string    `json:"remark" gorm:"type:varchar(500);comment:分配备注"`
	Status       int       `json:"status" gorm:"type:smallint;default:1;comment:状态 1:有效 0:无效"`

	// 关联
	Staff          User `json:"staff" gorm:"foreignKey:StaffID"`
	AssignedByUser User `json:"assigned_by_user" gorm:"foreignKey:AssignedBy"`
}

// MemberAssignment 会员分配（视图，方便查询）
type MemberAssignment struct {
	ResourceAssignment
	Member User `json:"member" gorm:"foreignKey:ResourceID"`
}

// StoreAssignment 门店分配（视图，方便查询）
type StoreAssignment struct {
	ResourceAssignment
	Store Store `json:"store" gorm:"foreignKey:ResourceID"`
}

// TableName 指定表名
func (ResourceAssignment) TableName() string {
	return "resource_assignments"
}

// IsActive 检查分配是否有效
func (ra *ResourceAssignment) IsActive() bool {
	return ra.Status == 1
}

// IsMemberAssignment 检查是否为会员分配
func (ra *ResourceAssignment) IsMemberAssignment() bool {
	return ra.ResourceType == "member"
}

// IsStoreAssignment 检查是否为门店分配
func (ra *ResourceAssignment) IsStoreAssignment() bool {
	return ra.ResourceType == "store"
}
