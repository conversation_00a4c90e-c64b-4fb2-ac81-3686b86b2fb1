package logger

import (
	"io"
	"os"
	"time"

	"github.com/charmbracelet/log"
	"yogaga/configs"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Setup 初始化日志配置
func Setup(config configs.LogConfig) {
	var outputs []io.Writer

	// 配置日志级别
	level := parseLogLevel(config.Level)

	// 配置输出
	for _, output := range config.Output {
		switch output {
		case "console":
			outputs = append(outputs, os.Stdout)
		case "file":
			outputs = append(outputs, &lumberjack.Logger{
				Filename:   config.FileConfig.Path,
				MaxSize:    config.FileConfig.MaxSize, // MB
				MaxAge:     config.FileConfig.MaxAge,  // days
				MaxBackups: config.FileConfig.MaxBackups,
				Compress:   config.FileConfig.Compress,
			})
		}
	}

	// 如果没有配置输出，默认输出到控制台
	if len(outputs) == 0 {
		outputs = append(outputs, os.Stdout)
	}

	// 创建多输出writer
	multiWriter := io.MultiWriter(outputs...)

	// 配置全局logger
	log.SetDefault(log.NewWithOptions(multiWriter, log.Options{
		Level:           level,
		Prefix:          "🌏 starter",
		TimeFormat:      time.RFC3339,
		ReportTimestamp: true,
		ReportCaller:    level == log.DebugLevel,
	}))
}

// parseLogLevel 解析日志级别
func parseLogLevel(level string) log.Level {
	switch level {
	case "debug":
		return log.DebugLevel
	case "info":
		return log.InfoLevel
	case "warn":
		return log.WarnLevel
	case "error":
		return log.ErrorLevel
	default:
		return log.InfoLevel
	}
}
