package service

import (
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// UserPlatformService 用户平台管理服务
type UserPlatformService struct {
	db *gorm.DB
}

// NewUserPlatformService 创建用户平台管理服务实例
func NewUserPlatformService(db *gorm.DB) *UserPlatformService {
	return &UserPlatformService{
		db: db,
	}
}

// SetupCoachPlatformAccess 为教练设置双平台访问权限
func (s *UserPlatformService) SetupCoachPlatformAccess(userID uint) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	// 设置教练的双平台访问权限
	user.SetCoachPlatformAccess()

	return s.db.Model(&user).Updates(map[string]interface{}{
		"platform_access": user.PlatformAccess,
	}).Error
}

// SetUserPlatformAccess 设置用户平台访问权限
func (s *UserPlatformService) SetUserPlatformAccess(userID uint, platforms ...enum.Platform) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	user.SetPlatformAccess(platforms...)

	return s.db.Model(&user).Updates(map[string]interface{}{
		"platform_access": user.PlatformAccess,
	}).Error
}

// AddUserPlatformAccess 为用户添加平台访问权限
func (s *UserPlatformService) AddUserPlatformAccess(userID uint, platform enum.Platform) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	user.AddPlatformAccess(platform)

	return s.db.Model(&user).Updates(map[string]interface{}{
		"platform_access": user.PlatformAccess,
	}).Error
}

// RemoveUserPlatformAccess 移除用户平台访问权限
func (s *UserPlatformService) RemoveUserPlatformAccess(userID uint, platform enum.Platform) error {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return err
	}

	user.RemovePlatformAccess(platform)

	return s.db.Model(&user).Updates(map[string]interface{}{
		"platform_access": user.PlatformAccess,
	}).Error
}

// MigrateExistingUsers 迁移现有用户的平台访问权限
func (s *UserPlatformService) MigrateExistingUsers() error {
	// 1. 为所有微信用户设置微信平台访问权限
	err := s.db.Model(&model.User{}).
		Where("platform_access = 0 OR platform_access IS NULL").
		Update("platform_access", enum.PlatformAccessWechat).Error
	if err != nil {
		return err
	}

	// 3. 为教练用户设置双平台访问权限
	var coaches []model.User
	err = s.db.Where("is_coach = ?", true).Find(&coaches).Error
	if err != nil {
		return err
	}

	for _, coach := range coaches {
		coach.SetCoachPlatformAccess()
		s.db.Model(&coach).Updates(map[string]interface{}{
			"platform_access": coach.PlatformAccess,
		})
	}

	return nil
}

// GetUsersByPlatformAccess 根据平台访问权限获取用户列表
func (s *UserPlatformService) GetUsersByPlatformAccess(platform enum.Platform) ([]model.User, error) {
	var users []model.User
	err := s.db.Preload("Roles").
		Where("platform_access LIKE ?", "%"+string(platform)+"%").
		Find(&users).Error
	return users, err
}

// ValidateUserPlatformAccess 验证用户是否可以访问指定平台
func (s *UserPlatformService) ValidateUserPlatformAccess(userID uint, platform enum.Platform) (bool, error) {
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		return false, err
	}

	return user.CanAccessPlatform(platform), nil
}
