package main

import (
	"fmt"
	"log"
	"yogaga/configs"
	"yogaga/internal/database"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔧 开始修复副卡 is_main_card 字段...")

	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.InitDB(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 修复副卡数据
	if err := fixSubCardIsMainCard(db); err != nil {
		log.Fatalf("修复副卡数据失败: %v", err)
	}

	fmt.Println("✅ 副卡 is_main_card 字段修复完成！")
}

func fixSubCardIsMainCard(db *gorm.DB) error {
	fmt.Println("\n📋 检查当前副卡数据...")

	// 1. 查找所有副卡（有 main_card_id 的卡片）
	var subCards []model.MembershipCard
	err := db.Where("main_card_id IS NOT NULL").Find(&subCards).Error
	if err != nil {
		return fmt.Errorf("查询副卡失败: %v", err)
	}

	fmt.Printf("找到 %d 张副卡\n", len(subCards))

	if len(subCards) == 0 {
		fmt.Println("没有找到副卡，无需修复")
		return nil
	}

	// 2. 检查和修复每张副卡的 is_main_card 字段
	var fixedCount int
	for _, card := range subCards {
		fmt.Printf("\n🔍 检查副卡 ID:%d, 卡号:%s\n", card.ID, card.CardNumber)
		fmt.Printf("  当前 IsMainCard: %t\n", card.IsMainCard)
		fmt.Printf("  主卡ID: %v\n", card.MainCardID)

		// 检查 IsMainCard 字段
		if card.IsMainCard {
			fmt.Printf("  ⚠️ IsMainCard 应该为 false，当前为 true，正在修复...\n")
			
			// 使用 Select 强制更新 is_main_card 字段
			err := db.Model(&card).Select("is_main_card").Update("is_main_card", false).Error
			if err != nil {
				fmt.Printf("  ❌ 更新副卡 %d 失败: %v\n", card.ID, err)
				continue
			}
			fmt.Printf("  ✅ 副卡 %d 的 is_main_card 字段修复成功\n", card.ID)
			fixedCount++
		} else {
			fmt.Printf("  ✅ 副卡 %d 的 is_main_card 字段正常\n", card.ID)
		}
	}

	fmt.Printf("\n📊 修复统计:\n")
	fmt.Printf("  总副卡数量: %d\n", len(subCards))
	fmt.Printf("  修复数量: %d\n", fixedCount)
	fmt.Printf("  正常数量: %d\n", len(subCards)-fixedCount)

	// 3. 验证修复结果
	fmt.Println("\n🔍 验证修复结果...")
	return verifySubCardIsMainCard(db)
}

func verifySubCardIsMainCard(db *gorm.DB) error {
	// 查找所有副卡
	var subCards []model.MembershipCard
	err := db.Where("main_card_id IS NOT NULL").Find(&subCards).Error
	if err != nil {
		return fmt.Errorf("验证查询副卡失败: %v", err)
	}

	var problemCount int
	for _, card := range subCards {
		if card.IsMainCard {
			fmt.Printf("❌ 副卡 %d (卡号:%s) IsMainCard 仍为 true\n", card.ID, card.CardNumber)
			problemCount++
		}
	}

	// 同时检查主卡（没有 main_card_id 的卡片）
	var mainCards []model.MembershipCard
	err = db.Where("main_card_id IS NULL").Find(&mainCards).Error
	if err != nil {
		return fmt.Errorf("验证查询主卡失败: %v", err)
	}

	for _, card := range mainCards {
		if !card.IsMainCard {
			fmt.Printf("❌ 主卡 %d (卡号:%s) IsMainCard 应为 true，当前为 false\n", card.ID, card.CardNumber)
			// 修复主卡的 is_main_card 字段
			err := db.Model(&card).Select("is_main_card").Update("is_main_card", true).Error
			if err != nil {
				fmt.Printf("  ❌ 修复主卡 %d 失败: %v\n", card.ID, err)
			} else {
				fmt.Printf("  ✅ 主卡 %d 的 is_main_card 字段已修复\n", card.ID)
			}
			problemCount++
		}
	}

	if problemCount == 0 {
		fmt.Println("✅ 所有卡片的 is_main_card 字段验证通过！")
	} else {
		fmt.Printf("⚠️ 发现并修复了 %d 个 is_main_card 字段问题\n", problemCount)
	}

	return nil
}
