# 验证API文档修复结果
param(
    [string]$ApiDocPath = "api-collections/admin/admin_api.json"
)

Write-Host "🔍 验证API文档修复结果..." -ForegroundColor Green
Write-Host ""

# 读取API文档
if (-not (Test-Path $ApiDocPath)) {
    Write-Host "❌ API文档不存在: $ApiDocPath" -ForegroundColor Red
    exit 1
}

try {
    $apiDoc = Get-Content $ApiDocPath -Raw | ConvertFrom-Json
    Write-Host "✅ JSON格式验证通过" -ForegroundColor Green
} catch {
    Write-Host "❌ JSON格式错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 统计API数量
$totalRequests = 0
function Count-Requests($folder) {
    $script:totalRequests += $folder.requests.Count
    foreach ($subfolder in $folder.folders) {
        Count-Requests $subfolder
    }
}

foreach ($folder in $apiDoc.folders) {
    Count-Requests $folder
}

Write-Host "📊 API统计信息:" -ForegroundColor Yellow
Write-Host "  总文件夹数: $($apiDoc.folders.Count)" -ForegroundColor Cyan
Write-Host "  总接口数: $totalRequests" -ForegroundColor Cyan
Write-Host ""

# 检查关键修复
Write-Host "🔧 检查关键修复项:" -ForegroundColor Yellow

$fixedIssues = 0
$totalChecks = 0

# 检查副卡创建路径
$totalChecks++
$subCardCreate = $null
foreach ($folder in $apiDoc.folders) {
    foreach ($request in $folder.requests) {
        if ($request.name -eq "创建副卡" -and $request.endpoint -like "*membership-cards/1/sub-cards") {
            $subCardCreate = $request
            break
        }
    }
}

if ($subCardCreate) {
    Write-Host "  ✅ 副卡创建路径已修复: $($subCardCreate.endpoint)" -ForegroundColor Green
    $fixedIssues++
} else {
    Write-Host "  ❌ 副卡创建路径未找到或未修复" -ForegroundColor Red
}

# 检查会员卡详情路径
$totalChecks++
$cardDetail = $null
foreach ($folder in $apiDoc.folders) {
    foreach ($request in $folder.requests) {
        if ($request.name -eq "获取会员卡详情" -and $request.endpoint -like "*membership-cards/1" -and $request.endpoint -notlike "*detail") {
            $cardDetail = $request
            break
        }
    }
}

if ($cardDetail) {
    Write-Host "  ✅ 会员卡详情路径已修复: $($cardDetail.endpoint)" -ForegroundColor Green
    $fixedIssues++
} else {
    Write-Host "  ❌ 会员卡详情路径未找到或未修复" -ForegroundColor Red
}

# 检查副卡列表接口
$totalChecks++
$subCardList = $null
foreach ($folder in $apiDoc.folders) {
    foreach ($request in $folder.requests) {
        if ($request.name -eq "获取副卡列表" -and $request.endpoint -like "*membership-cards/1/sub-cards" -and $request.method -eq "GET") {
            $subCardList = $request
            break
        }
    }
}

if ($subCardList) {
    Write-Host "  ✅ 副卡列表接口已添加: $($subCardList.endpoint)" -ForegroundColor Green
    $fixedIssues++
} else {
    Write-Host "  ❌ 副卡列表接口未找到" -ForegroundColor Red
}

# 检查是否移除了高级会员卡管理接口
$totalChecks++
$advancedMembershipFound = $false
foreach ($folder in $apiDoc.folders) {
    if ($folder.name -like "*高级会员卡管理*") {
        $advancedMembershipFound = $true
        break
    }
    foreach ($request in $folder.requests) {
        if ($request.endpoint -like "*advanced-membership*") {
            $advancedMembershipFound = $true
            break
        }
    }
}

if (-not $advancedMembershipFound) {
    Write-Host "  ✅ 高级会员卡管理接口已移除" -ForegroundColor Green
    $fixedIssues++
} else {
    Write-Host "  ❌ 高级会员卡管理接口仍然存在" -ForegroundColor Red
}

Write-Host ""

# 显示修复结果
Write-Host "📋 修复结果汇总:" -ForegroundColor Green
Write-Host "  检查项目: $totalChecks" -ForegroundColor Cyan
Write-Host "  修复成功: $fixedIssues" -ForegroundColor Green
Write-Host "  修复失败: $($totalChecks - $fixedIssues)" -ForegroundColor Red

if ($fixedIssues -eq $totalChecks) {
    Write-Host ""
    Write-Host "🎉 所有关键问题已修复！API文档与路由器保持一致。" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️  还有 $($totalChecks - $fixedIssues) 个问题需要修复。" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 下一步建议:" -ForegroundColor Cyan
Write-Host "  1. 运行API测试验证接口功能" -ForegroundColor Yellow
Write-Host "  2. 更新API文档的描述和示例" -ForegroundColor Yellow
Write-Host "  3. 添加缺失的批量操作接口" -ForegroundColor Yellow
