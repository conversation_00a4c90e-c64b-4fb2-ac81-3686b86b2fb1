package main

import (
	"context"
	"fmt"

	"net/http"
	"os"
	"os/signal"
	"time"

	"yogaga/common/redis"
	"yogaga/configs"
	"yogaga/global"
	"yogaga/internal/bootstrap"
	"yogaga/internal/database"
	"yogaga/internal/model"
	"yogaga/routers"

	"yogaga/pkg/jwtx"
	"yogaga/pkg/logger"
	"yogaga/pkg/storage"

	"github.com/casbin/casbin/v2"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"github.com/limitcool/lib"
	"gorm.io/gorm"

	"yogaga/pkg/env"

	"github.com/spf13/pflag"
	"github.com/spf13/viper"
)

var (
	// CommitHash will be injected by the linker
	CommitHash string
	// BuildTime will be injected by the linker
	BuildTime string
	// Platform will be injected by the linker
	Platform string
	// AppName will be injected by the linker
	AppName string
)

// checkIfTableMigrationNeeded 检查是否需要进行表结构迁移
func checkIfTableMigrationNeeded(db *gorm.DB) bool {
	// 检查是否存在迁移状态表，如果不存在则需要迁移
	if !db.Migrator().HasTable(&model.MigrationStatus{}) {
		return true
	}

	// 检查一些关键表是否存在，如果不存在则需要迁移
	requiredTables := []any{
		&model.User{},
		&model.Role{},
		&model.Permission{},
		&model.Menu{},
		&model.Store{},
	}

	for _, table := range requiredTables {
		if !db.Migrator().HasTable(table) {
			return true
		}
	}

	// 检查users表是否有微信相关字段
	if db.Migrator().HasTable(&model.User{}) {
		if !db.Migrator().HasColumn(&model.User{}, "wechat_openid") {
			log.Info("检测到users表缺少wechat_openid字段，需要迁移")
			return true
		}
		if !db.Migrator().HasColumn(&model.User{}, "wechat_unionid") {
			log.Info("检测到users表缺少wechat_unionid字段，需要迁移")
			return true
		}
	}

	// 检查courses表是否有classroom_id字段
	if db.Migrator().HasTable(&model.Course{}) {
		if !db.Migrator().HasColumn(&model.Course{}, "classroom_id") {
			log.Info("检测到courses表缺少classroom_id字段，需要迁移")
			return true
		}
		// 检查courses表是否有coach_ids字段（多教练支持）
		if !db.Migrator().HasColumn(&model.Course{}, "coach_ids") {
			log.Info("检测到courses表缺少coach_ids字段，需要迁移")
			return true
		}
	}

	// 检查banners表是否存在以及字段是否正确
	if !db.Migrator().HasTable(&model.Banner{}) {
		log.Info("检测到banners表不存在，需要迁移")
		return true
	}
	if db.Migrator().HasTable(&model.Banner{}) {
		if !db.Migrator().HasColumn(&model.Banner{}, "description") {
			log.Info("检测到banners表缺少description字段，需要迁移")
			return true
		}
		// 检查是否存在已废弃的link_type字段
		if db.Migrator().HasColumn(&model.Banner{}, "link_type") {
			log.Info("检测到banners表存在已废弃的link_type字段，需要迁移")
			return true
		}
	}

	// 注意：course_deduction_rules表已废弃，使用flexible_deduction_rules表

	// 可以添加更多的检查逻辑，比如检查特定列是否存在
	// 这里简化处理，如果所有关键表都存在，则认为不需要迁移
	return false
}

func loadConfig() {
	pflag.String("config", "", "Path to config file")
	pflag.Parse()
	_ = viper.BindPFlags(pflag.CommandLine)

	env := env.Get()
	log.Info("current env", "env", env)

	// 设置默认的 AppName
	if AppName == "" {
		AppName = "yogaga"
	}

	// 从命令行参数读取配置文件路径
	if configFile := viper.GetString("config"); configFile != "" {
		log.Info("using config file from command line", "file", configFile)
		viper.SetConfigFile(configFile)
	} else {
		// 保留原来的逻辑作为备用
		viper.SetConfigName("config")
		viper.AddConfigPath("./")
		viper.AddConfigPath("./configs/")
		if AppName != "" {
			viper.AddConfigPath(fmt.Sprintf("/etc/%s", AppName)) // 在 FHS 标准位置寻找
		}
		log.Info("searching for config file in paths", "paths", "./, ./configs/, /etc/"+AppName)
	}

	viper.SetConfigType("yaml")
	viper.AutomaticEnv()

	// 读取默认配置
	if err := viper.ReadInConfig(); err != nil {
		log.Fatalf("read default config err = %v, searched paths: ./, ./configs/, /etc/%s", err, AppName)
	}
	log.Info("successfully loaded config file", "file", viper.ConfigFileUsed())

	// 读取环境配置
	viper.SetConfigName(fmt.Sprintf("config-%s", env))
	if err := viper.MergeInConfig(); err != nil {
		log.Warn("environment specific config not found, use default config")
	} else {
		log.Info("successfully merged environment config", "file", fmt.Sprintf("config-%s.yaml", env))
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(&global.Config); err != nil {
		log.Fatal("config unmarshal err = ", err)
	}

	log.Info("config loaded successfully")
}

func main() {
	lib.SetDebugMode(func() {
		log.Info("Debug Mode")
		log.SetLevel(log.DebugLevel)
		log.SetReportCaller(true)
	})

	log.SetPrefix("🌏 starter ")
	log.Infof("Version: %s, Build Time: %s, Platform: %s", CommitHash, BuildTime, Platform)

	// 加载配置
	loadConfig()

	// 初始化日志
	logger.Setup(global.Config.Log)
	var db *gorm.DB
	switch global.Config.Driver {
	case configs.DriverMysql, configs.DriverPostgres, configs.DriverSqlite, configs.DriverMssql, configs.DriverOracle:
		log.Info("driver is", "driver", global.Config.Driver)
		db = database.NewDB(*global.Config)

		// 检查是否需要进行表结构迁移
		needsMigration := checkIfTableMigrationNeeded(db)

		if needsMigration {
			log.Info("🔄 开始数据库表迁移...")
			startTime := time.Now()
			err := db.AutoMigrate(
				model.MigrationStatus{}, // 迁移状态表，必须最先迁移
				model.Store{},
				model.File{},
				model.Role{},
				model.Permission{},
				model.Menu{},
				model.User{},
				model.CourseCategory{},
				model.Course{},
				// model.CourseDeductionRule{}, // 已废弃，使用灵活扣减系统
				model.CourseTypeConfig{},      // 课程类型配置表
				model.FlexibleDeductionRule{}, // 灵活扣减规则表
				model.ClassRoom{},
				model.ClassSchedule{},
				model.MembershipCardType{},
				model.MembershipCard{},
				model.MembershipCardLeave{},       // 会员卡请假记录表
				model.MembershipCardTransaction{}, // 会员卡交易记录表
				model.Booking{},
				model.BookingQueue{},
				model.BookingApplication{},
				model.AdminNotification{},
				model.Banner{},
				model.CoachBanner{},
				model.Favorite{},              // 收藏表
				model.OperationLog{},          // 操作日志表
				model.UserStoreAssignment{},   // 用户门店分配表
				model.MemberSalesAssignment{}, // 会员销售分配表
				model.ResourceAssignment{},    // 资源分配表
			)

			if err != nil {
				log.Fatal("数据库表迁移失败", "error", err)
			}

			migrationDuration := time.Since(startTime)
			log.Info("✅ 数据库表迁移完成", "duration", migrationDuration)
		} else {
			log.Info("✅ 数据库表结构已是最新，跳过迁移")
		}
	default:
		log.Info("driver is none")
	}
	// 启用Redis连接用于权限缓存
	redisClient, redisCleanup, err := redis.NewRedisClient(global.Config)
	if err != nil {
		log.Warn("⚠️ Redis连接失败，将跳过Redis相关功能", "error", err)
		redisClient = nil
		redisCleanup = func() {} // 空函数
	} else {
		defer redisCleanup()
		log.Info("✅ Redis连接成功")
	}
	storageInstance, err := storage.NewStorage(&global.Config.Storage)
	if err != nil {
		log.Warn("⚠️ 存储连接失败，将跳过存储相关功能", "error", err)
		storageInstance = nil
	} else {
		log.Info("✅ 存储连接成功")
	}

	// Setup Casbin enforcer with GORM adapter
	log.Info("🔐 开始初始化Casbin权限系统...")
	casbinStartTime := time.Now()
	var casbinEnforcer *casbin.Enforcer
	if db != nil {
		casbinEnforcer, err = newCasbinEnforcer(db)
		if err != nil {
			log.Fatal("casbin enforcer setup err = ", err)
		}
		casbinDuration := time.Since(casbinStartTime)
		log.Info("✅ Casbin权限系统初始化完成", "duration", casbinDuration)
	}

	// Initialize database with admin user, roles, and permissions
	log.Info("🚀 开始数据初始化...")
	bootstrapStartTime := time.Now()
	bootstrap.InitData(db, casbinEnforcer)

	// 灵活扣减系统已集成到主迁移系统中，无需单独初始化

	bootstrapDuration := time.Since(bootstrapStartTime)
	log.Info("✅ 数据初始化完成", "duration", bootstrapDuration)

	// Setup JWT Service
	jwtSvc := jwtx.NewService(global.Config.JwtAuth)

	// Setup Asynq Redis Client
	asynqRedisOpt := asynq.RedisClientOpt{
		Addr: global.Config.Redis["default"].Addr,
		DB:   global.Config.Redis["default"].DB,
	}
	if global.Config.Redis["default"].Password != "" {
		asynqRedisOpt.Password = global.Config.Redis["default"].Password
	}

	log.Info("storageInstance initialized", "instance", storageInstance)
	router := routers.NewRouter(storageInstance, db, casbinEnforcer, jwtSvc, global.Config.WeChat, asynqRedisOpt, redisClient, CommitHash, BuildTime, Platform)
	s := &http.Server{
		Addr:           fmt.Sprint("0.0.0.0:", global.Config.App.Port),
		Handler:        router,
		MaxHeaderBytes: 1 << 20,
	}
	log.Infof("Listen: %s:%d\n", "http://127.0.0.1", global.Config.App.Port)
	go func() {
		// 服务连接 监听
		if err := s.ListenAndServe(); err != nil {
			log.Fatalf("Listen:%s\n", err)
		}
	}()
	// 等待中断信号以优雅地关闭服务器,这里需要缓冲
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, os.Interrupt)
	<-quit
	//(设置5秒超时时间)
	ctx, cancel := context.WithTimeout(context.Background(), 20*time.Second)
	defer cancel()
	if err := s.Shutdown(ctx); err != nil {
		// 处理错误，例如记录日志、返回错误等
		log.Infof("Error during shutdown: %v", err)
	}
}

func newCasbinEnforcer(db *gorm.DB) (*casbin.Enforcer, error) {
	log.Info("📋 创建Casbin适配器...")
	adapter, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		return nil, err
	}

	log.Info("📖 加载Casbin模型文件...", "model_path", global.Config.Casbin.ModelPath)
	// 从配置文件加载模型路径
	enforcer, err := casbin.NewEnforcer(global.Config.Casbin.ModelPath, adapter)
	if err != nil {
		return nil, err
	}

	log.Info("🔄 从数据库加载权限策略...")
	// Load policies from DB
	err = enforcer.LoadPolicy()
	if err != nil {
		return nil, err
	}

	log.Info("✅ Casbin权限策略加载完成")
	return enforcer, nil
}
