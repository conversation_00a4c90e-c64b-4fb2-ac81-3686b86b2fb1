// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblMember = "tbl_members"

// TblMember mapped from table <tbl_members>
type TblMember struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID     int32     `gorm:"column:shop_id;not null" json:"shop_id"`
	SaleID     int32     `gorm:"column:sale_id;not null" json:"sale_id"`
	Openid     string    `gorm:"column:openid" json:"openid"`
	Name       string    `gorm:"column:name" json:"name"`
	Nickname   string    `gorm:"column:nickname" json:"nickname"`
	Sex        string    `gorm:"column:sex" json:"sex"`
	Mobile     string    `gorm:"column:mobile" json:"mobile"`
	Career     string    `gorm:"column:career" json:"career"`
	Birth      time.Time `gorm:"column:birth" json:"birth"`
	Note       string    `gorm:"column:note" json:"note"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Picture    string    `gorm:"column:picture" json:"picture"`
	PictureOri string    `gorm:"column:picture_ori" json:"picture_ori"`
	IsDelete   int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	Country    string    `gorm:"column:country" json:"country"`
	City       string    `gorm:"column:city" json:"city"`
	Points     int32     `gorm:"column:points;not null" json:"points"`
}

// TableName TblMember's table name
func (*TblMember) TableName() string {
	return TableNameTblMember
}
