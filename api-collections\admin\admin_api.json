{"v": 8, "id": "yogaga-admin-api-complete", "name": "Yogaga Admin API Complete (完整管理端)", "description": "Yogaga瑜伽管理系统 - 管理员后台API集合，包含138个接口，覆盖用户管理、课程管理、预约管理、灵活扣减系统、报表统计等完整功能模块", "version": "1.0.0", "author": "Yogaga Development Team", "created": "2024-01-16", "updated": "2024-01-16", "folders": [{"v": 1, "id": "dashboard-folder", "name": "📊 仪表盘模块", "folders": [], "requests": [{"v": "14", "name": "获取仪表盘数据", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/dashboard", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Response has dashboard data\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "batch-courses-folder", "name": "📚 批量课程管理", "folders": [], "requests": [{"v": "14", "name": "批量创建课程预览", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/batch-courses/preview", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-29\",\n  \"end_date\": \"2025-08-05\",\n  \"start_time\": \"09:00\",\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_name\": \"瑜伽基础\",\n      \"description\": \"适合初学者的基础瑜伽课程\",\n      \"coach_ids\": [\"<<coach_id>>\"],\n      \"duration\": 90\n    }\n  ],\n  \"global_description\": \"2025年7月瑜伽课程系列\",\n  \"capacity\": 20,\n  \"price\": 10800,\n  \"level\": 1,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量创建课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/batch-courses/create", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-29\",\n  \"end_date\": \"2025-08-05\",\n  \"start_time\": \"09:00\",\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_name\": \"瑜伽基础\",\n      \"description\": \"适合初学者的基础瑜伽课程\",\n      \"coach_ids\": [\"<<coach_id>>\"],\n      \"duration\": 90\n    }\n  ],\n  \"global_description\": \"2025年7月瑜伽课程系列\",\n  \"capacity\": 20,\n  \"price\": 10800,\n  \"level\": 1,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {"200": {"description": "批量创建成功", "body": "{\n  \"code\": 0,\n  \"message\": \"Ok\",\n  \"data\": {\n    \"message\": \"批量创建课程成功\",\n    \"created_count\": 7,\n    \"created_courses\": [\n      {\n        \"id\": 123456,\n        \"name\": \"瑜伽基础\",\n        \"start_time\": \"2025-07-29T09:00:00Z\",\n        \"end_time\": \"2025-07-29T10:30:00Z\",\n        \"capacity\": 20,\n        \"price\": 10800,\n        \"coach_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n        \"store_id\": 1,\n        \"status\": 1\n      }\n    ]\n  }\n}"}, "400": {"description": "参数错误或时间冲突", "body": "{\n  \"code\": 400,\n  \"message\": \"存在时间冲突或参数错误\",\n  \"data\": {\n    \"conflicts\": [\n      {\n        \"date\": \"2025-07-29\",\n        \"time\": \"09:00-10:30\",\n        \"reason\": \"教练时间冲突\"\n      }\n    ]\n  }\n}"}}}]}, {"v": 1, "id": "course-scheduling-folder", "name": "📅 排课管理", "folders": [], "requests": [{"v": "14", "name": "课程排课（单周）", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/schedule", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Scheduling successful\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.expect(jsonData.data.scheduled_count).toBeGreaterThan(0);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-21\",\n  \"end_date\": \"2025-07-27\",\n  \"start_time\": \"12:00\",\n  \"store_id\": 1,\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_id\": \"<<yoga_basic_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n      \"classroom_id\": 1\n    },\n    {\n      \"weekday\": 3,\n      \"course_id\": \"<<flow_yoga_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440001\", \"550e8400-e29b-41d4-a716-446655440002\"],\n      \"classroom_id\": 2\n    },\n    {\n      \"weekday\": 5,\n      \"course_id\": \"<<yoga_basic_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440003\"],\n      \"classroom_id\": 1\n    }\n  ]\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "课程排课（跨周期）", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/schedule", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Multi-week scheduling successful\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.expect(jsonData.data.scheduled_count).toBeGreaterThan(5);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-17\",\n  \"end_date\": \"2025-08-17\",\n  \"start_time\": \"19:00\",\n  \"store_id\": 1,\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_id\": \"<<yoga_basic_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n      \"classroom_id\": 1\n    },\n    {\n      \"weekday\": 3,\n      \"course_id\": \"<<flow_yoga_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440001\"],\n      \"classroom_id\": 2\n    }\n  ]\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "user-folder", "name": "👥 用户管理模块", "folders": [], "requests": [{"v": "14", "name": "创建管理员用户", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/users", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"User created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"user_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"username\": \"admin_user\",\n  \"password\": \"admin123\",\n  \"email\": \"<EMAIL>\",\n  \"role_ids\": [\n    1\n  ]\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新用户角色权限", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/users/1/roles", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"role_ids\": [1, 2]\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除用户", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/users/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取当前用户菜单", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/users/menus", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "role-folder", "name": "🔐 角色管理模块", "folders": [], "requests": [{"v": "14", "name": "获取角色列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/roles", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建角色", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/roles", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Role created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"role_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"店长\",\n  \"description\": \"门店管理员角色\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取角色详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/roles/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新角色", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/roles/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"高级店长\",\n  \"description\": \"高级门店管理员角色\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除角色", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/roles/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量删除角色", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/roles", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "[1, 2, 3]"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取角色权限列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/roles/1/permissions", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新角色权限", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/roles/1/permissions", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"permission_ids\": [1, 2, 3, 4, 5]\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "permission-folder", "name": "🔑 权限管理模块", "folders": [], "requests": [{"v": "14", "name": "获取权限列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/permissions", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建权限", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/permissions", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Permission created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"permission_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"用户管理\",\n  \"key\": \"user:manage\",\n  \"description\": \"用户管理权限\",\n  \"module\": \"user\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取权限详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/permissions/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新权限信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/permissions/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"用户更新\",\n  \"key\": \"user:update\",\n  \"description\": \"更新用户信息的权限\",\n  \"module\": \"user\",\n  \"action\": \"update\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除权限", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/permissions/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量删除权限", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/permissions", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "[1, 2, 3]"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "membership-type-folder", "name": "💳 会员卡类型管理", "folders": [], "requests": [{"v": "14", "name": "获取会员卡类型列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-types", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建会员卡类型", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/membership-types", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽次卡\",\n  \"category\": \"times\",\n  \"scope\": \"group\",\n  \"description\": \"10次瑜伽课程卡\",\n  \"validity_days\": 90,\n  \"times\": 10,\n  \"amount\": 0,\n  \"price\": 100000,\n  \"discount\": 1.0,\n  \"shareable\": false,\n  \"can_set_expiry\": true,\n  \"can_select_stores\": false,\n  \"can_add_sub_card\": false,\n  \"can_set_daily_limit\": false,\n  \"daily_booking_limit\": 1,\n  \"applicable_courses\": \"all\",\n  \"sort_order\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取会员卡类型详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-types/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新会员卡类型", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-types/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"次卡升级版\",\n  \"category\": \"times\",\n  \"scope\": \"group\",\n  \"description\": \"15次课程，90天有效期\",\n  \"validity_days\": 90,\n  \"times\": 15,\n  \"amount\": 0,\n  \"price\": 150000,\n  \"discount\": 0.9,\n  \"shareable\": false,\n  \"can_set_expiry\": true,\n  \"can_select_stores\": false,\n  \"can_add_sub_card\": false,\n  \"can_set_daily_limit\": false,\n  \"daily_booking_limit\": 1,\n  \"applicable_courses\": \"all\",\n  \"sort_order\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除会员卡类型", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/membership-types/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "membership-card-folder", "name": "💰 会员卡实例管理", "folders": [], "requests": [{"v": "14", "name": "获取会员卡列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "user_id", "value": "1", "active": false}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {"200": {"description": "获取成功", "body": "{\n  \"code\": 0,\n  \"message\": \"Ok\",\n  \"data\": {\n    \"list\": [\n      {\n        \"id\": 123456,\n        \"card_number\": \"YG20250729001\",\n        \"user_name\": \"张三\",\n        \"user_phone\": \"13800138000\",\n        \"card_type_name\": \"瑜伽次卡\",\n        \"category\": \"times\",\n        \"remaining_times\": 8,\n        \"remaining_amount\": 0,\n        \"start_date\": \"2025-07-19\",\n        \"end_date\": \"2025-10-17\",\n        \"status\": 1,\n        \"created_at\": \"2025-07-29 10:30:00\"\n      }\n    ],\n    \"total\": 1,\n    \"page\": 1,\n    \"page_size\": 10\n  }\n}"}}}, {"v": "14", "name": "获取会员卡详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建新会员卡", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Card created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"membership_card_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"user_id\": \"<<user_id>>\",\n  \"card_type_id\": \"<<card_type_id>>\",\n  \"start_date\": \"2025-07-19T00:00:00Z\",\n  \"purchase_price\": 100000,\n  \"discount\": 0.9,\n  \"payment_method\": \"cash\",\n  \"available_stores\": [\n    1\n  ],\n  \"daily_booking_limit\": 2,\n  \"remark\": \"新用户开卡\"\n}"}, "requestVariables": [], "responses": {"200": {"description": "创建成功", "body": "{\n  \"code\": 0,\n  \"message\": \"Ok\",\n  \"data\": {\n    \"id\": 123456,\n    \"card_number\": \"YG20250729001\",\n    \"user_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n    \"card_type_id\": 1,\n    \"card_type_name\": \"瑜伽次卡\",\n    \"remaining_times\": 10,\n    \"remaining_amount\": 0,\n    \"start_date\": \"2025-07-19\",\n    \"end_date\": \"2025-10-17\",\n    \"purchase_price\": 100000,\n    \"discount\": 0.9,\n    \"status\": 1,\n    \"created_at\": \"2025-07-29 10:30:00\"\n  }\n}"}, "400": {"description": "参数错误", "body": "{\n  \"code\": 400,\n  \"message\": \"请求参数错误\",\n  \"data\": null\n}"}, "404": {"description": "资源不存在", "body": "{\n  \"code\": 404,\n  \"message\": \"用户不存在或卡种不存在\",\n  \"data\": null\n}"}}}, {"v": "14", "name": "创建副卡", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/sub-cards", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"main_card_id\": 1,\n  \"user_id\": \"<<user_id>>\",\n  \"remark\": \"家属副卡\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取副卡列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/sub-cards", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "转让会员卡", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/transfer", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"target_phone\": \"13800138000\",\n  \"fee\": 5000,\n  \"remark\": \"用户要求转让\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "会员卡充值", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/recharge", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"recharge_times\": 5,\n  \"recharge_amount\": 50000,\n  \"payment_method\": \"offline\",\n  \"payment_card_id\": null,\n  \"actual_amount\": 50000,\n  \"extend_expiry\": false,\n  \"new_end_date\": null,\n  \"remark\": \"用户充值\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "会员卡扣费", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/deduct", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"amount\": 10000,\n  \"times\": 1,\n  \"reason\": \"课程消费\",\n  \"remark\": \"管理员手动扣费\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "冻结/解冻会员卡", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/freeze", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"action\": \"freeze\",\n  \"days\": 30,\n  \"reason\": \"用户违规\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "升级会员卡", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/upgrade", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"target_card_type_id\": 2,\n  \"remark\": \"用户要求升级到高级会员卡\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "请假会员卡", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/leave", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"leave_days\": 30,\n  \"reason\": \"出差请假\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "延期会员卡", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/extend", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"extend_days\": 30,\n  \"reason\": \"系统故障补偿\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取交易记录", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/membership-cards/1/transactions", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "20", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "course-category-folder", "name": "🏷️ 课程分类管理", "folders": [], "requests": [{"v": "14", "name": "获取课程分类列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-categories", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取所有启用的课程分类", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-categories/all", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程分类详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-categories/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建课程分类", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/course-categories", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽团课\",\n  \"description\": \"瑜伽团体课程\",\n  \"sort_order\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新课程分类", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/course-categories/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽团课进阶\",\n  \"description\": \"进阶版瑜伽团体课程\",\n  \"sort_order\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除课程分类", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/course-categories/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "flexible-deduction-folder", "name": "🔄 灵活扣减系统", "folders": [], "requests": [{"v": "14", "name": "获取课程类型配置列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/flexible/course-types", "params": [{"key": "status", "value": "1", "description": "状态筛选 1:启用 2:停用"}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": ""}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建课程类型配置", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/flexible/course-types", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"type_code\": \"yoga_group\",\n  \"type_name\": \"瑜伽团课\",\n  \"category\": \"group\",\n  \"description\": \"瑜伽团体课程\",\n  \"sort_order\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取灵活扣减规则列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/flexible/deduction-rules", "params": [{"key": "page", "value": "1", "description": "页码"}, {"key": "page_size", "value": "20", "description": "每页数量"}, {"key": "card_type_id", "value": "1", "description": "会员卡类型ID筛选"}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": ""}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建灵活扣减规则", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/flexible/deduction-rules", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"card_type_id\": 1,\n  \"course_type_code\": \"yoga_group\",\n  \"deduction_type\": \"times\",\n  \"deduction_times\": 1,\n  \"deduction_amount\": 0,\n  \"per_person_deduction\": false,\n  \"daily_limit\": 2,\n  \"min_people_count\": 1,\n  \"max_people_count\": 0,\n  \"min_booking_hours\": 2,\n  \"max_booking_days\": 7,\n  \"allow_cancellation\": true,\n  \"cancellation_hours\": 2,\n  \"priority\": 10,\n  \"rule_name\": \"瑜伽团课次数扣减\",\n  \"description\": \"瑜伽团课每次扣减1次\",\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "测试扣减规则", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/flexible/test-deduction", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"card_type_id\": 1,\n  \"course_id\": 1,\n  \"course_type_code\": \"yoga_group\",\n  \"course_name\": \"瑜伽基础课程\",\n  \"people_count\": 1,\n  \"booking_time\": \"2024-01-20T10:00:00Z\",\n  \"user_id\": \"user-001\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "切换扣减规则状态", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/flexible/deduction-rules/1/toggle", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取会员卡支持的课程类型", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/flexible/card-types/1/supported-courses", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": ""}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "store-folder", "name": "🏪 门店管理", "folders": [], "requests": [{"v": "14", "name": "获取门店列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/stores", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建门店", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/stores", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Store created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"store_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽生活馆\",\n  \"address\": \"深圳市南山区科技园\",\n  \"phone\": \"0755-12345678\",\n  \"latitude\": 22.5431,\n  \"longitude\": 114.0579,\n  \"image_ids\": [\"<<file_id>>\"],\n  \"description\": \"专业瑜伽培训中心\",\n  \"sort_order\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取门店详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/stores/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新门店信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/stores/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽馆总店\",\n  \"address\": \"北京市朝阳区三里屯路123号\",\n  \"phone\": \"010-12345678\",\n  \"latitude\": 39.9042,\n  \"longitude\": 116.4074,\n  \"image_ids\": [\"<<file_id>>\"],\n  \"description\": \"环境优雅的瑜伽馆总店\",\n  \"sort_order\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除门店", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/stores/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取门店教室列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/stores/1/classrooms", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "booking-folder", "name": "📋 预约管理", "folders": [], "requests": [{"v": "14", "name": "获取预约列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/bookings", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "status", "value": "confirmed", "active": false}, {"key": "store_id", "value": "1", "active": false}, {"key": "coach_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取预约详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/bookings/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新预约状态", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/bookings/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"status\": 3\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取预约统计数据", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/bookings/stats", "params": [{"key": "start_date", "value": "2025-07-01", "active": false}, {"key": "end_date", "value": "2025-07-31", "active": false}, {"key": "store_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "自动取消课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/bookings/auto-cancel", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"schedule_id\": 1,\n  \"reason\": \"人数不足，自动取消\"\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "notification-folder", "name": "📢 消息通知管理", "folders": [], "requests": [{"v": "14", "name": "获取消息列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/notifications", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "type", "value": "system", "active": false}, {"key": "is_read", "value": "false", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取未读消息数量", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/notifications/unread-count", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "标记消息为已读", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/notifications/1/read", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取消息统计", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/notifications/stats", "params": [{"key": "start_date", "value": "2025-07-01", "active": false}, {"key": "end_date", "value": "2025-07-31", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "标记所有消息为已读", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/notifications/read-all", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除消息", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/notifications/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "report-folder", "name": "📊 报表管理", "folders": [], "requests": [{"v": "14", "name": "会员卡销售报表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/reports/card-sales", "params": [{"key": "start_date", "value": "2025-07-01", "active": true}, {"key": "end_date", "value": "2025-07-31", "active": true}, {"key": "store_id", "value": "1", "active": false}, {"key": "sales_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "预约报表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/reports/bookings", "params": [{"key": "start_date", "value": "2025-07-01", "active": true}, {"key": "end_date", "value": "2025-07-31", "active": true}, {"key": "store_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "课程报表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/reports/courses", "params": [{"key": "start_date", "value": "2025-07-01", "active": true}, {"key": "end_date", "value": "2025-07-31", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "教练报表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/reports/coaches", "params": [{"key": "start_date", "value": "2025-07-01", "active": true}, {"key": "end_date", "value": "2025-07-31", "active": true}, {"key": "coach_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "operation-log-folder", "name": "📋 操作日志管理", "folders": [], "requests": [{"v": "14", "name": "获取操作日志列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/logs", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "user_id", "value": "1", "active": false}, {"key": "module", "value": "user", "active": false}, {"key": "action", "value": "create", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取操作日志详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/logs/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除操作日志", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/logs/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取操作统计", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/logs/stats", "params": [{"key": "start_date", "value": "2025-07-01", "active": false}, {"key": "end_date", "value": "2025-07-31", "active": false}, {"key": "module", "value": "user", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "清理旧日志", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/logs/clean", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"keep_days\": 90\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取日志模块列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/logs/modules", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取日志动作列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/logs/actions", "params": [{"key": "module", "value": "user", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "file-folder", "name": "📁 文件管理", "folders": [], "requests": [{"v": "14", "name": "文件上传", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/files/upload", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"File uploaded successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.file_id) {\n        pw.env.set(\"file_id\", jsonData.data.file_id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "multipart/form-data", "body": [{"key": "file", "type": "file", "src": "avatar.jpg"}, {"key": "purpose", "value": "avatar", "type": "text"}]}, "requestVariables": [], "responses": {"200": {"description": "上传成功", "body": "{\n  \"code\": 0,\n  \"message\": \"Ok\",\n  \"data\": {\n    \"file_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n    \"download_url\": \"/api/v1/file/download/2025/07/29/550e8400-e29b-41d4-a716-446655440000.jpg\",\n    \"object_name\": \"2025/07/29/550e8400-e29b-41d4-a716-446655440000.jpg\",\n    \"bucket_name\": \"default\",\n    \"purpose\": \"avatar\",\n    \"file_size\": 102400,\n    \"content_type\": \"image/jpeg\"\n  }\n}"}, "400": {"description": "上传失败", "body": "{\n  \"code\": 400,\n  \"message\": \"文件上传失败或参数错误\",\n  \"data\": null\n}"}}}, {"v": "14", "name": "获取文件列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/files", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "purpose", "value": "avatar", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除文件", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/files/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量删除文件", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/files", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "[\"550e8400-e29b-41d4-a716-446655440000\", \"550e8400-e29b-41d4-a716-446655440001\", \"550e8400-e29b-41d4-a716-446655440002\"]"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取实体关联的文件列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/files/by-entity", "params": [{"key": "entity_type", "value": "user", "active": true}, {"key": "entity_id", "value": "1", "active": true}, {"key": "purpose", "value": "avatar", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "staff-folder", "name": "👥 员工管理", "folders": [], "requests": [{"v": "14", "name": "获取员工列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/staff", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}, {"key": "role", "value": "sales", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取在职员工列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/staff/active", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新员工状态", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/staff/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"is_active\": true\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取销售员工列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/staff/sales", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "coach-status-folder", "name": "🧘‍♀️ 教练状态管理", "folders": [], "requests": [{"v": "14", "name": "获取教练列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/coach-status", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新教练状态", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/coach-status/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"is_active\": true\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "menu-folder", "name": "📋 菜单管理", "folders": [], "requests": [{"v": "14", "name": "获取菜单列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/menus", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建菜单", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/menus", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Menu created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"menu_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"title\": \"用户管理\",\n  \"path\": \"/users\",\n  \"component\": \"UserManagement\",\n  \"type\": 2,\n  \"icon\": \"user\",\n  \"parent_id\": 0,\n  \"sort\": 1,\n  \"status\": 1,\n  \"permission\": \"user:list\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取菜单详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/menus/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新菜单信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/menus/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"title\": \"用户管理\",\n  \"path\": \"/users\",\n  \"component\": \"UserManagement\",\n  \"type\": 2,\n  \"icon\": \"user\",\n  \"parent_id\": 0,\n  \"sort\": 1,\n  \"status\": 1,\n  \"permission\": \"user:list\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除菜单", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/menus/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量删除菜单", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/menus", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "[1, 2, 3]"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "classroom-folder", "name": "🏛️ 教室管理", "folders": [], "requests": [{"v": "14", "name": "获取教室列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/classrooms", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "store_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建教室", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/classrooms", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"store_id\": 1,\n  \"name\": \"瑜伽教室A\",\n  \"capacity\": 20,\n  \"equipment\": \"瑜伽垫、瑜伽球、拉力带\",\n  \"description\": \"宽敞明亮的瑜伽教室\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取教室详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/classrooms/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新教室信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/classrooms/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽教室A升级版\",\n  \"capacity\": 25,\n  \"equipment\": \"瑜伽垫、瑜伽球、拉力带、瑜伽砖\",\n  \"description\": \"宽敞明亮的瑜伽教室，设备齐全\",\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除教室", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/classrooms/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取门店下的教室列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/stores/1/classrooms", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "schedule-folder", "name": "📅 课程排期管理", "folders": [], "requests": [{"v": "14", "name": "获取课程排期列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/schedules", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "start_date", "value": "2025-07-15", "active": false}, {"key": "end_date", "value": "2025-07-21", "active": false}, {"key": "coach_id", "value": "1", "active": false}, {"key": "store_id", "value": "1", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建课程排期", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/schedules", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"哈他瑜伽基础课排期\",\n  \"description\": \"适合初学者的哈他瑜伽课程排期\",\n  \"start_time\": \"2025-07-20T09:00:00Z\",\n  \"end_time\": \"2025-07-20T10:30:00Z\",\n  \"duration\": 90,\n  \"capacity\": 20,\n  \"price\": 8000,\n  \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n  \"store_id\": 1,\n  \"classroom_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新课程排期", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/schedules/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"哈他瑜伽基础课排期（更新）\",\n  \"description\": \"更新后的课程排期描述\",\n  \"start_time\": \"2025-07-20T10:00:00Z\",\n  \"end_time\": \"2025-07-20T11:30:00Z\",\n  \"duration\": 90,\n  \"capacity\": 25,\n  \"price\": 90.0,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "banner-folder", "name": "🎠 轮播图管理", "folders": [], "requests": [{"v": "14", "name": "获取轮播图列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/banners", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "type", "value": "home", "active": false}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建轮播图", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/banners", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"title\": \"夏季瑜伽特惠\",\n  \"file_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n  \"link_url\": \"https://example.com/summer-yoga\",\n  \"description\": \"夏季瑜伽课程特惠活动\",\n  \"sort_order\": 1,\n  \"position\": \"home\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取轮播图详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新轮播图", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"title\": \"秋季瑜伽特惠\",\n  \"file_id\": \"550e8400-e29b-41d4-a716-446655440001\",\n  \"link_url\": \"https://example.com/autumn-yoga\",\n  \"description\": \"秋季瑜伽课程特惠活动\",\n  \"sort_order\": 1,\n  \"status\": 1,\n  \"position\": \"home\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除轮播图", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "course-review-folder", "name": "⭐ 课程评价管理", "folders": [], "requests": [{"v": "14", "name": "获取课程评价列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-reviews", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "course_id", "value": "1", "active": false}, {"key": "coach_id", "value": "1", "active": false}, {"key": "rating", "value": "5", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程评价详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-reviews/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除课程评价", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/course-reviews/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取评价统计", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/course-reviews/stats", "params": [{"key": "course_id", "value": "1", "active": false}, {"key": "coach_id", "value": "1", "active": false}, {"key": "start_date", "value": "2025-07-01", "active": false}, {"key": "end_date", "value": "2025-07-31", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "resource-assignment-folder", "name": "🔄 资源分配管理", "folders": [], "requests": [{"v": "14", "name": "分配会员给销售", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/resource-assignments/members", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"sales_staff_id\": 1,\n  \"member_ids\": [\n    1,\n    2,\n    3\n  ],\n  \"remark\": \"批量分配会员给销售\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取销售负责的会员列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/resource-assignments/sales/1/members", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "转移会员给其他销售", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/resource-assignments/members/transfer", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"from_sales_staff_id\": 1,\n  \"target_sales_staff_id\": 2,\n  \"member_id\": 1,\n  \"remark\": \"销售调整\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取未分配的会员列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/resource-assignments/members/unassigned", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "移除会员分配", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/resource-assignments/members", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"sales_staff_id\": 1,\n  \"member_id\": 1\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "user-store-assignment-folder", "name": "🏪 用户门店分配管理", "folders": [], "requests": [{"v": "14", "name": "分配用户到门店", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"user_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n  \"store_id\": 1,\n  \"remark\": \"分配用户到门店\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取用户门店分配列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments/users/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取门店的用户列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments/stores/1", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "移除用户门店分配", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"user_id\": \"550e8400-e29b-41d4-a716-446655440000\",\n  \"store_id\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新用户平台访问权限", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments/users/1/platform-access", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"platform_access\": \"admin,app\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "根据平台获取用户列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/user-store-assignments/users", "params": [{"key": "platform", "value": "admin", "active": true}, {"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "course-folder", "name": "📚 课程管理", "folders": [], "requests": [{"v": "14", "name": "获取课程列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/courses", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}, {"key": "category_id", "value": "1", "active": false}, {"key": "level", "value": "beginner", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建瑜伽基础课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Course created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"yoga_basic_course_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"瑜伽基础\",\n  \"description\": \"适合初学者的基础瑜伽课程，注重体式正位和呼吸配合\",\n  \"duration\": 60,\n  \"capacity\": 20,\n  \"price\": 10800,\n  \"level\": 1,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建流瑜伽课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Course created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"flow_yoga_course_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"流瑜伽\",\n  \"description\": \"动态流动的瑜伽练习，提升力量和柔韧性\",\n  \"duration\": 75,\n  \"capacity\": 15,\n  \"price\": 12800,\n  \"level\": 2,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建阴瑜伽课程", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Course created successfully\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    if (jsonData.data && jsonData.data.id) {\n        pw.env.set(\"yin_yoga_course_id\", jsonData.data.id);\n    }\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"阴瑜伽\",\n  \"description\": \"深度放松的静态瑜伽练习，平衡身心能量\",\n  \"duration\": 90,\n  \"capacity\": 12,\n  \"price\": 15800,\n  \"level\": 2,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "课程排课", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/schedule", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Course scheduling successful\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.expect(jsonData.data.scheduled_count).toBeGreaterThan(0);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-17\",\n  \"end_date\": \"2025-07-27\",\n  \"start_time\": \"12:00\",\n  \"store_id\": 1,\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_id\": \"<<yoga_basic_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n      \"classroom_id\": 1\n    },\n    {\n      \"weekday\": 3,\n      \"course_id\": \"<<flow_yoga_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440001\"],\n      \"classroom_id\": 2\n    },\n    {\n      \"weekday\": 5,\n      \"course_id\": \"<<yoga_basic_course_id>>\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440002\"],\n      \"classroom_id\": 1\n    }\n  ]\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "批量创建课程（兼容接口）", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/batch", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Batch course creation successful\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.expect(jsonData.data.created_count).toBeGreaterThan(0);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"start_date\": \"2025-07-17\",\n  \"end_date\": \"2025-07-27\",\n  \"start_time\": \"12:00\",\n  \"weekly_schedules\": [\n    {\n      \"weekday\": 1,\n      \"course_name\": \"瑜伽基础\",\n      \"description\": \"适合初学者的基础瑜伽课程，注重体式正位和呼吸配合\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n      \"duration\": 60\n    },\n    {\n      \"weekday\": 3,\n      \"course_name\": \"流瑜伽\",\n      \"description\": \"动态流动的瑜伽练习，提升力量和柔韧性\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440001\", \"550e8400-e29b-41d4-a716-446655440002\"],\n      \"duration\": 75\n    },\n    {\n      \"weekday\": 0,\n      \"course_name\": \"阴瑜伽\",\n      \"description\": \"深度放松的静态瑜伽练习，平衡身心能量\",\n      \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440001\"],\n      \"duration\": 90\n    }\n  ],\n  \"global_description\": \"2025年7月瑜伽课程系列\",\n  \"capacity\": 20,\n  \"price\": 10800,\n  \"level\": 2,\n  \"category_id\": 1,\n  \"store_id\": 1,\n  \"type\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/courses/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新课程信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/courses/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"name\": \"哈他瑜伽进阶课\",\n  \"description\": \"适合有基础的学员的哈他瑜伽课程\",\n  \"start_time\": \"2025-07-20T09:00:00Z\",\n  \"end_time\": \"2025-07-20T10:30:00Z\",\n  \"duration\": 90,\n  \"capacity\": 15,\n  \"price\": 10000,\n  \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n  \"store_id\": 1,\n  \"classroom_id\": 1,\n  \"type\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除课程", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/courses/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取课程支持的会员卡类型", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/courses/1/supported-card-types", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "课程预约（会员卡扣减）", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/<<yoga_basic_course_id>>/book", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});\n\npw.test(\"Booking successful\", () => {\n    var jsonData = pw.response.body;\n    pw.expect(jsonData.code).toBe(0);\n    pw.expect(jsonData.data.booking_id).toBeGreaterThan(0);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"user_id\": \"550e8400-e29b-41d4-a716-446655440000\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "检查课程时间冲突", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/courses/check-conflict", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>", "active": true}], "body": {"contentType": "application/json", "body": "{\n  \"coach_ids\": [\"550e8400-e29b-41d4-a716-446655440000\"],\n  \"classroom_id\": 1,\n  \"start_time\": \"2025-07-20T09:00:00Z\",\n  \"end_time\": \"2025-07-20T10:30:00Z\",\n  \"course_id\": null\n}"}, "preRequestScript": "", "testScript": "", "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "coach-folder", "name": "🧘‍♀️ 教练管理", "folders": [], "requests": [{"v": "14", "name": "获取教练列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/coaches", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "search", "value": "", "active": false}, {"key": "store_id", "value": "1", "active": false}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取教练详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/coaches/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新教练信息", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/coaches/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"nick_name\": \"张教练\",\n  \"description\": \"资深瑜伽教练，10年教学经验\",\n  \"experience\": 10,\n  \"specialty\": \"哈他瑜伽、阴瑜伽\",\n  \"avatar\": \"<<file_id>>\",\n  \"gender\": 2,\n  \"country\": \"中国\",\n  \"province\": \"广东省\",\n  \"city\": \"深圳市\"\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新教练状态", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/coaches/1/status", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"is_active\": true\n}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "coach-banner-folder", "name": "🧘‍♀️ 教练轮播图管理", "folders": [], "requests": [{"v": "14", "name": "获取指定教练的轮播图列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/coach-banners/coach/1", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "10", "active": true}, {"key": "status", "value": "active", "active": false}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "创建教练轮播图", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/coach-banners", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"coach_id\": \"<<coach_id>>\",\n  \"title\": \"明星教练推荐\",\n  \"file_id\": \"<<file_id>>\",\n  \"sort_order\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "获取教练轮播图详情", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/coach-banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "更新教练轮播图", "method": "PUT", "endpoint": "<<baseURL>>/api/v1/admin/coach-banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{\n  \"title\": \"资深教练推荐\",\n  \"file_id\": \"550e8400-e29b-41d4-a716-446655440001\",\n  \"sort_order\": 1,\n  \"status\": 1\n}"}, "requestVariables": [], "responses": {}}, {"v": "14", "name": "删除教练轮播图", "method": "DELETE", "endpoint": "<<baseURL>>/api/v1/admin/coach-banners/1", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": null, "body": null}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "queue-folder", "name": "🎯 排队管理", "folders": [], "requests": [{"v": "14", "name": "手动处理排队通知", "method": "POST", "endpoint": "<<baseURL>>/api/v1/admin/queue/courses/1/notify", "params": [], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "body": {"contentType": "application/json", "body": "{}"}, "requestVariables": [], "responses": {}}]}, {"v": 1, "id": "user-management-folder", "name": "👥 用户管理", "folders": [], "requests": [{"v": "14", "name": "获取用户列表", "method": "GET", "endpoint": "<<baseURL>>/api/v1/admin/users", "params": [{"key": "page", "value": "1", "active": true}, {"key": "page_size", "value": "20", "active": true}], "headers": [{"key": "Authorization", "value": "Bearer <<access_token>>"}], "preRequestScript": "", "testScript": "pw.test(\"Status code is 200\", () => {\n    pw.expect(pw.response.status).toBe(200);\n});", "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "requestVariables": [], "responses": {}}]}], "requests": [], "auth": {"authType": "bearer", "authActive": true, "token": "<<access_token>>"}, "headers": [], "variables": [{"key": "baseURL", "value": "http://localhost:9095", "secret": false}, {"key": "access_token", "value": "", "secret": true}, {"key": "refresh_token", "value": "", "secret": true}]}