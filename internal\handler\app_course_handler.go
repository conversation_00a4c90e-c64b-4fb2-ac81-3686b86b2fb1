package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// AppCourseHandler 小程序端课程处理器
type AppCourseHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewAppCourseHandler 创建小程序端课程处理器实例
func NewAppCourseHandler(db *gorm.DB, storage storage.Storage) *AppCourseHandler {
	return &AppCourseHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetCourses 获取课程列表（小程序端）
func (h *AppCourseHandler) GetCourses(c *gin.Context) {
	var req dto.CourseListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件 - 只显示正常状态的课程
	query := h.db.Model(&model.Course{}).
		Preload("Store").
		Preload("CategoryRef").
		Where("status = 1")

	// 门店筛选
	if req.StoreID != "" {
		if storeIDInt := cast.ToUint(req.StoreID); storeIDInt > 0 {
			query = query.Where("store_id = ?", storeIDInt)
		}
	}

	// 教练筛选 - 使用PostgreSQL数组查询
	if req.CoachID != "" {
		query = query.Where("? = ANY(coach_ids)", req.CoachID)
	}

	// 课程类型筛选
	if req.Type != "" {
		if typeInt := cast.ToInt(req.Type); typeInt > 0 {
			query = query.Where("type = ?", typeInt)
		}
	}

	// 课程级别筛选
	if req.Level != "" {
		if levelInt := cast.ToInt(req.Level); levelInt > 0 {
			query = query.Where("level = ?", levelInt)
		}
	}

	// 课程分类筛选
	if req.CategoryID != "" {
		if categoryIDInt := cast.ToUint(req.CategoryID); categoryIDInt > 0 {
			query = query.Where("category_id = ?", categoryIDInt)
		}
	}

	// 时间筛选 - 只显示未来的课程
	showPast := c.Query("show_past")
	if showPast != "true" {
		query = query.Where("start_time > ?", time.Now())
	}

	// 关键词搜索
	if req.Keyword != "" {
		query = query.Where("name LIKE ?", "%"+req.Keyword+"%")
	}

	// 查询总数
	var total int64
	query.Count(&total)

	// 分页查询 - 按开始时间和创建时间排序
	var courses []model.Course
	err := query.Order("start_time ASC, created_at DESC").
		Offset(req.GetOffset()).
		Limit(req.PageSize).
		Find(&courses).Error

	if err != nil {
		log.Error("查询课程失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		return
	}

	// 转换为小程序端格式
	var courseList []dto.AppCourseItem
	for _, course := range courses {
		// 获取教练信息
		coach := h.getCoachInfo(course.CoachIDs)

		courseItem := dto.AppCourseItem{
			ID:             course.ID,
			Name:           course.Name,
			Description:    course.Description,
			StartTime:      course.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:        course.EndTime.Format("2006-01-02 15:04:05"),
			Duration:       course.Duration,
			Capacity:       course.Capacity,
			EnrollCount:    course.EnrollCount,
			Price:          course.Price,
			Type:           int(course.Type),
			CoachName:      getCoachName(coach),
			CoachAvatar:    h.getCoachAvatarURL(coach),
			AvailableSpots: course.Capacity - course.EnrollCount,
		}

		// 安全地设置门店信息
		if course.Store.ID != 0 {
			courseItem.StoreName = course.Store.Name
			courseItem.StoreAddress = course.Store.Address
		}

		// 判断是否可预约
		courseItem.CanBook = course.StartTime.After(time.Now()) && courseItem.AvailableSpots > 0

		courseList = append(courseList, courseItem)
	}

	// 构建响应
	response := dto.PageResponse{
		List:     courseList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// GetCourse 获取课程详情（小程序端）
func (h *AppCourseHandler) GetCourse(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("课程ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var course model.Course
	err := h.db.Preload("Store").Preload("CategoryRef").First(&course, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "课程不存在"))
		} else {
			log.Error("查询课程失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		}
		return
	}

	// 获取教练信息
	coach := h.getCoachInfo(course.CoachIDs)

	// 构建详情响应
	courseDetail := dto.AppCourseDetail{
		ID:          course.ID,
		Name:        course.Name,
		Description: course.Description,
		StartTime:   course.StartTime.Format("2006-01-02 15:04:05"),
		EndTime:     course.EndTime.Format("2006-01-02 15:04:05"),
		Duration:    course.Duration,
		Capacity:    course.Capacity,
		EnrollCount: course.EnrollCount,
		Price:       course.Price,
		Type:        int(course.Type),
		Status:      int(course.Status),
		CoachInfo: dto.AppCoachInfo{
			ID: func() string {
				if coach != nil {
					return coach.ID.String()
				}
				return ""
			}(),
			Name:   getCoachName(coach),
			Avatar: h.getCoachAvatarURL(coach),
			Experience: func() int {
				if coach != nil {
					return coach.Experience
				}
				return 0
			}(),
			Speciality: func() string {
				if coach != nil {
					return coach.Speciality
				}
				return ""
			}(),
			Description: func() string {
				if coach != nil {
					return coach.CoachDescription
				}
				return ""
			}(),
		},
		StoreInfo: dto.AppStoreInfo{
			ID:        course.Store.ID,
			Name:      course.Store.Name,
			Address:   course.Store.Address,
			Phone:     course.Store.Phone,
			Latitude:  course.Store.Latitude,
			Longitude: course.Store.Longitude,
		},
		AvailableSpots: course.Capacity - course.EnrollCount,
	}

	// 判断是否可预约
	courseDetail.CanBook = course.StartTime.After(time.Now()) && courseDetail.AvailableSpots > 0

	code.AutoResponse(c, courseDetail, nil)
}

// GetCoursesByDate 按日期获取课程列表
func (h *AppCourseHandler) GetCoursesByDate(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		dateStr = time.Now().Format("2006-01-02")
	}

	// 解析日期
	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "日期格式错误"))
		return
	}

	// 查询当天的课程
	startTime := date
	endTime := date.Add(24 * time.Hour)

	// 构建查询条件
	query := h.db.Preload("Store").Preload("CategoryRef").
		Where("status = 1 AND start_time >= ? AND start_time < ?", startTime, endTime)

	// 支持筛选参数
	if storeID := c.Query("store_id"); storeID != "" {
		if storeIDInt := cast.ToUint(storeID); storeIDInt > 0 {
			query = query.Where("store_id = ?", storeIDInt)
		}
	}

	if coachID := c.Query("coach_id"); coachID != "" {
		query = query.Where("coach_id = ?", coachID)
	}

	if courseType := c.Query("type"); courseType != "" {
		if typeInt := cast.ToInt(courseType); typeInt > 0 {
			query = query.Where("type = ?", typeInt)
		}
	}

	if level := c.Query("level"); level != "" {
		if levelInt := cast.ToInt(level); levelInt > 0 {
			query = query.Where("level = ?", levelInt)
		}
	}

	if categoryID := c.Query("category_id"); categoryID != "" {
		if categoryIDInt := cast.ToUint(categoryID); categoryIDInt > 0 {
			query = query.Where("category_id = ?", categoryIDInt)
		}
	}

	var courses []model.Course
	err = query.Order("start_time ASC").Find(&courses).Error

	if err != nil {
		log.Error("查询日期课程失败", "date", dateStr, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		return
	}

	// 转换为小程序端格式
	var courseList []dto.AppCourseItem
	for _, course := range courses {
		// 获取教练信息
		coach := h.getCoachInfo(course.CoachIDs)

		courseItem := dto.AppCourseItem{
			ID:             course.ID,
			Name:           course.Name,
			Description:    course.Description,
			StartTime:      course.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:        course.EndTime.Format("2006-01-02 15:04:05"),
			Duration:       course.Duration,
			Capacity:       course.Capacity,
			EnrollCount:    course.EnrollCount,
			Price:          course.Price,
			Type:           int(course.Type),
			CoachName:      getCoachName(coach),
			CoachAvatar:    h.getCoachAvatarURL(coach),
			AvailableSpots: course.Capacity - course.EnrollCount,
		}

		// 安全地设置门店信息
		if course.Store.ID != 0 {
			courseItem.StoreName = course.Store.Name
			courseItem.StoreAddress = course.Store.Address
		}

		// 判断是否可预约
		courseItem.CanBook = course.StartTime.After(time.Now()) && courseItem.AvailableSpots > 0

		courseList = append(courseList, courseItem)
	}

	response := dto.DateCoursesResponse{
		Date:    dateStr,
		Courses: courseList,
		Total:   len(courseList),
	}

	code.AutoResponse(c, response, nil)
}

// GetCoursesCalendar 获取课程日历
func (h *AppCourseHandler) GetCoursesCalendar(c *gin.Context) {
	// 获取月份参数，使用ISO 8601格式：month=2024-01
	monthStr := c.Query("month")
	if monthStr == "" {
		monthStr = time.Now().Format("2006-01")
	}

	// 解析月份
	month, err := time.Parse("2006-01", monthStr)
	if err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "月份格式错误，请使用YYYY-MM格式，如：2024-01"))
		return
	}

	// 计算月份范围
	startTime := month
	endTime := month.AddDate(0, 1, 0)

	// 查询该月的课程
	var courses []model.Course
	err = h.db.Select("DATE(start_time) as date, COUNT(*) as count").
		Where("status = 1 AND start_time >= ? AND start_time < ?", startTime, endTime).
		Group("DATE(start_time)").
		Find(&courses).Error

	if err != nil {
		log.Error("查询课程日历失败", "month", monthStr, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程日历失败"))
		return
	}

	// 构建日历数据
	calendar := make(map[string]int)

	// 查询详细的课程数据来构建日历
	var allCourses []model.Course
	err = h.db.Where("status = 1 AND start_time >= ? AND start_time < ?", startTime, endTime).
		Find(&allCourses).Error

	if err != nil {
		log.Error("查询课程详情失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询课程失败"))
		return
	}

	for _, course := range allCourses {
		dateKey := course.StartTime.Format("2006-01-02")
		calendar[dateKey]++
	}

	response := dto.CourseCalendarResponse{
		Month:    monthStr,
		Calendar: calendar,
	}

	code.AutoResponse(c, response, nil)
}

// getCoachInfo 获取教练信息（从CoachIDs数组中获取第一个教练）
func (h *AppCourseHandler) getCoachInfo(coachIDs []string) *model.User {
	if len(coachIDs) == 0 {
		return nil
	}

	var coach model.User
	err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", coachIDs[0], true, true).First(&coach).Error
	if err != nil {
		return nil
	}

	return &coach
}

// getCoachAvatarURL 获取教练头像URL
func (h *AppCourseHandler) getCoachAvatarURL(coach *model.User) string {
	if coach == nil || coach.AvatarID == nil || *coach.AvatarID == "" {
		return ""
	}
	return h.urlConverter.ConvertFileIDToURL(*coach.AvatarID)
}
