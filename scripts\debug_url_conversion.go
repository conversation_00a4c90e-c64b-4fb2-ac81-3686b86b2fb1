package main

import (
	"fmt"
	"log"

	"github.com/lib/pq"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// 简化的Store模型用于调试
type Store struct {
	ID       uint           `gorm:"primarykey"`
	Name     string         `gorm:"type:varchar(100)"`
	ImageIDs pq.StringArray `gorm:"type:text[]"`
}

// 复制URL转换函数进行调试
func ConvertFileIDToURL(fileID string) string {
	if fileID == "" {
		return ""
	}
	return "/api/v1/file/download/" + fileID
}

func ConvertFileIDsToURLs(fileIDs []string) []string {
	if len(fileIDs) == 0 {
		return []string{}
	}
	
	urls := make([]string, len(fileIDs))
	for i, fileID := range fileIDs {
		urls[i] = ConvertFileIDToURL(fileID)
	}
	return urls
}

func ConvertStringArrayToURLs(fileIDs interface{}) []string {
	fmt.Printf("ConvertStringArrayToURLs 输入类型: %T\n", fileIDs)
	fmt.Printf("ConvertStringArrayToURLs 输入值: %+v\n", fileIDs)
	
	switch v := fileIDs.(type) {
	case []string:
		fmt.Println("匹配到 []string 类型")
		return ConvertFileIDsToURLs(v)
	case pq.StringArray:
		fmt.Println("匹配到 pq.StringArray 类型")
		return ConvertFileIDsToURLs([]string(v))
	case nil:
		fmt.Println("匹配到 nil")
		return []string{}
	default:
		fmt.Printf("未匹配的类型: %T\n", v)
		return []string{}
	}
}

func getStoreImageURLs(store *Store) []string {
	if store == nil {
		return []string{}
	}
	fmt.Printf("门店 %s (ID: %d) 的 ImageIDs: %+v\n", store.Name, store.ID, store.ImageIDs)
	return ConvertStringArrayToURLs(store.ImageIDs)
}

func main() {
	// 初始化数据库连接
	dsn := "host=************ user=yogaga password=QjZ2xyD42bPYKTyx dbname=yogaga port=5432 sslmode=disable TimeZone=Asia/Shanghai"
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 查询有图片的门店
	var stores []Store
	err = db.Where("image_ids IS NOT NULL AND array_length(image_ids, 1) > 0").Find(&stores).Error
	if err != nil {
		log.Fatal("查询门店失败:", err)
	}

	fmt.Printf("找到 %d 个有图片的门店\n", len(stores))
	fmt.Println("===========================================")

	for _, store := range stores {
		fmt.Printf("\n门店: %s (ID: %d)\n", store.Name, store.ID)
		fmt.Printf("原始 ImageIDs: %+v\n", store.ImageIDs)
		fmt.Printf("ImageIDs 类型: %T\n", store.ImageIDs)
		fmt.Printf("ImageIDs 长度: %d\n", len(store.ImageIDs))
		
		// 测试URL转换
		urls := getStoreImageURLs(&store)
		fmt.Printf("转换后的 URLs: %+v\n", urls)
		fmt.Printf("URLs 长度: %d\n", len(urls))
		
		fmt.Println("-------------------------------------------")
	}
}
