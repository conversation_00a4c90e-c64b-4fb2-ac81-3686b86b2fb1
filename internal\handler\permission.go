package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/utils"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type PermissionHandler struct {
	db       *gorm.DB
	enforcer *casbin.Enforcer
}

func NewPermissionHandler(db *gorm.DB, enforcer *casbin.Enforcer) *PermissionHandler {
	return &PermissionHandler{
		db:       db,
		enforcer: enforcer,
	}
}

// List all available permissions in the system.
func (h *PermissionHandler) List(c *gin.Context) {
	var permissions []model.Permission
	if err := h.db.Order("name ASC").Find(&permissions).Error; err != nil {
		log.Error("查询权限列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询权限列表失败"))
		return
	}
	code.AutoResponse(c, permissions, nil)
}

// Create 创建新权限
func (h *PermissionHandler) Create(c *gin.Context) {
	var req dto.CreatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建权限请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 检查权限标识是否已存在
	var existingPermission model.Permission
	if err := h.db.Where("key = ?", req.Key).First(&existingPermission).Error; err == nil {
		log.Warn("权限标识已存在", "key", req.Key)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限标识已存在"))
		return
	}

	permission := model.Permission{
		Name:        req.Name,
		Key:         req.Key,
		Description: req.Description,
	}

	if err := h.db.Create(&permission).Error; err != nil {
		log.Error("创建权限失败", "name", req.Name, "key", req.Key, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建权限失败"))
		return
	}

	code.AutoResponse(c, permission, nil)
}

// Get 获取单个权限详情
func (h *PermissionHandler) Get(c *gin.Context) {
	permissionID := cast.ToInt(c.Param("id"))
	if permissionID <= 0 {
		log.Error("无效的权限ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的权限ID"))
		return
	}

	var permission model.Permission
	if err := h.db.Preload("Menus").First(&permission, permissionID).Error; err != nil {
		log.Error("查询权限详情失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "权限不存在"))
		return
	}

	code.AutoResponse(c, permission, nil)
}

// Update 更新权限
func (h *PermissionHandler) Update(c *gin.Context) {
	permissionID := cast.ToInt(c.Param("id"))
	if permissionID <= 0 {
		log.Error("无效的权限ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的权限ID"))
		return
	}

	var req dto.UpdatePermissionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新权限请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	var permission model.Permission
	if err := h.db.First(&permission, permissionID).Error; err != nil {
		log.Error("查找权限失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "权限不存在"))
		return
	}

	// 检查权限标识是否与其他权限冲突
	oldKey := permission.Key
	if req.Key != "" && req.Key != permission.Key {
		var existingPermission model.Permission
		if err := h.db.Where("key = ? AND id != ?", req.Key, permissionID).First(&existingPermission).Error; err == nil {
			log.Warn("权限标识已存在", "key", req.Key)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限标识已存在"))
			return
		}
	}

	// 开始事务
	tx := h.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Key != "" {
		updates["key"] = req.Key
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	// 1. 更新数据库
	if err := tx.Model(&permission).Updates(updates).Error; err != nil {
		tx.Rollback()
		log.Error("更新权限失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新权限失败"))
		return
	}

	// 2. 如果权限标识发生变化，需要同步更新 Casbin 策略
	if h.enforcer != nil && req.Key != "" && req.Key != oldKey {
		// 获取所有使用该权限的角色
		var roles []model.Role
		if err := tx.Joins("JOIN role_permissions ON roles.id = role_permissions.role_id").
			Where("role_permissions.permission_id = ?", permissionID).
			Find(&roles).Error; err != nil {
			tx.Rollback()
			log.Error("查询使用该权限的角色失败", "permission_id", permissionID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询角色失败"))
			return
		}

		// 为每个角色更新 Casbin 策略
		httpMethods := []string{"GET", "POST", "PUT", "DELETE"}
		for _, role := range roles {
			// 删除旧的权限策略
			for _, method := range httpMethods {
				_, err := h.enforcer.RemovePolicy(role.Name, oldKey, method)
				if err != nil {
					log.Warn("删除旧权限策略失败", "role_name", role.Name, "old_key", oldKey, "method", method, "error", err)
				}
			}

			// 添加新的权限策略
			for _, method := range httpMethods {
				_, err := h.enforcer.AddPolicy(role.Name, req.Key, method)
				if err != nil {
					tx.Rollback()
					log.Error("添加新权限策略失败", "role_name", role.Name, "new_key", req.Key, "method", method, "error", err)
					code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "同步权限策略失败"))
					return
				}
			}
		}

		// 保存 Casbin 策略
		if err := h.enforcer.SavePolicy(); err != nil {
			tx.Rollback()
			log.Error("保存 Casbin 策略失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "保存权限策略失败"))
			return
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		log.Error("提交事务失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新权限失败"))
		return
	}

	// 重新查询更新后的权限
	if err := h.db.Preload("Menus").First(&permission, permissionID).Error; err != nil {
		log.Error("查询更新后的权限失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询更新后的权限失败"))
		return
	}

	code.AutoResponse(c, permission, nil)
}

// Delete 删除权限
func (h *PermissionHandler) Delete(c *gin.Context) {
	permissionID := cast.ToInt(c.Param("id"))
	if permissionID <= 0 {
		log.Error("无效的权限ID参数", "id", c.Param("id"))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的权限ID"))
		return
	}

	var permission model.Permission
	if err := h.db.First(&permission, permissionID).Error; err != nil {
		log.Error("查找权限失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "权限不存在"))
		return
	}

	// 检查权限是否被角色使用
	var roleCount int64
	if err := h.db.Table("role_permissions").Where("permission_id = ?", permissionID).Count(&roleCount).Error; err != nil {
		log.Error("检查权限使用情况失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查权限使用情况失败"))
		return
	}

	if roleCount > 0 {
		log.Warn("权限正在被角色使用，无法删除", "permission_id", permissionID, "role_count", roleCount)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限正在被角色使用，无法删除"))
		return
	}

	// 检查权限是否被菜单使用
	var menuCount int64
	if err := h.db.Model(&model.Menu{}).Where("permission_key = ?", permission.Key).Count(&menuCount).Error; err != nil {
		log.Error("检查权限菜单使用情况失败", "permission_key", permission.Key, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查权限使用情况失败"))
		return
	}

	if menuCount > 0 {
		log.Warn("权限正在被菜单使用，无法删除", "permission_key", permission.Key, "menu_count", menuCount)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限正在被菜单使用，无法删除"))
		return
	}

	if err := h.db.Delete(&permission).Error; err != nil {
		log.Error("删除权限失败", "permission_id", permissionID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除权限失败"))
		return
	}

	code.AutoResponse(c, nil, nil)
}

// BatchDelete 批量删除权限
func (h *PermissionHandler) BatchDelete(c *gin.Context) {
	// 使用通用函数解析批量ID
	ids, err := utils.ParseBatchIDs(c)
	if err != nil {
		log.Error("解析批量删除权限ID失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	if len(ids) == 0 {
		log.Error("未提供要删除的权限ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请提供要删除的权限ID"))
		return
	}

	log.Info("收到批量删除权限请求", "ids", ids)

	// 查询要删除的权限信息
	var permissions []model.Permission
	if err := h.db.Where("id IN ?", ids).Find(&permissions).Error; err != nil {
		log.Error("查询权限失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询权限失败"))
		return
	}

	if len(permissions) != len(ids) {
		log.Warn("部分权限不存在", "requested_ids", ids, "found_count", len(permissions))
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "部分权限不存在"))
		return
	}

	// 检查权限是否被角色使用
	for _, permission := range permissions {
		var roleCount int64
		if err := h.db.Table("role_permissions").Where("permission_id = ?", permission.ID).Count(&roleCount).Error; err != nil {
			log.Error("检查权限使用情况失败", "permission_id", permission.ID, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查权限使用情况失败"))
			return
		}

		if roleCount > 0 {
			log.Warn("权限正在被角色使用，无法删除", "permission_id", permission.ID, "permission_name", permission.Name, "role_count", roleCount)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限正在被角色使用，无法删除"))
			return
		}

		// 检查权限是否被菜单使用
		var menuCount int64
		if err := h.db.Model(&model.Menu{}).Where("permission_key = ?", permission.Key).Count(&menuCount).Error; err != nil {
			log.Error("检查权限菜单使用情况失败", "permission_key", permission.Key, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查权限使用情况失败"))
			return
		}

		if menuCount > 0 {
			log.Warn("权限正在被菜单使用，无法删除", "permission_key", permission.Key, "permission_name", permission.Name, "menu_count", menuCount)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "权限正在被菜单使用，无法删除"))
			return
		}
	}

	// 执行批量删除
	if err := h.db.Delete(&model.Permission{}, ids).Error; err != nil {
		log.Error("批量删除权限失败", "ids", ids, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "批量删除权限失败"))
		return
	}

	log.Info("批量删除权限成功", "ids", ids, "count", len(ids))
	response := dto.MessageResponse{Message: "Permissions deleted successfully"}
	code.AutoResponse(c, response, nil)
}
