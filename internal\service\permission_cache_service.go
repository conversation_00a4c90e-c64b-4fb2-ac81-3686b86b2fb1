package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// PermissionCacheService 权限缓存服务
type PermissionCacheService struct {
	db    *gorm.DB
	redis *redis.Client
}

// NewPermissionCacheService 创建权限缓存服务
func NewPermissionCacheService(db *gorm.DB, redis *redis.Client) *PermissionCacheService {
	return &PermissionCacheService{
		db:    db,
		redis: redis,
	}
}

// 缓存键名常量
const (
	UserRolesCacheKey  = "auth:user:roles:%s"       // 用户角色缓存
	UserMenusCacheKey  = "auth:user:menus:%s"       // 用户菜单缓存
	PermissionCacheKey = "auth:permission:%s:%s:%s" // 权限验证结果缓存
	AllMenusCacheKey   = "auth:menus:all"           // 所有菜单缓存
)

// 缓存过期时间
const (
	UserRolesCacheTTL  = 30 * time.Minute // 用户角色缓存30分钟
	UserMenusCacheTTL  = 15 * time.Minute // 用户菜单缓存15分钟
	PermissionCacheTTL = 5 * time.Minute  // 权限验证结果缓存5分钟
	AllMenusCacheTTL   = 1 * time.Hour    // 所有菜单缓存1小时
)

// GetUserRoles 获取用户角色（带缓存）
func (s *PermissionCacheService) GetUserRoles(ctx context.Context, userID string) ([]string, error) {
	cacheKey := fmt.Sprintf(UserRolesCacheKey, userID)

	// 1. 尝试从缓存获取
	cached, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		var roles []string
		if err := json.Unmarshal([]byte(cached), &roles); err == nil {
			log.Debug("用户角色缓存命中", "user_id", userID, "roles", roles)
			return roles, nil
		}
	}

	// 2. 缓存未命中，从数据库查询
	var user model.User
	if err := s.db.Preload("Roles").Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("查询用户角色失败: %w", err)
	}

	// 3. 提取角色名称
	roles := make([]string, len(user.Roles))
	for i, role := range user.Roles {
		roles[i] = role.Name
	}

	// 4. 存入缓存
	if data, err := json.Marshal(roles); err == nil {
		s.redis.Set(ctx, cacheKey, data, UserRolesCacheTTL)
		log.Debug("用户角色已缓存", "user_id", userID, "roles", roles)
	}

	return roles, nil
}

// GetUserMenus 获取用户菜单（带缓存）
func (s *PermissionCacheService) GetUserMenus(ctx context.Context, userID string, userRoles []string) ([]*dto.MenuTreeResponse, error) {
	cacheKey := fmt.Sprintf(UserMenusCacheKey, userID)

	// 1. 尝试从缓存获取
	cached, err := s.redis.Get(ctx, cacheKey).Result()
	if err == nil {
		var menus []*dto.MenuTreeResponse
		if err := json.Unmarshal([]byte(cached), &menus); err == nil {
			log.Debug("用户菜单缓存命中", "user_id", userID, "menu_count", len(menus))
			return menus, nil
		}
	}

	// 2. 缓存未命中，需要重新构建菜单
	allMenus, err := s.getAllMenus(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取所有菜单失败: %w", err)
	}

	// 3. 构建菜单树（这里需要你们的菜单构建逻辑）
	// 注意：这里需要集成你们现有的菜单构建和权限过滤逻辑
	filteredMenus := s.filterMenusByRoles(allMenus, userRoles)

	// 4. 存入缓存
	if data, err := json.Marshal(filteredMenus); err == nil {
		s.redis.Set(ctx, cacheKey, data, UserMenusCacheTTL)
		log.Debug("用户菜单已缓存", "user_id", userID, "menu_count", len(filteredMenus))
	}

	return filteredMenus, nil
}

// GetAllMenus 获取所有菜单（带缓存）- 公开方法
func (s *PermissionCacheService) GetAllMenus(ctx context.Context) ([]model.Menu, error) {
	return s.getAllMenus(ctx)
}

// getAllMenus 获取所有菜单（带缓存）
func (s *PermissionCacheService) getAllMenus(ctx context.Context) ([]model.Menu, error) {
	// 1. 尝试从缓存获取
	cached, err := s.redis.Get(ctx, AllMenusCacheKey).Result()
	if err == nil {
		var menus []model.Menu
		if err := json.Unmarshal([]byte(cached), &menus); err == nil {
			log.Debug("所有菜单缓存命中", "menu_count", len(menus))
			return menus, nil
		}
	}

	// 2. 缓存未命中，从数据库查询
	var menus []model.Menu
	if err := s.db.Where("status = ?", 1).Order("sort ASC, id ASC").Find(&menus).Error; err != nil {
		return nil, fmt.Errorf("查询菜单失败: %w", err)
	}

	// 3. 存入缓存
	if data, err := json.Marshal(menus); err == nil {
		s.redis.Set(ctx, AllMenusCacheKey, data, AllMenusCacheTTL)
		log.Debug("所有菜单已缓存", "menu_count", len(menus))
	}

	return menus, nil
}

// CachePermissionResult 缓存权限验证结果
func (s *PermissionCacheService) CachePermissionResult(ctx context.Context, role, permission, action string, result bool) {
	cacheKey := fmt.Sprintf(PermissionCacheKey, role, permission, action)
	s.redis.Set(ctx, cacheKey, result, PermissionCacheTTL)
}

// GetCachedPermissionResult 获取缓存的权限验证结果
func (s *PermissionCacheService) GetCachedPermissionResult(ctx context.Context, role, permission, action string) (bool, bool) {
	cacheKey := fmt.Sprintf(PermissionCacheKey, role, permission, action)
	result, err := s.redis.Get(ctx, cacheKey).Bool()
	if err != nil {
		return false, false // 返回结果和是否命中缓存
	}
	return result, true
}

// 注意：资源权限相关方法已移除，如需要可通过业务逻辑实现

// InvalidateUserCache 清除用户相关缓存
func (s *PermissionCacheService) InvalidateUserCache(ctx context.Context, userID string) error {
	keys := []string{
		fmt.Sprintf(UserRolesCacheKey, userID),
		fmt.Sprintf(UserMenusCacheKey, userID),
	}

	for _, key := range keys {
		if err := s.redis.Del(ctx, key).Err(); err != nil {
			log.Warn("清除缓存失败", "key", key, "error", err)
		}
	}

	log.Info("用户缓存已清除", "user_id", userID)
	return nil
}

// InvalidateAllMenusCache 清除所有菜单缓存
func (s *PermissionCacheService) InvalidateAllMenusCache(ctx context.Context) error {
	// 1. 清除所有菜单缓存
	if err := s.redis.Del(ctx, AllMenusCacheKey).Err(); err != nil {
		log.Warn("清除所有菜单缓存失败", "error", err)
	}

	// 2. 清除所有用户菜单缓存
	pattern := fmt.Sprintf(UserMenusCacheKey, "*")
	keys, err := s.redis.Keys(ctx, pattern).Result()
	if err == nil && len(keys) > 0 {
		s.redis.Del(ctx, keys...)
	}

	log.Info("所有菜单缓存已清除")
	return nil
}

// InvalidatePermissionCache 清除权限相关缓存
func (s *PermissionCacheService) InvalidatePermissionCache(ctx context.Context, role string) error {
	// 清除该角色的所有权限缓存
	pattern := fmt.Sprintf(PermissionCacheKey, role, "*", "*")
	keys, err := s.redis.Keys(ctx, pattern).Result()
	if err == nil && len(keys) > 0 {
		s.redis.Del(ctx, keys...)
	}

	log.Info("角色权限缓存已清除", "role", role)
	return nil
}

// filterMenusByRoles 根据角色过滤菜单（简化版本，需要集成你们的实际逻辑）
func (s *PermissionCacheService) filterMenusByRoles(menus []model.Menu, userRoles []string) []*dto.MenuTreeResponse {
	// 这里需要集成你们现有的菜单过滤逻辑
	// 暂时返回空数组，实际使用时需要替换为真实的过滤逻辑
	return []*dto.MenuTreeResponse{}
}

// WarmUpCache 预热缓存
func (s *PermissionCacheService) WarmUpCache(ctx context.Context) error {
	log.Info("开始预热权限缓存")

	// 预热所有菜单缓存
	_, err := s.getAllMenus(ctx)
	if err != nil {
		log.Error("预热菜单缓存失败", "error", err)
		return err
	}

	log.Info("权限缓存预热完成")
	return nil
}
