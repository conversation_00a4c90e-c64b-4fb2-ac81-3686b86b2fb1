package enum

import (
	"testing"
)

func TestCourseType(t *testing.T) {
	tests := []struct {
		name     string
		ctype    CourseType
		expected string
		valid    bool
	}{
		{"Group", CourseTypeGroup, "group", true},
		{"Private", CourseTypePrivate, "private", true},
		{"Invalid", CourseType(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ctype.String(); got != tt.expected {
				t.<PERSON>rf("CourseType.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.ctype.IsValid(); got != tt.valid {
				t.<PERSON>rf("CourseType.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestMembershipCardCategory(t *testing.T) {
	tests := []struct {
		name     string
		category MembershipCardCategory
		expected string
		valid    bool
	}{
		{"Times", MembershipCardCategoryTimes, "times", true},
		{"Period", MembershipCardCategoryPeriod, "period", true},
		{"Balance", MembershipCardCategoryBalance, "balance", true},
		{"Invalid", MembershipCardCategory("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.category.String(); got != tt.expected {
				t.Errorf("MembershipCardCategory.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.category.IsValid(); got != tt.valid {
				t.Errorf("MembershipCardCategory.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestFavoriteType(t *testing.T) {
	tests := []struct {
		name     string
		ftype    FavoriteType
		expected string
		valid    bool
	}{
		{"Course", FavoriteTypeCourse, "course", true},
		{"Coach", FavoriteTypeCoach, "coach", true},
		{"Store", FavoriteTypeStore, "store", true},
		{"Invalid", FavoriteType("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.ftype.String(); got != tt.expected {
				t.Errorf("FavoriteType.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.ftype.IsValid(); got != tt.valid {
				t.Errorf("FavoriteType.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestPaymentMethod(t *testing.T) {
	tests := []struct {
		name     string
		method   PaymentMethod
		expected string
		valid    bool
	}{
		{"MembershipCard", PaymentMethodMembershipCard, "membership_card", true},
		{"WechatPay", PaymentMethodWechatPay, "wechat_pay", true},
		{"Alipay", PaymentMethodAlipay, "alipay", true},
		{"Cash", PaymentMethodCash, "cash", true},
		{"Invalid", PaymentMethod("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.method.String(); got != tt.expected {
				t.Errorf("PaymentMethod.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.method.IsValid(); got != tt.valid {
				t.Errorf("PaymentMethod.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestGender(t *testing.T) {
	tests := []struct {
		name     string
		gender   Gender
		expected string
		valid    bool
	}{
		{"Unknown", GenderUnknown, "unknown", true},
		{"Male", GenderMale, "male", true},
		{"Female", GenderFemale, "female", true},
		{"Invalid", Gender(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.gender.String(); got != tt.expected {
				t.Errorf("Gender.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.gender.IsValid(); got != tt.valid {
				t.Errorf("Gender.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestPlatform(t *testing.T) {
	tests := []struct {
		name     string
		platform Platform
		expected string
		valid    bool
	}{
		{"Default", PlatformDefault, "default", true},
		{"Admin", PlatformAdmin, "admin", true},
		{"App", PlatformApp, "app", true},
		{"Wechat", PlatformWechat, "wechat", true},
		{"Invalid", Platform("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.platform.String(); got != tt.expected {
				t.Errorf("Platform.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.platform.IsValid(); got != tt.valid {
				t.Errorf("Platform.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestPlatformAccess(t *testing.T) {
	tests := []struct {
		name      string
		access    PlatformAccess
		platform  Platform
		hasAccess bool
	}{
		{"Wechat only", PlatformAccessWechat, PlatformWechat, true},
		{"Wechat only - Admin", PlatformAccessWechat, PlatformAdmin, false},
		{"Admin only", PlatformAccessAdmin, PlatformAdmin, true},
		{"Admin only - Wechat", PlatformAccessAdmin, PlatformWechat, false},
		{"Both platforms", PlatformAccessWechat | PlatformAccessAdmin, PlatformWechat, true},
		{"Both platforms - Admin", PlatformAccessWechat | PlatformAccessAdmin, PlatformAdmin, true},
		{"All platforms", PlatformAccessAll, PlatformApp, true},
		{"None", PlatformAccessNone, PlatformWechat, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.access.HasAccess(tt.platform); got != tt.hasAccess {
				t.Errorf("PlatformAccess.HasAccess(%v) = %v, want %v", tt.platform, got, tt.hasAccess)
			}
		})
	}
}

func TestPlatformAccessOperations(t *testing.T) {
	// 测试添加权限
	access := PlatformAccessNone
	access = access.AddAccess(PlatformWechat)
	if !access.HasAccess(PlatformWechat) {
		t.Error("AddAccess failed")
	}

	// 测试添加多个权限
	access = access.AddAccess(PlatformAdmin)
	if !access.HasAccess(PlatformAdmin) || !access.HasAccess(PlatformWechat) {
		t.Error("AddAccess multiple failed")
	}

	// 测试移除权限
	access = access.RemoveAccess(PlatformWechat)
	if access.HasAccess(PlatformWechat) || !access.HasAccess(PlatformAdmin) {
		t.Error("RemoveAccess failed")
	}

	// 测试获取平台列表
	access = PlatformAccessWechat | PlatformAccessAdmin
	platforms := access.GetAccessiblePlatforms()
	if len(platforms) != 2 {
		t.Errorf("GetAccessiblePlatforms() = %v, want 2 platforms", platforms)
	}

	// 测试字符串表示
	str := access.String()
	if str != "wechat,admin" && str != "admin,wechat" {
		t.Errorf("String() = %v, want wechat,admin or admin,wechat", str)
	}
}

func TestBookingApplicationStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   BookingApplicationStatus
		expected string
		valid    bool
	}{
		{"Pending", BookingApplicationStatusPending, "pending", true},
		{"Processing", BookingApplicationStatusProcessing, "processing", true},
		{"Completed", BookingApplicationStatusCompleted, "completed", true},
		{"Failed", BookingApplicationStatusFailed, "failed", true},
		{"Queued", BookingApplicationStatusQueued, "queued", true},
		{"Invalid", BookingApplicationStatus("invalid"), "invalid", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("BookingApplicationStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("BookingApplicationStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}
