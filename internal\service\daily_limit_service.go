package service

import (
	"fmt"
	"time"

	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// DailyLimitService 单日限制检查服务
type DailyLimitService struct {
	db *gorm.DB
}

// NewDailyLimitService 创建单日限制服务
func NewDailyLimitService(db *gorm.DB) *DailyLimitService {
	return &DailyLimitService{db: db}
}

// DailyLimitCheck 单日限制检查结果
type DailyLimitCheck struct {
	CanBook        bool   `json:"can_book"`        // 是否可以预约
	CurrentCount   int    `json:"current_count"`   // 当前已预约数量
	LimitCount     int    `json:"limit_count"`     // 限制数量
	RemainingCount int    `json:"remaining_count"` // 剩余可预约数量
	Message        string `json:"message"`         // 提示信息
}

// CheckDailyLimit 检查单日预约限制
func (s *DailyLimitService) CheckDailyLimit(cardID uint, userID string, courseID uint, bookingDate time.Time) (*DailyLimitCheck, error) {
	// 获取会员卡信息
	var card model.MembershipCard
	err := s.db.Preload("CardType").First(&card, cardID).Error
	if err != nil {
		return nil, fmt.Errorf("会员卡不存在: %w", err)
	}

	// 如果没有设置单日限制，直接通过
	if card.DailyBookingLimit <= 0 && card.CardType.DailyBookingLimit <= 0 {
		return &DailyLimitCheck{
			CanBook:        true,
			CurrentCount:   0,
			LimitCount:     0,
			RemainingCount: -1, // -1表示无限制
			Message:        "无单日预约限制",
		}, nil
	}

	// 确定实际的限制数量
	limitCount := card.DailyBookingLimit
	if limitCount <= 0 {
		limitCount = card.CardType.DailyBookingLimit
	}

	// 获取课程信息
	var course model.Course
	err = s.db.First(&course, courseID).Error
	if err != nil {
		return nil, fmt.Errorf("课程不存在: %w", err)
	}

	// 计算当日已预约数量
	currentCount, err := s.getTodayBookingCount(cardID, userID, bookingDate, course.Type)
	if err != nil {
		return nil, fmt.Errorf("查询当日预约数量失败: %w", err)
	}

	// 检查是否超限
	canBook := currentCount < limitCount
	remainingCount := limitCount - currentCount
	if remainingCount < 0 {
		remainingCount = 0
	}

	message := fmt.Sprintf("今日已预约 %d 节，限制 %d 节", currentCount, limitCount)
	if !canBook {
		message = fmt.Sprintf("已达到单日预约限制(%d节)", limitCount)
	}

	return &DailyLimitCheck{
		CanBook:        canBook,
		CurrentCount:   currentCount,
		LimitCount:     limitCount,
		RemainingCount: remainingCount,
		Message:        message,
	}, nil
}

// getTodayBookingCount 获取当日预约数量
func (s *DailyLimitService) getTodayBookingCount(cardID uint, userID string, bookingDate time.Time, courseType enum.CourseType) (int, error) {
	today := bookingDate.Format("2006-01-02")

	var count int64
	query := s.db.Model(&model.Booking{}).
		Joins("JOIN courses c ON bookings.course_id = c.id").
		Where("bookings.membership_card_id = ? AND DATE(bookings.created_at) = ? AND bookings.status IN (1, 2)",
			cardID, today)

	// 如果是副卡，只统计该用户的预约
	var card model.MembershipCard
	err := s.db.First(&card, cardID).Error
	if err != nil {
		return 0, err
	}

	if !card.IsMainCard {
		query = query.Where("bookings.user_id = ?", userID)
	}

	// 根据卡种类型决定是否按课程类型限制
	// 期限卡通常按课程类型限制，其他卡种可能是全局限制
	if card.CardType.Category == enum.MembershipCardCategoryPeriod {
		query = query.Where("c.type = ?", courseType)
	}

	err = query.Count(&count).Error
	return int(count), err
}

// CheckWeeklyLimit 检查单周预约限制
func (s *DailyLimitService) CheckWeeklyLimit(cardID uint, userID string, courseID uint, bookingDate time.Time) (*DailyLimitCheck, error) {
	// 获取会员卡信息
	var card model.MembershipCard
	err := s.db.Preload("CardType").First(&card, cardID).Error
	if err != nil {
		return nil, fmt.Errorf("会员卡不存在: %w", err)
	}

	// 这里可以扩展周限制逻辑
	// 目前先返回无限制
	return &DailyLimitCheck{
		CanBook:        true,
		CurrentCount:   0,
		LimitCount:     0,
		RemainingCount: -1,
		Message:        "暂无单周预约限制",
	}, nil
}

// GetUserDailyBookingStats 获取用户当日预约统计
func (s *DailyLimitService) GetUserDailyBookingStats(userID string, date time.Time) (map[string]interface{}, error) {
	today := date.Format("2006-01-02")

	// 统计当日总预约数
	var totalBookings int64
	err := s.db.Model(&model.Booking{}).
		Where("user_id = ? AND DATE(created_at) = ? AND status IN (1, 2)", userID, today).
		Count(&totalBookings).Error
	if err != nil {
		return nil, err
	}

	// 按课程类型统计
	var typeStats []struct {
		CourseType string `json:"course_type"`
		Count      int64  `json:"count"`
	}

	err = s.db.Model(&model.Booking{}).
		Select("c.type as course_type, COUNT(*) as count").
		Joins("JOIN courses c ON bookings.course_id = c.id").
		Where("bookings.user_id = ? AND DATE(bookings.created_at) = ? AND bookings.status IN (1, 2)", userID, today).
		Group("c.type").
		Find(&typeStats).Error
	if err != nil {
		return nil, err
	}

	// 获取用户所有会员卡的限制信息
	var cards []model.MembershipCard
	err = s.db.Preload("CardType").
		Where("user_id = ? AND status = ?", userID, enum.MembershipCardStatusNormal).
		Find(&cards).Error
	if err != nil {
		return nil, err
	}

	var cardLimits []map[string]interface{}
	for _, card := range cards {
		limit := card.DailyBookingLimit
		if limit <= 0 {
			limit = card.CardType.DailyBookingLimit
		}

		cardLimits = append(cardLimits, map[string]interface{}{
			"card_id":     card.ID,
			"card_number": card.CardNumber,
			"card_name":   card.CardType.Name,
			"limit":       limit,
		})
	}

	return map[string]interface{}{
		"date":           today,
		"total_bookings": totalBookings,
		"type_stats":     typeStats,
		"card_limits":    cardLimits,
	}, nil
}

// ValidateBookingWithLimits 验证预约是否符合所有限制
func (s *DailyLimitService) ValidateBookingWithLimits(cardID uint, userID string, courseID uint, peopleCount int, bookingDate time.Time) error {
	// 检查单日限制
	dailyCheck, err := s.CheckDailyLimit(cardID, userID, courseID, bookingDate)
	if err != nil {
		return fmt.Errorf("检查单日限制失败: %w", err)
	}

	if !dailyCheck.CanBook {
		return fmt.Errorf(dailyCheck.Message)
	}

	// 检查剩余次数是否足够本次预约
	if dailyCheck.RemainingCount >= 0 && dailyCheck.RemainingCount < peopleCount {
		return fmt.Errorf("今日剩余预约次数不足，剩余 %d 次，需要 %d 次", dailyCheck.RemainingCount, peopleCount)
	}

	// 可以在这里添加更多限制检查
	// 比如周限制、月限制等

	return nil
}

// GetCardDailyLimitInfo 获取会员卡的单日限制信息
func (s *DailyLimitService) GetCardDailyLimitInfo(cardID uint) (map[string]interface{}, error) {
	var card model.MembershipCard
	err := s.db.Preload("CardType").First(&card, cardID).Error
	if err != nil {
		return nil, fmt.Errorf("会员卡不存在: %w", err)
	}

	limit := card.DailyBookingLimit
	if limit <= 0 {
		limit = card.CardType.DailyBookingLimit
	}

	return map[string]interface{}{
		"card_id":             card.ID,
		"card_number":         card.CardNumber,
		"card_type_name":      card.CardType.Name,
		"daily_limit":         limit,
		"has_limit":           limit > 0,
		"is_main_card":        card.IsMainCard,
		"can_set_daily_limit": card.CardType.CanSetDailyLimit,
	}, nil
}
