package enum

import (
	"testing"
)

func TestStoreStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   StoreStatus
		expected string
		valid    bool
	}{
		{"Open", StoreStatusOpen, "open", true},
		{"Closed", StoreStatusClosed, "closed", true},
		{"Invalid", StoreStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("StoreStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("StoreStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestClassRoomStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   ClassRoomStatus
		expected string
		valid    bool
	}{
		{"Available", ClassRoomStatusAvailable, "available", true},
		{"Maintenance", ClassRoomStatusMaintenance, "maintenance", true},
		{"Invalid", ClassRoomStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("ClassRoomStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("ClassRoomStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestCourseStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   CourseStatus
		expected string
		valid    bool
	}{
		{"Normal", CourseStatusNormal, "normal", true},
		{"Cancelled", CourseStatusCancelled, "cancelled", true},
		{"Invalid", CourseStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("CourseStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("CourseStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestBookingStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   BookingStatus
		expected string
		valid    bool
	}{
		{"Booked", BookingStatusBooked, "booked", true},
		{"CheckedIn", BookingStatusCheckedIn, "checked_in", true},
		{"Completed", BookingStatusCompleted, "completed", true},
		{"Cancelled", BookingStatusCancelled, "cancelled", true},
		{"NoShow", BookingStatusNoShow, "no_show", true},
		{"Invalid", BookingStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("BookingStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("BookingStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestMembershipCardStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   MembershipCardStatus
		expected string
		valid    bool
	}{
		{"Normal", MembershipCardStatusNormal, "normal", true},
		{"Frozen", MembershipCardStatusFrozen, "frozen", true},
		{"Expired", MembershipCardStatusExpired, "expired", true},
		{"UsedUp", MembershipCardStatusUsedUp, "used_up", true},
		{"Invalid", MembershipCardStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("MembershipCardStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("MembershipCardStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestQueueStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   QueueStatus
		expected string
		valid    bool
	}{
		{"Waiting", QueueStatusWaiting, "waiting", true},
		{"Notified", QueueStatusNotified, "notified", true},
		{"Converted", QueueStatusConverted, "converted", true},
		{"Cancelled", QueueStatusCancelled, "cancelled", true},
		{"Invalid", QueueStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("QueueStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("QueueStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}

func TestScheduleStatus(t *testing.T) {
	tests := []struct {
		name     string
		status   ScheduleStatus
		expected string
		valid    bool
	}{
		{"Bookable", ScheduleStatusBookable, "bookable", true},
		{"Full", ScheduleStatusFull, "full", true},
		{"Started", ScheduleStatusStarted, "started", true},
		{"Ended", ScheduleStatusEnded, "ended", true},
		{"Cancelled", ScheduleStatusCancelled, "cancelled", true},
		{"Invalid", ScheduleStatus(99), "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.status.String(); got != tt.expected {
				t.Errorf("ScheduleStatus.String() = %v, want %v", got, tt.expected)
			}
			if got := tt.status.IsValid(); got != tt.valid {
				t.Errorf("ScheduleStatus.IsValid() = %v, want %v", got, tt.valid)
			}
		})
	}
}
