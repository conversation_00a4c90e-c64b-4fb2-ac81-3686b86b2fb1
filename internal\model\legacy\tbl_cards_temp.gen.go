// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblCardsTemp = "tbl_cards_temp"

// TblCardsTemp mapped from table <tbl_cards_temp>
type TblCardsTemp struct {
	ID             int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID         int32     `gorm:"column:shop_id" json:"shop_id"`
	ShopIds        string    `gorm:"column:shop_ids" json:"shop_ids"`
	CardtpID       int32     `gorm:"column:cardtp_id" json:"cardtp_id"`
	MemberID       int32     `gorm:"column:member_id;not null" json:"member_id"`
	SaleID         int32     `gorm:"column:sale_id;not null" json:"sale_id"`
	Status         bool      `gorm:"column:status;not null;default:1" json:"status"`
	AmountActually float64   `gorm:"column:amount_actually;not null;default:0.00" json:"amount_actually"`
	Times          int32     `gorm:"column:times" json:"times"`
	Amount         float64   `gorm:"column:amount;not null;default:0.00" json:"amount"`
	DateStart      time.Time `gorm:"column:date_start" json:"date_start"`
	DateEnd        time.Time `gorm:"column:date_end" json:"date_end"`
	Note           string    `gorm:"column:note" json:"note"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete       int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	IsZhuankaOut   int32     `gorm:"column:is_zhuanka_out;not null" json:"is_zhuanka_out"`
	IsZhuankaIn    int32     `gorm:"column:is_zhuanka_in;not null" json:"is_zhuanka_in"`
	IsCankoufei    bool      `gorm:"column:is_cankoufei;not null" json:"is_cankoufei"`
	PayMethod      int32     `gorm:"column:pay_method;not null" json:"pay_method"`
	FromCardID     int32     `gorm:"column:from_card_id;not null" json:"from_card_id"`
	AmountDeduct   float64   `gorm:"column:amount_deduct;not null;default:0.00" json:"amount_deduct"`
	Istrue         bool      `gorm:"column:istrue;not null;default:1" json:"istrue"`
	Reason         string    `gorm:"column:reason" json:"reason"`
}

// TableName TblCardsTemp's table name
func (*TblCardsTemp) TableName() string {
	return TableNameTblCardsTemp
}
