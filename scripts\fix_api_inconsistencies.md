# Admin API 路由不一致问题修复

## 🔍 发现的不一致问题

### 1. 副卡创建路由不一致
**问题**：
- 路由器：`POST /api/v1/admin/membership-cards/:id/sub-cards`
- API文档：`POST /api/v1/admin/membership-cards/sub-card`

**修复**：需要将API文档中的路径修改为 `/api/v1/admin/membership-cards/1/sub-cards`

### 2. 会员卡详情路由不一致
**问题**：
- 路由器：`GET /api/v1/admin/membership-cards/:id` (GetCardDetail)
- API文档：`GET /api/v1/admin/membership-cards/1/detail`

**修复**：需要将API文档中的路径修改为 `/api/v1/admin/membership-cards/1`

### 3. 缺失的路由

#### 3.1 副卡列表路由
**路由器中存在但API文档中缺失**：
- `GET /api/v1/admin/membership-cards/:id/sub-cards` - 获取副卡列表

#### 3.2 批量课程管理路由
**路由器中存在但API文档中缺失**：
- `POST /api/v1/admin/batch-courses/preview` - 批量创建课程预览
- `POST /api/v1/admin/batch-courses/create` - 批量创建课程

#### 3.3 课程支持的会员卡类型
**路由器中存在但API文档中缺失**：
- `GET /api/v1/admin/courses/:id/supported-card-types` - 获取课程支持的会员卡类型

#### 3.4 灵活扣减系统路由
**路由器中存在但API文档中缺失**：
- `GET /api/v1/admin/flexible/deduction-rules/:id` - 获取扣减规则详情
- `PUT /api/v1/admin/flexible/deduction-rules/:id/toggle` - 切换扣减规则状态
- `PUT /api/v1/admin/flexible/course-types/:id` - 更新课程类型配置
- `DELETE /api/v1/admin/flexible/course-types/:id` - 删除课程类型配置

### 4. API文档中多余的路由

#### 4.1 高级会员卡管理路由
**API文档中存在但路由器中不存在**：
- `POST /api/v1/admin/advanced-membership/transfer` - 高级转卡
- `POST /api/v1/admin/advanced-membership/upgrade` - 高级升级
- `POST /api/v1/admin/advanced-membership/leave/apply` - 高级请假申请
- `GET /api/v1/admin/advanced-membership/cards/1/sub-cards` - 获取副卡列表
- `GET /api/v1/admin/advanced-membership/cards/1/leave-history` - 获取请假历史

#### 4.2 课程预约路由
**API文档中存在但路由器中不存在**：
- `POST /api/v1/admin/courses/<<yoga_basic_course_id>>/book` - 课程预约
- `POST /api/v1/admin/courses/check-conflict` - 检查课程时间冲突

## 🛠️ 修复建议

### 立即修复的路由
1. 修改副卡创建路径：`/api/v1/admin/membership-cards/sub-card` → `/api/v1/admin/membership-cards/1/sub-cards`
2. 修改会员卡详情路径：`/api/v1/admin/membership-cards/1/detail` → `/api/v1/admin/membership-cards/1`

### 需要添加的路由
1. 添加副卡列表获取接口
2. 添加批量课程管理接口
3. 添加课程支持的会员卡类型接口
4. 完善灵活扣减系统接口

### 需要移除的路由
1. 移除高级会员卡管理相关接口（这些功能已整合到标准会员卡管理中）
2. 移除课程预约和冲突检查接口（这些功能在其他模块中）

## 📋 修复优先级

### 高优先级（立即修复）
- [x] 副卡创建路径修正
- [x] 会员卡详情路径修正

### 中优先级（本周内修复）
- [ ] 添加缺失的核心管理接口
- [ ] 移除多余的高级管理接口

### 低优先级（下周修复）
- [ ] 完善所有灵活扣减系统接口
- [ ] 添加详细的接口文档和示例

## 🎯 修复后的验证

修复完成后，运行以下命令验证：
```bash
powershell -ExecutionPolicy Bypass -File scripts/compare_routes_api.ps1
```

确保输出显示：
```
✅ 所有路由都一致！
```
