// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblPointsHistory = "tbl_points_history"

// TblPointsHistory mapped from table <tbl_points_history>
type TblPointsHistory struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	MemberID   int32     `gorm:"column:member_id;not null" json:"member_id"`
	AdminID    int32     `gorm:"column:admin_id;not null" json:"admin_id"`
	ValTp      int32     `gorm:"column:val_tp;not null;default:1" json:"val_tp"`
	ValID      int32     `gorm:"column:val_id;not null" json:"val_id"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Points     int32     `gorm:"column:points;not null" json:"points"`
	Notes      string    `gorm:"column:notes" json:"notes"`
}

// TableName TblPointsHistory's table name
func (*TblPointsHistory) TableName() string {
	return TableNameTblPointsHistory
}
