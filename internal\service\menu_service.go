package service

import (
	"yogaga/internal/dto"
	"yogaga/internal/model"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
)

// MenuService 菜单服务
type MenuService struct {
	enforcer *casbin.Enforcer
}

// NewMenuService 创建菜单服务
func NewMenuService(enforcer *casbin.Enforcer) *MenuService {
	return &MenuService{
		enforcer: enforcer,
	}
}

// FilterMenusByPermission 根据用户角色过滤菜单
func (s *MenuService) FilterMenusByPermission(menus []*dto.MenuTreeResponse, userRoles []string) []*dto.MenuTreeResponse {
	if len(menus) == 0 {
		return []*dto.MenuTreeResponse{}
	}

	var filteredMenus []*dto.MenuTreeResponse

	for _, menu := range menus {
		// 检查菜单是否可访问
		if s.isMenuAccessible(menu, userRoles) {
			// 创建菜单副本，避免修改原始数据
			filteredMenu := *menu

			// 递归过滤子菜单
			if len(menu.Children) > 0 {
				filteredMenu.Children = s.FilterMenusByPermission(menu.Children, userRoles)
			} else {
				filteredMenu.Children = []*dto.MenuTreeResponse{} // 确保不是nil
			}

			// 根据菜单类型决定是否添加到结果中
			if s.shouldIncludeMenu(&filteredMenu) {
				filteredMenus = append(filteredMenus, &filteredMenu)
			}
		}
	}

	return filteredMenus
}

// shouldIncludeMenu 判断是否应该包含该菜单
func (s *MenuService) shouldIncludeMenu(menu *dto.MenuTreeResponse) bool {
	// 如果是目录类型，检查是否有可访问的子菜单
	if menu.Type == int(model.MenuTypeDirectory) {
		// 目录类型：如果有可访问的子菜单或者目录本身不需要权限，则显示
		return len(menu.Children) > 0 || menu.AccessType == "public"
	}

	// 页面或按钮类型：直接添加
	return true
}

// isMenuAccessible 检查菜单是否可访问
func (s *MenuService) isMenuAccessible(menu *dto.MenuTreeResponse, userRoles []string) bool {
	// 参数验证
	if menu == nil {
		log.Warn("菜单对象为空")
		return false
	}

	// 如果菜单不需要权限（public），直接可访问
	if menu.AccessType == "public" || menu.PermissionKey == "" {
		log.Debug("菜单无需权限验证", "menu", menu.Title, "access_type", menu.AccessType)
		return true
	}

	// 如果用户没有角色，拒绝访问需要权限的菜单
	if len(userRoles) == 0 {
		log.Debug("用户无角色，拒绝访问需要权限的菜单", "menu", menu.Title)
		return false
	}

	// 如果权限执行器未初始化，拒绝访问
	if s.enforcer == nil {
		log.Warn("权限执行器未初始化，拒绝访问", "menu", menu.Title)
		return false
	}

	// 检查用户的任一角色是否有权限
	for _, role := range userRoles {
		if role == "" {
			continue // 跳过空角色
		}

		// 检查菜单权限，通常菜单权限对应 GET 方法
		allowed, err := s.enforcer.Enforce(role, menu.PermissionKey, "GET")
		if err != nil {
			log.Error("权限检查失败",
				"role", role,
				"permission", menu.PermissionKey,
				"menu", menu.Title,
				"error", err)
			continue
		}

		if allowed {
			log.Debug("菜单权限验证通过",
				"menu", menu.Title,
				"role", role,
				"permission", menu.PermissionKey)
			return true
		}
	}

	log.Debug("菜单权限验证失败",
		"menu", menu.Title,
		"roles", userRoles,
		"permission", menu.PermissionKey)
	return false
}

// GetUserAccessibleMenus 获取用户可访问的菜单树
func (s *MenuService) GetUserAccessibleMenus(allMenus []*dto.MenuTreeResponse, userRoles []string) []*dto.MenuTreeResponse {
	// 参数验证
	if len(allMenus) == 0 {
		log.Debug("没有菜单数据需要过滤")
		return []*dto.MenuTreeResponse{}
	}

	// 如果用户没有角色，直接返回空菜单
	if len(userRoles) == 0 {
		log.Debug("用户无角色，返回空菜单")
		return []*dto.MenuTreeResponse{}
	}

	log.Debug("开始过滤用户菜单",
		"roles", userRoles,
		"total_menus", len(allMenus))

	// 执行权限过滤
	filteredMenus := s.FilterMenusByPermission(allMenus, userRoles)

	log.Debug("菜单过滤完成",
		"roles", userRoles,
		"total_menus", len(allMenus),
		"accessible_menus", len(filteredMenus))

	return filteredMenus
}

// ValidateMenuPermission 验证菜单权限配置
func (s *MenuService) ValidateMenuPermission(menu *model.Menu) error {
	// 如果菜单类型是按钮，必须有权限
	if menu.Type == model.MenuTypeButton && menu.PermissionKey == "" {
		return &MenuValidationError{
			MenuID:  menu.ID,
			Message: "按钮类型菜单必须配置权限",
		}
	}

	// 如果配置了权限，检查权限是否存在
	if menu.PermissionKey != "" && s.enforcer != nil {
		// 这里可以添加权限存在性检查
		// 暂时跳过，因为需要数据库查询
	}

	return nil
}

// MenuValidationError 菜单验证错误
type MenuValidationError struct {
	MenuID  uint
	Message string
}

func (e *MenuValidationError) Error() string {
	return e.Message
}
