package model

import "yogaga/internal/enum"

// Favorite 收藏模型
type Favorite struct {
	BaseModel
	UserID       string            `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	FavoriteType enum.FavoriteType `json:"favorite_type" gorm:"type:varchar(20);not null;comment:收藏类型 course:课程 coach:教练 store:门店"`
	TargetID     string            `json:"target_id" gorm:"type:varchar(50);not null;comment:目标ID，支持uint和UUID"`

	// 关联
	User User `json:"user" gorm:"foreignKey:UserID"`
}
