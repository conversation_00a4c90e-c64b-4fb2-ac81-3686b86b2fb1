package main

import (
	"fmt"
	"log"
	"time"
	"yogaga/configs"
	"yogaga/internal/database"
	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

func main() {
	fmt.Println("🧪 开始测试会员卡交易记录功能...")

	// 加载配置
	cfg, err := configs.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 连接数据库
	db, err := database.InitDB(cfg)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	// 测试交易记录
	if err := testTransactionRecords(db); err != nil {
		log.Fatalf("测试交易记录失败: %v", err)
	}

	fmt.Println("✅ 会员卡交易记录功能测试完成！")
}

func testTransactionRecords(db *gorm.DB) error {
	fmt.Println("\n📋 检查会员卡交易记录...")

	// 1. 统计各种交易类型的记录数量
	transactionTypes := []enum.CardTransactionType{
		enum.CardTransactionTypeIssue,    // 开卡
		enum.CardTransactionTypeRecharge, // 充值
		enum.CardTransactionTypeDeduct,   // 扣费
		enum.CardTransactionTypeTransfer, // 转卡
		enum.CardTransactionTypeFreeze,   // 冻结
		enum.CardTransactionTypeUnfreeze, // 解冻
		enum.CardTransactionTypeExtend,   // 延期
		enum.CardTransactionTypeUpgrade,  // 升级
		enum.CardTransactionTypeRefund,   // 退款
	}

	fmt.Println("\n📊 交易记录统计:")
	fmt.Println("交易类型\t\t记录数量\t中文名称")
	fmt.Println("----------------------------------------")

	totalRecords := 0
	for _, transactionType := range transactionTypes {
		var count int64
		err := db.Model(&model.MembershipCardTransaction{}).
			Where("transaction_type = ?", transactionType).
			Count(&count).Error
		if err != nil {
			return fmt.Errorf("查询交易记录失败: %v", err)
		}

		chineseName := transactionType.GetChineseName()
		fmt.Printf("%-15s\t%d\t\t%s\n", transactionType, count, chineseName)
		totalRecords += int(count)
	}

	fmt.Printf("----------------------------------------\n")
	fmt.Printf("总记录数: %d\n", totalRecords)

	// 2. 查看最近的交易记录
	fmt.Println("\n📝 最近10条交易记录:")
	var recentTransactions []model.MembershipCardTransaction
	err := db.Preload("Card").
		Order("created_at DESC").
		Limit(10).
		Find(&recentTransactions).Error
	if err != nil {
		return fmt.Errorf("查询最近交易记录失败: %v", err)
	}

	if len(recentTransactions) == 0 {
		fmt.Println("暂无交易记录")
	} else {
		fmt.Println("时间\t\t\t卡号\t\t交易类型\t操作员\t\t原因")
		fmt.Println("--------------------------------------------------------------------------------")
		for _, transaction := range recentTransactions {
			cardNumber := "未知"
			if transaction.Card != nil {
				cardNumber = transaction.Card.CardNumber
			}
			
			fmt.Printf("%s\t%s\t%s\t\t%s\t%s\n",
				transaction.CreatedAt.Format("2006-01-02 15:04:05"),
				cardNumber,
				transaction.TransactionType.GetChineseName(),
				transaction.OperatorName,
				transaction.Reason,
			)
		}
	}

	// 3. 检查是否有会员卡没有开卡记录
	fmt.Println("\n🔍 检查会员卡开卡记录完整性...")
	var cardsWithoutIssueRecord []model.MembershipCard
	err = db.Where(`id NOT IN (
		SELECT DISTINCT card_id 
		FROM membership_card_transactions 
		WHERE transaction_type = ?
	)`, enum.CardTransactionTypeIssue).Find(&cardsWithoutIssueRecord).Error
	if err != nil {
		return fmt.Errorf("查询缺失开卡记录的会员卡失败: %v", err)
	}

	if len(cardsWithoutIssueRecord) > 0 {
		fmt.Printf("⚠️ 发现 %d 张会员卡缺少开卡记录:\n", len(cardsWithoutIssueRecord))
		for _, card := range cardsWithoutIssueRecord {
			fmt.Printf("  - 卡号: %s, ID: %d, 创建时间: %s\n", 
				card.CardNumber, card.ID, card.CreatedAt.Format("2006-01-02 15:04:05"))
		}
	} else {
		fmt.Println("✅ 所有会员卡都有开卡记录")
	}

	// 4. 检查交易记录的数据完整性
	fmt.Println("\n🔍 检查交易记录数据完整性...")
	var incompleteTransactions []model.MembershipCardTransaction
	err = db.Where("operator_id = '' OR operator_name = '' OR reason = ''").
		Find(&incompleteTransactions).Error
	if err != nil {
		return fmt.Errorf("查询不完整交易记录失败: %v", err)
	}

	if len(incompleteTransactions) > 0 {
		fmt.Printf("⚠️ 发现 %d 条不完整的交易记录:\n", len(incompleteTransactions))
		for _, transaction := range incompleteTransactions {
			fmt.Printf("  - ID: %d, 类型: %s, 操作员: '%s', 原因: '%s'\n",
				transaction.ID, transaction.TransactionType, transaction.OperatorName, transaction.Reason)
		}
	} else {
		fmt.Println("✅ 所有交易记录数据完整")
	}

	return nil
}
