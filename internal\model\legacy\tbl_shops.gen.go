// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblShop = "tbl_shops"

// TblShop mapped from table <tbl_shops>
type TblShop struct {
	ID               int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Title            string    `gorm:"column:title" json:"title"`
	CreateTime       time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	Picture          string    `gorm:"column:picture" json:"picture"`
	PictureOri       string    `gorm:"column:picture_ori" json:"picture_ori"`
	DetailTopFile    string    `gorm:"column:detail_top_file" json:"detail_top_file"`
	IsDelete         int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	ServiceTel       string    `gorm:"column:service_tel" json:"service_tel"`
	Address          string    `gorm:"column:address" json:"address"`
	StoreX           string    `gorm:"column:storeX" json:"storeX"`
	StoreY           string    `gorm:"column:storeY" json:"storeY"`
	Notes            string    `gorm:"column:notes" json:"notes"`
	ClassesTimetable string    `gorm:"column:classes_timetable" json:"classes_timetable"`
}

// TableName TblShop's table name
func (*TblShop) TableName() string {
	return TableNameTblShop
}
