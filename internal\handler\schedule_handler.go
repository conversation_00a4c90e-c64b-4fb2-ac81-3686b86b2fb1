package handler

import (
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type ScheduleHandler struct {
	db *gorm.DB
}

func NewScheduleHandler(db *gorm.DB) *ScheduleHandler {
	return &ScheduleHandler{
		db: db,
	}
}

// GetSchedules 获取排课列表
func (h *ScheduleHandler) GetSchedules(c *gin.Context) {
	var req dto.CommonListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	// 构建查询条件
	query := h.db.Model(&model.Course{}).
		Preload("Coach").
		Preload("Store").
		Preload("ClassRoom")

	// 日期筛选
	dateStr := c.Query("date")
	if dateStr != "" {
		if date, err := time.Parse("2006-01-02", dateStr); err == nil {
			startTime := date
			endTime := date.Add(24 * time.Hour)
			query = query.Where("start_time >= ? AND start_time < ?", startTime, endTime)
		}
	}

	// 教练筛选
	if req.CoachID != "" {
		if coachIDInt := cast.ToUint(req.CoachID); coachIDInt > 0 {
			query = query.Where("coach_id = ?", coachIDInt)
		}
	}

	// 门店筛选
	if req.StoreID != "" {
		if storeIDInt := cast.ToUint(req.StoreID); storeIDInt > 0 {
			query = query.Where("store_id = ?", storeIDInt)
		}
	}

	// 状态筛选
	if req.Status != "" {
		if statusInt := cast.ToInt(req.Status); statusInt > 0 {
			query = query.Where("status = ?", statusInt)
		}
	}

	// 查询总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		log.Error("查询排课总数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排课列表失败"))
		return
	}

	// 分页查询 - 按开始时间和创建时间排序
	var schedules []model.Course
	err := query.Offset(req.GetOffset()).Limit(req.PageSize).Order("start_time ASC, created_at DESC").Find(&schedules).Error
	if err != nil {
		log.Error("查询排课列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排课列表失败"))
		return
	}

	// 构建响应数据
	var scheduleList []dto.AdminScheduleInfo
	for _, schedule := range schedules {
		// 获取教练信息
		var coachName string
		if len(schedule.CoachIDs) > 0 {
			var coach model.User
			if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", schedule.CoachIDs[0], true, true).First(&coach).Error; err == nil {
				coachName = coach.NickName
			}
		}

		scheduleInfo := dto.AdminScheduleInfo{
			ID:        schedule.ID,
			Name:      schedule.Name,
			StartTime: schedule.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:   schedule.EndTime.Format("2006-01-02 15:04:05"),
			Duration:  schedule.Duration,
			Capacity:  schedule.Capacity,
			Price:     schedule.Price,
			Status:    int(schedule.Status),
			CoachName: coachName,
			StoreName: schedule.Store.Name,
		}
		scheduleList = append(scheduleList, scheduleInfo)
	}

	response := dto.PageResponse{
		List:     scheduleList,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	code.AutoResponse(c, response, nil)
}

// CreateSchedule 创建排课
func (h *ScheduleHandler) CreateSchedule(c *gin.Context) {
	var req dto.CreateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建排课参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证教练是否存在
	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ? AND is_active = ?", req.CoachID, true, true).First(&coach).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在或已禁用"))
		return
	}

	// 验证门店是否存在
	var store model.Store
	if err := h.db.First(&store, req.StoreID).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "门店不存在"))
		return
	}

	// 验证教室是否存在（如果提供了教室ID）
	if req.ClassRoomID != nil {
		var classroom model.ClassRoom
		if err := h.db.Where("id = ? AND store_id = ?", *req.ClassRoomID, req.StoreID).First(&classroom).Error; err != nil {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教室不存在或不属于该门店"))
			return
		}
	}

	// 检查时间冲突
	var conflictCount int64
	conflictQuery := h.db.Model(&model.Course{}).
		Where("coach_id = ? AND status IN (1, 2)", req.CoachID).
		Where("(start_time < ? AND end_time > ?) OR (start_time < ? AND end_time > ?)",
			req.EndTime, req.StartTime, req.StartTime, req.EndTime)

	if err := conflictQuery.Count(&conflictCount).Error; err != nil {
		log.Error("检查时间冲突失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查时间冲突失败"))
		return
	}

	if conflictCount > 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该时间段教练已有其他课程安排"))
		return
	}

	// 创建排课
	schedule := model.Course{
		Name:        req.Name,
		Description: req.Description,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
		Duration:    req.Duration,
		Capacity:    req.Capacity,
		Price:       req.Price,
		CoachIDs:    []string{req.CoachID}, // 使用数组格式
		StoreID:     req.StoreID,
		Type:        enum.CourseType(req.Type),
		Status:      1, // 默认启用
	}

	if err := h.db.Create(&schedule).Error; err != nil {
		log.Error("创建排课失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建排课失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Coach").Preload("Store").Preload("ClassRoom").First(&schedule, schedule.ID)

	log.Info("创建排课成功", "id", schedule.ID, "name", schedule.Name)
	code.AutoResponse(c, schedule, nil)
}

// UpdateSchedule 更新排课
func (h *ScheduleHandler) UpdateSchedule(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("排课ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateScheduleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新排课参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询排课是否存在
	var schedule model.Course
	err := h.db.First(&schedule, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "排课不存在"))
		} else {
			log.Error("查询排课失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询排课失败"))
		}
		return
	}

	// 检查是否有预约，如果有预约则不能修改时间
	if req.StartTime != nil || req.EndTime != nil {
		var bookingCount int64
		if err := h.db.Model(&model.Booking{}).Where("course_id = ? AND status IN (1, 2)", id).Count(&bookingCount).Error; err != nil {
			log.Error("检查预约数量失败", "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "检查预约失败"))
			return
		}

		if bookingCount > 0 {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "该课程已有预约，不能修改时间"))
			return
		}
	}

	// 构建更新数据
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}
	if req.StartTime != nil {
		updates["start_time"] = *req.StartTime
	}
	if req.EndTime != nil {
		updates["end_time"] = *req.EndTime
	}
	if req.Duration != nil {
		updates["duration"] = *req.Duration
	}
	if req.Capacity != nil {
		updates["capacity"] = *req.Capacity
	}
	if req.Price != nil {
		updates["price"] = *req.Price
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}

	if len(updates) > 0 {
		if err := h.db.Model(&schedule).Updates(updates).Error; err != nil {
			log.Error("更新排课失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新排课失败"))
			return
		}
	}

	log.Info("更新排课成功", "id", id)
	response := dto.MessageResponse{Message: "更新成功"}
	code.AutoResponse(c, response, nil)
}
