#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试升级功能的交易记录
"""

import requests

class UpgradeTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_card_types(self) -> list:
        """获取所有卡种"""
        try:
            params = {"page": 1, "page_size": 50}
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {}).get('list', [])
            return []
        except Exception as e:
            print(f"❌ 获取卡种异常: {e}")
            return []
    
    def get_existing_cards(self) -> list:
        """获取现有的会员卡"""
        try:
            params = {"page": 1, "page_size": 50}
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {}).get('list', [])
            return []
        except Exception as e:
            print(f"❌ 获取会员卡异常: {e}")
            return []
    
    def test_upgrade_with_existing_card(self) -> bool:
        """使用现有卡片测试升级功能"""
        print("\n🧪 使用现有卡片测试升级功能...")
        
        # 获取所有卡种
        card_types = self.get_card_types()
        print(f"可用卡种: {len(card_types)} 个")
        
        # 按类别分组卡种
        category_groups = {}
        for card_type in card_types:
            category = card_type.get('category')
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(card_type)
        
        print("卡种分类:")
        for category, types in category_groups.items():
            print(f"  {category}: {len(types)} 个")
            for ct in types:
                print(f"    - {ct.get('name')} (ID: {ct.get('id')})")
        
        # 找到有多个卡种的类别
        upgrade_category = None
        source_type = None
        target_type = None
        
        for category, types in category_groups.items():
            if len(types) >= 2:
                upgrade_category = category
                source_type = types[0]
                target_type = types[1]
                break
        
        if not upgrade_category:
            print("⚠️ 没有找到可以升级的卡种组合")
            return True
        
        print(f"\n选择升级测试:")
        print(f"  类别: {upgrade_category}")
        print(f"  源卡种: {source_type.get('name')} (ID: {source_type.get('id')})")
        print(f"  目标卡种: {target_type.get('name')} (ID: {target_type.get('id')})")
        
        # 获取现有卡片
        existing_cards = self.get_existing_cards()
        test_card = None
        
        for card in existing_cards:
            if card.get('card_type_id') == source_type.get('id') and card.get('status') == 1:
                test_card = card
                break
        
        if not test_card:
            print(f"❌ 没有找到状态正常的 {source_type.get('name')} 卡片")
            return False
        
        card_id = test_card.get('id')
        print(f"使用测试卡片: ID {card_id}, 卡号 {test_card.get('card_number')}")
        
        # 执行升级
        upgrade_data = {
            "target_card_type_id": target_type.get('id'),
            "remark": "测试升级功能"
        }
        
        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/upgrade", json=upgrade_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 升级成功")
                    
                    # 检查交易记录
                    self.check_transactions(card_id)
                    return True
                else:
                    print(f"❌ 升级失败: {result.get('msg')}")
                    return False
            else:
                try:
                    result = response.json()
                    print(f"❌ 升级失败: {result.get('msg', response.status_code)}")
                except:
                    print(f"❌ 升级失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 升级异常: {e}")
            return False
    
    def check_transactions(self, card_id: int):
        """检查交易记录"""
        print(f"\n📋 检查卡片 {card_id} 的交易记录...")
        
        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    transactions = result.get('data', {}).get('list', [])
                    print(f"✅ 找到 {len(transactions)} 条交易记录")
                    
                    # 查找升级记录
                    upgrade_transactions = [t for t in transactions if t.get('transaction_type') == 'upgrade']
                    if upgrade_transactions:
                        print(f"🎉 找到 {len(upgrade_transactions)} 条升级交易记录:")
                        for transaction in upgrade_transactions:
                            print(f"  - 时间: {transaction.get('created_at')}")
                            print(f"  - 原因: {transaction.get('reason')}")
                            print(f"  - 备注: {transaction.get('remarks')}")
                            print(f"  - 操作员: {transaction.get('operator_name')}")
                    else:
                        print("❌ 没有找到升级交易记录")
                    
                    return True
                else:
                    print(f"❌ 获取交易记录失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取交易记录失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取交易记录异常: {e}")
            return False
    
    def run_test(self):
        """运行完整测试"""
        print("🚀 开始测试升级功能的交易记录...")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 测试升级功能
        upgrade_success = self.test_upgrade_with_existing_card()
        
        print("\n" + "=" * 80)
        print("📊 测试结果:")
        print(f"  升级功能: {'✅ 成功' if upgrade_success else '❌ 失败'}")
        
        return upgrade_success

def main():
    """主函数"""
    tester = UpgradeTester()
    success = tester.run_test()
    
    if success:
        print("\n🏆 升级功能测试成功！")
        exit(0)
    else:
        print("\n💥 升级功能测试失败！")
        exit(1)

if __name__ == "__main__":
    main()
