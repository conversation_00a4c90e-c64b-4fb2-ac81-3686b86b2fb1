package model

// MenuType 菜单类型枚举
type MenuType int

const (
	MenuTypeDirectory MenuType = 1 // 目录
	MenuTypePage      MenuType = 2 // 页面/菜单
	MenuTypeButton    MenuType = 3 // 按钮/权限
)

// String 返回菜单类型的字符串表示
func (mt MenuType) String() string {
	switch mt {
	case MenuTypeDirectory:
		return "directory"
	case MenuTypePage:
		return "page"
	case MenuTypeButton:
		return "button"
	default:
		return "unknown"
	}
}

// IsValid 检查菜单类型是否有效
func (mt MenuType) IsValid() bool {
	return mt >= MenuTypeDirectory && mt <= MenuTypeButton
}

// Menu 菜单模型
type Menu struct {
	BaseModel

	// 基本信息
	Title     string   `json:"title" gorm:"type:varchar(100);not null;comment:菜单标题"`
	Path      string   `json:"path" gorm:"type:varchar(255);comment:路由路径"`
	Component string   `json:"component" gorm:"type:varchar(255);comment:组件路径"`
	Redirect  string   `json:"redirect" gorm:"type:varchar(255);comment:重定向路径"`
	Type      MenuType `json:"type" gorm:"not null;comment:菜单类型:1=目录,2=页面,3=按钮"`

	// 图标相关
	Icon    string `json:"icon" gorm:"type:varchar(100);comment:图标类名"`
	SvgIcon string `json:"svg_icon" gorm:"type:varchar(100);comment:SVG图标名"`

	// 层级关系
	ParentID uint `json:"parent_id" gorm:"default:0;index;comment:父级ID"`
	Sort     int  `json:"sort" gorm:"default:0;comment:排序"`

	// 显示控制
	Hidden     bool `json:"hidden" gorm:"default:false;comment:是否隐藏"`
	KeepAlive  bool `json:"keep_alive" gorm:"default:false;comment:是否缓存"`
	Breadcrumb bool `json:"breadcrumb" gorm:"default:true;comment:是否显示面包屑"`
	ShowInTabs bool `json:"show_in_tabs" gorm:"default:true;comment:是否在标签页显示"`
	AlwaysShow bool `json:"always_show" gorm:"default:false;comment:是否总是显示"`
	Affix      bool `json:"affix" gorm:"default:false;comment:是否固定在标签页"`

	// 其他属性
	ActiveMenu string `json:"active_menu" gorm:"type:varchar(255);comment:激活的菜单路径"`
	Status     int    `json:"status" gorm:"default:1;comment:状态:1=启用,0=禁用"`

	// 权限关联 - 可选的权限标识，为空表示不需要权限验证
	PermissionKey string      `json:"permission" gorm:"type:varchar(200);comment:权限标识，为空表示无需权限"`
	Permission    *Permission `json:"permission_info,omitempty" gorm:"foreignKey:PermissionKey;references:Key"`

	// 关联关系
	Children []*Menu `json:"children" gorm:"-"` // 用于构建树形结构，不存储到数据库
	Parent   *Menu   `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
}

// RequiresPermission 检查菜单是否需要权限验证
func (m *Menu) RequiresPermission() bool {
	return m.PermissionKey != ""
}

// IsAccessible 检查菜单是否可访问（基于权限）
func (m *Menu) IsAccessible() bool {
	// 如果不需要权限，直接可访问
	if !m.RequiresPermission() {
		return true
	}
	// 如果需要权限，需要通过权限验证（这里只是标记，实际验证在中间件中）
	return false
}

// GetAccessibleMenuType 获取菜单的访问类型
func (m *Menu) GetAccessibleMenuType() string {
	if !m.RequiresPermission() {
		return "public"
	}
	return "protected"
}
