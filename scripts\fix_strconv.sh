#!/bin/bash

# 检查和修复 strconv 使用的脚本
# 根据项目规范，应该使用 cast 库而不是 strconv

echo "🔍 检查项目中 strconv 的使用情况..."

# 查找所有使用 strconv 的 Go 文件
echo "📋 发现以下文件使用了 strconv:"
grep -r "strconv\." internal/ --include="*.go" | grep -v "test" | head -20

echo ""
echo "⚠️  根据项目规范，应该使用 github.com/spf13/cast 库替代 strconv"
echo ""
echo "🔧 常见替换规则:"
echo "  strconv.Atoi(s)           -> cast.ToInt(s)"
echo "  strconv.ParseUint(s,10,32) -> cast.ToUint(s)"
echo "  strconv.ParseUint(s,10,64) -> cast.ToUint64(s)"
echo "  strconv.ParseInt(s,10,64)  -> cast.ToInt64(s)"
echo ""
echo "⚡ 特殊情况（可以保留 strconv）:"
echo "  - strconv.FormatInt() 用于格式化输出"
echo "  - strconv.Itoa() 用于整数转字符串"
echo "  - 其他非解析类的格式化函数"
echo ""
echo "✅ cast 库的优势:"
echo "  - 更安全的类型转换"
echo "  - 更好的错误处理"
echo "  - 处理边界情况更优雅"
echo "  - 零值友好，转换失败返回零值而不是 panic"
echo ""
echo "📝 请手动检查并替换不符合规范的 strconv 使用"
