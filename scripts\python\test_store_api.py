#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
门店API测试脚本
测试门店接口是否正确返回image_urls并且链接能否正常访问
"""

import requests
import json
import sys
import time
from urllib.parse import urlparse

# 配置
BASE_URL = "http://127.0.0.1:9095"
USERNAME = "admin"
PASSWORD = "admin123"

class StoreAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.token = None

    def login(self):
        """管理员登录"""
        login_url = f"{BASE_URL}/api/v1/public/admin/login"
        login_data = {
            "username": USERNAME,
            "password": PASSWORD
        }

        print("🔐 正在登录...")
        try:
            response = self.session.post(login_url, json=login_data)
            response.raise_for_status()

            result = response.json()
            print(f"登录响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0 and result.get("data"):
                self.token = result["data"].get("access_token")
                if self.token:
                    # 设置Authorization头
                    self.session.headers.update({
                        "Authorization": f"Bearer {self.token}",
                        "Content-Type": "application/json"
                    })
                    print("✅ 登录成功")
                    return True
                else:
                    print("❌ 登录失败：未获取到token")
                    return False
            else:
                print(f"❌ 登录失败：{result.get('msg', '未知错误')}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ 登录请求失败: {e}")
            return False

    def test_admin_stores(self):
        """测试管理端门店列表接口"""
        print("\n📋 测试管理端门店列表接口...")

        # 管理端接口需要分页参数，获取更多门店
        stores_url = f"{BASE_URL}/api/v1/admin/stores?page=1&page_size=30"

        try:
            response = self.session.get(stores_url)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")

            if response.status_code != 200:
                print(f"❌ HTTP错误 {response.status_code}: {response.text}")
                return False

            result = response.json()
            print(f"门店列表响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0:
                stores_data = result.get("data", {})
                stores_list = stores_data.get("list", [])

                print(f"✅ 获取到 {len(stores_list)} 个门店")

                # 检查每个门店的image_urls字段
                for i, store in enumerate(stores_list):
                    print(f"\n🏪 门店 {i+1}: {store.get('name', 'Unknown')}")
                    print(f"   ID: {store.get('id')}")

                    # 检查是否有image_urls字段
                    if 'image_urls' in store:
                        image_urls = store['image_urls']
                        print(f"   ✅ 包含image_urls字段: {len(image_urls)} 张图片")

                        # 测试每个图片URL
                        for j, url in enumerate(image_urls):
                            print(f"   📷 图片 {j+1}: {url}")
                            self.test_image_url(url)
                    else:
                        print("   ❌ 缺少image_urls字段")

                    # 检查是否还有旧的image_ids字段（应该被隐藏）
                    if 'image_ids' in store:
                        print("   ⚠️  仍然包含image_ids字段（应该被隐藏）")

                return True
            else:
                print(f"❌ 获取门店列表失败：{result.get('msg', '未知错误')}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return False

    def test_app_stores(self):
        """测试小程序端门店列表接口"""
        print("\n📱 测试小程序端门店列表接口...")

        stores_url = f"{BASE_URL}/api/v1/app/stores"

        try:
            # 小程序端也需要认证，使用相同的token
            response = self.session.get(stores_url)
            print(f"响应状态码: {response.status_code}")
            print(f"响应内容: {response.text}")

            if response.status_code != 200:
                print(f"❌ HTTP错误 {response.status_code}: {response.text}")
                return False

            result = response.json()
            print(f"小程序门店列表响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0:
                stores_list = result.get("data", [])
                print(f"✅ 获取到 {len(stores_list)} 个门店")

                # 检查每个门店的image_urls字段
                for i, store in enumerate(stores_list):
                    print(f"\n🏪 门店 {i+1}: {store.get('name', 'Unknown')}")

                    # 检查是否有image_urls字段
                    if 'image_urls' in store:
                        image_urls = store['image_urls']
                        print(f"   ✅ 包含image_urls字段: {len(image_urls)} 张图片")

                        # 测试每个图片URL
                        for j, url in enumerate(image_urls):
                            print(f"   📷 图片 {j+1}: {url}")
                            self.test_image_url(url)
                    else:
                        print("   ❌ 缺少image_urls字段")

                return True
            else:
                print(f"❌ 获取门店列表失败：{result.get('msg', '未知错误')}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return False

    def test_image_url(self, url):
        """测试图片URL是否可访问"""
        if not url:
            print("     ❌ 空URL")
            return False

        try:
            # 解析URL
            parsed = urlparse(url)
            if not parsed.scheme:
                # 相对URL，补充完整URL
                full_url = f"{BASE_URL}{url}"
            else:
                full_url = url

            print(f"     🔗 测试URL: {full_url}")

            # 发送HEAD请求检查URL是否可访问
            response = requests.head(full_url, timeout=5)

            if response.status_code == 200:
                print(f"     ✅ URL可访问 (状态码: {response.status_code})")
                return True
            elif response.status_code == 302:
                print(f"     ✅ URL重定向 (状态码: {response.status_code})")
                # 如果是重定向，获取重定向目标
                location = response.headers.get('Location')
                if location:
                    print(f"     🔄 重定向到: {location}")
                return True
            else:
                print(f"     ❌ URL不可访问 (状态码: {response.status_code})")
                return False

        except requests.exceptions.RequestException as e:
            print(f"     ❌ URL测试失败: {e}")
            return False

    def run_tests(self):
        """运行所有测试"""
        print("🚀 开始门店API测试")
        print("=" * 50)

        # 登录
        if not self.login():
            print("❌ 登录失败，无法继续测试")
            return False

        # 测试管理端门店接口
        admin_success = self.test_admin_stores()

        # 测试小程序端门店接口
        app_success = self.test_app_stores()

        # 总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        print(f"   管理端门店接口: {'✅ 通过' if admin_success else '❌ 失败'}")
        print(f"   小程序端门店接口: {'✅ 通过' if app_success else '❌ 失败'}")

        if admin_success and app_success:
            print("🎉 所有测试通过！")
            return True
        else:
            print("⚠️  部分测试失败")
            return False

def main():
    """主函数"""
    tester = StoreAPITester()
    success = tester.run_tests()

    if success:
        sys.exit(0)
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
