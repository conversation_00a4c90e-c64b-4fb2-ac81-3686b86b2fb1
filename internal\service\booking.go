package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
	"yogaga/internal/model"

	"github.com/charmbracelet/log"
	"github.com/hibiken/asynq"
	"gorm.io/gorm"
)

// 任务类型常量
const (
	TypeBookingProcess = "booking:process"
	TypeBookingCancel  = "booking:cancel"
	TypeQueueNotify    = "queue:notify"
	TypeClassReminder  = "class:reminder"
	TypeProcessNoShow  = "class:no_show"
)

// BookingPayload 预约任务载荷
type BookingPayload struct {
	UserID           string `json:"user_id"`
	ScheduleID       uint   `json:"schedule_id"`
	MembershipCardID uint   `json:"membership_card_id"`
	PeopleCount      int    `json:"people_count"`
	ApplicationID    uint   `json:"application_id"`
}

// BookingService 预约服务
type BookingService struct {
	db            *gorm.DB
	asynqClient   *asynq.Client
	wechatService *WeChatService
}

// NewBookingService 创建预约服务实例
func NewBookingService(db *gorm.DB, asynqClient *asynq.Client, wechatService *WeChatService) *BookingService {
	return &BookingService{
		db:            db,
		asynqClient:   asynqClient,
		wechatService: wechatService,
	}
}

// SubmitBookingApplication 提交预约申请
func (s *BookingService) SubmitBookingApplication(userID string, scheduleID uint, membershipCardID uint, peopleCount int) (*model.BookingApplication, error) {
	// 1. 创建预约申请记录
	application := model.BookingApplication{
		UserID:           userID,
		ScheduleID:       scheduleID,
		MembershipCardID: membershipCardID,
		PeopleCount:      peopleCount,
		Status:           "pending",
		Result:           "预约申请已提交，正在处理中...",
	}

	if err := s.db.Create(&application).Error; err != nil {
		return nil, fmt.Errorf("创建预约申请失败: %w", err)
	}

	// 2. 创建异步任务
	payload := BookingPayload{
		UserID:           userID,
		ScheduleID:       scheduleID,
		MembershipCardID: membershipCardID,
		PeopleCount:      peopleCount,
		ApplicationID:    application.ID,
	}

	payloadBytes, _ := json.Marshal(payload)
	task := asynq.NewTask(TypeBookingProcess, payloadBytes)

	// 3. 提交任务到队列
	info, err := s.asynqClient.Enqueue(task)
	if err != nil {
		// 更新申请状态为失败
		s.db.Model(&application).Updates(map[string]interface{}{
			"status":     "failed",
			"result":     "任务提交失败",
			"error_code": "TASK_SUBMIT_ERROR",
		})
		return nil, fmt.Errorf("提交预约任务失败: %w", err)
	}

	// 4. 更新任务ID
	application.TaskID = info.ID
	s.db.Model(&application).Update("task_id", info.ID)

	log.Info("预约申请提交成功", "application_id", application.ID, "task_id", info.ID)
	return &application, nil
}

// ProcessBookingTask 处理预约任务
func (s *BookingService) ProcessBookingTask(ctx context.Context, t *asynq.Task) error {
	var payload BookingPayload
	if err := json.Unmarshal(t.Payload(), &payload); err != nil {
		return fmt.Errorf("解析任务数据失败: %w", err)
	}

	log.Info("开始处理预约任务", "application_id", payload.ApplicationID, "user_id", payload.UserID, "schedule_id", payload.ScheduleID)

	// 更新申请状态为处理中
	s.updateApplicationStatus(payload.ApplicationID, "processing", "正在处理预约申请...")

	// 执行预约逻辑
	err := s.processBookingLogic(payload)
	if err != nil {
		log.Error("预约处理失败", "application_id", payload.ApplicationID, "error", err)
		s.updateApplicationStatus(payload.ApplicationID, "failed", err.Error())
		return err
	}

	log.Info("预约处理成功", "application_id", payload.ApplicationID)
	return nil
}

// processBookingLogic 预约业务逻辑
func (s *BookingService) processBookingLogic(payload BookingPayload) error {
	return s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 检查课程可用性
		var schedule model.ClassSchedule
		if err := tx.First(&schedule, payload.ScheduleID).Error; err != nil {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "课程不存在")
			return fmt.Errorf("课程不存在")
		}

		// 检查课程状态
		if schedule.Status != 1 {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "课程不可预约")
			return fmt.Errorf("课程不可预约")
		}

		// 检查开课时间
		if schedule.StartTime.Before(time.Now().Add(2 * time.Hour)) {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "开课前2小时内不可预约")
			return fmt.Errorf("开课前2小时内不可预约")
		}

		// 2. 检查容量，如果满了则加入排队
		if schedule.CurrentCount+payload.PeopleCount > schedule.MaxCapacity {
			return s.addToQueue(tx, payload)
		}

		// 3. 选择会员卡
		var selectedCard *model.MembershipCard
		var err error

		if payload.MembershipCardID > 0 {
			// 使用指定的会员卡
			err = tx.Preload("CardType").First(&selectedCard, payload.MembershipCardID).Error
			if err != nil {
				s.updateApplicationStatus(payload.ApplicationID, "failed", "指定的会员卡不存在")
				return fmt.Errorf("指定的会员卡不存在")
			}
		} else {
			// 智能选择最优会员卡
			rule := CardSelectionRule{
				ScheduleID: payload.ScheduleID,
				UserID:     payload.UserID,
			}
			selectedCard, err = s.SelectBestCard(rule)
			if err != nil {
				s.updateApplicationStatus(payload.ApplicationID, "failed", err.Error())
				return err
			}
		}

		// 4. 计算扣费
		deductTimes, deductAmount, actualAmount, err := s.CalculateDeduction(selectedCard, payload.ScheduleID, payload.PeopleCount)
		if err != nil {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "扣费计算失败")
			return err
		}

		// 5. 创建预约记录
		booking := model.Booking{
			UserID:           payload.UserID,
			ScheduleID:       payload.ScheduleID,
			MembershipCardID: selectedCard.ID,
			BookingNumber:    s.GenerateBookingNumber(),
			PeopleCount:      payload.PeopleCount,
			DeductTimes:      deductTimes,
			DeductAmount:     deductAmount,
			ActualAmount:     actualAmount,
			PaymentMethod:    "membership_card",
			Status:           1, // 已预约
		}

		if err := tx.Create(&booking).Error; err != nil {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "创建预约记录失败")
			return err
		}

		// 6. 更新课程人数
		if err := tx.Model(&schedule).Update("current_count", schedule.CurrentCount+payload.PeopleCount).Error; err != nil {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "更新课程人数失败")
			return err
		}

		// 7. 更新会员卡余额
		if err := s.updateCardBalanceInTx(tx, selectedCard.ID, deductTimes, deductAmount); err != nil {
			s.updateApplicationStatus(payload.ApplicationID, "failed", "更新会员卡余额失败")
			return err
		}

		// 8. 发送预约成功微信通知
		go func() {
			if s.wechatService != nil {
				// 查询用户微信信息
				var user model.User
				if err := s.db.First(&user, payload.UserID).Error; err == nil && user.HasWeChatOpenID() {
					err := s.wechatService.SendBookingConfirmation(
						user.GetWeChatOpenID(),
						schedule.Course.Name,
						schedule.StartTime.Format("2006-01-02 15:04"),
						schedule.Store.Name,
						booking.BookingNumber,
					)
					if err != nil {
						log.Error("发送预约成功通知失败", "user_id", payload.UserID, "booking_id", booking.ID, "error", err)
					} else {
						log.Info("预约成功通知发送成功", "user_id", payload.UserID, "booking_id", booking.ID)
					}
				}
			}
		}()

		// 9. 更新申请状态为成功
		s.updateApplicationStatus(payload.ApplicationID, "completed", "预约成功")
		s.db.Model(&model.BookingApplication{}).Where("id = ?", payload.ApplicationID).Update("booking_id", booking.ID)

		log.Info("预约创建成功", "booking_id", booking.ID, "booking_number", booking.BookingNumber)
		return nil
	})
}

// updateApplicationStatus 更新申请状态
func (s *BookingService) updateApplicationStatus(applicationID uint, status string, result string) {
	updates := map[string]any{
		"status": status,
		"result": result,
	}
	s.db.Model(&model.BookingApplication{}).Where("id = ?", applicationID).Updates(updates)
}

// addToQueue 添加到排队
func (s *BookingService) addToQueue(tx *gorm.DB, payload BookingPayload) error {
	// 获取当前排队号
	var maxQueueNumber int
	tx.Model(&model.BookingQueue{}).
		Where("schedule_id = ? AND status = 1", payload.ScheduleID).
		Select("COALESCE(MAX(queue_number), 0)").
		Scan(&maxQueueNumber)

	// 获取课程ID
	var schedule model.ClassSchedule
	s.db.First(&schedule, payload.ScheduleID)

	// 创建排队记录
	queue := model.BookingQueue{
		UserID:      payload.UserID,
		CourseID:    schedule.CourseID,
		QueueNumber: maxQueueNumber + 1,
		Status:      1, // 排队中
	}

	if err := tx.Create(&queue).Error; err != nil {
		s.updateApplicationStatus(payload.ApplicationID, "failed", "加入排队失败")
		return err
	}

	// 更新申请状态为排队
	s.updateApplicationStatus(payload.ApplicationID, "queued", fmt.Sprintf("课程已满，已加入排队，当前排队位置：%d", queue.QueueNumber))
	s.db.Model(&model.BookingApplication{}).Where("id = ?", payload.ApplicationID).Update("queue_position", queue.QueueNumber)

	log.Info("用户加入排队", "user_id", payload.UserID, "schedule_id", payload.ScheduleID, "queue_number", queue.QueueNumber)
	return nil
}

// updateCardBalanceInTx 在事务中更新会员卡余额
func (s *BookingService) updateCardBalanceInTx(tx *gorm.DB, cardID uint, deductTimes int, deductAmount int) error {
	updates := make(map[string]any)

	if deductTimes > 0 {
		updates["remaining_times"] = gorm.Expr("remaining_times - ?", deductTimes)
	}

	if deductAmount > 0 {
		updates["remaining_amount"] = gorm.Expr("remaining_amount - ?", deductAmount)
	}

	if len(updates) > 0 {
		return tx.Model(&model.MembershipCard{}).Where("id = ?", cardID).Updates(updates).Error
	}

	return nil
}

// CardSelectionRule 会员卡选择规则
type CardSelectionRule struct {
	ScheduleID uint
	UserID     string
}

// SelectBestCard 智能选择最优会员卡
func (s *BookingService) SelectBestCard(rule CardSelectionRule) (*model.MembershipCard, error) {
	var cards []model.MembershipCard

	// 查询用户的有效会员卡
	err := s.db.Preload("CardType").
		Where("user_id = ? AND status = 1", rule.UserID).
		Where("(end_date IS NULL OR end_date > ?) AND (remaining_times > 0 OR remaining_amount > 0)", time.Now()).
		Order("CASE WHEN card_type.category = 'period' THEN 1 ELSE 2 END, end_date ASC").
		Find(&cards).Error

	if err != nil {
		return nil, fmt.Errorf("查询会员卡失败: %w", err)
	}

	if len(cards) == 0 {
		return nil, fmt.Errorf("没有可用的会员卡")
	}

	// 按优先级选择：期限卡 > 其他卡种，按有效期临近排序
	for _, card := range cards {
		if s.canUseCard(&card, rule.ScheduleID) {
			return &card, nil
		}
	}

	return nil, fmt.Errorf("没有适用的会员卡")
}

// canUseCard 检查会员卡是否可用于指定课程
func (s *BookingService) canUseCard(card *model.MembershipCard, scheduleID uint) bool {
	// 检查有效期
	if card.EndDate.Before(time.Now()) {
		return false
	}

	// 检查余额/次数
	switch card.CardType.Category {
	case "times":
		return card.RemainingTimes > 0
	case "period":
		return card.EndDate.After(time.Now())
	case "balance":
		// 这里需要根据课程价格判断，暂时简化
		return card.RemainingAmount > 0
	}

	return false
}

// CalculateDeduction 计算扣费
func (s *BookingService) CalculateDeduction(card *model.MembershipCard, scheduleID uint, peopleCount int) (deductTimes int, deductAmount int, actualAmount int, err error) {
	// 获取课程信息
	var schedule model.ClassSchedule
	err = s.db.Preload("Course").First(&schedule, scheduleID).Error
	if err != nil {
		return 0, 0, 0, fmt.Errorf("查询课程失败: %w", err)
	}

	// 使用默认计算逻辑
	switch card.CardType.Category {
	case "times":
		// 次数卡：扣除次数
		deductTimes = peopleCount
		actualAmount = int(float64(schedule.Price*peopleCount) * card.Discount)

	case "period":
		// 期限卡：不扣次数，只记录实际消费
		deductTimes = 0
		actualAmount = int(float64(schedule.Price*peopleCount) * card.Discount)

	case "balance":
		// 储值卡：扣除金额
		deductAmount = int(float64(schedule.Price*peopleCount) * card.Discount)
		actualAmount = deductAmount

	default:
		return 0, 0, 0, fmt.Errorf("不支持的卡种类别: %s", card.CardType.Category)
	}

	return deductTimes, deductAmount, actualAmount, nil
}

// GenerateBookingNumber 生成预约单号
func (s *BookingService) GenerateBookingNumber() string {
	return fmt.Sprintf("BK%d%06d", time.Now().Unix(), time.Now().Nanosecond()%1000000)
}

// CheckBookingConflict 检查预约冲突
func (s *BookingService) CheckBookingConflict(userID uint, scheduleID uint) error {
	var count int64

	// 检查用户是否已经预约了同一时间段的课程
	err := s.db.Table("bookings b").
		Joins("JOIN class_schedules cs ON b.schedule_id = cs.id").
		Where("b.user_id = ? AND b.status IN (1, 2)", userID).
		Where("cs.start_time <= (SELECT start_time FROM class_schedules WHERE id = ?) AND cs.end_time >= (SELECT start_time FROM class_schedules WHERE id = ?)", scheduleID, scheduleID).
		Count(&count).Error

	if err != nil {
		return fmt.Errorf("检查预约冲突失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("该时间段已有预约，无法重复预约")
	}

	return nil
}

// CheckScheduleAvailability 检查课程可预约性
func (s *BookingService) CheckScheduleAvailability(scheduleID uint, peopleCount int) error {
	var schedule model.ClassSchedule
	err := s.db.First(&schedule, scheduleID).Error
	if err != nil {
		return fmt.Errorf("课程不存在")
	}

	// 检查课程状态
	if schedule.Status != 1 {
		return fmt.Errorf("课程不可预约")
	}

	// 检查开课时间
	if schedule.StartTime.Before(time.Now().Add(2 * time.Hour)) {
		return fmt.Errorf("开课前2小时内不可预约")
	}

	// 检查容量
	if schedule.CurrentCount+peopleCount > schedule.MaxCapacity {
		return fmt.Errorf("课程人数已满")
	}

	return nil
}

// UpdateScheduleCount 更新课程人数
func (s *BookingService) UpdateScheduleCount(scheduleID uint, delta int) error {
	return s.db.Model(&model.ClassSchedule{}).
		Where("id = ?", scheduleID).
		Update("current_count", gorm.Expr("current_count + ?", delta)).Error
}

// UpdateCardBalance 更新会员卡余额
func (s *BookingService) UpdateCardBalance(cardID uint, deductTimes int, deductAmount int) error {
	updates := make(map[string]interface{})

	if deductTimes > 0 {
		updates["remaining_times"] = gorm.Expr("remaining_times - ?", deductTimes)
	}

	if deductAmount > 0 {
		updates["remaining_amount"] = gorm.Expr("remaining_amount - ?", deductAmount)
	}

	if len(updates) > 0 {
		return s.db.Model(&model.MembershipCard{}).Where("id = ?", cardID).Updates(updates).Error
	}

	return nil
}

// CanCancelBooking 检查是否可以取消预约
func (s *BookingService) CanCancelBooking(booking *model.Booking) bool {
	// 已取消或已完成的不能再取消
	if booking.Status == 4 || booking.Status == 3 {
		return false
	}

	// 获取课程开始时间
	var schedule model.ClassSchedule
	if err := s.db.First(&schedule, booking.ScheduleID).Error; err != nil {
		return false
	}

	// 开课前2小时内不能取消
	return schedule.StartTime.After(time.Now().Add(2 * time.Hour))
}

// RefundBooking 退款处理
func (s *BookingService) RefundBooking(booking *model.Booking) error {
	// 恢复会员卡余额
	if booking.MembershipCardID > 0 {
		updates := make(map[string]interface{})

		if booking.DeductTimes > 0 {
			updates["remaining_times"] = gorm.Expr("remaining_times + ?", booking.DeductTimes)
		}

		if booking.DeductAmount > 0 {
			updates["remaining_amount"] = gorm.Expr("remaining_amount + ?", booking.DeductAmount)
		}

		if len(updates) > 0 {
			err := s.db.Model(&model.MembershipCard{}).Where("id = ?", booking.MembershipCardID).Updates(updates).Error
			if err != nil {
				return fmt.Errorf("恢复会员卡余额失败: %w", err)
			}
		}
	}

	// 恢复课程人数
	err := s.UpdateScheduleCount(booking.ScheduleID, -booking.PeopleCount)
	if err != nil {
		return fmt.Errorf("恢复课程人数失败: %w", err)
	}

	return nil
}

// GetBookingStats 获取预约统计
func (s *BookingService) GetBookingStats(userID uint) (totalClasses int, totalDays int, upcomingClasses int, err error) {
	// 总上课次数（已完成的预约）
	var totalClassesCount int64
	err = s.db.Model(&model.Booking{}).
		Where("user_id = ? AND status = 3", userID).
		Count(&totalClassesCount).Error
	if err != nil {
		return 0, 0, 0, err
	}

	// 总上课天数（按日期去重）
	err = s.db.Raw(`
		SELECT COUNT(DISTINCT DATE(cs.start_time))
		FROM bookings b
		JOIN class_schedules cs ON b.schedule_id = cs.id
		WHERE b.user_id = ? AND b.status = 3
	`, userID).Scan(&totalDays).Error
	if err != nil {
		return 0, 0, 0, err
	}

	// 即将上课数量（未来的预约）
	var upcomingClassesCount int64
	err = s.db.Table("bookings b").
		Joins("JOIN class_schedules cs ON b.schedule_id = cs.id").
		Where("b.user_id = ? AND b.status IN (1, 2) AND cs.start_time > ?", userID, time.Now()).
		Count(&upcomingClassesCount).Error
	if err != nil {
		return 0, 0, 0, err
	}

	return int(totalClassesCount), totalDays, int(upcomingClassesCount), nil
}

// ProcessQueueNotification 处理排队通知
func (s *BookingService) ProcessQueueNotification(scheduleID uint) error {
	// 查找排队中的用户
	var queueItems []model.BookingQueue
	err := s.db.Where("schedule_id = ? AND status = 1", scheduleID).
		Order("queue_number ASC").
		Find(&queueItems).Error

	if err != nil {
		return fmt.Errorf("查询排队信息失败: %w", err)
	}

	// 检查是否有空位
	var schedule model.ClassSchedule
	err = s.db.First(&schedule, scheduleID).Error
	if err != nil {
		return fmt.Errorf("查询课程失败: %w", err)
	}

	availableSlots := schedule.MaxCapacity - schedule.CurrentCount
	if availableSlots <= 0 {
		return nil // 没有空位
	}

	// 通知排队用户（集成微信消息推送）
	for i, item := range queueItems {
		if i >= availableSlots {
			break
		}

		// 更新通知时间
		now := time.Now()
		s.db.Model(&item).Update("notified_at", &now)

		log.Info("通知排队用户", "user_id", item.UserID, "schedule_id", scheduleID)

		// 发送微信消息通知
		go s.sendQueueNotification(item.UserID, scheduleID)
	}

	return nil
}

// sendQueueNotification 发送排队通知
func (s *BookingService) sendQueueNotification(userID string, scheduleID uint) {
	// 查询用户信息
	var user model.User
	if err := s.db.First(&user, userID).Error; err != nil {
		log.Error("查询用户信息失败", "user_id", userID, "error", err)
		return
	}

	// 检查用户是否有微信OpenID
	if !user.HasWeChatOpenID() {
		log.Warn("用户没有微信OpenID，跳过推送", "user_id", userID)
		return
	}

	// 查询课程信息
	var schedule model.ClassSchedule
	if err := s.db.Preload("Course").First(&schedule, scheduleID).Error; err != nil {
		log.Error("查询课程信息失败", "schedule_id", scheduleID, "error", err)
		return
	}

	// 发送排队通知
	if s.wechatService != nil {
		err := s.wechatService.SendQueueNotification(
			user.GetWeChatOpenID(),
			schedule.Course.Name,
			time.Now().Add(10*time.Minute).Format("2006-01-02 15:04"), // 10分钟内预约
		)
		if err != nil {
			log.Error("发送排队通知失败", "user_id", userID, "schedule_id", scheduleID, "error", err)
		} else {
			log.Info("排队通知发送成功", "user_id", userID, "schedule_id", scheduleID)
		}
	} else {
		log.Warn("微信服务未初始化，跳过推送", "user_id", userID)
	}
}

// AutoCancelCourse 自动取消课程（因人数不足）
func (s *BookingService) AutoCancelCourse(scheduleID uint, reason string) error {
	// 查询课程信息
	var schedule model.ClassSchedule
	err := s.db.Preload("Course").Preload("Store").First(&schedule, scheduleID).Error
	if err != nil {
		return fmt.Errorf("查询课程信息失败: %w", err)
	}

	// 查询该课程的所有预约
	var bookings []model.Booking
	err = s.db.Preload("User").Where("schedule_id = ? AND status IN (1, 2)", scheduleID).Find(&bookings).Error
	if err != nil {
		return fmt.Errorf("查询预约记录失败: %w", err)
	}

	if len(bookings) == 0 {
		log.Info("没有需要取消的预约", "schedule_id", scheduleID)
		return nil
	}

	// 开始事务
	tx := s.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 批量取消预约并退款
	for _, booking := range bookings {
		// 更新预约状态
		now := time.Now()
		updates := map[string]interface{}{
			"status":        4, // 已取消
			"cancel_time":   &now,
			"cancel_reason": reason,
		}

		if err := tx.Model(&booking).Updates(updates).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("更新预约状态失败: %w", err)
		}

		// 退款处理
		if err := s.refundBookingInTx(tx, &booking); err != nil {
			tx.Rollback()
			return fmt.Errorf("退款处理失败: %w", err)
		}

		// 发送取消通知
		go func(userID string, openID string) {
			if s.wechatService != nil && openID != "" {
				err := s.wechatService.SendCourseCancelNotice(
					openID,
					schedule.Course.Name,
					schedule.StartTime.Format("2006-01-02 15:04"),
					schedule.Store.Name,
					reason,
				)
				if err != nil {
					log.Error("发送课程取消通知失败", "user_id", userID, "error", err)
				} else {
					log.Info("课程取消通知发送成功", "user_id", userID)
				}
			}
		}(booking.UserID, booking.User.GetWeChatOpenID())
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交事务失败: %w", err)
	}

	log.Info("自动取消课程完成", "schedule_id", scheduleID, "canceled_bookings", len(bookings))
	return nil
}

// refundBookingInTx 在事务中退款
func (s *BookingService) refundBookingInTx(tx *gorm.DB, booking *model.Booking) error {
	// 恢复会员卡余额
	if booking.MembershipCardID > 0 {
		updates := make(map[string]interface{})

		if booking.DeductTimes > 0 {
			updates["remaining_times"] = gorm.Expr("remaining_times + ?", booking.DeductTimes)
		}

		if booking.DeductAmount > 0 {
			updates["remaining_amount"] = gorm.Expr("remaining_amount + ?", booking.DeductAmount)
		}

		if len(updates) > 0 {
			err := tx.Model(&model.MembershipCard{}).Where("id = ?", booking.MembershipCardID).Updates(updates).Error
			if err != nil {
				return fmt.Errorf("恢复会员卡余额失败: %w", err)
			}
		}
	}

	return nil
}
