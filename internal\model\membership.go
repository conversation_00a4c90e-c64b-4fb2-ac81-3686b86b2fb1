package model

import (
	"time"
	"yogaga/internal/enum"
)

// MembershipCardType 会员卡类型
type MembershipCardType struct {
	BaseModel
	Name              string                      `json:"name" gorm:"type:varchar(100);not null;comment:卡种名称"`
	Category          enum.MembershipCardCategory `json:"category" gorm:"type:varchar(20);not null;comment:卡种类别 times:次数卡 period:期限卡 balance:储值卡"`
	Scope             enum.MembershipCardScope    `json:"scope" gorm:"type:varchar(20);not null;default:'universal';comment:适用范围 group:团课 private:私教 small:小班课 universal:通用"`
	Description       string                      `json:"description" gorm:"type:text;comment:卡种描述"`
	Price             int                         `json:"price" gorm:"type:int;not null;comment:售价(分)"`
	OriginalPrice     int                         `json:"original_price" gorm:"type:int;comment:原价(分)"`
	ValidityDays      int                         `json:"validity_days" gorm:"type:int;comment:有效天数 0表示永久"`
	Times             int                         `json:"times" gorm:"type:int;comment:次数(次数卡专用)"`
	Amount            int                         `json:"amount" gorm:"type:int;comment:储值金额(储值卡专用,分)"`
	Discount          float64                     `json:"discount" gorm:"type:decimal(3,2);default:1.00;comment:折扣率"`
	TransferLimit     int                         `json:"transfer_limit" gorm:"type:int;default:0;comment:转让次数限制 0表示不可转让"`
	FreezeLimit       int                         `json:"freeze_limit" gorm:"type:int;default:0;comment:冻结次数限制 0表示不可冻结"`
	Refundable        bool                        `json:"refundable" gorm:"default:false;comment:是否可退款"`
	Shareable         bool                        `json:"shareable" gorm:"default:false;comment:是否可共享使用"`
	CanSetExpiry      bool                        `json:"can_set_expiry" gorm:"default:true;comment:是否可设置有效期"`
	CanSelectStores   bool                        `json:"can_select_stores" gorm:"default:true;comment:是否可勾选使用门店"`
	CanAddSubCard     bool                        `json:"can_add_sub_card" gorm:"default:false;comment:是否可添加副卡"`
	CanSetDailyLimit  bool                        `json:"can_set_daily_limit" gorm:"default:false;comment:是否可设置单日预约课程上限"`
	DailyBookingLimit int                         `json:"daily_booking_limit" gorm:"type:int;default:0;comment:单日预约课程上限 0表示无限制"`
	ApplicableCourses string                      `json:"applicable_courses" gorm:"type:text;comment:适用课程JSON"`
	ExcludedCourses   string                      `json:"excluded_courses" gorm:"type:text;comment:排除课程JSON"`
	SortOrder         int                         `json:"sort_order" gorm:"type:int;default:0;comment:排序"`
	Status            enum.CourseCategoryStatus   `json:"status" gorm:"type:smallint;default:1;comment:状态 1:启用 0:停用"`
}

// MembershipCard 会员卡
type MembershipCard struct {
	BaseModel
	UserID            string                    `json:"user_id" gorm:"type:char(36);not null;comment:用户ID"`
	CardTypeID        uint                      `json:"card_type_id" gorm:"not null;comment:卡种ID"`
	CardNumber        string                    `json:"card_number" gorm:"type:varchar(50);uniqueIndex;not null;comment:卡号"`
	MainCardID        *uint                     `json:"main_card_id" gorm:"comment:主卡ID(副卡专用)"`
	IsMainCard        bool                      `json:"is_main_card" gorm:"comment:是否为主卡"`
	RemainingTimes    int                       `json:"remaining_times" gorm:"type:int;default:0;comment:剩余次数"`
	RemainingAmount   int                       `json:"remaining_amount" gorm:"type:int;default:0;comment:剩余金额(分)"`
	TotalTimes        int                       `json:"total_times" gorm:"type:int;default:0;comment:总次数"`
	TotalAmount       int                       `json:"total_amount" gorm:"type:int;default:0;comment:总金额(分)"`
	Discount          float64                   `json:"discount" gorm:"type:decimal(3,2);default:1.00;comment:折扣率"`
	StartDate         time.Time                 `json:"start_date" gorm:"not null;comment:开始日期"`
	EndDate           time.Time                 `json:"end_date" gorm:"comment:结束日期"`
	PurchasePrice     int                       `json:"purchase_price" gorm:"type:int;not null;comment:购买价格(分)"`
	PaymentMethod     enum.PaymentMethod        `json:"payment_method" gorm:"type:varchar(20);comment:支付方式"`
	OrderNumber       string                    `json:"order_number" gorm:"type:varchar(100);comment:订单号"`
	TransferCount     int                       `json:"transfer_count" gorm:"type:int;default:0;comment:已转让次数"`
	FreezeCount       int                       `json:"freeze_count" gorm:"type:int;default:0;comment:已冻结次数"`
	AvailableStores   string                    `json:"available_stores" gorm:"type:text;comment:可用门店JSON 空表示全部门店"`
	DailyBookingLimit int                       `json:"daily_booking_limit" gorm:"type:int;default:0;comment:单日预约课程上限 0表示无限制"`
	Status            enum.MembershipCardStatus `json:"status" gorm:"type:smallint;default:1;comment:状态 1:正常 2:冻结 3:过期 4:用完"`
	Remark            string                    `json:"remark" gorm:"type:text;comment:备注"`

	// 关联
	User     *User               `json:"user,omitempty" gorm:"foreignKey:UserID"`
	CardType *MembershipCardType `json:"card_type,omitempty" gorm:"foreignKey:CardTypeID"`
	MainCard *MembershipCard     `json:"main_card,omitempty" gorm:"foreignKey:MainCardID"`
	SubCards []MembershipCard    `json:"sub_cards,omitempty" gorm:"foreignKey:MainCardID"`
}

// MembershipCardTransaction 会员卡交易记录表
type MembershipCardTransaction struct {
	BaseModel
	CardID          uint                     `json:"card_id" gorm:"not null;comment:会员卡ID"`
	TransactionType enum.CardTransactionType `json:"transaction_type" gorm:"type:varchar(20);not null;comment:交易类型"`

	// 余额变化
	TimesChange  int `json:"times_change" gorm:"type:int;default:0;comment:次数变化(正数增加,负数减少)"`
	AmountChange int `json:"amount_change" gorm:"type:int;default:0;comment:金额变化(分,正数增加,负数减少)"`

	// 变化前后状态
	BeforeTimes  int                       `json:"before_times" gorm:"type:int;default:0;comment:操作前次数"`
	AfterTimes   int                       `json:"after_times" gorm:"type:int;default:0;comment:操作后次数"`
	BeforeAmount int                       `json:"before_amount" gorm:"type:int;default:0;comment:操作前金额(分)"`
	AfterAmount  int                       `json:"after_amount" gorm:"type:int;default:0;comment:操作后金额(分)"`
	BeforeStatus enum.MembershipCardStatus `json:"before_status" gorm:"type:smallint;comment:操作前状态"`
	AfterStatus  enum.MembershipCardStatus `json:"after_status" gorm:"type:smallint;comment:操作后状态"`

	// 业务关联
	RelatedBookingID *uint  `json:"related_booking_id" gorm:"comment:关联预约ID(扣费时)"`
	RelatedUserID    string `json:"related_user_id" gorm:"type:char(36);comment:关联用户ID(转卡时)"`

	// 操作信息
	OperatorID   string `json:"operator_id" gorm:"type:char(36);not null;comment:操作人ID"`
	OperatorName string `json:"operator_name" gorm:"type:varchar(50);comment:操作人姓名"`
	Reason       string `json:"reason" gorm:"type:varchar(200);comment:操作原因"`
	Remarks      string `json:"remarks" gorm:"type:text;comment:备注"`

	// 财务信息
	RealCost int `json:"real_cost" gorm:"type:int;default:0;comment:实际花费(分,用于折扣计算)"`

	// 关联
	Card           *MembershipCard `json:"card,omitempty" gorm:"foreignKey:CardID"`
	RelatedUser    *User           `json:"related_user,omitempty" gorm:"foreignKey:RelatedUserID"`
	Operator       *User           `json:"operator,omitempty" gorm:"foreignKey:OperatorID"`
	RelatedBooking *Booking        `json:"related_booking,omitempty" gorm:"foreignKey:RelatedBookingID"`
}

// MembershipCardLeave 会员卡请假记录
type MembershipCardLeave struct {
	BaseModel
	CardID             uint      `json:"card_id" gorm:"type:int;not null;comment:会员卡ID"`
	LeaveDays          int       `json:"leave_days" gorm:"type:int;not null;comment:请假天数"`
	LeaveStartDate     time.Time `json:"leave_start_date" gorm:"type:date;not null;comment:请假开始日期"`
	LeaveEndDate       time.Time `json:"leave_end_date" gorm:"type:date;not null;comment:请假结束日期"`
	ActualLeaveDays    int       `json:"actual_leave_days" gorm:"type:int;comment:实际请假天数"`
	OriginalExpiryDate time.Time `json:"original_expiry_date" gorm:"type:date;comment:原有效期"`
	NewExpiryDate      time.Time `json:"new_expiry_date" gorm:"type:date;comment:新有效期"`
	Status             int       `json:"status" gorm:"type:int;default:1;comment:状态 1:请假中 2:已销假 3:自动到期"`
	Reason             string    `json:"reason" gorm:"type:text;comment:请假原因"`

	// 关联
	Card *MembershipCard `json:"card,omitempty" gorm:"foreignKey:CardID"`
}

// TableName 指定表名
func (MembershipCardLeave) TableName() string {
	return "membership_card_leaves"
}
