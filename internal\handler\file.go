package handler

import (
	"fmt"
	"net/url"
	"strings"
	"time"
	"yogaga/internal/dto"
	"yogaga/internal/model"
	"yogaga/internal/service"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"
	"yogaga/pkg/utils"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

type FileHandler struct {
	storage     storage.Storage
	db          *gorm.DB
	fileService *service.FileService
}

func NewFileHandler(storage storage.Storage, db *gorm.DB) *FileHandler {
	return &FileHandler{
		storage:     storage,
		db:          db,
		fileService: service.NewFileService(db, storage),
	}
}

// RequestUploadBody defines the expected JSON body for requesting an upload URL.
// This struct is currently not used and can be removed or implemented as needed.

// ProxyUpload handles file upload by proxying through the server.
func (h *FileHandler) ProxyUpload(c *gin.Context) {
	fileHeader, err := c.FormFile("file")
	if err != nil {
		log.Error("获取表单文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "文件上传失败"))
		return
	}

	// You can customize objectName from a form value, or generate it.
	// Here we use the original filename.
	objectName := c.PostForm("object_name")
	if objectName == "" {
		objectName = fileHeader.Filename
	}

	file, err := fileHeader.Open()
	if err != nil {
		log.Error("打开文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "文件处理失败"))
		return
	}
	defer file.Close()

	uploadInfo, err := h.storage.Upload(objectName, file)
	if err != nil {
		log.Error("上传文件到存储失败", "object_name", objectName, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "文件上传失败"))
		return
	}

	code.AutoResponse(c, uploadInfo, nil)
}

// GetPresignedUploadURL generates a presigned URL for direct client-side uploads.
// 注释掉预签名上传，只使用代理上传
/*
func (h *FileHandler) GetPresignedUploadURL(c *gin.Context) {
	var req dto.GetPresignedURLRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定预签名上传URL请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	// Default expiration: 15 minutes
	presignedURL, err := h.storage.GetPresignedUploadURL(c.Request.Context(), req.ObjectName, 15*time.Minute)
	if err != nil {
		log.Error("生成预签名上传URL失败", "object_name", req.ObjectName, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "生成上传链接失败"))
		return
	}

	response := dto.PresignedUploadURLResponse{
		UploadURL: presignedURL.String(),
	}
	code.AutoResponse(c, response, nil)
}
*/

// GetPresignedDownloadURL generates a presigned URL for client-side downloads.
// 注释掉预签名下载，使用统一的下载接口
/*
func (h *FileHandler) GetPresignedDownloadURL(c *gin.Context) {
	var req dto.GetPresignedURLRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定预签名下载URL请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// Example: setting response headers for download
	reqParams := make(url.Values)
	reqParams.Set("response-content-disposition", fmt.Sprintf("attachment; filename=\"%s\"", req.ObjectName))

	// Default expiration: 15 minutes
	presignedURL, err := h.storage.GetPresignedDownloadURL(c.Request.Context(), req.ObjectName, 15*time.Minute, reqParams)
	if err != nil {
		log.Error("生成预签名下载URL失败", "object_name", req.ObjectName, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "生成下载链接失败"))
		return
	}

	response := dto.DownloadURLResponse{
		DownloadURL: presignedURL.String(),
	}
	code.AutoResponse(c, response, nil)
}
*/

// Upload 文件上传 - 仅限admin端使用，代理上传模式
func (h *FileHandler) Upload(c *gin.Context) {
	// 获取文件用途参数
	purpose := c.PostForm("purpose")
	if purpose == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "purpose参数不能为空"))
		return
	}

	// 验证文件用途
	validPurposes := []string{"avatar", "banner", "course", "store", "document"}
	isValid := false
	for _, validPurpose := range validPurposes {
		if purpose == validPurpose {
			isValid = true
			break
		}
	}
	if !isValid {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "无效的文件用途"))
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		log.Error("获取表单文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "文件上传失败"))
		return
	}

	// 验证文件大小（10MB限制）
	if file.Size > 10*1024*1024 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "文件大小不能超过10MB"))
		return
	}

	// 验证文件类型
	allowedTypes := map[string][]string{
		"avatar":   {"image/jpeg", "image/png", "image/gif"},
		"banner":   {"image/jpeg", "image/png"},
		"course":   {"image/jpeg", "image/png"},
		"store":    {"image/jpeg", "image/png"},
		"document": {"image/jpeg", "image/png", "application/pdf"},
	}

	contentType := file.Header.Get("Content-Type")
	if allowedTypesForFile, exists := allowedTypes[purpose]; exists {
		isValidContentType := false
		for _, allowedType := range allowedTypesForFile {
			if contentType == allowedType {
				isValidContentType = true
				break
			}
		}
		if !isValidContentType {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "不支持的文件格式"))
			return
		}
	}

	// 获取当前用户ID
	userID := h.getCurrentUserID(c)

	// 调用服务层处理文件上传（代理上传）
	fileRecord, err := h.fileService.ProxyUploadWithPurpose(userID, file, purpose)
	if err != nil {
		log.Error("代理上传文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "文件上传失败"))
		return
	}

	// 只返回file_id和基本信息，不返回URL
	code.AutoResponse(c, gin.H{
		"file_id":   fileRecord.ID,
		"file_name": fileRecord.FileName,
		"file_size": fileRecord.FileSize,
		"purpose":   fileRecord.Purpose,
	}, nil)
}

// getCurrentUserID 获取当前用户ID（UUID字符串）
func (h *FileHandler) getCurrentUserID(c *gin.Context) string {
	// 从JWT中获取用户ID，如果没有则返回空字符串（匿名用户）
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return "" // 匿名用户
}

// GetFileList 获取文件列表
func (h *FileHandler) GetFileList(c *gin.Context) {
	// 获取分页参数
	var req dto.PageRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		log.Error("绑定分页参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}
	req.SetDefaults()

	entityType := c.Query("entity_type")

	// 获取文件列表
	files, total, err := h.fileService.GetFileList(req.Page, req.PageSize, entityType)
	if err != nil {
		log.Error("获取文件列表失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取文件列表失败"))
		return
	}

	// 构造响应
	response := dto.PageResponse{
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
		List:     files,
	}

	code.AutoResponse(c, response, nil)
}

// DeleteFile 删除文件
func (h *FileHandler) DeleteFile(c *gin.Context) {
	fileID := c.Param("id")
	if fileID == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "文件ID不能为空"))
		return
	}

	// 软删除文件
	if err := h.fileService.DeleteFile(fileID); err != nil {
		log.Error("删除文件失败", "file_id", fileID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除文件失败"))
		return
	}

	response := dto.StatusResponse{Status: "success"}
	code.AutoResponse(c, response, nil)
}

// BatchDeleteFiles 批量删除文件 - 支持查询参数和请求体两种方式
func (h *FileHandler) BatchDeleteFiles(c *gin.Context) {
	// 使用通用函数解析批量ID
	fileIDs, err := utils.ParseBatchStrings(c)
	if err != nil {
		log.Error("解析批量删除文件ID失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	if len(fileIDs) == 0 {
		log.Error("未提供要删除的文件ID")
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请提供要删除的文件ID"))
		return
	}

	log.Info("收到批量删除文件请求", "file_ids", fileIDs)

	// 批量删除文件
	var failedIDs []string
	for _, fileID := range fileIDs {
		if err := h.fileService.DeleteFile(fileID); err != nil {
			log.Error("批量删除文件失败", "file_id", fileID, "error", err)
			failedIDs = append(failedIDs, fileID)
		}
	}

	if len(failedIDs) > 0 {
		response := struct {
			Success   bool     `json:"success"`
			FailedIDs []string `json:"failed_ids"`
		}{
			Success:   len(failedIDs) < len(fileIDs),
			FailedIDs: failedIDs,
		}
		code.AutoResponse(c, response, code.NewErrCodeMsg(code.DatabaseDeleteError, "部分文件删除失败"))
		return
	}

	log.Info("批量删除文件成功", "file_ids", fileIDs, "count", len(fileIDs))
	response := dto.MessageResponse{Message: "Files deleted successfully"}
	code.AutoResponse(c, response, nil)
}

// GetFilesByEntity 获取实体关联的文件列表
func (h *FileHandler) GetFilesByEntity(c *gin.Context) {
	entityType := c.Query("entity_type")
	entityIDStr := c.Query("entity_id")

	if entityType == "" || entityIDStr == "" {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "实体类型和ID不能为空"))
		return
	}

	entityID := cast.ToUint(entityIDStr)
	if entityID == 0 {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "实体ID格式错误"))
		return
	}

	// 获取实体关联的文件列表
	files, err := h.fileService.GetFilesByEntity(entityType, uint(entityID))
	if err != nil {
		log.Error("获取实体关联的文件列表失败", "entity_type", entityType, "entity_id", entityID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "获取文件列表失败"))
		return
	}

	code.AutoResponse(c, files, nil)
}

// LegacyUpload 兼容旧系统的文件上传
func (h *FileHandler) LegacyUpload(c *gin.Context) {
	// 绑定请求参数
	var req dto.LegacyFileUploadRequest
	if err := c.ShouldBind(&req); err != nil {
		log.Error("绑定旧版文件上传请求参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证文件类型
	if !req.FileType.IsValid() {
		log.Error("无效的文件类型", "file_type", req.FileType)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidFileType, "无效的文件类型"))
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("file")
	if err != nil {
		log.Error("获取表单文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "文件上传失败"))
		return
	}

	// 获取当前用户ID
	userID := h.getCurrentUserID(c)

	// 使用文件服务上传并保存文件（统一使用legacy存储桶）
	fileRecord, err := h.fileService.UploadFile(userID, file, "legacy")
	if err != nil {
		log.Error("上传旧版文件失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.FileOperationError, "文件上传失败"))
		return
	}

	// 构造下载链接
	downloadURL := fmt.Sprintf("/api/v1/file/download/%s", fileRecord.ObjectName)

	// 构造响应
	response := dto.LegacyFileUploadResponse{
		FileID:      fileRecord.ID.String(),
		UploadURL:   "", // MinIO 内部URL，不对外暴露
		DownloadURL: downloadURL,
		ObjectName:  fileRecord.ObjectName,
		FileType:    string(req.FileType),
		LegacyPath:  strings.TrimPrefix(fileRecord.ObjectName, "legacy/"), // 返回兼容的路径
	}

	code.AutoResponse(c, response, nil)
}

// downloadByObjectName 通过 object_name 下载公共文件 - 已废弃
/*
func (h *FileHandler) downloadByObjectName(c *gin.Context, objectName, filename string) {
	// 公共文件直接重定向到MinIO URL
	directURL := h.getDirectPublicURL(objectName)

	// 如果指定了filename，添加Content-Disposition头
	if filename != "" {
		c.Header("Content-Disposition", fmt.Sprintf(`attachment; filename="%s"`, filename))
	}

	log.Info("redirecting to public file", "object_name", objectName, "url", directURL)
	c.Redirect(302, directURL)
}
*/

// downloadByFileID 通过 file_id 下载私有文件
func (h *FileHandler) downloadByFileID(c *gin.Context, fileID, filename string) {
	// 从数据库查询文件信息
	var file model.File
	if err := h.db.Where("id = ? AND status = ?", fileID, model.StatusActive).First(&file).Error; err != nil {
		log.Error("file not found", "file_id", fileID, "error", err)
		code.AutoResponse(c, nil, code.NewErrMsg("File not found"))
		return
	}

	// TODO: 这里可以添加权限检查逻辑
	// 例如：检查用户是否有权限访问该文件

	// 使用原始文件名（如果没有指定filename参数）
	if filename == "" {
		filename = file.OriginalFilename
	}

	// 私有文件生成预签名URL并重定向
	reqParams := make(url.Values)
	if filename != "" {
		disposition := fmt.Sprintf(`attachment; filename="%s"`, filename)
		reqParams.Set("response-content-disposition", disposition)
	}

	// 根据文件的Content-Type设置正确的响应Content-Type
	// 这样可以确保浏览器正确识别文件类型，即使MinIO中存储的Content-Type不正确
	if file.ContentType != "" {
		reqParams.Set("response-content-type", file.ContentType)
	}

	// 生成预签名URL（默认10分钟有效期）
	presignedURL, err := h.storage.GetPresignedDownloadURL(c.Request.Context(), file.ObjectName, 10*time.Minute, reqParams)
	if err != nil {
		log.Error("failed to get presigned download url", "file_id", fileID, "object_name", file.ObjectName, "err", err)
		code.AutoResponse(c, nil, code.NewErrMsg("File not found"))
		return
	}

	// TODO: 这里可以记录下载日志
	log.Info("redirecting to private file", "file_id", fileID, "object_name", file.ObjectName, "url", presignedURL.String())
	c.Redirect(302, presignedURL.String())
}

// DownloadFileByID 通过file_id下载文件的专用处理器
// 路由: GET /download/files/:file_id
func (h *FileHandler) DownloadFileByID(c *gin.Context) {
	fileID := c.Param("file_id")
	if fileID == "" {
		log.Error("file_id is required")
		code.AutoResponse(c, nil, code.NewErrMsg("File ID is required"))
		return
	}

	// 可选的查询参数
	filename := c.Query("filename")

	// 直接调用现有的downloadByFileID方法
	h.downloadByFileID(c, fileID, filename)
}
