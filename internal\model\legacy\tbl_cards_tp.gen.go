// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblCardsTp = "tbl_cards_tp"

// TblCardsTp mapped from table <tbl_cards_tp>
type TblCardsTp struct {
	ID             int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ShopID         int32     `gorm:"column:shop_id;not null" json:"shop_id"`
	ShopIds        string    `gorm:"column:shop_ids" json:"shop_ids"`
	Title          string    `gorm:"column:title" json:"title"`
	Suitclass      string    `gorm:"column:suitclass" json:"suitclass"`
	Tp             bool      `gorm:"column:tp;default:1;comment:1-储值卡 2-次数卡 3-期限卡" json:"tp"` // 1-储值卡 2-次数卡 3-期限卡
	Note           string    `gorm:"column:note" json:"note"`
	CreateTime     time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
	IsDelete       int32     `gorm:"column:is_delete;not null" json:"is_delete"`
	Terms          string    `gorm:"column:terms" json:"terms"`
	Picture        string    `gorm:"column:picture" json:"picture"`
	PictureOri     string    `gorm:"column:picture_ori" json:"picture_ori"`
	PreLimitNum    int32     `gorm:"column:pre_limit_num;not null" json:"pre_limit_num"`
	DayLimitNum    int32     `gorm:"column:day_limit_num;not null" json:"day_limit_num"`
	WeekLimitNum   int32     `gorm:"column:week_limit_num;not null" json:"week_limit_num"`
	MonthLimitNum  int32     `gorm:"column:month_limit_num;not null" json:"month_limit_num"`
	MemberLimitNum int32     `gorm:"column:member_limit_num;not null" json:"member_limit_num"`
	ClassLimitNum  int32     `gorm:"column:class_limit_num;not null;default:1" json:"class_limit_num"`
}

// TableName TblCardsTp's table name
func (*TblCardsTp) TableName() string {
	return TableNameTblCardsTp
}
