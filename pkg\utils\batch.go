package utils

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/spf13/cast"
)

// ParseBatchIDs 通用的批量ID解析函数
// 支持从查询参数和请求体两种方式获取ID列表
// 查询参数格式: ?ids=1,2,3,4
// 请求体格式: [1, 2, 3, 4] 或 {"ids": [1, 2, 3, 4]}
func ParseBatchIDs(c *gin.Context) ([]uint, error) {
	var ids []uint

	// 优先尝试从查询参数获取IDs (RESTful方式)
	if idsParam := c.Query("ids"); idsParam != "" {
		// 解析逗号分隔的ID字符串
		idStrings := strings.Split(idsParam, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			// 使用 cast 库进行类型转换，更安全
			if id := cast.ToUint(idStr); id > 0 {
				ids = append(ids, id)
			}
		}
	} else {
		// 如果查询参数中没有IDs，则尝试从请求体获取
		// 首先尝试直接绑定到数组 [1, 2, 3, 4]
		if err := c.ShouldBindJSON(&ids); err != nil {
			// 如果直接绑定失败，尝试绑定到对象 {"ids": [1, 2, 3, 4]}
			var req struct {
				IDs []uint `json:"ids"`
			}
			if err2 := c.ShouldBindJSON(&req); err2 != nil {
				// 两种格式都失败，返回原始错误
				return nil, err
			}
			ids = req.IDs
		}
	}

	return ids, nil
}

// ParseBatchIDsWithType 支持指定ID类型的批量解析函数
// T 可以是 uint, uint32, uint64, int, int32, int64 等
func ParseBatchIDsWithType[T any](c *gin.Context) ([]T, error) {
	var ids []T

	// 优先尝试从查询参数获取IDs (RESTful方式)
	if idsParam := c.Query("ids"); idsParam != "" {
		// 解析逗号分隔的ID字符串
		idStrings := strings.Split(idsParam, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			// 使用 cast 库进行类型转换
			var id T
			switch any(id).(type) {
			case uint:
				if val := cast.ToUint(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			case uint32:
				if val := cast.ToUint32(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			case uint64:
				if val := cast.ToUint64(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			case int:
				if val := cast.ToInt(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			case int32:
				if val := cast.ToInt32(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			case int64:
				if val := cast.ToInt64(idStr); val > 0 {
					id = any(val).(T)
					ids = append(ids, id)
				}
			}
		}
	} else {
		// 如果查询参数中没有IDs，则尝试从请求体获取 - 直接绑定到数组
		if err := c.ShouldBindJSON(&ids); err != nil {
			return nil, err
		}
	}

	return ids, nil
}

// ParseBatchStrings 解析批量字符串ID
// 支持从查询参数和请求体两种方式获取字符串ID列表
// 查询参数格式: ?ids=abc,def,ghi
// 请求体格式: ["abc", "def", "ghi"]
func ParseBatchStrings(c *gin.Context) ([]string, error) {
	var ids []string

	// 优先尝试从查询参数获取IDs (RESTful方式)
	if idsParam := c.Query("ids"); idsParam != "" {
		// 解析逗号分隔的ID字符串
		idStrings := strings.Split(idsParam, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr != "" {
				ids = append(ids, idStr)
			}
		}
	} else {
		// 如果查询参数中没有IDs，则尝试从请求体获取 - 直接绑定到数组
		if err := c.ShouldBindJSON(&ids); err != nil {
			return nil, err
		}
	}

	return ids, nil
}

// ParseBatchUUIDs 解析批量UUID
// 支持从查询参数和请求体两种方式获取UUID列表
// 查询参数格式: ?ids=550e8400-e29b-41d4-a716-************,6ba7b810-9dad-11d1-80b4-00c04fd430c8
// 请求体格式: ["550e8400-e29b-41d4-a716-************", "6ba7b810-9dad-11d1-80b4-00c04fd430c8"]
func ParseBatchUUIDs(c *gin.Context) ([]uuid.UUID, error) {
	var ids []uuid.UUID

	// 优先尝试从查询参数获取IDs (RESTful方式)
	if idsParam := c.Query("ids"); idsParam != "" {
		// 解析逗号分隔的UUID字符串
		idStrings := strings.Split(idsParam, ",")
		for _, idStr := range idStrings {
			idStr = strings.TrimSpace(idStr)
			if idStr == "" {
				continue
			}
			// 验证并解析UUID
			if parsedUUID, err := uuid.Parse(idStr); err == nil {
				ids = append(ids, parsedUUID)
			} else {
				return nil, err // 返回UUID解析错误
			}
		}
	} else {
		// 如果查询参数中没有IDs，则尝试从请求体获取
		var uuidStrings []string
		if err := c.ShouldBindJSON(&uuidStrings); err != nil {
			return nil, err
		}

		// 解析UUID字符串
		for _, uuidStr := range uuidStrings {
			if parsedUUID, err := uuid.Parse(uuidStr); err == nil {
				ids = append(ids, parsedUUID)
			} else {
				return nil, err // 返回UUID解析错误
			}
		}
	}

	return ids, nil
}
