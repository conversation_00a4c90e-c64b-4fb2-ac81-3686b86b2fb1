package enum

// ErrorCode 错误码枚举
type ErrorCode int

const (
	// 成功
	ErrorCodeSuccess ErrorCode = 0

	// 通用错误 10000+
	ErrorCodeInvalidParams     ErrorCode = 10001
	ErrorCodeUnknown           ErrorCode = 10002
	ErrorCodeNotExistCert      ErrorCode = 10003
	ErrorCodeInvalidRoleID     ErrorCode = 10004
	ErrorCodeInvalidFileType   ErrorCode = 10005
	ErrorCodeInvalidAccessType ErrorCode = 10006
	ErrorCodeResourceNotFound  ErrorCode = 10007
	ErrorCodeFileOperation     ErrorCode = 10008

	// 数据库错误 20000+
	ErrorCodeDatabaseInsert ErrorCode = 20001
	ErrorCodeDatabaseDelete ErrorCode = 20002
	ErrorCodeDatabaseQuery  ErrorCode = 20003
	ErrorCodeDatabaseUpdate ErrorCode = 20004

	// 用户相关错误 30000+
	ErrorCodeUserNoLogin             ErrorCode = 30001
	ErrorCodeUserNotFound            ErrorCode = 30002
	ErrorCodeUserPasswordError       ErrorCode = 30003
	ErrorCodeUserNotVerify           ErrorCode = 30004
	ErrorCodeUserLocked              ErrorCode = 30005
	ErrorCodeUserDisabled            ErrorCode = 30006
	ErrorCodeUserExpired             ErrorCode = 30007
	ErrorCodeUserAlreadyExists       ErrorCode = 30008
	ErrorCodeUserNameOrPasswordError ErrorCode = 30009
	ErrorCodeUserAuthFailed          ErrorCode = 30010
	ErrorCodeUserNoPermission        ErrorCode = 30011
)

// String 返回错误码的字符串表示
func (e ErrorCode) String() string {
	switch e {
	case ErrorCodeSuccess:
		return "success"
	case ErrorCodeInvalidParams:
		return "invalid_params"
	case ErrorCodeUnknown:
		return "unknown_error"
	case ErrorCodeNotExistCert:
		return "cert_not_exist"
	case ErrorCodeInvalidRoleID:
		return "invalid_role_id"
	case ErrorCodeInvalidFileType:
		return "invalid_file_type"
	case ErrorCodeInvalidAccessType:
		return "invalid_access_type"
	case ErrorCodeResourceNotFound:
		return "resource_not_found"
	case ErrorCodeFileOperation:
		return "file_operation_error"
	case ErrorCodeDatabaseInsert:
		return "database_insert_error"
	case ErrorCodeDatabaseDelete:
		return "database_delete_error"
	case ErrorCodeDatabaseQuery:
		return "database_query_error"
	case ErrorCodeDatabaseUpdate:
		return "database_update_error"
	case ErrorCodeUserNoLogin:
		return "user_no_login"
	case ErrorCodeUserNotFound:
		return "user_not_found"
	case ErrorCodeUserPasswordError:
		return "user_password_error"
	case ErrorCodeUserNotVerify:
		return "user_not_verify"
	case ErrorCodeUserLocked:
		return "user_locked"
	case ErrorCodeUserDisabled:
		return "user_disabled"
	case ErrorCodeUserExpired:
		return "user_expired"
	case ErrorCodeUserAlreadyExists:
		return "user_already_exists"
	case ErrorCodeUserNameOrPasswordError:
		return "username_or_password_error"
	case ErrorCodeUserAuthFailed:
		return "user_auth_failed"
	case ErrorCodeUserNoPermission:
		return "user_no_permission"
	default:
		return "unknown"
	}
}

// IsValid 验证错误码是否有效
func (e ErrorCode) IsValid() bool {
	return e >= ErrorCodeSuccess && e <= ErrorCodeUserNoPermission
}

// FileAccessType 文件访问类型枚举
type FileAccessType string

const (
	FileAccessTypePublic  FileAccessType = "public"  // 公开访问
	FileAccessTypePrivate FileAccessType = "private" // 私有访问
)

// String 返回文件访问类型的字符串表示
func (t FileAccessType) String() string {
	return string(t)
}

// IsValid 验证文件访问类型是否有效
func (t FileAccessType) IsValid() bool {
	switch t {
	case FileAccessTypePublic, FileAccessTypePrivate:
		return true
	default:
		return false
	}
}

// FileType 文件类型枚举
type FileType string

const (
	// 基础文件类型
	FileTypeAvatar   FileType = "avatar"   // 头像
	FileTypeBanner   FileType = "banner"   // 横幅
	FileTypeHeader   FileType = "header"   // 头图
	FileTypeDocument FileType = "document" // 文档
	FileTypeImage    FileType = "image"    // 图片
	FileTypeVideo    FileType = "video"    // 视频
	FileTypeAudio    FileType = "audio"    // 音频
	FileTypeOther    FileType = "other"    // 其他

	// 兼容旧系统的文件类型
	FileTypeCardsTP          FileType = "cards_tp"          // 卡种图片
	FileTypeClassesTimetable FileType = "classes_timetable" // 课程时间表
	FileTypeClassesTP        FileType = "classes_tp"        // 课程类型图片
	FileTypeClassesTPRotate  FileType = "classes_tp_rotate" // 课程类型轮播图
	FileTypeCoachRotate      FileType = "coach_rotate"      // 教练轮播图
	FileTypeCoachVideo       FileType = "coach_video"       // 教练视频
	FileTypeCoaches          FileType = "coaches"           // 教练图片
	FileTypeHeadimg          FileType = "headimg"           // 头像图片
	FileTypeMembers          FileType = "members"           // 会员图片
	FileTypeRotate           FileType = "rotate"            // 轮播图
	FileTypeShopRotate       FileType = "shop_rotate"       // 门店轮播图
	FileTypeShops            FileType = "shops"             // 门店图片
)

// String 返回文件类型的字符串表示
func (t FileType) String() string {
	return string(t)
}

// IsValid 验证文件类型是否有效
func (t FileType) IsValid() bool {
	switch t {
	case FileTypeAvatar, FileTypeBanner, FileTypeHeader, FileTypeDocument,
		FileTypeImage, FileTypeVideo, FileTypeAudio, FileTypeOther,
		FileTypeCardsTP, FileTypeClassesTimetable, FileTypeClassesTP, FileTypeClassesTPRotate,
		FileTypeCoachRotate, FileTypeCoachVideo, FileTypeCoaches, FileTypeHeadimg,
		FileTypeMembers, FileTypeRotate, FileTypeShopRotate, FileTypeShops:
		return true
	default:
		return false
	}
}
