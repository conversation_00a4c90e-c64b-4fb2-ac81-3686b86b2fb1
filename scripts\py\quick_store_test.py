#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速门店更新测试脚本
用于测试现有门店的 image_ids 字段更新功能
"""

import requests
import json
import uuid

def test_store_update():
    """快速测试门店更新功能"""
    base_url = "http://localhost:9095"

    # 1. 检查服务器连接
    print(f"🔗 检查服务器连接: {base_url}")
    try:
        health_response = requests.get(f"{base_url}/health", timeout=5)
        print("✅ 服务器连接正常")
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("请确保：")
        print("1. Go 服务器正在运行")
        print("2. 服务器监听在 localhost:9095 端口")
        print("3. 运行命令：go run main.go")
        return False
    except Exception as e:
        print(f"⚠️ 服务器连接检查异常: {e}")

    # 2. 登录
    print("🔐 正在登录...")
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json={
            "username": "admin",
            "password": "admin123"
        }, timeout=10)
    except requests.exceptions.ConnectionError:
        print("❌ 登录请求失败：无法连接到服务器")
        return False

    if login_response.status_code != 200:
        print(f"❌ 登录失败: {login_response.text}")
        return False

    login_data = login_response.json()
    if login_data.get("code") != 200:
        print(f"❌ 登录失败: {login_data}")
        return False

    access_token = login_data["data"]["access_token"]
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    print("✅ 登录成功")

    # 2. 获取门店列表
    print("📋 获取门店列表...")
    stores_response = requests.get(f"{base_url}/api/v1/admin/stores", headers=headers)

    if stores_response.status_code != 200:
        print(f"❌ 获取门店列表失败: {stores_response.text}")
        return False

    stores_data = stores_response.json()
    if stores_data.get("code") != 200:
        print(f"❌ 获取门店列表失败: {stores_data}")
        return False

    stores = stores_data["data"].get("list", [])
    if not stores:
        print("⚠️ 没有找到门店，创建一个测试门店...")

        # 创建测试门店
        test_image_ids = [str(uuid.uuid4()), str(uuid.uuid4())]
        create_data = {
            "name": "测试门店",
            "address": "测试地址",
            "phone": "13800138000",
            "latitude": 39.9042,
            "longitude": 116.4074,
            "image_ids": test_image_ids,
            "description": "测试门店",
            "sort_order": 1
        }

        create_response = requests.post(f"{base_url}/api/v1/admin/stores",
                                      json=create_data, headers=headers)

        if create_response.status_code != 200:
            print(f"❌ 创建测试门店失败: {create_response.text}")
            return False

        create_result = create_response.json()
        if create_result.get("code") != 200:
            print(f"❌ 创建测试门店失败: {create_result}")
            return False

        store_id = create_result["data"]["id"]
        print(f"✅ 创建测试门店成功，ID: {store_id}")
    else:
        store_id = stores[0]["id"]
        print(f"✅ 使用现有门店，ID: {store_id}")

    # 3. 测试更新门店（重点测试 image_ids 字段）
    print(f"🔄 测试更新门店 ID: {store_id}...")

    # 生成新的测试图片ID
    new_image_ids = [str(uuid.uuid4()), str(uuid.uuid4()), str(uuid.uuid4())]

    update_data = {
        "name": "更新测试门店",
        "address": "更新后地址",
        "phone": "13900139000",
        "latitude": 40.0042,
        "longitude": 116.5074,
        "image_ids": new_image_ids,  # 关键测试字段
        "description": "更新后描述",
        "sort_order": 999,
        "status": 1
    }

    print(f"   测试 image_ids: {new_image_ids}")

    update_response = requests.put(f"{base_url}/api/v1/admin/stores/{store_id}",
                                 json=update_data, headers=headers)

    print(f"   响应状态码: {update_response.status_code}")

    if update_response.status_code != 200:
        print(f"❌ 更新门店失败: {update_response.text}")
        return False

    update_result = update_response.json()
    print(f"   响应内容: {json.dumps(update_result, ensure_ascii=False, indent=2)}")

    if update_result.get("code") != 200:
        print(f"❌ 更新门店失败: {update_result}")
        return False

    print("✅ 门店更新成功")

    # 4. 验证更新结果
    print("🔍 验证更新结果...")
    detail_response = requests.get(f"{base_url}/api/v1/admin/stores/{store_id}", headers=headers)

    if detail_response.status_code == 200:
        detail_result = detail_response.json()
        if detail_result.get("code") == 200:
            updated_store = detail_result["data"]
            actual_image_ids = updated_store.get("image_ids", [])

            print(f"   期望 image_ids: {new_image_ids}")
            print(f"   实际 image_ids: {actual_image_ids}")

            if actual_image_ids == new_image_ids:
                print("✅ image_ids 字段更新验证成功")
                return True
            else:
                print("❌ image_ids 字段更新验证失败")
                return False

    print("⚠️ 无法验证更新结果")
    return True

if __name__ == "__main__":
    print("🧪 快速门店更新测试")
    print("=" * 40)

    try:
        success = test_store_update()
        print()
        print("=" * 40)
        if success:
            print("🎉 测试通过！")
        else:
            print("❌ 测试失败！")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
