#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试延期和升级功能的交易记录
"""

import requests
from datetime import datetime

class ExtendUpgradeTester:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None

    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")

        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}

        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False

    def get_card_types(self) -> list:
        """获取所有卡种"""
        try:
            params = {"page": 1, "page_size": 50}
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-types", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {}).get('list', [])
            return []
        except Exception as e:
            print(f"❌ 获取卡种异常: {e}")
            return []

    def get_existing_user(self) -> str:
        """获取一个存在的用户ID"""
        try:
            params = {"page": 1, "page_size": 1}
            response = self.session.get(f"{self.base_url}/api/v1/admin/users", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    users = result.get('data', {}).get('list', [])
                    if users:
                        return users[0]['id']
            return None
        except Exception as e:
            print(f"❌ 获取用户异常: {e}")
            return None

    def create_amount_card(self) -> int:
        """创建一张金额卡用于测试延期"""
        print("\n💳 创建金额卡用于测试延期...")

        # 获取用户
        user_id = self.get_existing_user()
        if not user_id:
            print("❌ 没有找到可用的用户")
            return None

        # 获取卡种
        card_types = self.get_card_types()
        print(f"可用卡种: {len(card_types)} 个")
        for ct in card_types:
            print(f"  - {ct.get('name')} (类别: {ct.get('category')})")

        amount_card_type = None

        for card_type in card_types:
            if card_type.get('category') in ['amount', 'period']:  # 金额卡或期限卡都支持延期
                amount_card_type = card_type
                break

        if not amount_card_type:
            print("❌ 没有找到支持延期的卡种类型")
            return None

        # 创建金额卡
        card_data = {
            "user_id": user_id,
            "card_type_id": amount_card_type['id'],
            "purchase_price": 100000,  # 1000元
            "payment_method": "cash",
            "start_date": datetime.now().isoformat() + "Z",
            "discount": 1.0,
            "available_stores": [],
            "daily_booking_limit": 0,
            "remark": "延期测试金额卡"
        }

        try:
            response = self.session.post(f"{self.base_url}/api/v1/admin/membership-cards", json=card_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    card_info = result.get('data', {})
                    card_id = card_info.get('id')
                    print(f"✅ 金额卡创建成功，ID: {card_id}")
                    return card_id
                else:
                    print(f"❌ 创建金额卡失败: {result.get('msg')}")
                    return None
            else:
                print(f"❌ 创建金额卡失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 创建金额卡异常: {e}")
            return None

    def test_extend_transaction(self, card_id: int) -> bool:
        """测试延期交易记录"""
        print(f"\n🧪 测试延期交易记录 (卡片ID: {card_id})...")

        extend_data = {
            "extend_days": 30,
            "reason": "测试延期功能"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/extend", json=extend_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 延期成功")
                    return True
                else:
                    print(f"❌ 延期失败: {result.get('msg')}")
                    return False
            else:
                try:
                    result = response.json()
                    print(f"❌ 延期失败: {result.get('msg', response.status_code)}")
                except:
                    print(f"❌ 延期失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 延期异常: {e}")
            return False

    def test_upgrade_transaction(self, card_id: int) -> bool:
        """测试升级交易记录"""
        print(f"\n🧪 测试升级交易记录 (卡片ID: {card_id})...")

        # 获取当前卡片信息
        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}")
            if response.status_code != 200:
                print("❌ 获取卡片信息失败")
                return False

            result = response.json()
            if result.get('code') != 0:
                print("❌ 获取卡片信息失败")
                return False

            card_info = result.get('data', {})
            current_card_type_id = card_info.get('card_type_id')
            current_category = card_info.get('card_type', {}).get('category')

            print(f"当前卡种ID: {current_card_type_id}, 类别: {current_category}")

        except Exception as e:
            print(f"❌ 获取卡片信息异常: {e}")
            return False

        # 查找同类型的其他卡种
        card_types = self.get_card_types()
        target_card_type = None

        print(f"查找同类型卡种 (当前类型: {current_category}):")
        same_category_types = []
        for card_type in card_types:
            if card_type.get('category') == current_category:
                same_category_types.append(card_type)
                print(f"  - {card_type.get('name')} (ID: {card_type.get('id')})")

        for card_type in same_category_types:
            if card_type.get('id') != current_card_type_id:
                target_card_type = card_type
                break

        if not target_card_type:
            print(f"⚠️ 没有找到同类型的其他卡种用于升级测试")
            print(f"   当前类型: {current_category}, 同类型卡种数: {len(same_category_types)}")
            return True  # 不算失败，只是没有可升级的目标

        print(f"目标卡种: {target_card_type['name']} (ID: {target_card_type['id']})")

        # 执行升级
        upgrade_data = {
            "target_card_type_id": target_card_type['id'],
            "remark": "测试升级功能"
        }

        try:
            response = self.session.put(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/upgrade", json=upgrade_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    print("✅ 升级成功")
                    return True
                else:
                    print(f"❌ 升级失败: {result.get('msg')}")
                    return False
            else:
                try:
                    result = response.json()
                    print(f"❌ 升级失败: {result.get('msg', response.status_code)}")
                except:
                    print(f"❌ 升级失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 升级异常: {e}")
            return False

    def check_transactions(self, card_id: int):
        """检查交易记录"""
        print(f"\n📋 检查卡片 {card_id} 的交易记录...")

        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    transactions = result.get('data', {}).get('list', [])
                    print(f"✅ 找到 {len(transactions)} 条交易记录")

                    # 统计交易类型
                    transaction_types = {}
                    for transaction in transactions:
                        trans_type = transaction.get('transaction_type')
                        transaction_types[trans_type] = transaction_types.get(trans_type, 0) + 1

                    print("\n📊 交易类型统计:")
                    for trans_type, count in transaction_types.items():
                        print(f"  {trans_type}: {count} 条")

                    # 显示交易记录
                    print("\n📝 交易记录详情:")
                    for transaction in transactions:
                        print(f"  - {transaction.get('transaction_type')}: {transaction.get('reason')} ({transaction.get('created_at')})")
                        if transaction.get('remarks'):
                            print(f"    备注: {transaction.get('remarks')}")

                    return True
                else:
                    print(f"❌ 获取交易记录失败: {result.get('msg')}")
                    return False
            else:
                print(f"❌ 获取交易记录失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 获取交易记录异常: {e}")
            return False

    def run_test(self):
        """运行完整测试"""
        print("🚀 开始测试延期和升级功能的交易记录...")
        print("=" * 80)

        # 1. 登录
        if not self.login():
            return False

        # 2. 创建金额卡用于测试延期
        card_id = self.create_amount_card()
        if not card_id:
            return False

        # 3. 测试延期功能
        extend_success = self.test_extend_transaction(card_id)

        # 4. 测试升级功能
        upgrade_success = self.test_upgrade_transaction(card_id)

        # 5. 检查交易记录
        self.check_transactions(card_id)

        print("\n" + "=" * 80)
        print("📊 测试结果:")
        print(f"  延期功能: {'✅ 成功' if extend_success else '❌ 失败'}")
        print(f"  升级功能: {'✅ 成功' if upgrade_success else '❌ 失败'}")

        return extend_success and upgrade_success

def main():
    """主函数"""
    tester = ExtendUpgradeTester()
    success = tester.run_test()

    if success:
        print("\n🏆 延期和升级功能测试成功！")
        exit(0)
    else:
        print("\n💥 延期和升级功能测试失败！")
        exit(1)

if __name__ == "__main__":
    main()
