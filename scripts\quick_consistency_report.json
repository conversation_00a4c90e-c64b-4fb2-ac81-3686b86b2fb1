{"total_routes": 115, "total_apis": 146, "matched_count": 35, "consistency_rate": 30.***************, "missing_in_api": ["DELETE:/api/v1/admin/files/:id", "DELETE:/api/v1/admin/banners/:id", "DELETE:/api/v1/admin/course-reviews/:id", "PUT:/api/v1/admin/flexible/deduction-rules/:id/toggle", "PUT:/api/v1/admin/membership-cards/:id/recharge", "GET:/api/v1/admin/flexible/card-types/:card_type_id/supported-courses", "GET:/api/v1/admin/queue/courses/:course_id/status", "GET:/api/v1/admin/coach-banners/coach/:coach_id", "DELETE:/api/v1/admin/flexible/course-types/:id", "GET:/api/v1/admin/user-store-assignments/stores/:store_id", "PUT:/api/v1/admin/menus/:id", "DELETE:/api/v1/admin/coach-banners/:id", "DELETE:/api/v1/admin/course-categories/:id", "PUT:/api/v1/admin/membership-cards/:id/extend", "GET:/api/v1/admin/coach-banners/:id", "POST:/api/v1/admin/files/presigned-upload-url", "DELETE:/api/v1/admin/users/:id", "GET:/api/v1/admin/membership-cards/:id/sub-cards", "PUT:/api/v1/admin/coach-banners/:id", "PUT:/api/v1/admin/roles/:id", "DELETE:/api/v1/admin/flexible/deduction-rules/:id", "GET:/api/v1/admin/membership-cards/:id/transactions", "GET:/api/v1/admin/user-store-assignments/users/:user_id", "PUT:/api/v1/admin/staff/:id/status", "DELETE:/api/v1/admin/queue/:queue_id", "DELETE:/api/v1/admin/stores/:id", "PUT:/api/v1/admin/roles/:id/permissions", "PUT:/api/v1/admin/membership-cards/:id/upgrade", "DELETE:/api/v1/admin/notifications/:id", "PUT:/api/v1/admin/notifications/:id/read", "GET:/api/v1/admin/resource-assignments/sales/:sales_id/members", "PUT:/api/v1/admin/membership-cards/:id/leave", "DELETE:/api/v1/admin/logs/:id", "PUT:/api/v1/admin/coach-status/:id/status", "DELETE:/api/v1/admin/courses/:id", "PUT:/api/v1/admin/schedules/:id", "GET:/api/v1/admin/permissions/:id", "PUT:/api/v1/admin/banners/:id", "GET:/api/v1/admin/menus/:id", "PUT:/api/v1/admin/flexible/course-types/:id", "POST:/api/v1/admin/queue/courses/:course_id/notify", "GET:/api/v1/admin/roles/:id", "GET:/api/v1/admin/stores/:id", "PUT:/api/v1/admin/permissions/:id", "PUT:/api/v1/admin/membership-cards/:id/transfer", "GET:/api/v1/admin/membership-cards/:id", "GET:/api/v1/admin/courses/:id/supported-card-types", "GET:/api/v1/admin/coaches/:id", "DELETE:/api/v1/admin/classrooms/:id", "GET:/api/v1/admin/logs/:id", "DELETE:/api/v1/admin/permissions/:id", "PUT:/api/v1/admin/coaches/:id", "PUT:/api/v1/admin/courses/:id", "PUT:/api/v1/admin/user-store-assignments/users/:user_id/platform-access", "PUT:/api/v1/admin/membership-types/:id", "PUT:/api/v1/admin/stores/:id", "GET:/api/v1/admin/bookings/:id", "GET:/api/v1/admin/membership-types/:id", "PUT:/api/v1/admin/membership-cards/:id/deduct", "GET:/api/v1/admin/banners/:id", "GET:/api/v1/admin/courses/:id", "PUT:/api/v1/admin/flexible/deduction-rules/:id", "PUT:/api/v1/admin/bookings/:id/status", "PUT:/api/v1/admin/classrooms/:id", "PUT:/api/v1/admin/course-categories/:id", "POST:/api/v1/admin/membership-cards/:id/sub-cards", "DELETE:/api/v1/admin/roles/:id", "GET:/api/v1/admin/course-categories/:id", "GET:/api/v1/admin/roles/:id/permissions", "PUT:/api/v1/admin/membership-cards/:id/freeze", "GET:/api/v1/admin/files/presigned-download-url", "GET:/api/v1/admin/flexible/deduction-rules/:id", "PUT:/api/v1/admin/coaches/:id/status", "DELETE:/api/v1/admin/membership-types/:id", "GET:/api/v1/admin/stores/:id/classrooms", "GET:/api/v1/admin/classrooms/:id", "DELETE:/api/v1/admin/menus/:id", "POST:/api/v1/admin/files/proxy-upload", "GET:/api/v1/admin/course-reviews/:id", "PUT:/api/v1/admin/users/:id/roles"], "missing_in_router": ["GET:/api/v1/admin/permissions/1", "DELETE:/api/v1/admin/course-reviews/1", "GET:/api/v1/admin/classrooms", "DELETE:/api/v1/admin/files/1", "GET:/api/v1/admin/membership-types/1", "GET:/api/v1/admin/roles/1/permissions", "POST:/api/v1/admin/classrooms", "GET:/api/v1/admin/stores/1/classrooms", "GET:/api/v1/admin/roles", "PUT:/api/v1/admin/notifications/1/read", "DELETE:/api/v1/admin/banners/1", "GET:/api/v1/admin/user-store-assignments/stores/1", "PUT:/api/v1/admin/course-categories/1", "PUT:/api/v1/admin/permissions/1", "POST:/api/v1/admin/membership-cards/1/sub-cards", "GET:/api/v1/admin/stores/1", "GET:/api/v1/admin/banners", "DELETE:/api/v1/admin/files", "PUT:/api/v1/admin/users/1/roles", "PUT:/api/v1/admin/membership-cards/1/extend", "POST:/api/v1/admin/schedules", "GET:/api/v1/admin/course-reviews", "GET:/api/v1/admin/user-store-assignments/users/1", "GET:/api/v1/admin/menus/1", "PUT:/api/v1/admin/flexible/deduction-rules/1/toggle", "GET:/api/v1/admin/course-categories", "PUT:/api/v1/admin/menus/1", "DELETE:/api/v1/admin/roles", "GET:/api/v1/admin/coach-banners/1", "GET:/api/v1/admin/membership-cards/1/sub-cards", "PUT:/api/v1/admin/user-store-assignments/users/1/platform-access", "GET:/api/v1/admin/courses/1/supported-card-types", "PUT:/api/v1/admin/schedules/1", "GET:/api/v1/admin/permissions", "PUT:/api/v1/admin/membership-cards/1/deduct", "GET:/api/v1/admin/logs/1", "PUT:/api/v1/admin/courses/1", "PUT:/api/v1/admin/membership-cards/1/transfer", "GET:/api/v1/admin/banners/1", "POST:/api/v1/admin/membership-cards", "DELETE:/api/v1/admin/coach-banners/1", "GET:/api/v1/admin/membership-cards/1/transactions", "GET:/api/v1/admin/menus", "POST:/api/v1/admin/coach-banners", "GET:/api/v1/admin/schedules", "GET:/api/v1/admin/stores", "DELETE:/api/v1/admin/users/1", "POST:/api/v1/admin/roles", "GET:/api/v1/admin/bookings", "DELETE:/api/v1/admin/notifications/1", "PUT:/api/v1/admin/membership-cards/1/leave", "DELETE:/api/v1/admin/permissions/1", "GET:/api/v1/admin/membership-types", "GET:/api/v1/admin/courses", "DELETE:/api/v1/admin/stores/1", "GET:/api/v1/admin/staff", "PUT:/api/v1/admin/coach-banners/1", "GET:/api/v1/admin/course-reviews/1", "GET:/api/v1/admin/coach-banners/coach/1", "PUT:/api/v1/admin/membership-types/1", "GET:/api/v1/admin/roles/1", "POST:/api/v1/admin/courses", "POST:/api/v1/admin/permissions", "GET:/api/v1/admin/flexible/card-types/1/supported-courses", "POST:/api/v1/admin/stores", "PUT:/api/v1/admin/membership-cards/1/freeze", "GET:/api/v1/admin/course-categories/1", "DELETE:/api/v1/admin/course-categories/1", "GET:/api/v1/admin/bookings/1", "PUT:/api/v1/admin/coaches/1/status", "GET:/api/v1/admin/notifications", "POST:/api/v1/admin/courses/:id/book", "PUT:/api/v1/admin/roles/1", "POST:/api/v1/admin/courses/check-conflict", "GET:/api/v1/admin/membership-cards/1", "DELETE:/api/v1/admin/classrooms/1", "GET:/api/v1/admin/courses/1", "GET:/api/v1/admin/membership-cards", "POST:/api/v1/admin/users", "DELETE:/api/v1/admin/permissions", "PUT:/api/v1/admin/stores/1", "PUT:/api/v1/admin/banners/1", "PUT:/api/v1/admin/staff/1/status", "PUT:/api/v1/admin/classrooms/1", "GET:/api/v1/admin/classrooms/1", "DELETE:/api/v1/admin/user-store-assignments", "DELETE:/api/v1/admin/membership-types/1", "GET:/api/v1/admin/users", "PUT:/api/v1/admin/bookings/1/status", "DELETE:/api/v1/admin/logs/1", "DELETE:/api/v1/admin/menus/1", "POST:/api/v1/admin/queue/courses/1/notify", "PUT:/api/v1/admin/coach-status/1/status", "DELETE:/api/v1/admin/menus", "POST:/api/v1/admin/menus", "GET:/api/v1/admin/coaches", "POST:/api/v1/admin/course-categories", "PUT:/api/v1/admin/coaches/1", "GET:/api/v1/admin/coach-status", "POST:/api/v1/admin/banners", "GET:/api/v1/admin/resource-assignments/sales/1/members", "DELETE:/api/v1/admin/roles/1", "PUT:/api/v1/admin/roles/1/permissions", "DELETE:/api/v1/admin/courses/1", "POST:/api/v1/admin/membership-types", "POST:/api/v1/admin/user-store-assignments", "PUT:/api/v1/admin/membership-cards/1/recharge", "GET:/api/v1/admin/coaches/1", "PUT:/api/v1/admin/membership-cards/1/upgrade", "GET:/api/v1/admin/files", "GET:/api/v1/admin/logs"], "matched": ["POST:/api/v1/admin/resource-assignments/members", "POST:/api/v1/admin/courses/batch", "GET:/api/v1/admin/resource-assignments/members/unassigned", "PUT:/api/v1/admin/notifications/read-all", "GET:/api/v1/admin/user-store-assignments/users", "PUT:/api/v1/admin/resource-assignments/members/transfer", "GET:/api/v1/admin/users/menus", "GET:/api/v1/admin/staff/sales", "POST:/api/v1/admin/flexible/course-types", "POST:/api/v1/admin/files/upload", "GET:/api/v1/admin/flexible/deduction-rules", "GET:/api/v1/admin/files/by-entity", "DELETE:/api/v1/admin/resource-assignments/members", "GET:/api/v1/admin/staff/active", "GET:/api/v1/admin/notifications/unread-count", "GET:/api/v1/admin/notifications/stats", "GET:/api/v1/admin/course-categories/all", "GET:/api/v1/admin/logs/modules", "GET:/api/v1/admin/bookings/stats", "POST:/api/v1/admin/batch-courses/create", "GET:/api/v1/admin/dashboard", "GET:/api/v1/admin/reports/coaches", "GET:/api/v1/admin/flexible/course-types", "GET:/api/v1/admin/reports/card-sales", "POST:/api/v1/admin/courses/schedule", "POST:/api/v1/admin/bookings/auto-cancel", "GET:/api/v1/admin/reports/bookings", "GET:/api/v1/admin/logs/stats", "POST:/api/v1/admin/logs/clean", "GET:/api/v1/admin/logs/actions", "GET:/api/v1/admin/course-reviews/stats", "POST:/api/v1/admin/batch-courses/preview", "GET:/api/v1/admin/reports/courses", "POST:/api/v1/admin/flexible/deduction-rules", "POST:/api/v1/admin/flexible/test-deduction"]}