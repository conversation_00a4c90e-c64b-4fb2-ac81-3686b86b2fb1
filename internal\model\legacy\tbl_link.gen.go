// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

const TableNameTblLink = "tbl_link"

// TblLink mapped from table <tbl_link>
type TblLink struct {
	ID     int32  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Title  string `gorm:"column:title" json:"title"`
	IsPara bool   `gorm:"column:is_para;not null" json:"is_para"`
	URL    string `gorm:"column:url" json:"url"`
}

// TableName TblLink's table name
func (*TblLink) TableName() string {
	return TableNameTblLink
}
