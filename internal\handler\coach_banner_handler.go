package handler

import (
	"yogaga/internal/dto"
	"yogaga/internal/enum"
	"yogaga/internal/model"
	"yogaga/pkg/code"
	"yogaga/pkg/storage"

	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

// CoachBannerHandler 教练轮播图处理器
type CoachBannerHandler struct {
	db           *gorm.DB
	urlConverter URLConverter
}

// NewCoachBannerHandler 创建教练轮播图处理器实例
func NewCoachBannerHandler(db *gorm.DB, storage storage.Storage) *CoachBannerHandler {
	return &CoachBannerHandler{
		db:           db,
		urlConverter: NewURLConverter(db, storage),
	}
}

// GetCoachBanners 获取教练轮播图列表
func (h *CoachBannerHandler) GetCoachBanners(c *gin.Context) {
	coachIDStr := c.<PERSON>("coach_id")
	coachID := cast.ToUint(coachIDStr)
	if coachID == 0 {
		log.Error("教练ID格式错误", "coach_id", coachIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "教练ID格式错误"))
		return
	}

	var banners []model.CoachBanner
	if err := h.db.Preload("Coach").
		Where("coach_id = ?", coachID).
		Order("sort_order ASC, created_at DESC").
		Find(&banners).Error; err != nil {
		log.Error("查询教练轮播图失败", "coach_id", coachID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		return
	}

	// 转换为DTO格式，包含图片URL
	var bannerList []dto.CoachBannerResponse
	for _, banner := range banners {
		bannerDTO := dto.CoachBannerResponse{
			ID:        banner.ID,
			CoachID:   banner.CoachID, // CoachID是string类型，不需要.String()
			Title:     banner.Title,
			Image:     h.urlConverter.ConvertFileIDToURL(banner.FileID),
			SortOrder: banner.SortOrder,
			Status:    int(banner.Status),
			CreatedAt: banner.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: banner.UpdatedAt.Format("2006-01-02 15:04:05"),
		}
		bannerList = append(bannerList, bannerDTO)
	}

	code.AutoResponse(c, bannerList, nil)
}

// GetCoachBanner 获取教练轮播图详情
func (h *CoachBannerHandler) GetCoachBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var banner model.CoachBanner
	if err := h.db.Preload("Coach").First(&banner, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "轮播图不存在"))
		} else {
			log.Error("查询教练轮播图失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		}
		return
	}

	code.AutoResponse(c, banner, nil)
}

// CreateCoachBanner 创建教练轮播图
func (h *CoachBannerHandler) CreateCoachBanner(c *gin.Context) {
	var req dto.CreateCoachBannerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定创建教练轮播图参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 验证教练是否存在
	var coach model.User
	if err := h.db.Where("id = ? AND is_coach = ?", req.CoachID, true).First(&coach).Error; err != nil {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "教练不存在"))
		return
	}

	// 创建教练轮播图
	banner := model.CoachBanner{
		CoachID:   req.CoachID,
		FileID:    req.FileID, // 使用文件ID
		Title:     req.Title,
		SortOrder: req.SortOrder,
		Status:    enum.BannerStatusEnabled, // 默认启用
	}

	if err := h.db.Create(&banner).Error; err != nil {
		log.Error("创建教练轮播图失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseInsertError, "创建轮播图失败"))
		return
	}

	// 重新查询完整信息
	h.db.Preload("Coach").First(&banner, banner.ID)

	log.Info("创建教练轮播图成功", "id", banner.ID, "coach_id", banner.CoachID)
	code.AutoResponse(c, banner, nil)
}

// UpdateCoachBanner 更新教练轮播图
func (h *CoachBannerHandler) UpdateCoachBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	var req dto.UpdateCoachBannerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error("绑定更新教练轮播图参数失败", "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
		return
	}

	// 查询轮播图是否存在
	var banner model.CoachBanner
	if err := h.db.First(&banner, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ResourceNotFound, "轮播图不存在"))
		} else {
			log.Error("查询教练轮播图失败", "id", id, "error", err)
			code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		}
		return
	}

	// 更新字段
	updates := dto.NewUpdateFields().
		SetIfNotEmpty("file_id", req.FileID).
		SetIfNotEmpty("title", req.Title).
		SetIfNotNil("sort_order", req.SortOrder).
		SetIfNotNil("status", req.Status)

	if !updates.HasFields() {
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "没有需要更新的字段"))
		return
	}

	if err := h.db.Model(&banner).Updates(updates.GetFields()).Error; err != nil {
		log.Error("更新教练轮播图失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseUpdateError, "更新轮播图失败"))
		return
	}

	// 重新查询更新后的数据
	h.db.Preload("Coach").First(&banner, id)

	log.Info("更新教练轮播图成功", "id", id)
	code.AutoResponse(c, banner, nil)
}

// DeleteCoachBanner 删除教练轮播图
func (h *CoachBannerHandler) DeleteCoachBanner(c *gin.Context) {
	idStr := c.Param("id")
	id := cast.ToUint(idStr)
	if id == 0 {
		log.Error("轮播图ID格式错误", "id", idStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "ID格式错误"))
		return
	}

	// 删除教练轮播图
	if err := h.db.Delete(&model.CoachBanner{}, id).Error; err != nil {
		log.Error("删除教练轮播图失败", "id", id, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseDeleteError, "删除轮播图失败"))
		return
	}

	log.Info("删除教练轮播图成功", "id", id)
	response := dto.MessageResponse{Message: "删除成功"}
	code.AutoResponse(c, response, nil)
}

// GetActiveCoachBanners 获取教练的启用轮播图（前端使用）
func (h *CoachBannerHandler) GetActiveCoachBanners(c *gin.Context) {
	coachIDStr := c.Param("id")
	coachID := cast.ToUint(coachIDStr)
	if coachID == 0 {
		log.Error("教练ID格式错误", "coach_id", coachIDStr)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "教练ID格式错误"))
		return
	}

	var banners []model.CoachBanner
	if err := h.db.Where("coach_id = ? AND status = ?", coachID, enum.BannerStatusEnabled).
		Order("sort_order ASC, created_at DESC").
		Find(&banners).Error; err != nil {
		log.Error("查询教练启用轮播图失败", "coach_id", coachID, "error", err)
		code.AutoResponse(c, nil, code.NewErrCodeMsg(code.DatabaseQueryError, "查询轮播图失败"))
		return
	}

	// 转换为DTO格式，包含图片URL
	var bannerList []dto.ActiveCoachBannerResponse
	for _, banner := range banners {
		bannerDTO := dto.ActiveCoachBannerResponse{
			ID:        banner.ID,
			Title:     banner.Title,
			Image:     h.urlConverter.ConvertFileIDToURL(banner.FileID),
			SortOrder: banner.SortOrder,
		}
		bannerList = append(bannerList, bannerDTO)
	}

	code.AutoResponse(c, bannerList, nil)
}
