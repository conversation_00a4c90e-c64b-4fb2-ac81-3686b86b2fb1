#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
权限系统测试脚本
测试权限配置是否正常工作
"""

import requests
import json

def login():
    """登录获取token"""
    url = "http://localhost:9095/api/v1/public/admin/login"
    data = {"username": "admin", "password": "admin123"}

    response = requests.post(url, json=data, timeout=30)
    if response.status_code == 200:
        result = response.json()
        if result.get('code') == 0:
            return result['data']['access_token']
    return None

def test_menu_permissions():
    """测试菜单权限，特别是menu:get权限"""
    token = login()
    if not token:
        print("❌ 登录失败")
        return False

    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}

    print("🔍 测试菜单权限...")

    # 测试菜单列表权限 (menu:list)
    print("  📋 测试菜单列表权限 (menu:list)")
    response = requests.get("http://localhost:9095/api/v1/admin/menus", headers=headers, timeout=30)
    if response.status_code == 200:
        print("  ✅ 菜单列表权限正常")
    else:
        print(f"  ❌ 菜单列表权限失败: {response.status_code}")
        return False

    # 测试菜单详情权限 (menu:get) - 这是我们新添加的权限
    print("  📄 测试菜单详情权限 (menu:get)")
    response = requests.get("http://localhost:9095/api/v1/admin/menus/1", headers=headers, timeout=30)
    if response.status_code == 200:
        print("  ✅ 菜单详情权限正常")
    else:
        print(f"  ❌ 菜单详情权限失败: {response.status_code}")
        return False

    return True

def test_role_permissions():
    """测试角色权限"""
    token = login()
    if not token:
        print("❌ 登录失败")
        return False

    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}

    print("🔍 测试角色权限...")

    # 测试角色列表权限 (role:list)
    print("  📋 测试角色列表权限 (role:list)")
    response = requests.get("http://localhost:9095/api/v1/admin/roles", headers=headers, timeout=30)
    if response.status_code == 200:
        print("  ✅ 角色列表权限正常")
        result = response.json()
        if result.get('code') == 0 and result.get('data'):
            data = result['data']
            if isinstance(data, list):
                roles = data
            else:
                roles = data.get('list', [])
            print(f"  📊 发现 {len(roles)} 个角色")
            return True
    else:
        print(f"  ❌ 角色列表权限失败: {response.status_code}")
        return False

    return True

def test_permission_permissions():
    """测试权限管理权限"""
    token = login()
    if not token:
        print("❌ 登录失败")
        return False

    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}

    print("🔍 测试权限管理权限...")

    # 测试权限列表权限 (permission:list)
    print("  📋 测试权限列表权限 (permission:list)")
    response = requests.get("http://localhost:9095/api/v1/admin/permissions", headers=headers, timeout=30)
    if response.status_code == 200:
        print("  ✅ 权限列表权限正常")
        result = response.json()
        if result.get('code') == 0 and result.get('data'):
            data = result['data']
            if isinstance(data, list):
                permissions = data
            else:
                permissions = data.get('list', [])
            print(f"  📊 发现 {len(permissions)} 个权限")
            return True
    else:
        print(f"  ❌ 权限列表权限失败: {response.status_code}")
        return False

    return True

def test_file_permissions():
    """测试文件管理权限"""
    token = login()
    if not token:
        print("❌ 登录失败")
        return False

    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}

    print("🔍 测试文件管理权限...")

    # 测试文件列表权限 (file:list)
    print("  📋 测试文件列表权限 (file:list)")
    response = requests.get("http://localhost:9095/api/v1/admin/files", headers=headers, timeout=30)
    if response.status_code == 200:
        print("  ✅ 文件列表权限正常")
    else:
        print(f"  ❌ 文件列表权限失败: {response.status_code}")
        return False

    # 测试获取上传链接权限 (file:get_presigned_upload_url)
    print("  🔗 测试获取上传链接权限 (file:get_presigned_upload_url)")
    response = requests.get("http://localhost:9095/api/v1/admin/files/presigned-upload-url",
                          headers=headers, params={"filename": "test.jpg"}, timeout=30)
    if response.status_code == 200:
        print("  ✅ 获取上传链接权限正常")
    else:
        print(f"  ❌ 获取上传链接权限失败: {response.status_code}")
        return False

    return True

def main():
    print("🚀 开始测试权限系统...")
    print("=" * 60)

    success_count = 0
    total_tests = 4

    # 测试各种权限
    if test_menu_permissions():
        success_count += 1

    if test_role_permissions():
        success_count += 1

    if test_permission_permissions():
        success_count += 1

    if test_file_permissions():
        success_count += 1

    print("\n" + "=" * 60)
    print(f"📊 权限测试完成: {success_count}/{total_tests} 个测试通过")

    if success_count == total_tests:
        print("🎉 所有权限测试通过！权限系统工作正常！")
    else:
        print("⚠️  部分权限测试失败，请检查权限配置")

if __name__ == "__main__":
    main()
