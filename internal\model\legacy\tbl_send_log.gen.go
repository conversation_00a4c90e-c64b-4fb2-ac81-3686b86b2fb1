// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package legacy

import (
	"time"
)

const TableNameTblSendLog = "tbl_send_log"

// TblSendLog mapped from table <tbl_send_log>
type TblSendLog struct {
	ID         int32     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Tpid       int32     `gorm:"column:tpid;not null" json:"tpid"`
	Para       string    `gorm:"column:para" json:"para"`
	Res        string    `gorm:"column:res" json:"res"`
	CreateTime time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP" json:"create_time"`
}

// TableName TblSendLog's table name
func (*TblSendLog) TableName() string {
	return TableNameTblSendLog
}
