-- 批量添加会员卡种类
-- 根据Yogaga实际业务需求添加所有会员卡类型

-- 清理现有测试数据（可选）
-- DELETE FROM membership_card_types WHERE id > 0;

-- 1. 次数卡类型
INSERT INTO membership_card_types (name, category, description, price, original_price, validity_days, times, amount, discount, transfer_limit, freeze_limit, refundable, shareable, can_set_expiry, can_select_stores, can_add_sub_card, can_set_daily_limit, daily_booking_limit, sort_order, status, created_at, updated_at) VALUES
-- 企业课次卡
('企业课次卡（60mins)', 'times', '企业定制60分钟课程次数卡', 0, 0, 365, 10, 0, 1.00, 0, 1, false, true, true, true, true, false, 0, 1, 1, NOW(), NOW()),

-- 团课卡
('Yogaga瑜伽团课卡', 'times', '瑜伽团课专用次数卡', 150000, 180000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, true, 1, 2, 1, NOW(), NOW()),
('Yogaga团课通卡', 'times', '所有团课通用次数卡', 200000, 240000, 365, 15, 0, 1.00, 1, 2, true, false, true, false, false, true, 1, 3, 1, NOW(), NOW()),
('Yogaga特惠团课卡', 'times', '特惠团课次数卡', 120000, 150000, 180, 8, 0, 1.00, 0, 1, false, false, true, true, false, true, 1, 4, 1, NOW(), NOW()),

-- 月缴卡
('Yogaga月缴 (瑜伽团课10次)', 'times', '瑜伽团课月缴套餐10次', 150000, 180000, 30, 10, 0, 1.00, 0, 1, false, false, false, true, false, true, 1, 5, 1, NOW(), NOW()),
('Yogaga月缴 (瑜伽团课5次)', 'times', '瑜伽团课月缴套餐5次', 80000, 100000, 30, 5, 0, 1.00, 0, 1, false, false, false, true, false, true, 1, 6, 1, NOW(), NOW()),
('Yogaga月缴 (普拉提小班10次)', 'times', '普拉提小班月缴套餐10次', 200000, 240000, 30, 10, 0, 1.00, 0, 1, false, false, false, true, false, true, 1, 7, 1, NOW(), NOW()),
('Yogaga月缴 (普拉提小班5次)', 'times', '普拉提小班月缴套餐5次', 110000, 130000, 30, 5, 0, 1.00, 0, 1, false, false, false, true, false, true, 1, 8, 1, NOW(), NOW()),

-- 小班课卡
('Yogaga小班课卡', 'times', '小班课程专用次数卡', 180000, 220000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, true, 1, 9, 1, NOW(), NOW()),
('Yogaga普拉提器械卡', 'times', '普拉提器械课程专用卡', 250000, 300000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, true, 1, 10, 1, NOW(), NOW()),

-- 私教卡
('Yogaga标准私教卡（1V1）', 'times', '标准一对一私教课程卡', 500000, 600000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, false, 0, 11, 1, NOW(), NOW()),
('Yogaga标准私教卡 (1V2)', 'times', '标准一对二私教课程卡', 350000, 420000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, false, 0, 12, 1, NOW(), NOW()),
('Yogaga明星私教卡（1V1）', 'times', '明星教练一对一私教课程卡', 800000, 1000000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, false, 0, 13, 1, NOW(), NOW()),
('Yogaga明星私教卡（1V2)', 'times', '明星教练一对二私教课程卡', 600000, 750000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, false, 0, 14, 1, NOW(), NOW()),
('Yogaga私教通卡 (1V1)', 'times', '私教通用一对一课程卡', 600000, 720000, 365, 12, 0, 1.00, 1, 2, true, false, true, false, false, false, 0, 15, 1, NOW(), NOW()),

-- 特殊课程卡
('Yogaga舞蹈卡', 'times', '舞蹈课程专用次数卡', 160000, 200000, 365, 10, 0, 1.00, 1, 2, true, false, true, true, false, true, 1, 16, 1, NOW(), NOW()),

-- 体验卡
('Yogaga体验卡-焦新琦 (员工代约)', 'times', '员工代约体验卡', 0, 0, 30, 1, 0, 1.00, 0, 0, false, false, false, false, false, false, 0, 17, 1, NOW(), NOW()),
('Yogaga太阳宫预售体验卡', 'times', '太阳宫店预售体验卡', 1000, 5000, 30, 1, 0, 1.00, 0, 0, false, false, false, false, false, false, 0, 18, 1, NOW(), NOW()),
('Yogaga体验卡 (员工代约)', 'times', '员工代约通用体验卡', 0, 0, 30, 1, 0, 1.00, 0, 0, false, false, false, false, false, false, 0, 19, 1, NOW(), NOW()),

-- 员工卡
('Yogaga老师卡', 'times', '瑜伽老师专用卡', 0, 0, 365, 100, 0, 1.00, 0, 0, false, false, true, false, false, false, 0, 20, 1, NOW(), NOW());

-- 2. 期限卡类型
INSERT INTO membership_card_types (name, category, description, price, original_price, validity_days, times, amount, discount, transfer_limit, freeze_limit, refundable, shareable, can_set_expiry, can_select_stores, can_add_sub_card, can_set_daily_limit, daily_booking_limit, sort_order, status, created_at, updated_at) VALUES
-- 门店期限卡
('Yogaga瑜伽&普拉提期限卡 (太阳宫店)', 'period', '太阳宫店瑜伽&普拉提期限卡', 300000, 360000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 21, 1, NOW(), NOW()),
('Yogaga瑜伽&普拉提期限卡 (长寿路店)', 'period', '长寿路店瑜伽&普拉提期限卡', 300000, 360000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 22, 1, NOW(), NOW()),
('Yogaga瑜伽&普拉提期限卡 (书城店)', 'period', '书城店瑜伽&普拉提期限卡', 300000, 360000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 23, 1, NOW(), NOW()),
('Yogaga瑜伽&普拉提期限卡 (全市通)', 'period', '全市通用瑜伽&普拉提期限卡', 350000, 420000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 24, 1, NOW(), NOW()),

('Yogaga瑜伽期限卡 (太阳宫店)', 'period', '太阳宫店瑜伽期限卡', 250000, 300000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 25, 1, NOW(), NOW()),
('Yogaga瑜伽期限卡 (长寿路店)', 'period', '长寿路店瑜伽期限卡', 250000, 300000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 26, 1, NOW(), NOW()),
('Yogaga瑜伽期限卡 (书城店)', 'period', '书城店瑜伽期限卡', 250000, 300000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 27, 1, NOW(), NOW()),
('Yogaga瑜伽期限卡 (全市通)', 'period', '全市通用瑜伽期限卡', 280000, 350000, 90, 0, 0, 1.00, 1, 2, true, false, true, false, false, true, 2, 28, 1, NOW(), NOW());

-- 3. 储值卡类型
INSERT INTO membership_card_types (name, category, description, price, original_price, validity_days, times, amount, discount, transfer_limit, freeze_limit, refundable, shareable, can_set_expiry, can_select_stores, can_add_sub_card, can_set_daily_limit, daily_booking_limit, sort_order, status, created_at, updated_at) VALUES
-- 储值卡
('Yogaga储值卡', 'balance', '通用储值卡，可用于支付各类课程', 0, 0, 365, 0, 100000, 1.00, 1, 2, true, false, true, false, false, false, 0, 29, 1, NOW(), NOW()),
('Yogaga共享储值卡', 'balance', '共享储值卡，可多人使用', 0, 0, 365, 0, 200000, 1.00, 1, 2, true, true, true, false, true, false, 0, 30, 1, NOW(), NOW()),

-- 特殊服务卡
('Yogaga瑜伽垫存放卡', 'balance', '瑜伽垫存放服务卡', 5000, 5000, 365, 0, 5000, 1.00, 0, 0, false, false, true, false, false, false, 0, 31, 1, NOW(), NOW()),
('Yogaga场地租赁卡', 'balance', '场地租赁服务卡', 0, 0, 365, 0, 50000, 1.00, 0, 0, false, false, true, false, false, false, 0, 32, 1, NOW(), NOW());

-- 查看插入结果
SELECT id, name, category, price/100 as price_yuan, validity_days, times, amount/100 as amount_yuan, status 
FROM membership_card_types 
ORDER BY sort_order, id;
