#!/usr/bin/env python3
import requests
import json

def test_api(url, description):
    print(f"\n🔍 测试: {description}")
    print(f"URL: {url}")

    try:
        response = requests.get(url)
        print(f"状态码: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")

    except Exception as e:
        print(f"请求失败: {e}")

def main():
    base_url = "http://127.0.0.1:9095"

    # 测试不需要登录的接口
    test_api(f"{base_url}/api/v1/app/course-categories", "课程分类接口")
    test_api(f"{base_url}/api/v1/app/courses", "课程列表接口")

    # 测试首页接口
    test_api(f"{base_url}/api/v1/app/home", "首页数据接口")

    # 测试门店详情接口（需要ID）
    print("\n🔍 测试门店详情接口（使用ID=1）")
    test_api(f"{base_url}/api/v1/stores/1", "门店详情接口")

if __name__ == "__main__":
    main()
