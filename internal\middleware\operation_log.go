package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"yogaga/internal/model"
	"yogaga/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// responseWriter 包装响应写入器以捕获响应数据
type responseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// OperationLogMiddleware 操作日志中间件
func OperationLogMiddleware(db *gorm.DB) gin.HandlerFunc {
	logService := service.NewOperationLogService(db)

	return func(c *gin.Context) {
		// 获取用户信息
		userID, exists := c.Get("user_id")
		if !exists {
			c.Next()
			return
		}

		username, _ := c.Get("username")
		if username == nil {
			username = "unknown"
		}

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 包装响应写入器
		w := &responseWriter{
			ResponseWriter: c.Writer,
			body:           bytes.NewBufferString(""),
		}
		c.Writer = w

		// 执行请求
		c.Next()

		// 解析操作信息
		module, action, description, resourceType, resourceID := parseOperationInfo(c)

		// 只记录需要记录的操作
		if shouldLogOperation(c.Request.Method, c.Request.URL.Path) {
			// 解析请求数据
			var requestData interface{}
			if len(requestBody) > 0 {
				json.Unmarshal(requestBody, &requestData)
			}

			// 解析响应数据
			var responseData interface{}
			if w.body.Len() > 0 {
				json.Unmarshal(w.body.Bytes(), &responseData)
			}

			// 检查是否有错误
			var err error
			if c.Writer.Status() >= 400 {
				err = fmt.Errorf("HTTP %d", c.Writer.Status())
			}

			// 记录日志
			logService.LogOperation(
				c,
				userID.(string),
				username.(string),
				module,
				action,
				description,
				resourceType,
				resourceID,
				requestData,
				responseData,
				err,
			)
		}
	}
}

// parseOperationInfo 解析操作信息
func parseOperationInfo(c *gin.Context) (model.OperationModule, model.OperationAction, string, string, string) {
	method := c.Request.Method
	path := c.Request.URL.Path

	// 根据路径和方法解析模块和动作
	var module model.OperationModule
	var action model.OperationAction
	var description string
	var resourceType string
	var resourceID string

	// 解析路径获取模块
	switch {
	case strings.Contains(path, "/users"):
		module = model.ModuleUser
		resourceType = "user"
		description = "用户管理操作"
	case strings.Contains(path, "/roles"):
		module = model.ModuleRole
		resourceType = "role"
		description = "角色管理操作"
	case strings.Contains(path, "/permissions"):
		module = model.ModulePermission
		resourceType = "permission"
		description = "权限管理操作"
	case strings.Contains(path, "/menus"):
		module = model.ModuleMenu
		resourceType = "menu"
		description = "菜单管理操作"
	case strings.Contains(path, "/stores"):
		module = model.ModuleStore
		resourceType = "store"
		description = "门店管理操作"
	case strings.Contains(path, "/courses"):
		module = model.ModuleCourse
		resourceType = "course"
		description = "课程管理操作"
	case strings.Contains(path, "/schedules"):
		module = model.ModuleSchedule
		resourceType = "schedule"
		description = "排课管理操作"
	case strings.Contains(path, "/bookings"):
		module = model.ModuleBooking
		resourceType = "booking"
		description = "预约管理操作"
	case strings.Contains(path, "/membership-cards"):
		module = model.ModuleMembershipCard
		resourceType = "membership_card"
		description = "会员卡管理操作"
	case strings.Contains(path, "/coaches"):
		module = model.ModuleCoach
		resourceType = "coach"
		description = "教练管理操作"
	case strings.Contains(path, "/reports"):
		module = model.ModuleReport
		resourceType = "report"
		description = "报表查看操作"
	default:
		module = model.ModuleSystem
		resourceType = "system"
		description = "系统操作"
	}

	// 根据HTTP方法解析动作
	switch method {
	case "POST":
		action = model.ActionCreate
		description = "创建" + getResourceName(resourceType)
	case "PUT", "PATCH":
		action = model.ActionUpdate
		description = "更新" + getResourceName(resourceType)
	case "DELETE":
		action = model.ActionDelete
		description = "删除" + getResourceName(resourceType)
	case "GET":
		action = model.ActionView
		description = "查看" + getResourceName(resourceType)
	default:
		action = model.ActionView
	}

	// 尝试从路径中提取资源ID
	// 这里可以根据具体的路由规则来解析
	// 例如: /api/v1/admin/users/123 -> resourceID = 123

	return module, action, description, resourceType, resourceID
}

// getResourceName 获取资源中文名称
func getResourceName(resourceType string) string {
	switch resourceType {
	case "user":
		return "用户"
	case "role":
		return "角色"
	case "permission":
		return "权限"
	case "menu":
		return "菜单"
	case "store":
		return "门店"
	case "course":
		return "课程"
	case "schedule":
		return "排课"
	case "booking":
		return "预约"
	case "membership_card":
		return "会员卡"
	case "coach":
		return "教练"
	case "report":
		return "报表"
	default:
		return "资源"
	}
}

// shouldLogOperation 判断是否需要记录操作
func shouldLogOperation(method, path string) bool {
	// 不记录的路径
	skipPaths := []string{
		"/health",
		"/ping",
		"/metrics",
		"/favicon.ico",
	}

	for _, skipPath := range skipPaths {
		if strings.Contains(path, skipPath) {
			return false
		}
	}

	// 只记录管理端的操作
	if !strings.Contains(path, "/admin") {
		return false
	}

	// 不记录GET请求（查看操作太频繁）
	if method == "GET" {
		return false
	}

	return true
}
