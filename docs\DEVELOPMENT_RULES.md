# 🏗️ Yogaga 项目开发规范

## 📋 概述

本文档定义了 Yogaga 项目的开发规范，确保代码一致性、API 响应格式统一，以及良好的开发体验。所有开发者和 AI 工具（如 Cursor、Augment）都应该严格遵循这些规范。

## 🎯 核心原则

1. **一致性优先**：所有 API 响应使用统一的 JSON 格式
2. **类型安全**：使用强类型和枚举提高代码安全性
3. **可维护性**：遵循清晰的架构模式和命名规范
4. **前端友好**：API 设计考虑前端使用便利性

## 📊 数据模型规范

### 1. BaseModel 强制使用规范

#### ❌ 错误做法
```go
type User struct {
    gorm.Model  // 不要使用 gorm.Model
    Username string
    Email    string
}
```

#### ✅ 正确做法
```go
type User struct {
    BaseModel   // 必须使用 BaseModel
    Username string `json:"username" gorm:"type:varchar(50);not null"`
    Email    string `json:"email" gorm:"type:varchar(100)"`
}
```

#### BaseModel 定义
```go
type BaseModel struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`
}
```

### 2. JSON 标签规范

#### 必须遵循的规则
- ✅ 所有字段必须有 `json` 标签
- ✅ 使用 snake_case 格式：`json:"user_name"`
- ✅ 敏感字段使用 `json:"-"`：`Password string json:"-"`
- ✅ 关联字段也要有标签：`Roles []Role json:"roles"`

#### 示例对比

**❌ 错误示例**：
```go
type User struct {
    BaseModel
    Username string `gorm:"type:varchar(50)"` // 缺少 json 标签
    Email    string `json:"Email"`            // 使用了 PascalCase
    Password string `json:"password"`         // 敏感字段未隐藏
}
```

**✅ 正确示例**：
```go
type User struct {
    BaseModel
    Username string `json:"username" gorm:"type:varchar(50);not null"`
    Email    string `json:"email" gorm:"type:varchar(100)"`
    Password string `json:"-" gorm:"type:varchar(255);not null"`
    IsActive bool   `json:"is_active" gorm:"default:true"`
    Roles    []Role `json:"roles" gorm:"many2many:user_roles;"`
}
```

### 3. 模型创建检查清单

创建新模型时，必须确保：

- [ ] 继承 `BaseModel` 而不是 `gorm.Model`
- [ ] 所有字段都有 `json` 标签
- [ ] JSON 标签使用 snake_case 格式
- [ ] 敏感字段使用 `json:"-"`
- [ ] 关联字段有正确的 JSON 标签
- [ ] GORM 标签包含必要的约束和注释
- [ ] 枚举类型有相应的验证方法

## 🌐 API 响应规范

### 1. 响应格式统一

#### ✅ 使用 code.AutoResponse
```go
// 成功响应
code.AutoResponse(c, data, nil)

// 错误响应
code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorCode, "错误消息"))
```

#### ❌ 不要使用 gin.H
```go
// 错误做法
c.JSON(200, gin.H{"code": 0, "data": data})
```

#### 例外情况
健康检查端点可以使用 `gin.H`：
```go
func HealthCheck(c *gin.Context) {
    c.JSON(200, gin.H{"status": "ok"})
}
```

### 2. 错误处理规范

#### ✅ 正确的错误响应格式
```go
code.AutoResponse(c, nil, code.NewErrCodeMsg(code.ErrorNotExistCert, "证书不存在"))
```

#### ❌ 错误的格式
```go
code.AutoResponse(c, code.ErrorNotExistCert, err)
```

### 3. JSON 响应示例

#### 修复前（不一致）
```json
{
  "code": 0,
  "msg": "Ok",
  "data": {
    "ID": 5,
    "CreatedAt": "2025-07-10T19:07:21.267+08:00",
    "Name": "admin",
    "parentId": 0
  }
}
```

#### 修复后（一致的 snake_case）
```json
{
  "code": 0,
  "msg": "Ok",
  "data": {
    "id": 5,
    "created_at": "2025-07-10T19:07:21.267+08:00",
    "name": "admin",
    "parent_id": 0
  }
}
```

## 🔐 权限系统规范

### 1. RBAC 模型设计

- **菜单关联权限**：菜单应该与权限关联，而不是直接与角色关联
- **可选权限**：菜单可以选择性地不关联权限（无需权限验证）
- **类型安全**：使用枚举类型定义菜单类型

### 2. 菜单权限关联

```go
type Menu struct {
    BaseModel
    Title         string `json:"title"`
    PermissionKey string `json:"permission"` // 可以为空，表示无需权限
    Type          MenuType `json:"type"`     // 使用枚举类型
}

// 检查菜单是否需要权限验证
func (m *Menu) RequiresPermission() bool {
    return m.PermissionKey != ""
}
```

### 3. JWT 集成

- 用户菜单端点组织在 `/user/menu` 路径下
- JWT 与 Casbin 角色-权限-菜单系统集成
- 确保菜单数据组装时不会因为空权限关联而出错

## 📦 包管理规范

### 1. 依赖管理原则

**强制要求**：使用包管理器，不要手动编辑配置文件

#### ✅ 正确做法
```bash
# Go
go get github.com/gin-gonic/gin
go mod tidy

# Node.js
npm install express
yarn add express

# Python
pip install fastapi
poetry add fastapi
```

#### ❌ 错误做法
```bash
# 不要手动编辑这些文件
vim go.mod
vim package.json
vim requirements.txt
```

### 2. 原因说明

- 包管理器自动解析正确版本
- 处理依赖冲突
- 更新锁文件
- 维护环境一致性
- AI 模型可能会产生错误的版本号

## 🔄 类型转换规范

### 1. 强制使用 cast 库

**强制要求**：禁止使用 strconv，优先使用 cast 库进行类型转换

#### ✅ 正确做法
```go
import "github.com/spf13/cast"

// 字符串转整数
id := cast.ToUint(idStr)
page := cast.ToInt(pageStr)
amount := cast.ToInt64(amountStr)

// 安全转换带验证
if id := cast.ToUint(idStr); id > 0 {
    // 安全使用 id
}

// 处理查询参数
page := cast.ToInt(c.DefaultQuery("page", "1"))
pageSize := cast.ToInt(c.DefaultQuery("page_size", "20"))
```

#### ❌ 错误做法
```go
// 不要使用 strconv
id, err := strconv.ParseUint(idStr, 10, 32)  // 禁止
page, _ := strconv.Atoi(pageStr)             // 禁止
amount, _ := strconv.ParseInt(amountStr, 10, 64) // 禁止
```

### 2. cast 库优势

- **更安全**：提供更好的错误处理和边界情况处理
- **更简洁**：减少样板代码，无需处理错误返回值
- **更一致**：跨不同数据类型的一致性转换接口
- **更健壮**：优雅处理 nil 值和边界情况
- **零值友好**：转换失败时返回类型的零值而不是 panic

## 🛠️ 开发工具配置

### 1. AI 工具使用指南

#### Cursor 和 Augment 应该：
- 创建新模型时自动检查 BaseModel 使用
- 修改现有模型时确保 JSON 标签一致性
- 生成 API 代码时使用项目的响应格式规范
- 遵循权限系统设计模式

#### 代码生成模板
AI 工具应该使用以下模板：

```go
// 模型模板
type {{ModelName}} struct {
    BaseModel
    {{FieldName}} {{FieldType}} `json:"{{snake_case_name}}" gorm:"{{gorm_tags}}"`
}

// Handler 模板
func (h *{{ModelName}}Handler) Create(c *gin.Context) {
    var req dto.Create{{ModelName}}Request
    if err := c.ShouldBindJSON(&req); err != nil {
        code.AutoResponse(c, nil, code.NewErrCodeMsg(code.InvalidParams, "请求参数错误"))
        return
    }

    // 业务逻辑...

    code.AutoResponse(c, result, nil)
}
```

### 2. 代码审查要点

#### 必须检查的项目：
- [ ] 模型是否正确继承 BaseModel
- [ ] JSON 标签格式是否一致（snake_case）
- [ ] API 响应格式是否符合规范
- [ ] 权限系统实现是否正确
- [ ] 错误处理是否使用统一格式
- [ ] 敏感字段是否正确隐藏
- [ ] 类型转换是否使用 cast 库（禁止 strconv）

## 🚀 构建和部署规范

### 1. Docker 构建

- 提供可选的多平台构建开关
- 不是所有情况都需要 multi-platform builds
- 构建脚本应该有清晰的选项控制

### 2. API 路径设计

- 下载路由保持与 GitHub 风格一致
- 用户菜单端点使用 `/user/menu` 路径结构
- 遵循 RESTful 设计原则

## 📝 快速参考

### 常用命令

```bash
# 检查模型 JSON 标签
grep -r "gorm.Model" internal/model/  # 应该返回空

# 检查 strconv 使用情况
grep -r "strconv\." internal/  # 应该尽量避免

# 验证 JSON 格式
curl -H "Authorization: Bearer $TOKEN" http://localhost:9095/api/v1/admin/roles
```

### 常见错误和解决方案

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| API 返回 PascalCase | 使用了 gorm.Model | 改用 BaseModel |
| 字段缺失 JSON 标签 | 忘记添加标签 | 添加 `json:"field_name"` |
| 密码字段暴露 | 未隐藏敏感字段 | 使用 `json:"-"` |
| 类型转换错误 | 使用了 strconv | 改用 cast 库 |

---

**重要提醒**：这些规范是强制性的，所有代码提交前都应该经过检查。AI 工具生成的代码也必须符合这些规范。
