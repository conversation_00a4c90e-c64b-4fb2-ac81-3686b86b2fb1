package bootstrap

import (
	"fmt"
	"log"

	"yogaga/internal/enum"
	"yogaga/internal/model"

	"gorm.io/gorm"
)

// 定义会员卡类型初始化数据
var autoMembershipCardTypes = []struct {
	ID                uint
	Name              string
	Category          enum.MembershipCardCategory
	Description       string
	Price             int
	OriginalPrice     int
	ValidityDays      int
	Times             int
	Amount            int
	Discount          float64
	Shareable         bool
	CanSetExpiry      bool
	CanSelectStores   bool
	CanAddSubCard     bool
	CanSetDailyLimit  bool
	DailyBookingLimit int
	SortOrder         int
	Status            int
}{
	// 次数卡类型
	{ID: 1, Name: "团课通卡", Category: enum.MembershipCardCategoryTimes, Description: "所有团课通用次数卡", Price: 200000, OriginalPrice: 240000, ValidityDays: 365, Times: 15, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 1, Status: 1},
	{ID: 2, Name: "普拉提次卡", Category: enum.MembershipCardCategoryTimes, Description: "普拉提课程专用次数卡", Price: 180000, OriginalPrice: 220000, ValidityDays: 365, Times: 10, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 2, Status: 1},
	{ID: 3, Name: "提升小班卡", Category: enum.MembershipCardCategoryTimes, Description: "提升小班课程专用次数卡", Price: 250000, OriginalPrice: 300000, ValidityDays: 365, Times: 10, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 3, Status: 1},
	{ID: 4, Name: "标准私教1V1", Category: enum.MembershipCardCategoryTimes, Description: "标准私教一对一课程卡", Price: 699000, OriginalPrice: 799000, ValidityDays: 365, Times: 10, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 4, Status: 1},
	{ID: 5, Name: "明星私教1V1", Category: enum.MembershipCardCategoryTimes, Description: "明星私教一对一课程卡", Price: 799000, OriginalPrice: 899000, ValidityDays: 365, Times: 10, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 5, Status: 1},
	{ID: 6, Name: "明星私教1V2", Category: enum.MembershipCardCategoryTimes, Description: "明星私教一对二课程卡", Price: 899000, OriginalPrice: 999000, ValidityDays: 365, Times: 10, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 6, Status: 1},

	// 期限卡类型
	{ID: 7, Name: "瑜伽期限卡", Category: enum.MembershipCardCategoryPeriod, Description: "瑜伽课程期限卡，每日限约2节", Price: 300000, OriginalPrice: 360000, ValidityDays: 30, Times: 0, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: true, DailyBookingLimit: 2, SortOrder: 7, Status: 1},
	{ID: 8, Name: "舞蹈期限卡", Category: enum.MembershipCardCategoryPeriod, Description: "舞蹈课程期限卡，每日限约2节", Price: 350000, OriginalPrice: 420000, ValidityDays: 30, Times: 0, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: true, DailyBookingLimit: 2, SortOrder: 8, Status: 1},
	{ID: 9, Name: "瑜/普期限卡", Category: enum.MembershipCardCategoryPeriod, Description: "瑜伽普拉提课程期限卡，每日限约2节", Price: 400000, OriginalPrice: 480000, ValidityDays: 30, Times: 0, Amount: 0, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: true, DailyBookingLimit: 2, SortOrder: 9, Status: 1},

	// 储值卡类型
	{ID: 10, Name: "共享储值卡", Category: enum.MembershipCardCategoryBalance, Description: "多人共享储值卡，支持副卡功能", Price: 0, OriginalPrice: 0, ValidityDays: 365, Times: 0, Amount: 100000, Discount: 1.0, Shareable: true, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: true, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 10, Status: 1},
	{ID: 11, Name: "储值卡", Category: enum.MembershipCardCategoryBalance, Description: "个人储值卡", Price: 0, OriginalPrice: 0, ValidityDays: 365, Times: 0, Amount: 100000, Discount: 1.0, Shareable: false, CanSetExpiry: true, CanSelectStores: true, CanAddSubCard: false, CanSetDailyLimit: false, DailyBookingLimit: 0, SortOrder: 11, Status: 1},
}

// initializeMembershipCardTypes 初始化会员卡类型数据
func initializeMembershipCardTypes(db *gorm.DB) error {
	log.Println("💳 开始检查会员卡类型数据...")

	// 检查是否已有会员卡类型数据
	var cardTypeCount int64
	if err := db.Model(&model.MembershipCardType{}).Count(&cardTypeCount).Error; err != nil {
		return fmt.Errorf("检查会员卡类型数据失败: %v", err)
	}

	if cardTypeCount > 0 {
		log.Printf("✅ 发现 %d 个会员卡类型，跳过初始化", cardTypeCount)
		return nil
	}

	log.Printf("📝 开始创建 %d 个默认会员卡类型...", len(autoMembershipCardTypes))

	// 开始事务
	tx := db.Begin()
	if tx.Error != nil {
		return fmt.Errorf("开始事务失败: %v", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建会员卡类型
	for _, cardTypeInfo := range autoMembershipCardTypes {

		cardType := model.MembershipCardType{
			BaseModel: model.BaseModel{
				ID: cardTypeInfo.ID,
			},
			Name:              cardTypeInfo.Name,
			Category:          cardTypeInfo.Category,
			Description:       cardTypeInfo.Description,
			Price:             cardTypeInfo.Price,
			OriginalPrice:     cardTypeInfo.OriginalPrice,
			ValidityDays:      cardTypeInfo.ValidityDays,
			Times:             cardTypeInfo.Times,
			Amount:            cardTypeInfo.Amount,
			Discount:          cardTypeInfo.Discount,
			Shareable:         cardTypeInfo.Shareable,
			CanSetExpiry:      cardTypeInfo.CanSetExpiry,
			CanSelectStores:   cardTypeInfo.CanSelectStores,
			CanAddSubCard:     cardTypeInfo.CanAddSubCard,
			CanSetDailyLimit:  cardTypeInfo.CanSetDailyLimit,
			DailyBookingLimit: cardTypeInfo.DailyBookingLimit,
			SortOrder:         cardTypeInfo.SortOrder,
			Status:            enum.CourseCategoryStatus(cardTypeInfo.Status),
		}

		if err := tx.Create(&cardType).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("创建会员卡类型 %s 失败: %v", cardTypeInfo.Name, err)
		}

		log.Printf("✅ 创建会员卡类型: %s (ID: %d)", cardTypeInfo.Name, cardTypeInfo.ID)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("提交会员卡类型初始化事务失败: %v", err)
	}

	// 更新PostgreSQL序列
	if err := updatePostgreSQLSequences(db); err != nil {
		log.Printf("⚠️ 更新PostgreSQL序列失败: %v", err)
		// 不返回错误，因为这不是致命问题
	}

	log.Printf("✅ 会员卡类型初始化完成: %d 个类型", len(autoMembershipCardTypes))
	return nil
}
