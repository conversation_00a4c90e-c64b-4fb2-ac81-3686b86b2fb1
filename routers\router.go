package routers

import (
	"yogaga/configs"
	"yogaga/internal/dto"
	"yogaga/internal/handler"
	"yogaga/internal/middleware"
	"yogaga/internal/service"
	"yogaga/internal/worker"
	"yogaga/pkg/jwtx"
	"yogaga/pkg/storage"

	"github.com/casbin/casbin/v2"
	"github.com/charmbracelet/log"
	"github.com/gin-gonic/gin"
	"github.com/hibiken/asynq"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

// NewRouter loads the middlewares, routes, handlers following best practices.
func NewRouter(storage storage.Storage, db *gorm.DB, enforcer *casbin.Enforcer, jwtSvc *jwtx.Service, wechatConfig configs.WeChat, asynqRedisOpt asynq.RedisClientOpt, redisClient *redis.Client, commitHash, buildTime, platform string) *gin.Engine {
	gin.SetMode(gin.DebugMode)
	g := gin.New()
	g.Use(gin.Recovery(), middleware.LoggerWithCharmbracelet(), middleware.Cors())

	// 🏥 健康检查接口 (Kubernetes 存活/就绪探针)
	// 功能：检查服务运行状态，返回版本信息和构建时间
	g.GET("/healthz", func(c *gin.Context) {
		response := dto.HealthzResponse{
			Status:     "ok",
			CommitHash: commitHash,
			BuildTime:  buildTime,
			Platform:   platform,
		}
		c.JSON(200, response)
	})

	// Initialize Asynq client
	asynqClient := asynq.NewClient(asynqRedisOpt)

	// Initialize all handlers first
	userHandler := handler.NewUserHandler(db, jwtSvc, enforcer, storage)
	roleHandler := handler.NewRoleHandler(db, enforcer)
	fileHandler := handler.NewFileHandler(storage, db)
	permissionHandler := handler.NewPermissionHandler(db, enforcer)
	menuHandler := handler.NewMenuHandler(db, enforcer)

	// Initialize WeChat service and handler
	wechatService := service.NewWeChatService(wechatConfig)
	wechatHandler := handler.NewWeChatHandler(db, jwtSvc, wechatService)

	// Initialize Notification service
	notificationService := service.NewNotificationService(db, wechatService, asynqClient)

	// Initialize Membership Reminder service
	membershipReminderService := service.NewMembershipReminderService(db, wechatService, asynqClient)

	// Initialize Attendance service
	_ = service.NewAttendanceService(db, membershipReminderService)

	// Initialize Booking service and handler
	bookingService := service.NewBookingService(db, asynqClient, wechatService)
	bookingHandler := handler.NewBookingHandler(db, bookingService, notificationService, storage)

	// Initialize Membership handlers
	membershipTypeHandler := handler.NewMembershipTypeHandler(db)
	membershipCardHandler := handler.NewMembershipCardHandler(db)

	// Initialize Course handlers
	courseCategoryHandler := handler.NewCourseCategoryHandler(db)
	courseHandler := handler.NewCourseHandler(db)

	// Initialize Store handlers
	storeHandler := handler.NewStoreHandler(db, storage)
	classroomHandler := handler.NewClassRoomHandler(db)

	// Initialize Schedule handlers
	scheduleHandler := handler.NewScheduleHandler(db)

	// Initialize Admin Booking handlers
	adminBookingHandler := handler.NewAdminBookingHandler(db, bookingService)

	// Initialize Admin Notification handlers
	adminNotificationHandler := handler.NewAdminNotificationHandler(db)

	// Initialize Banner handlers
	bannerHandler := handler.NewBannerHandler(db, storage)
	coachBannerHandler := handler.NewCoachBannerHandler(db, storage)

	// Initialize Dashboard handlers
	dashboardHandler := handler.NewDashboardHandler(db)

	// Initialize Batch Course handlers
	batchCourseHandler := handler.NewBatchCourseHandler(db)

	// Initialize Staff handlers (基于现有users和roles表)
	staffHandler := handler.NewStaffHandler(db)

	// Initialize Resource Assignment handlers
	resourceAssignmentHandler := handler.NewResourceAssignmentHandler(db)

	// Initialize User Store Assignment handlers
	userStoreAssignmentHandler := handler.NewUserStoreAssignmentHandler(db)

	// Initialize Operation Log handlers
	operationLogHandler := handler.NewOperationLogHandler(db)

	// Initialize Course Review handlers
	courseReviewHandler := handler.NewCourseReviewHandler(db)

	// Initialize Report handlers
	reportHandler := handler.NewReportHandler(db)

	// Initialize Flexible Deduction handlers
	flexibleDeductionHandler := handler.NewFlexibleDeductionHandler(db)

	// Advanced membership functionality is now integrated into main membership handler

	// Initialize Checkin and Queue handlers
	checkinHandler := handler.NewCheckinHandler(db)
	queueHandler := handler.NewQueueHandler(db, notificationService)

	// Initialize App handlers
	homeHandler := handler.NewHomeHandler(db, storage)
	favoriteHandler := handler.NewFavoriteHandler(db, storage)
	appCourseHandler := handler.NewAppCourseHandler(db, storage)
	userCenterHandler := handler.NewUserCenterHandler(db, storage)
	// shareHandler := handler.NewShareHandler(db, storage) // 分享功能由前端实现

	// Initialize and start booking worker
	bookingWorker := worker.NewBookingWorker(db, bookingService, asynqRedisOpt)
	go func() {
		if err := bookingWorker.Start(); err != nil {
			log.Error("启动预约任务处理器失败", "error", err)
		}
	}()

	// Schedule daily membership checks
	go func() {
		if err := membershipReminderService.ScheduleDailyCheck(); err != nil {
			log.Error("安排会员卡检查定时任务失败", "error", err)
		}
	}()

	// 📁 文件下载接口 (公开访问)
	// 功能：统一通过file_id下载文件，支持URL格式 /files/{file_id}
	g.GET("/files/:file_id", fileHandler.DownloadFileByID)

	// 1. API Prefix Group
	api := g.Group("/api")
	{
		// 2. Version Group
		v1 := api.Group("/v1")
		{
			// 🌐 公开接口 (无需认证)
			public := v1.Group("/public")
			{
				// 🔑 管理员登录接口
				// 功能：管理员用户名密码登录，返回JWT令牌
				public.POST("/admin/login", userHandler.AdminLogin)
				// 🔄 令牌刷新接口
				// 功能：使用refresh_token刷新access_token
				public.POST("/auth/refresh", userHandler.RefreshToken)
			}

			// 🛡️ 管理端接口 (需要管理员权限)
			admin := v1.Group("/admin")
			admin.Use(middleware.AuthMiddleware(jwtSvc))     // 应用管理员认证中间件
			admin.Use(middleware.OperationLogMiddleware(db)) // 应用操作日志中间件
			{
				// � 仪表盘
				// 功能：获取仪表盘数据汇总
				admin.GET("/dashboard", dashboardHandler.GetDashboardData)

				// 📚 批量课程管理
				batchCourseGroup := admin.Group("/batch-courses")
				{
					// 功能：批量创建课程预览
					batchCourseGroup.POST("/preview", batchCourseHandler.GetBatchCreatePreview)
					// 功能：批量创建课程
					batchCourseGroup.POST("/create", batchCourseHandler.BatchCreateCourses)
				}

				// �� 用户管理
				userGroup := admin.Group("/users")
				{
					// 功能：获取用户列表
					userGroup.GET("", userHandler.GetUsers)
					// 功能：创建管理员用户
					userGroup.POST("", middleware.CheckPermissionByKey("user:create", enforcer), userHandler.AdminCreateUser)
					// 功能：更新用户角色权限
					userGroup.PUT("/:id/roles", middleware.CheckPermissionByKey("user:update_roles", enforcer), userHandler.UpdateUserRoles)
					// 功能：删除用户
					userGroup.DELETE("/:id", middleware.CheckPermissionByKey("user:delete", enforcer), userHandler.DeleteUser)
					// 功能：获取当前用户可访问的菜单树
					userGroup.GET("/menus", menuHandler.GetUserMenus)
				}

				// 👥 员工管理（基于现有users和roles表）
				staffGroup := admin.Group("/staff")
				{
					// 功能：获取员工列表（管理员用户）
					staffGroup.GET("", staffHandler.GetStaff)
					// 功能：获取在职员工列表（用于销售选择等场景）
					staffGroup.GET("/active", staffHandler.GetActiveStaff)
					// 功能：更新员工状态（在职/离职）
					staffGroup.PUT("/:id/status", staffHandler.UpdateStaffStatus)
					// 功能：获取销售员工列表（用于会员开卡/充值场景）
					staffGroup.GET("/sales", staffHandler.GetSalesStaff)
				}

				// 👨‍🏫 教练状态管理（基于现有users表）
				coachStatusGroup := admin.Group("/coach-status")
				{
					// 功能：获取教练列表
					coachStatusGroup.GET("", staffHandler.GetCoaches)
					// 功能：更新教练状态
					coachStatusGroup.PUT("/:id/status", staffHandler.UpdateCoachStatus)
				}

				// 🔄 资源分配管理
				resourceGroup := admin.Group("/resource-assignments")
				{
					// 功能：分配会员给销售
					resourceGroup.POST("/members", resourceAssignmentHandler.AssignMembersToSales)
					// 功能：获取销售负责的会员列表
					resourceGroup.GET("/sales/:sales_id/members", resourceAssignmentHandler.GetSalesMembers)
					// 功能：转移会员给其他销售
					resourceGroup.PUT("/members/transfer", resourceAssignmentHandler.TransferMember)
					// 功能：获取未分配的会员列表
					resourceGroup.GET("/members/unassigned", resourceAssignmentHandler.GetUnassignedMembers)
					// 功能：移除会员分配
					resourceGroup.DELETE("/members", resourceAssignmentHandler.RemoveMemberAssignment)
				}

				// � 用户门店分配管理
				userStoreGroup := admin.Group("/user-store-assignments")
				{
					// 功能：分配用户到门店
					userStoreGroup.POST("", userStoreAssignmentHandler.AssignUserToStore)
					// 功能：获取用户门店分配列表
					userStoreGroup.GET("/users/:user_id", userStoreAssignmentHandler.GetUserStoreAssignments)
					// 功能：获取门店的用户列表
					userStoreGroup.GET("/stores/:store_id", userStoreAssignmentHandler.GetStoreUsers)
					// 功能：移除用户门店分配
					userStoreGroup.DELETE("", userStoreAssignmentHandler.RemoveUserStoreAssignment)
					// 功能：更新用户平台访问权限
					userStoreGroup.PUT("/users/:user_id/platform-access", userStoreAssignmentHandler.UpdateUserPlatformAccess)
					// 功能：根据平台获取用户列表
					userStoreGroup.GET("/users", userStoreAssignmentHandler.GetUsersByPlatform)
				}

				// �� 角色管理
				roleGroup := admin.Group("/roles")
				{
					// 功能：创建新角色
					roleGroup.POST("", middleware.CheckPermissionByKey("role:create", enforcer), roleHandler.Create)
					// 功能：获取角色列表（支持分页和搜索）
					roleGroup.GET("", middleware.CheckPermissionByKey("role:list", enforcer), roleHandler.List)
					// 功能：获取角色详情
					roleGroup.GET("/:id", middleware.CheckPermissionByKey("role:get", enforcer), roleHandler.Get)
					// 功能：更新角色信息
					roleGroup.PUT("/:id", middleware.CheckPermissionByKey("role:update", enforcer), roleHandler.Update)
					// 功能：删除单个角色
					roleGroup.DELETE("/:id", middleware.CheckPermissionByKey("role:delete", enforcer), roleHandler.Delete)
					// 功能：批量删除角色
					roleGroup.DELETE("", middleware.CheckPermissionByKey("role:delete", enforcer), roleHandler.BatchDelete)
					// 功能：获取角色关联的权限列表
					roleGroup.GET("/:id/permissions", middleware.CheckPermissionByKey("role:get_permissions", enforcer), roleHandler.GetPermissions)
					// 功能：更新角色权限关联
					roleGroup.PUT("/:id/permissions", middleware.CheckPermissionByKey("role:update_permissions", enforcer), roleHandler.UpdatePermissions)
				}

				// 📁 文件管理
				fileGroup := admin.Group("/files")
				{
					// 统一文件上传接口（前端传递 purpose 参数指定用途）
					fileGroup.POST("/upload", middleware.CheckPermissionByKey("file:upload", enforcer), fileHandler.Upload)

					// 注释掉多余的上传接口，统一使用 /upload
					// fileGroup.POST("/proxy-upload", middleware.CheckPermissionByKey("file:proxy_upload", enforcer), fileHandler.ProxyUpload)

					// 注释掉预签名相关接口，只使用代理上传
					// fileGroup.POST("/presigned-upload-url", middleware.CheckPermissionByKey("file:get_presigned_upload_url", enforcer), fileHandler.GetPresignedUploadURL)
					// fileGroup.GET("/presigned-download-url", middleware.CheckPermissionByKey("file:get_presigned_download_url", enforcer), fileHandler.GetPresignedDownloadURL)

					// 文件管理接口
					// 功能：获取文件列表
					fileGroup.GET("", middleware.CheckPermissionByKey("file:list", enforcer), fileHandler.GetFileList)
					// 功能：删除文件
					fileGroup.DELETE("/:id", middleware.CheckPermissionByKey("file:delete", enforcer), fileHandler.DeleteFile)
					// 功能：批量删除文件
					fileGroup.DELETE("", middleware.CheckPermissionByKey("file:delete", enforcer), fileHandler.BatchDeleteFiles)
					// 功能：获取实体关联的文件列表
					fileGroup.GET("/by-entity", middleware.CheckPermissionByKey("file:list", enforcer), fileHandler.GetFilesByEntity)
				}

				// 🔐 权限管理
				permissionGroup := admin.Group("/permissions")
				{
					// 功能：获取权限列表（支持分页和搜索）
					permissionGroup.GET("", middleware.CheckPermissionByKey("permission:list", enforcer), permissionHandler.List)
					// 功能：创建新权限
					permissionGroup.POST("", middleware.CheckPermissionByKey("permission:create", enforcer), permissionHandler.Create)
					// 功能：获取权限详情
					permissionGroup.GET("/:id", middleware.CheckPermissionByKey("permission:view", enforcer), permissionHandler.Get)
					// 功能：更新权限信息
					permissionGroup.PUT("/:id", middleware.CheckPermissionByKey("permission:update", enforcer), permissionHandler.Update)
					// 功能：删除单个权限
					permissionGroup.DELETE("/:id", middleware.CheckPermissionByKey("permission:delete", enforcer), permissionHandler.Delete)
					// 功能：批量删除权限
					permissionGroup.DELETE("", middleware.CheckPermissionByKey("permission:delete", enforcer), permissionHandler.BatchDelete)
				}

				// 📋 菜单管理
				menuGroup := admin.Group("/menus")
				{
					// 功能：创建菜单项（支持多级菜单）
					menuGroup.POST("", middleware.CheckPermissionByKey("menu:create", enforcer), menuHandler.Create)
					// 功能：获取菜单列表（树形结构）
					menuGroup.GET("", middleware.CheckPermissionByKey("menu:list", enforcer), menuHandler.List)
					// 功能：获取菜单详情
					menuGroup.GET("/:id", middleware.CheckPermissionByKey("menu:get", enforcer), menuHandler.Get)
					// 功能：更新菜单信息
					menuGroup.PUT("/:id", middleware.CheckPermissionByKey("menu:update", enforcer), menuHandler.Update)
					// 功能：删除单个菜单
					menuGroup.DELETE("/:id", middleware.CheckPermissionByKey("menu:delete", enforcer), menuHandler.Delete)
					// 功能：批量删除菜单
					menuGroup.DELETE("", middleware.CheckPermissionByKey("menu:delete", enforcer), menuHandler.BatchDelete)
				}

				// 💳 会员卡类型管理
				membershipTypeGroup := admin.Group("/membership-types")
				{
					// 功能：获取会员卡类型列表（次卡、期限卡、储值卡）
					membershipTypeGroup.GET("", membershipTypeHandler.GetMembershipTypes)
					// 功能：获取会员卡类型详情
					membershipTypeGroup.GET("/:id", membershipTypeHandler.GetMembershipType)
					// 功能：创建新的会员卡类型
					membershipTypeGroup.POST("", membershipTypeHandler.CreateMembershipType)
					// 功能：更新会员卡类型信息
					membershipTypeGroup.PUT("/:id", membershipTypeHandler.UpdateMembershipType)
					// 功能：删除会员卡类型
					membershipTypeGroup.DELETE("/:id", membershipTypeHandler.DeleteMembershipType)
				}

				// 💰 会员卡实例管理
				membershipCardGroup := admin.Group("/membership-cards")
				{
					// 功能：获取会员卡列表（支持用户筛选）
					membershipCardGroup.GET("", membershipCardHandler.GetMembershipCards)
					// 功能：获取会员卡详情（统一接口，包含副卡信息）
					membershipCardGroup.GET("/:id", membershipCardHandler.GetCardDetail)
					// 功能：获取会员卡交易记录
					membershipCardGroup.GET("/:id/transactions", membershipCardHandler.GetCardTransactions)
					// 功能：创建新会员卡
					membershipCardGroup.POST("", membershipCardHandler.CreateMembershipCard)
					// 功能：创建副卡（共享储值卡功能）
					membershipCardGroup.POST("/:id/sub-cards", membershipCardHandler.CreateSubCard)
					// 功能：获取副卡列表
					membershipCardGroup.GET("/:id/sub-cards", membershipCardHandler.GetSubCards)
					// 功能：转让会员卡
					membershipCardGroup.PUT("/:id/transfer", membershipCardHandler.TransferMembershipCard)
					// 功能：会员卡充值
					membershipCardGroup.PUT("/:id/recharge", membershipCardHandler.RechargeMembershipCard)
					// 功能：会员卡扣费
					membershipCardGroup.PUT("/:id/deduct", membershipCardHandler.DeductMembershipCard)
					// 功能：冻结/解冻会员卡
					membershipCardGroup.PUT("/:id/freeze", membershipCardHandler.FreezeMembershipCard)
					// 功能：升级会员卡
					membershipCardGroup.PUT("/:id/upgrade", membershipCardHandler.UpgradeMembershipCard)
					// 功能：请假会员卡
					membershipCardGroup.PUT("/:id/leave", membershipCardHandler.LeaveMembershipCard)
					// 功能：延期会员卡
					membershipCardGroup.PUT("/:id/extend", membershipCardHandler.ExtendMembershipCard)
				}

				// 🏷️ 课程分类管理
				courseCategoryGroup := admin.Group("/course-categories")
				{
					// 功能：获取课程分类列表
					courseCategoryGroup.GET("", courseCategoryHandler.GetCourseCategories)
					// 功能：获取所有启用的课程分类
					courseCategoryGroup.GET("/all", courseCategoryHandler.GetAllCourseCategories)
					// 功能：获取课程分类详情
					courseCategoryGroup.GET("/:id", courseCategoryHandler.GetCourseCategory)
					// 功能：创建课程分类
					courseCategoryGroup.POST("", courseCategoryHandler.CreateCourseCategory)
					// 功能：更新课程分类
					courseCategoryGroup.PUT("/:id", courseCategoryHandler.UpdateCourseCategory)
					// 功能：删除课程分类
					courseCategoryGroup.DELETE("/:id", courseCategoryHandler.DeleteCourseCategory)
				}

				// ⚙️ 扣减规则管理已迁移到灵活扣减系统
				// 请使用 /api/v1/admin/flexible/deduction-rules 相关接口

				// 📚 课程管理
				courseGroup := admin.Group("/courses")
				{
					// 功能：获取课程列表（支持分页、搜索、筛选）
					courseGroup.GET("", courseHandler.GetCourses)
					// 功能：获取课程详情
					courseGroup.GET("/:id", courseHandler.GetCourse)
					// 功能：创建新课程（课程模板）
					courseGroup.POST("", courseHandler.CreateCourse)
					// 功能：更新课程信息
					courseGroup.PUT("/:id", courseHandler.UpdateCourse)
					// 功能：删除课程
					courseGroup.DELETE("/:id", courseHandler.DeleteCourse)

					// 功能：批量创建课程（一步完成：课程创建+排课）
					courseGroup.POST("/batch", courseHandler.BatchCreateCourses)
					// 功能：课程排课（基于已创建的课程进行排课）
					courseGroup.POST("/schedule", courseHandler.ScheduleCourses)
					// 功能：获取课程支持的会员卡类型（基于扣减规则）
					courseGroup.GET("/:id/supported-card-types", courseHandler.GetCourseSupportedCardTypes)
				}

				// 🔄 灵活扣减系统
				flexibleGroup := admin.Group("/flexible")
				{
					// 课程类型配置管理
					flexibleGroup.GET("/course-types", middleware.CheckPermissionByKey("flexible:course_types:list", enforcer), flexibleDeductionHandler.GetCourseTypeConfigs)
					flexibleGroup.POST("/course-types", middleware.CheckPermissionByKey("flexible:course_types:create", enforcer), flexibleDeductionHandler.CreateCourseTypeConfig)
					flexibleGroup.PUT("/course-types/:id", middleware.CheckPermissionByKey("flexible:course_types:update", enforcer), flexibleDeductionHandler.UpdateCourseTypeConfig)
					flexibleGroup.DELETE("/course-types/:id", middleware.CheckPermissionByKey("flexible:course_types:delete", enforcer), flexibleDeductionHandler.DeleteCourseTypeConfig)

					// 灵活扣减规则管理
					flexibleGroup.GET("/deduction-rules", middleware.CheckPermissionByKey("flexible:deduction_rules:list", enforcer), flexibleDeductionHandler.GetFlexibleDeductionRules)
					flexibleGroup.GET("/deduction-rules/:id", middleware.CheckPermissionByKey("flexible:deduction_rules:detail", enforcer), flexibleDeductionHandler.GetFlexibleDeductionRuleDetail)
					flexibleGroup.POST("/deduction-rules", middleware.CheckPermissionByKey("flexible:deduction_rules:create", enforcer), flexibleDeductionHandler.CreateFlexibleDeductionRule)
					flexibleGroup.PUT("/deduction-rules/:id", middleware.CheckPermissionByKey("flexible:deduction_rules:update", enforcer), flexibleDeductionHandler.UpdateFlexibleDeductionRule)
					flexibleGroup.DELETE("/deduction-rules/:id", middleware.CheckPermissionByKey("flexible:deduction_rules:delete", enforcer), flexibleDeductionHandler.DeleteFlexibleDeductionRule)
					flexibleGroup.PUT("/deduction-rules/:id/toggle", middleware.CheckPermissionByKey("flexible:deduction_rules:toggle", enforcer), flexibleDeductionHandler.ToggleFlexibleDeductionRuleStatus)

					// 测试扣减规则
					flexibleGroup.POST("/test-deduction", middleware.CheckPermissionByKey("flexible:test_deduction", enforcer), flexibleDeductionHandler.TestDeductionRule)

					// 获取会员卡支持的课程类型
					flexibleGroup.GET("/card-types/:card_type_id/supported-courses", middleware.CheckPermissionByKey("flexible:card_supported_courses", enforcer), flexibleDeductionHandler.GetCardSupportedCourseTypes)
				}

				// �🏪 门店管理
				storeGroup := admin.Group("/stores")
				{
					// 功能：获取门店列表
					storeGroup.GET("", storeHandler.GetStores)
					// 功能：获取门店详情
					storeGroup.GET("/:id", storeHandler.GetStore)
					// 功能：创建新门店
					storeGroup.POST("", storeHandler.CreateStore)
					// 功能：更新门店信息
					storeGroup.PUT("/:id", storeHandler.UpdateStore)
					// 功能：删除门店
					storeGroup.DELETE("/:id", storeHandler.DeleteStore)
				}

				// 🏛️ 教室管理
				classroomGroup := admin.Group("/classrooms")
				{
					// 功能：获取教室列表
					classroomGroup.GET("", classroomHandler.GetClassRooms)
					// 功能：获取教室详情
					classroomGroup.GET("/:id", classroomHandler.GetClassRoom)
					// 功能：创建新教室
					classroomGroup.POST("", classroomHandler.CreateClassRoom)
					// 功能：更新教室信息
					classroomGroup.PUT("/:id", classroomHandler.UpdateClassRoom)
					// 功能：删除教室
					classroomGroup.DELETE("/:id", classroomHandler.DeleteClassRoom)
				}

				// 功能：获取门店下的教室列表
				storeGroup.GET("/:id/classrooms", classroomHandler.GetStoreClassRooms)

				// 🧘‍♀️ 教练管理 (管理端)
				coachGroup := admin.Group("/coaches")
				{
					// 功能：获取教练列表（管理端视图）
					coachGroup.GET("", userHandler.GetAdminCoaches)
					// 功能：获取教练详情（管理端视图）
					coachGroup.GET("/:id", userHandler.GetAdminCoach)
					// 功能：更新教练信息
					coachGroup.PUT("/:id", userHandler.UpdateCoach)
					// 功能：更新教练状态（启用/停用）
					coachGroup.PUT("/:id/status", userHandler.UpdateCoachStatus)
				}

				// 📅 课程排期管理 (管理端)
				scheduleGroup := admin.Group("/schedules")
				{
					// 功能：获取课程排期列表（支持日期、教练、门店筛选）
					scheduleGroup.GET("", scheduleHandler.GetSchedules)
					// 功能：创建课程排期
					scheduleGroup.POST("", scheduleHandler.CreateSchedule)
					// 功能：更新课程排期
					scheduleGroup.PUT("/:id", scheduleHandler.UpdateSchedule)
				}

				// 📋 预约管理 (管理端)
				bookingGroup := admin.Group("/bookings")
				{
					// 功能：获取预约列表（管理端视图，支持多维度筛选）
					bookingGroup.GET("", adminBookingHandler.GetBookings)
					// 功能：获取预约详情（管理端视图）
					bookingGroup.GET("/:id", adminBookingHandler.GetBooking)
					// 功能：更新预约状态（确认、取消、完成等）
					bookingGroup.PUT("/:id/status", adminBookingHandler.UpdateBookingStatus)
					// 功能：获取预约统计数据
					bookingGroup.GET("/stats", adminBookingHandler.GetBookingStats)
					// 功能：自动取消课程（因人数不足）
					bookingGroup.POST("/auto-cancel", adminBookingHandler.AutoCancelCourse)
				}

				// 后台消息通知管理
				notificationGroup := admin.Group("/notifications")
				{
					// 功能：获取消息列表
					notificationGroup.GET("", adminNotificationHandler.GetNotifications)
					// 功能：获取未读消息数量
					notificationGroup.GET("/unread-count", adminNotificationHandler.GetUnreadCount)
					// 功能：获取消息统计
					notificationGroup.GET("/stats", adminNotificationHandler.GetNotificationStats)
					// 功能：标记消息为已读
					notificationGroup.PUT("/:id/read", adminNotificationHandler.MarkAsRead)
					// 功能：标记所有消息为已读
					notificationGroup.PUT("/read-all", adminNotificationHandler.MarkAllAsRead)
					// 功能：删除消息
					notificationGroup.DELETE("/:id", adminNotificationHandler.DeleteNotification)
				}

				// 🎠 轮播图管理
				bannerGroup := admin.Group("/banners")
				{
					// 功能：获取轮播图列表
					bannerGroup.GET("", bannerHandler.GetBanners)
					// 功能：获取轮播图详情
					bannerGroup.GET("/:id", bannerHandler.GetBanner)
					// 功能：创建轮播图
					bannerGroup.POST("", bannerHandler.CreateBanner)
					// 功能：更新轮播图
					bannerGroup.PUT("/:id", bannerHandler.UpdateBanner)
					// 功能：删除轮播图
					bannerGroup.DELETE("/:id", bannerHandler.DeleteBanner)
				}

				// 🧘‍♀️ 教练轮播图管理
				coachBannerGroup := admin.Group("/coach-banners")
				{
					// 功能：获取教练轮播图列表
					coachBannerGroup.GET("/coach/:coach_id", coachBannerHandler.GetCoachBanners)
					// 功能：获取教练轮播图详情
					coachBannerGroup.GET("/:id", coachBannerHandler.GetCoachBanner)
					// 功能：创建教练轮播图
					coachBannerGroup.POST("", coachBannerHandler.CreateCoachBanner)
					// 功能：更新教练轮播图
					coachBannerGroup.PUT("/:id", coachBannerHandler.UpdateCoachBanner)
					// 功能：删除教练轮播图
					coachBannerGroup.DELETE("/:id", coachBannerHandler.DeleteCoachBanner)
				}

				// 📊 报表管理 (管理端)
				reportGroup := admin.Group("/reports")
				{
					// 功能：会员卡销售报表（销量、收入统计）
					reportGroup.GET("/card-sales", reportHandler.GetCardSalesReport)
					// 功能：预约报表（预约量、取消率等）
					reportGroup.GET("/bookings", reportHandler.GetBookingReport)
					// 功能：课程报表（热门课程、上课率等）
					reportGroup.GET("/courses", reportHandler.GetCourseReport)
					// 功能：教练报表（教练工作量、评价等）
					reportGroup.GET("/coaches", reportHandler.GetCoachReport)
				}

				// 📋 操作日志管理
				logGroup := admin.Group("/logs")
				{
					// 功能：获取操作日志列表
					logGroup.GET("", operationLogHandler.GetOperationLogs)
					// 功能：获取操作日志详情
					logGroup.GET("/:id", operationLogHandler.GetOperationLogDetail)
					// 功能：删除操作日志（仅超级管理员）
					logGroup.DELETE("/:id", operationLogHandler.DeleteOperationLog)
					// 功能：获取操作统计
					logGroup.GET("/stats", operationLogHandler.GetOperationStats)
					// 功能：清理旧日志
					logGroup.POST("/clean", operationLogHandler.CleanOldLogs)
					// 功能：获取日志模块列表
					logGroup.GET("/modules", operationLogHandler.GetLogModules)
					// 功能：获取日志动作列表
					logGroup.GET("/actions", operationLogHandler.GetLogActions)
				}

				// ⭐ 课程评价管理
				reviewGroup := admin.Group("/course-reviews")
				{
					// 功能：获取课程评价列表
					reviewGroup.GET("", courseReviewHandler.GetCourseReviews)
					// 功能：获取课程评价详情
					reviewGroup.GET("/:id", courseReviewHandler.GetCourseReviewDetail)
					// 功能：删除课程评价
					reviewGroup.DELETE("/:id", courseReviewHandler.DeleteCourseReview)
					// 功能：获取评价统计
					reviewGroup.GET("/stats", courseReviewHandler.GetReviewStats)
				}

				// 🎯 排队管理 (管理端)
				queueGroup := admin.Group("/queue")
				{
					// 功能：手动处理排队通知（管理员操作）
					queueGroup.POST("/courses/:course_id/notify", queueHandler.ProcessQueueNotification)
				}
			}

			// 3. Audience: App (for regular users, e.g., web app, mini-program)
			app := v1.Group("/app")
			{
				// 🔐 微信小程序登录（无需认证）
				// 功能：用户通过微信授权登录小程序
				app.POST("/wechat/login", wechatHandler.Login)

				// 🏃‍♀️ 课程功能（免登录）
				// 功能：获取课程列表（支持门店、教练、类型、级别筛选）
				app.GET("/courses", appCourseHandler.GetCourses)
				// 功能：获取课程详情（包含教练、门店、排期信息）
				app.GET("/courses/:id", appCourseHandler.GetCourse)
				// 功能：按日期查询课程（日历视图）
				app.GET("/courses/date", appCourseHandler.GetCoursesByDate)
				// 功能：获取课程日历（月视图，显示有课程的日期）
				app.GET("/courses/calendar", appCourseHandler.GetCoursesCalendar)
				// 功能：获取课程分类列表
				app.GET("/course-categories", courseCategoryHandler.GetAllCourseCategories)

				// 🧘‍♀️ 我们的教练功能（免登录）
				// 功能：获取教练列表（支持首页推荐筛选）
				app.GET("/coaches", userHandler.GetCoaches)
				// 功能：获取教练详情（简介、专长、课程排期）
				app.GET("/coaches/:id", userHandler.GetCoach)
				// 功能：获取教练详情轮播图
				app.GET("/coaches/:id/banners", coachBannerHandler.GetActiveCoachBanners)

				// 🏪 门店精选功能（免登录）
				// 功能：获取门店列表（支持距离计算和排序）
				app.GET("/stores", storeHandler.GetStoresWithDistance)
				// 功能：获取门店详情（包含教练、课程信息）
				app.GET("/stores/:id", storeHandler.GetStore)

				// 🏠 轮播图功能（免登录）
				// 功能：获取轮播图列表（支持点击跳转）
				app.GET("/banners", bannerHandler.GetActiveBanners)

				// 需要认证的微信小程序接口
				appAuth := app.Group("")
				appAuth.Use(middleware.AuthMiddleware(jwtSvc))
				{
					// 👤 用户信息管理
					// 功能：更新用户微信信息（昵称、头像等）
					appAuth.PUT("/wechat/userinfo", wechatHandler.UpdateUserInfo)
					// 功能：获取当前用户微信信息
					appAuth.GET("/wechat/userinfo", wechatHandler.GetUserInfo)

					// 📅 预约管理系统
					// 功能：创建课程预约（支持异步处理和排队机制）
					appAuth.POST("/bookings", bookingHandler.CreateBooking)
					// 功能：查询异步预约任务状态
					appAuth.GET("/bookings/status/:task_id", bookingHandler.GetBookingStatus)
					// 功能：获取用户预约列表（支持状态筛选）
					appAuth.GET("/bookings", bookingHandler.GetBookingList)
					// 功能：取消预约（支持退款逻辑）
					appAuth.DELETE("/bookings/cancel", bookingHandler.CancelBooking)
					// 功能：课程评价（1-5星评分+文字评价）
					appAuth.POST("/bookings/rate", bookingHandler.RateBooking)
					// 功能：获取预约详情
					appAuth.GET("/bookings/:id", bookingHandler.GetBookingDetail)
					// 功能：获取取消预约信息（弹框显示）
					appAuth.GET("/bookings/:id/cancel-info", bookingHandler.GetCancelBookingInfo)

					// 💳 会员卡系统
					// 功能：获取用户所有会员卡（次卡、期限卡、储值卡）
					appAuth.GET("/membership-cards", membershipCardHandler.GetUserMembershipCards)
					// 功能：获取会员卡详情（包含使用记录）
					appAuth.GET("/membership-cards/:id", membershipCardHandler.GetMembershipCard)

					// 🏠 首页数据聚合
					// 功能：获取首页所有数据（轮播图、精选课程、推荐教练、门店）
					appAuth.GET("/home", homeHandler.GetHomeData)
					// 功能：获取广告弹窗（后台可配置）
					appAuth.GET("/ads", homeHandler.GetAds)
					// 功能：获取精选内容（课程+教练）
					appAuth.GET("/featured", homeHandler.GetFeaturedContent)

					// ❤️ 收藏功能
					// 功能：获取用户收藏列表（支持类型筛选）
					appAuth.GET("/favorites", favoriteHandler.GetFavorites)
					// 功能：添加收藏（课程、教练、门店）
					appAuth.POST("/favorites", favoriteHandler.AddFavorite)
					// 功能：取消收藏
					appAuth.DELETE("/favorites/:id", favoriteHandler.RemoveFavorite)
					// 功能：检查是否已收藏
					appAuth.GET("/favorites/check", favoriteHandler.CheckFavorite)

					// 📤 分享功能 - 由前端实现，后端路由已注释
					// 功能：分享课程（生成分享链接和文案）
					// appAuth.POST("/share/courses/:id", shareHandler.ShareCourse)
					// 功能：分享教练（生成分享链接和文案）
					// appAuth.POST("/share/coaches/:id", shareHandler.ShareCoach)
					// 功能：分享门店（生成分享链接和文案）
					// appAuth.POST("/share/stores/:id", shareHandler.ShareStore)
					// 功能：获取分享统计（总次数、今日次数）
					// appAuth.GET("/share/stats", shareHandler.GetShareStats)

					// 👤 用户中心功能
					// 功能：获取用户个人资料
					appAuth.GET("/user/profile", userCenterHandler.GetUserProfile)
					// 功能：更新用户个人资料
					appAuth.PUT("/user/profile", userCenterHandler.UpdateUserProfile)
					// 功能：获取用户统计数据（预约次数、消费金额等）
					appAuth.GET("/user/stats", userCenterHandler.GetUserStats)
					// 功能：获取用户会员卡汇总信息
					appAuth.GET("/user/membership-summary", userCenterHandler.GetUserMembershipSummary)
					// 功能：获取最近预约记录
					appAuth.GET("/user/recent-bookings", userCenterHandler.GetRecentBookings)

					// 📁 文件上传功能 - 注释掉，只有admin端需要文件上传
					// 功能：通用文件上传（前端传递 file_type 参数指定用途，如 avatar、banner 等）
					// appAuth.POST("/files/upload", fileHandler.Upload)

					// 📱 扫码签到功能
					checkinGroup := appAuth.Group("/checkin")
					{
						// 功能：生成课程签到二维码（开课前后90分钟内有效，300米位置验证）
						checkinGroup.GET("/courses/:course_id/qrcode", checkinHandler.GenerateQRCode)
						// 功能：扫码签到（验证本人账号、位置距离≤300米、时间窗口90分钟）
						checkinGroup.POST("", checkinHandler.CheckIn)
						// 功能：获取课程签到状态
						checkinGroup.GET("/courses/:course_id/status", checkinHandler.GetCheckinStatus)
					}

					// 🎯 智能排队功能
					queueGroup := appAuth.Group("/queue")
					{
						// 功能：加入课程排队（课程满员时自动排队）
						queueGroup.POST("", queueHandler.JoinQueue)
						// 功能：获取课程排队状态和位置
						queueGroup.GET("/courses/:course_id/status", queueHandler.GetQueueStatus)
						// 功能：取消排队
						queueGroup.DELETE("/:queue_id", queueHandler.CancelQueue)
						// 功能：获取用户所有排队记录
						queueGroup.GET("", queueHandler.GetUserQueues)
					}
				}
			}

			// 3. Audience: Open/Developer (requires API Key)
			open := v1.Group("/open")
			// The line below is to avoid 'unused variable' error, remove it when you add routes.
			_ = open
			// open.Use(middleware.APIKeyAuthMiddleware())
			// {
			// 	open.POST("/partner/sync", ...)
			// }
		}
	}
	return g
}
