#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查现有会员卡交易记录情况
"""

import requests
import json
from collections import defaultdict

class TransactionRecordChecker:
    def __init__(self, base_url: str = "http://localhost:9095"):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.token = None
        
    def login(self, username: str = "admin", password: str = "admin123") -> bool:
        """登录系统"""
        print("🔐 正在登录系统...")
        
        login_url = f"{self.base_url}/api/v1/public/admin/login"
        login_data = {"username": username, "password": password}
        
        try:
            response = self.session.post(login_url, json=login_data)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    self.token = result.get('data', {}).get('access_token')
                    if self.token:
                        self.session.headers.update({
                            'Authorization': f'Bearer {self.token}',
                            'Content-Type': 'application/json'
                        })
                        print("✅ 登录成功")
                        return True
            print(f"❌ 登录失败: {response.status_code}")
            return False
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def get_all_cards(self) -> list:
        """获取所有会员卡"""
        print("\n📋 获取所有会员卡...")
        
        try:
            params = {
                "page": 1,
                "page_size": 100
            }
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards", params=params)
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    cards = result.get('data', {}).get('list', [])
                    print(f"✅ 获取到 {len(cards)} 张会员卡")
                    return cards
                else:
                    print(f"❌ 获取会员卡失败: {result.get('msg')}")
                    return []
            else:
                print(f"❌ 获取会员卡失败: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ 获取会员卡异常: {e}")
            return []
    
    def get_card_transactions(self, card_id: int) -> list:
        """获取指定会员卡的交易记录"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/admin/membership-cards/{card_id}/transactions")
            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    return result.get('data', {}).get('list', [])
            return []
        except Exception as e:
            print(f"❌ 获取卡片 {card_id} 交易记录异常: {e}")
            return []
    
    def analyze_transaction_records(self, cards: list):
        """分析交易记录情况"""
        print("\n🔍 分析交易记录情况...")
        
        # 交易类型中文映射
        transaction_type_names = {
            "issue": "开卡",
            "recharge": "充值", 
            "deduct": "扣费",
            "transfer": "转卡",
            "freeze": "冻结",
            "unfreeze": "解冻",
            "extend": "延期",
            "upgrade": "升级",
            "refund": "退款"
        }
        
        # 统计数据
        total_cards = len(cards)
        cards_with_transactions = 0
        cards_without_issue_record = []
        transaction_type_stats = defaultdict(int)
        total_transactions = 0
        
        print(f"\n📊 分析 {total_cards} 张会员卡的交易记录...")
        
        for i, card in enumerate(cards):
            card_id = card.get('id')
            card_number = card.get('card_number')
            
            # 获取交易记录
            transactions = self.get_card_transactions(card_id)
            
            if transactions:
                cards_with_transactions += 1
                total_transactions += len(transactions)
                
                # 统计交易类型
                has_issue_record = False
                for transaction in transactions:
                    trans_type = transaction.get('transaction_type')
                    transaction_type_stats[trans_type] += 1
                    if trans_type == 'issue':
                        has_issue_record = True
                
                # 检查是否缺少开卡记录
                if not has_issue_record:
                    cards_without_issue_record.append({
                        'id': card_id,
                        'card_number': card_number,
                        'created_at': card.get('created_at')
                    })
            else:
                # 没有任何交易记录的卡片
                cards_without_issue_record.append({
                    'id': card_id,
                    'card_number': card_number,
                    'created_at': card.get('created_at')
                })
            
            # 显示进度
            if (i + 1) % 10 == 0 or (i + 1) == total_cards:
                print(f"  进度: {i + 1}/{total_cards}")
        
        # 输出统计结果
        print(f"\n📈 统计结果:")
        print(f"  总会员卡数量: {total_cards}")
        print(f"  有交易记录的卡片: {cards_with_transactions}")
        print(f"  无交易记录的卡片: {total_cards - cards_with_transactions}")
        print(f"  总交易记录数: {total_transactions}")
        
        print(f"\n📋 交易类型统计:")
        print("交易类型\t中文名称\t记录数量")
        print("----------------------------------------")
        for trans_type, count in sorted(transaction_type_stats.items()):
            chinese_name = transaction_type_names.get(trans_type, "未知")
            print(f"{trans_type:<12}\t{chinese_name}\t\t{count}")
        
        # 检查缺少开卡记录的卡片
        if cards_without_issue_record:
            print(f"\n⚠️ 发现 {len(cards_without_issue_record)} 张卡片缺少开卡记录:")
            print("卡号\t\t\tID\t创建时间")
            print("------------------------------------------------")
            for card in cards_without_issue_record[:10]:  # 只显示前10个
                print(f"{card['card_number']:<15}\t{card['id']}\t{card['created_at']}")
            
            if len(cards_without_issue_record) > 10:
                print(f"... 还有 {len(cards_without_issue_record) - 10} 张卡片")
        else:
            print(f"\n✅ 所有会员卡都有开卡记录")
        
        # 分析问题
        print(f"\n🔍 问题分析:")
        missing_types = []
        expected_types = ["issue", "recharge", "deduct", "freeze", "unfreeze", "extend", "upgrade"]
        
        for expected_type in expected_types:
            if transaction_type_stats[expected_type] == 0:
                missing_types.append(transaction_type_names[expected_type])
        
        if missing_types:
            print(f"  缺少的交易类型: {', '.join(missing_types)}")
            print(f"  这表明相应的操作没有记录交易")
        else:
            print(f"  ✅ 所有主要交易类型都有记录")
        
        return {
            'total_cards': total_cards,
            'cards_with_transactions': cards_with_transactions,
            'total_transactions': total_transactions,
            'transaction_type_stats': dict(transaction_type_stats),
            'cards_without_issue_record': len(cards_without_issue_record),
            'missing_types': missing_types
        }
    
    def run_check(self):
        """运行完整检查"""
        print("🚀 开始检查会员卡交易记录...")
        print("=" * 80)
        
        # 1. 登录
        if not self.login():
            return False
        
        # 2. 获取所有会员卡
        cards = self.get_all_cards()
        if not cards:
            return False
        
        # 3. 分析交易记录
        stats = self.analyze_transaction_records(cards)
        
        print("\n" + "=" * 80)
        print("📊 检查结果总结:")
        print(f"  会员卡总数: {stats['total_cards']}")
        print(f"  有交易记录: {stats['cards_with_transactions']}")
        print(f"  交易记录总数: {stats['total_transactions']}")
        print(f"  缺少开卡记录: {stats['cards_without_issue_record']}")
        
        if stats['missing_types']:
            print(f"  ⚠️ 缺少交易类型: {', '.join(stats['missing_types'])}")
            print(f"  💡 建议: 检查相应操作的代码，确保记录了交易")
        else:
            print(f"  ✅ 所有主要交易类型都有记录")
        
        return True

def main():
    """主函数"""
    checker = TransactionRecordChecker()
    success = checker.run_check()
    
    if success:
        print("\n🏆 交易记录检查完成！")
        exit(0)
    else:
        print("\n💥 交易记录检查失败！")
        exit(1)

if __name__ == "__main__":
    main()
